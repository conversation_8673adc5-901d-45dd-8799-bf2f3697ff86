/*
 * @Author: luoxi
 * @Date: 2022-01-25 09:51:12
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-02-21 22:57:42
 * @FilePath: \vue-admin-box\vite.config.ts
 * @Description: 
 */
import { ConfigEnv, UserConfigExport,loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import { viteMockServe } from 'vite-plugin-mock'
// import { vitePluginSvg } from "@webxrd/vite-plugin-svg"
import { resolve } from 'path'
// const WaitExternalPlugin = require('wait-external-webpack-plugin');
const pathResolve = (dir: string): any => {
  return resolve(__dirname, ".", dir)
}

const alias: Record<string, string> = {
  '@': pathResolve("src")
}

/** 
 * @description-en vite document address
 * @description-cn vite官网
 * https://vitejs.cn/config/ */
export default ({mode, command }: ConfigEnv): UserConfigExport => {
   const env = loadEnv(mode, process.cwd());
  const { VITE_STATIC_URL } = env;
  const prodMock = false;
  return {
    base:VITE_STATIC_URL,
    // publicDir:'assets',

    resolve: {
      alias
    },
    server: {
      port: 3001,
      host: '0.0.0.0',
      https: true,
      open: true,  
      headers: {
        'Access-Control-Allow-Origin': '*',
      },
      hmr: {
        timeout: 120000 // 增加 HMR 超时时间到 120 秒
      },
      watch: {
        usePolling: true, // 在某些系统上更可靠
      },
      proxy: { // 代理配置
         "/api": {
          // target: 'http://butler.idicc.cn',
          target:  'https://pangustg.idicc.cn',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, ""),
        },  
        "/sso":  {
         target:   'https://pangustg.idicc.cn/',
         changeOrigin: true,
         rewrite: (path) => path.replace(/^\/sso/, ""),
       },

         "/pangu":  {
          target:   'https://pangustg.idicc.cn/',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/pangu/, ""),
        },
        // 增加代理超时设置
        // timeout: 120000,
      },
    },
    build: {
      sourcemap: false,
      chunkSizeWarningLimit: 1500,
      // assetsDir: 'static/jh/assets',
   
      // assetsDir: '/static/jh/',

      // publicDir: 'http://static.idicc.cn/static/jh/',
      // rollupOptions: {
      //   output: {
      //     manualChunks: {
      //       'echarts': ['echarts']
      //     }
      //   }
      // }
      rollupOptions: {
        input: {
          index: resolve(__dirname, 'index.html')
        },
        output: {
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/name1-[hash].[ext]',
          // assetFileNames: (assetInfo) => {
          //   if (assetInfo.type === 'asset' && /\.(jpe?g|png|gif|svg)$/i.test(assetInfo.name)) {
          //     return 'static/img/[name]-[hash].[ext]';
          //   }   if (assetInfo.type === 'asset' && /\.(ttf|woff|woff2|eot)$/i.test(assetInfo.name)) {
          //     return 'static/fonts/[name]-[hash].[ext]';
          //   } 
          //   return 'static/[ext]/name1-[hash].[ext]';
          // },
          // inlineDynamicImports: true,
        // 最小化拆分包
        manualChunks: (id) => {
          if (id.includes('node_modules')) {
            return id.toString().split('node_modules/')[1].split('/')[0].toString();
          }
        },
          // compact: isProduction ? true : false
        }
      }
    },
    plugins: [
      vue(),
      viteMockServe({
        mockPath: 'mock',
        localEnabled: command === 'serve',
        prodEnabled: command !== 'serve' && prodMock,
        watchFiles: true,
        injectCode: `
          import { setupProdMockServer } from '../mockProdServer';
          setupProdMockServer();
        `,
        logger: true,
      }),
/*       // vitePluginSvg({
      //   // 必要的。必须是绝对路径组成的数组。
      //   iconDirs: [
      //     resolve(__dirname, 'src/assets/svg'),
      //   ],
      //   // 必要的。入口script
      //   main: resolve(__dirname, 'src/main.js'),
      //   symbolIdFormat: 'icon-[name]'
      // }), */
    //   new WaitExternalPlugin({
    //     test: /\.js$/,  // 正则匹配需要处理的 entry，默认对所有 entry 进行处理
    // }),
    ],
    css: {
      postcss: {
        plugins: [
          require("postcss-pxtorem")({
            rootValue: 16, // 换算的基数
            unitPrecision: 5, //保留小数位
            selectorBlackList: [".wu"], // 忽略转换正则匹配项
            propList: ["*"],
            minPixelValue: 12,
          }),
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === 'charset') {
                  atRule.remove();
                }
              }
            }
          },
        ],
      },
      
    },
    optimizeDeps: {
      // 优化依赖预构建
      esbuildOptions: {
        target: 'es2020', // 设置目标 ES 版本
      },
      force: true, // 强制重新构建依赖
    },

  };
}
