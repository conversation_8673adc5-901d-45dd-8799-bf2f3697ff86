import { MockMethod } from 'vite-plugin-mock'

export default [
  {
    url: `/mock/card/list`,
    method: 'post',
    response: ({ body }) => {
      const { page, pageSize } = body
      return {
        code: 200,
        data: {
          [`list|${pageSize}`]: [{
            'id|+1': 0,
            title: '@ctitle',
            image: 'http://blog.51weblove.com/wp-content/uploads/2019/03/2019032323331541.jpg',
            time: '@date(yyyy-MM-dd hh:mm:ss)'
          }],
          pager: {
            page: page,
            pageSize: pageSize,
            total: 198
          }
        },
        msg: ''
      };
    }
  },
  {
    url: `/mock/sso/admin/role/acl/roleAclTree`,
    method: 'get',
    response: ({ body }) => {
      const { page, pageSize } = body
      return{
        "result": [
            {
                "id": 182,
                "aclCode": "dpar",
                "aclName": "数字产研",
                "aclType": 1,
                "aclUrl": null,
                "seq": 10,
                "parentId": 0,
                "aclDTOList": [
                    {
                        "id": 183,
                        "aclCode": "dpar",
                        "aclName": "产业速览",
                        "aclType": 2,
                        "aclUrl": null,
                        "seq": 2,
                        "parentId": 182,
                        "aclDTOList": []
                    },
                    {
                        "id": 184,
                        "aclCode": "dpar",
                        "aclName": "产业分析",
                        "aclType": 2,
                        "aclUrl": null,
                        "seq": 1,
                        "parentId": 182,
                        "aclDTOList": []
                    },
                    {
                        "id": 185,
                        "aclCode": "dpar",
                        "aclName": "产业报告",
                        "aclType": 2,
                        "aclUrl": null,
                        "seq": 0,
                        "parentId": 182,
                        "aclDTOList": []
                    }
                ]
            },
            {
                "id": 186,
                "aclCode": "dpar",
                "aclName": "数智招商",
                "aclType": 1,
                "aclUrl": null,
                "seq": 9,
                "parentId": 0,
                "aclDTOList": [
                    {
                        "id": 187,
                        "aclCode": "dpar",
                        "aclName": "优企智搜",
                        "aclType": 2,
                        "aclUrl": null,
                        "seq": 0,
                        "parentId": 186,
                        "aclDTOList": []
                    },
                    {
                        "id": 188,
                        "aclCode": "dpar",
                        "aclName": "招商智推",
                        "aclType": 2,
                        "aclUrl": null,
                        "seq": 0,
                        "parentId": 186,
                        "aclDTOList": []
                    },
                    {
                        "id": 189,
                        "aclCode": "dpar",
                        "aclName": "招商智管",
                        "aclType": 2,
                        "aclUrl": null,
                        "seq": 0,
                        "parentId": 186,
                        "aclDTOList": []
                    }
                ]
            },
            {
                "id": 190,
                "aclCode": "190",
                "aclName": "蓝屏",
                "aclType": 1,
                "aclUrl": null,
                "seq": 6,
                "parentId": 0,
                "aclDTOList": [
                    {
                        "id": 112,
                        "aclCode": "112",
                        "aclName": "产业链首页",
                        "aclType": 3,
                        "aclUrl": "/admin/orgIndustryChainRelation/list",
                        "seq": 0,
                        "parentId": 190,
                        "aclDTOList": [
                            {
                                "id": 49,
                                "aclCode": "49",
                                "aclName": "产业地图",
                                "aclType": 3,
                                "aclUrl": "/admin/orgIndustryChainRelation/map",
                                "seq": 0,
                                "parentId": 112,
                                "aclDTOList": []
                            },
                            {
                                "id": 50,
                                "aclCode": "50",
                                "aclName": "产业全景",
                                "aclType": 3,
                                "aclUrl": "/admin/orgIndustryChainRelation/atlas",
                                "seq": 0,
                                "parentId": 112,
                                "aclDTOList": []
                            },
                            {
                                "id": 139,
                                "aclCode": null,
                                "aclName": "产业洞察",
                                "aclType": 3,
                                "aclUrl": "/admin/org/count/report/countEnterpriseNumByModel",
                                "seq": 0,
                                "parentId": 112,
                                "aclDTOList": []
                            },
                            {
                                "id": 162,
                                "aclCode": null,
                                "aclName": "产业招商",
                                "aclType": 1,
                                "aclUrl": null,
                                "seq": 0,
                                "parentId": 112,
                                "aclDTOList": [
                                    {
                                        "id": 135,
                                        "aclCode": null,
                                        "aclName": "招商情报",
                                        "aclType": 3,
                                        "aclUrl": "/admin/business/enterprise/attractInvestmentInformationStatistics",
                                        "seq": 0,
                                        "parentId": 162,
                                        "aclDTOList": []
                                    },
                                    {
                                        "id": 163,
                                        "aclCode": null,
                                        "aclName": "招商雷达",
                                        "aclType": 3,
                                        "aclUrl": "/admin/business/enterprise/attractInvestmentRadarList",
                                        "seq": 0,
                                        "parentId": 162,
                                        "aclDTOList": []
                                    },
                                    {
                                        "id": 173,
                                        "aclCode": null,
                                        "aclName": "招商管理",
                                        "aclType": 3,
                                        "aclUrl": "/admin/investment/attraction/clue/pageList",
                                        "seq": 0,
                                        "parentId": 162,
                                        "aclDTOList": []
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                "id": 15911140,
                "aclCode": "yqjsc",
                "aclName": "园区驾驶舱",
                "aclType": 1,
                "aclUrl": null,
                "seq": 3,
                "parentId": 0,
                "aclDTOList": [
                    {
                        "id": 15911142,
                        "aclCode": "qydc",
                        "aclName": "企业洞察",
                        "aclType": 2,
                        "aclUrl": "/board/pcEnterpriseInsight",
                        "seq": 4,
                        "parentId": 15911140,
                        "aclDTOList": []
                    },
                    {
                        "id": 15911143,
                        "aclCode": "nhtj",
                        "aclName": "能耗统计",
                        "aclType": 2,
                        "aclUrl": "/board/energy/overview",
                        "seq": 3,
                        "parentId": 15911140,
                        "aclDTOList": []
                    },
                    {
                        "id": 15911144,
                        "aclCode": "hzzx",
                        "aclName": "合作咨询",
                        "aclType": 1,
                        "aclUrl": null,
                        "seq": 2,
                        "parentId": 15911140,
                        "aclDTOList": []
                    },
                    {
                        "id": 15911145,
                        "aclCode": "grzx",
                        "aclName": "个人中心",
                        "aclType": 1,
                        "aclUrl": null,
                        "seq": 1,
                        "parentId": 15911140,
                        "aclDTOList": []
                    }
                ]
            },
            {
                "id": 15911146,
                "aclCode": "gzt",
                "aclName": "工作台",
                "aclType": 1,
                "aclUrl": null,
                "seq": 2,
                "parentId": 0,
                "aclDTOList": [
                    {
                        "id": 15911147,
                        "aclCode": "fygl",
                        "aclName": "房源管理",
                        "aclType": 1,
                        "aclUrl": null,
                        "seq": 10,
                        "parentId": 15911146,
                        "aclDTOList": [
                            {
                                "id": 15911158,
                                "aclCode": "lygl",
                                "aclName": "楼栋管理",
                                "aclType": 2,
                                "aclUrl": "/building/getListPage",
                                "seq": 0,
                                "parentId": 15911147,
                                "aclDTOList": []
                            },
                            {
                                "id": 15911159,
                                "aclCode": "fygl",
                                "aclName": "房源管理",
                                "aclType": 2,
                                "aclUrl": "/houses/getListPage",
                                "seq": 0,
                                "parentId": 15911147,
                                "aclDTOList": [
                                    {
                                        "id": 15911160,
                                        "aclCode": "bjfy",
                                        "aclName": "编辑房源",
                                        "aclType": 3,
                                        "aclUrl": "/houses/formSubmit",
                                        "seq": 0,
                                        "parentId": 15911159,
                                        "aclDTOList": []
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "id": 15911148,
                        "aclCode": "zygl",
                        "aclName": "租约管理",
                        "aclType": 1,
                        "aclUrl": null,
                        "seq": 9,
                        "parentId": 15911146,
                        "aclDTOList": [
                            {
                                "id": 15911161,
                                "aclCode": "zygl",
                                "aclName": "租约管理",
                                "aclType": 2,
                                "aclUrl": "/lease/page",
                                "seq": 1,
                                "parentId": 15911148,
                                "aclDTOList": [
                                    {
                                        "id": 15911163,
                                        "aclCode": "xzzy",
                                        "aclName": "新增租约",
                                        "aclType": 3,
                                        "aclUrl": "/lease/add",
                                        "seq": 0,
                                        "parentId": 15911161,
                                        "aclDTOList": []
                                    },
                                    {
                                        "id": 15911164,
                                        "aclCode": "bjzy",
                                        "aclName": "编辑租约",
                                        "aclType": 3,
                                        "aclUrl": "/lease/update",
                                        "seq": 0,
                                        "parentId": 15911161,
                                        "aclDTOList": []
                                    },
                                    {
                                        "id": 15911165,
                                        "aclCode": "sczy",
                                        "aclName": "删除",
                                        "aclType": 3,
                                        "aclUrl": "/lease/delete",
                                        "seq": 0,
                                        "parentId": 15911161,
                                        "aclDTOList": []
                                    }
                                ]
                            },
                            {
                                "id": 15911162,
                                "aclCode": "dbzy",
                                "aclName": "待办租约",
                                "aclType": 2,
                                "aclUrl": "/pendingLease/pageList",
                                "seq": 0,
                                "parentId": 15911148,
                                "aclDTOList": []
                            }
                        ]
                    },
                    {
                        "id": 15911149,
                        "aclCode": "zdgl",
                        "aclName": "账单管理",
                        "aclType": 1,
                        "aclUrl": "/bill/pageList",
                        "seq": 8,
                        "parentId": 15911146,
                        "aclDTOList": [
                            {
                                "id": 15911166,
                                "aclCode": "dyzd",
                                "aclName": "导入账单",
                                "aclType": 3,
                                "aclUrl": "/bill/upload",
                                "seq": 0,
                                "parentId": 15911149,
                                "aclDTOList": []
                            },
                            {
                                "id": 15911167,
                                "aclCode": "dczd",
                                "aclName": "导出账单",
                                "aclType": 3,
                                "aclUrl": "/bill/download",
                                "seq": 0,
                                "parentId": 15911149,
                                "aclDTOList": []
                            },
                            {
                                "id": 15911168,
                                "aclCode": "tjzd",
                                "aclName": "添加账单",
                                "aclType": 3,
                                "aclUrl": "/bill/add",
                                "seq": 0,
                                "parentId": 15911149,
                                "aclDTOList": []
                            },
                            {
                                "id": 15911169,
                                "aclCode": "tjzd",
                                "aclName": "编辑账单",
                                "aclType": 3,
                                "aclUrl": "/bill/update",
                                "seq": 0,
                                "parentId": 15911149,
                                "aclDTOList": []
                            }
                        ]
                    },
                    {
                        "id": 15911150,
                        "aclCode": "nhjc",
                        "aclName": "能耗监测",
                        "aclType": 1,
                        "aclUrl": null,
                        "seq": 7,
                        "parentId": 15911146,
                        "aclDTOList": [
                            {
                                "id": 15911170,
                                "aclCode": "nhlb",
                                "aclName": "能耗列表",
                                "aclType": 3,
                                "aclUrl": "/energy/getListPage",
                                "seq": 1,
                                "parentId": 15911150,
                                "aclDTOList": [
                                    {
                                        "id": 15911172,
                                        "aclCode": "nhdr",
                                        "aclName": "批量导入",
                                        "aclType": 3,
                                        "aclUrl": "/energy/upload",
                                        "seq": 0,
                                        "parentId": 15911170,
                                        "aclDTOList": []
                                    },
                                    {
                                        "id": 15911173,
                                        "aclCode": "nhdc",
                                        "aclName": "批量导出",
                                        "aclType": 3,
                                        "aclUrl": "/energy/downloadAll",
                                        "seq": 0,
                                        "parentId": 15911170,
                                        "aclDTOList": []
                                    },
                                    {
                                        "id": 15911174,
                                        "aclCode": "nhjcAdd",
                                        "aclName": "新增",
                                        "aclType": 3,
                                        "aclUrl": "",
                                        "seq": 0,
                                        "parentId": 15911170,
                                        "aclDTOList": []
                                    },
                                    {
                                        "id": 15911185,
                                        "aclCode": "nhjcEdit",
                                        "aclName": "编辑",
                                        "aclType": 3,
                                        "aclUrl": "",
                                        "seq": 0,
                                        "parentId": 15911170,
                                        "aclDTOList": []
                                    }
                                ]
                            },
                            {
                                "id": 15911171,
                                "aclCode": "yjlb",
                                "aclName": "预警列表",
                                "aclType": 3,
                                "aclUrl": "/energy/warn/getListPage",
                                "seq": 0,
                                "parentId": 15911150,
                                "aclDTOList": [
                                    {
                                        "id": 15911175,
                                        "aclCode": "nhyjgz",
                                        "aclName": "规则设置",
                                        "aclType": 3,
                                        "aclUrl": "/energy/warn/rule/setRule",
                                        "seq": 0,
                                        "parentId": 15911171,
                                        "aclDTOList": []
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "id": 15911151,
                        "aclCode": "qygl",
                        "aclName": "企业管理",
                        "aclType": 1,
                        "aclUrl": null,
                        "seq": 6,
                        "parentId": 15911146,
                        "aclDTOList": [
                            {
                                "id": 15911176,
                                "aclCode": "qylb",
                                "aclName": "企业列表",
                                "aclType": 2,
                                "aclUrl": "/enterprise/getListPage",
                                "seq": 1,
                                "parentId": 15911151,
                                "aclDTOList": [
                                    {
                                        "id": 15911178,
                                        "aclCode": "qyAdd",
                                        "aclName": "新增",
                                        "aclType": 3,
                                        "aclUrl": "",
                                        "seq": 0,
                                        "parentId": 15911176,
                                        "aclDTOList": []
                                    },
                                    {
                                        "id": 15911183,
                                        "aclCode": "qyEdit",
                                        "aclName": "编辑",
                                        "aclType": 3,
                                        "aclUrl": "",
                                        "seq": 0,
                                        "parentId": 15911176,
                                        "aclDTOList": []
                                    },
                                    {
                                        "id": 15911184,
                                        "aclCode": "qyDel",
                                        "aclName": "删除",
                                        "aclType": 3,
                                        "aclUrl": "",
                                        "seq": 0,
                                        "parentId": 15911176,
                                        "aclDTOList": []
                                    }
                                ]
                            },
                            {
                                "id": 15911177,
                                "aclCode": "qyjysj",
                                "aclName": "企业经营数据",
                                "aclType": 2,
                                "aclUrl": "/enterprise/business/getListPage",
                                "seq": 0,
                                "parentId": 15911151,
                                "aclDTOList": [
                                    {
                                        "id": 15911179,
                                        "aclCode": "qyjysjpldr",
                                        "aclName": "批量导入",
                                        "aclType": 3,
                                        "aclUrl": "/enterprise/business/uploadTemplate",
                                        "seq": 0,
                                        "parentId": 15911177,
                                        "aclDTOList": []
                                    },
                                    {
                                        "id": 15911180,
                                        "aclCode": "qyjysjpldc",
                                        "aclName": "批量导出",
                                        "aclType": 3,
                                        "aclUrl": "/enterprise/business/downloadAll",
                                        "seq": 0,
                                        "parentId": 15911177,
                                        "aclDTOList": []
                                    },
                                    {
                                        "id": 15911181,
                                        "aclCode": "qyjysjEdit",
                                        "aclName": "编辑",
                                        "aclType": 3,
                                        "aclUrl": null,
                                        "seq": 0,
                                        "parentId": 15911177,
                                        "aclDTOList": []
                                    },
                                    {
                                        "id": 15911182,
                                        "aclCode": "qyjysjDel",
                                        "aclName": "删除",
                                        "aclType": 3,
                                        "aclUrl": null,
                                        "seq": 0,
                                        "parentId": 15911177,
                                        "aclDTOList": []
                                    }
                                ]
                            }
                        ]
                    },
                    {
                        "id": 15911152,
                        "aclCode": "wywx",
                        "aclName": "物业维修",
                        "aclType": 1,
                        "aclUrl": "/billund/getListPage",
                        "seq": 5,
                        "parentId": 15911146,
                        "aclDTOList": [
                            {
                                "id": 15911186,
                                "aclCode": "wywxzp",
                                "aclName": "指派",
                                "aclType": 3,
                                "aclUrl": null,
                                "seq": 0,
                                "parentId": 15911152,
                                "aclDTOList": []
                            },
                            {
                                "id": 15911187,
                                "aclCode": "wywxwx",
                                "aclName": "维修(小程序)",
                                "aclType": 3,
                                "aclUrl": null,
                                "seq": 0,
                                "parentId": 15911152,
                                "aclDTOList": []
                            }
                        ]
                    },
                    {
                        "id": 15911153,
                        "aclCode": "jysq",
                        "aclName": "经营诉求",
                        "aclType": 1,
                        "aclUrl": "/businessDemands/page",
                        "seq": 4,
                        "parentId": 15911146,
                        "aclDTOList": []
                    },
                    {
                        "id": 15911154,
                        "aclCode": "zcsb",
                        "aclName": "政策申报",
                        "aclType": 1,
                        "aclUrl": "/policy/declaration/pageList",
                        "seq": 3,
                        "parentId": 15911146,
                        "aclDTOList": []
                    },
                    {
                        "id": 15911156,
                        "aclCode": "hzzx",
                        "aclName": "合作咨询",
                        "aclType": 1,
                        "aclUrl": "/coo/con/page",
                        "seq": 2,
                        "parentId": 15911146,
                        "aclDTOList": []
                    },
                    {
                        "id": 15911157,
                        "aclCode": "zsgl",
                        "aclName": "招商管理",
                        "aclType": 1,
                        "aclUrl": null,
                        "seq": 1,
                        "parentId": 15911146,
                        "aclDTOList": []
                    }
                ]
            },
            {
                "id": 15911188,
                "aclCode": "glht",
                "aclName": "管理后台",
                "aclType": 1,
                "aclUrl": null,
                "seq": 1,
                "parentId": 0,
                "aclDTOList": [
                    {
                        "id": 15911190,
                        "aclCode": "xxfb",
                        "aclName": "信息发布",
                        "aclType": 1,
                        "aclUrl": null,
                        "seq": 0,
                        "parentId": 15911188,
                        "aclDTOList": [
                            {
                                "id": 15911193,
                                "aclCode": "gggl",
                                "aclName": "公告管理",
                                "aclType": 2,
                                "aclUrl": null,
                                "seq": 2,
                                "parentId": 15911190,
                                "aclDTOList": [
                                    {
                                        "id": 15911196,
                                        "aclCode": "gg-xjxx",
                                        "aclName": "新建信息",
                                        "aclType": 2,
                                        "aclUrl": null,
                                        "seq": 2,
                                        "parentId": 15911193,
                                        "aclDTOList": []
                                    },
                                    {
                                        "id": 15911197,
                                        "aclCode": "gg-bjxx",
                                        "aclName": "编辑信息",
                                        "aclType": 2,
                                        "aclUrl": null,
                                        "seq": 1,
                                        "parentId": 15911193,
                                        "aclDTOList": []
                                    },
                                    {
                                        "id": 15911198,
                                        "aclCode": "gg-xxsh",
                                        "aclName": "信息审核",
                                        "aclType": 2,
                                        "aclUrl": null,
                                        "seq": 0,
                                        "parentId": 15911193,
                                        "aclDTOList": []
                                    }
                                ]
                            },
                            {
                                "id": 15911194,
                                "aclCode": "yqjj",
                                "aclName": "园区简介",
                                "aclType": 2,
                                "aclUrl": null,
                                "seq": 1,
                                "parentId": 15911190,
                                "aclDTOList": []
                            },
                            {
                                "id": 15911195,
                                "aclCode": "yqfc",
                                "aclName": "园区风采",
                                "aclType": 2,
                                "aclUrl": null,
                                "seq": 0,
                                "parentId": 15911190,
                                "aclDTOList": []
                            }
                        ]
                    },
                    {
                        "id": 15911191,
                        "aclCode": "xxtz",
                        "aclName": "消息通知",
                        "aclType": 1,
                        "aclUrl": null,
                        "seq": 0,
                        "parentId": 15911188,
                        "aclDTOList": [
                            {
                                "id": 15911200,
                                "aclCode": "xxtz",
                                "aclName": "消息通知",
                                "aclType": 2,
                                "aclUrl": "/message/page",
                                "seq": 1,
                                "parentId": 15911191,
                                "aclDTOList": []
                            },
                            {
                                "id": 15911201,
                                "aclCode": "xxtzpz",
                                "aclName": "消息通知配置",
                                "aclType": 3,
                                "aclUrl": "",
                                "seq": 0,
                                "parentId": 15911191,
                                "aclDTOList": []
                            }
                        ]
                    },
                    {
                        "id": 15911192,
                        "aclCode": "xtsz",
                        "aclName": "系统设置",
                        "aclType": 1,
                        "aclUrl": null,
                        "seq": 0,
                        "parentId": 15911188,
                        "aclDTOList": [
                            // {
                            //     "id": 15911202,
                            //     "aclCode": "qxgl",
                            //     "aclName": "权限管理",
                            //     "aclType": 1,
                            //     "aclUrl": null,
                            //     "seq": 0,
                            //     "parentId": 15911192,
                            //     "aclDTOList": []
                            // },
                            {
                                "id": 15911203,
                                "aclCode": "jsgl",
                                "aclName": "角色管理",
                                "aclType": 1,
                                "aclUrl": null,
                                "seq": 0,
                                "parentId": 15911192,
                                "aclDTOList": []
                            },
                            {
                                "id": 15911204,
                                "aclCode": "zhgl",
                                "aclName": "账号管理",
                                "aclType": 1,
                                "aclUrl": null,
                                "seq": 0,
                                "parentId": 15911192,
                                "aclDTOList": []
                            },
                            {
                                "id": 15911205,
                                "aclCode": "zhsh",
                                "aclName": "账号审核",
                                "aclType": 1,
                                "aclUrl": null,
                                "seq": 0,
                                "parentId": 15911192,
                                "aclDTOList": []
                            }
                        ]
                    }
                ]
            }
        ],
        "code": "SUCCESS",
        "msg": "请求成功",
        "requestId": null
    }
    }
  },


]