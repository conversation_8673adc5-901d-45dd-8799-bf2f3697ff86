import { Mock<PERSON>ethod } from "vite-plugin-mock";

export default [
  {
    url: `/mock/table/list`,
    method: "post",
    response: ({ body }) => {
      const { page, pageSize } = body;
      return {
        code: 200,
        data: {
          [`list|${pageSize}`]: [
            {
              "id|+1": 0,
              name: "@cname",
              "number|+1": 500,
              "choose|1": [1, 2, 3, 4],
              "radio|1": [1, 2, 3],
            },
          ],
          pager: {
            page: page,
            pageSize: pageSize,
            total: 198,
          },
        },
        msg: "",
      };
    },
  },
  {
    url: `/mock/table/category`,
    method: "post",
    response: ({ body }) => {
      const { page, pageSize } = body;
      return {
        code: 200,
        data: {
          [`list|${pageSize}`]: [
            {
              "id|+1": 100 * page,
              name: "@ctitle",
            },
          ],
          pager: {
            page: page,
            pageSize: pageSize,
            total: 100,
          },
        },
        msg: "",
      };
    },
  },
  {
    url: `/mock/table/tree`,
    method: "post",
    response: ({ body }) => {
      return {
        code: 200,
        data: [
          {
            label: "人事部",
            id: 1,
            "children|5": [
              {
                label: "@cname",
                "id|+1": 10,
              },
            ],
          },
          {
            label: "研发部",
            id: 2,
            children: [
              {
                label: "前端",
                id: 3,
                "children|5": [
                  {
                    label: "@cname",
                    "id|+1": 20,
                  },
                ],
              },
              {
                label: "后端",
                id: 4,
                "children|5": [
                  {
                    label: "@cname",
                    "id|+1": 30,
                  },
                ],
              },
            ],
          },
          {
            label: "运营部",
            id: 5,
            children: [
              {
                label: "市场运营",
                id: 6,
                "children|5": [
                  {
                    label: "@cname",
                    "id|+1": 40,
                  },
                ],
              },
              {
                label: "互联网营销",
                id: 7,
                "children|5": [
                  {
                    label: "@cname",
                    "id|+1": 50,
                  },
                ],
              },
            ],
          },
        ],
        msg: "",
      };
    },
  },
  {
    url: `/mock/table/add`,
    method: "post",
    response: ({ body }) => {
      return {
        code: 200,
        data: {},
        msg: "",
      };
    },
  },
  {
    url: `/mock/table/update`,
    method: "post",
    response: ({ body }) => {
      return {
        code: 200,
        data: {},
        msg: "",
      };
    },
  },
  {
    url: `/mock/table/del`,
    method: "post",
    response: ({ body }) => {
      return {
        code: 200,
        data: {},
        msg: "",
      };
    },
  },
  {
    url: `/mock/businessDemands/page`,
    method: "post",
    response: ({ body }) => {
      const { page, pageSize } = body;
      return {
        data: {
          totalNum: "1",
          records: [
            {
              id: "1",
              enterpriseName: "哈密永鑫中诚商贸有限责任公司",
              realName: "望丹",
              phone: "***********",
              demandsDesc: "经营诉求aaaaa",
              status: "0",
              gmtCreate: "2023-09-19 13:51:44",
              img: "https://t7.baidu.com/it/u=1595072465,3644073269&fm=193&f=GIF",
            },
          ],
        },
        status: "0",
        msg: "请求成功",
      };
    },
  },
  {
    url: `/mock/billund/getList`,
    method: "post",
    response: ({ body }) => {
      const { page, pageSize } = body;
      return {
        data: {
          businessCode: "business_demands_record",
          elements: [
            {
              submit_username: "***********",
              create_by: "system",
              gmt_create: "1695052800000",
              business_demands_id: "1",
              demands_desc: "经营诉求aaaaa",
              deleted: "0",
              gmt_modify: "1695052800000",
              id: "1",
              update_by: "system",

              demands_image:
                "https://t7.baidu.com/it/u=1595072465,3644073269&fm=193&f=GIF",

              demands_file:
                "https://t7.baidu.com/it/u=1595072465,3644073269&fm=193&f=GIF",

              enterprise_name: "企业名称或物业名称",
            }, {
              submit_username: "***********",
              create_by: "system",
              gmt_create: "1695052800000",
              business_demands_id: "1",
              demands_desc: "经营诉求aaaaa",
              deleted: "0",
              gmt_modify: "1695052800000",
              id: "1",
              update_by: "system",

              demands_image:
                "https://t7.baidu.com/it/u=1595072465,3644073269&fm=193&f=GIF",

              demands_file:
                "https://t7.baidu.com/it/u=1595072465,3644073269&fm=193&f=GIF",

              enterprise_name: "sfsddsdwwqwqwwdwda",
            },
          ],
        },
        status: "0",
        msg: "请求成功",
      };
    },
  },
  {
    url: '/mock/billund/getListPage',
    method: "post",
    response: ({ body }) => {
      const { page, pageSize } = body;
      return {
        data: {
          businessCode: "business_demands_record",
          elements: [
            {
              id: "11",
              publish_time: "1694793600000",
              title:'22'
            }, {
              id: "1",
              title:'11',
              publish_time: "1694793600000",

            },{
              id: "1",
              title:'11',
              publish_time: "1694793600000",
            },{
              id: "1",
              title:'11',
            },{
              id: "1",
              title:'11',
            },{
              id: "1",
              title:'11',
            },{
              id: "1",
              title:'11',
            },{
              id: "1",
              title:'11',
            },{
              id: "1",
              title:'11',
            }, {
              id: "1",
              title:'11',
            }, {
              id: "1",
              title:'11',
            }, {
              id: "1",
              title:'11',
            }, 
          ],
          pageSize:10,
          total:25,
        },
        status: "0",
        msg: "请求成功",
      };
    },
  },
  {
    
    url: '/mock/energy/getListPage',
    method: "post",
    response: ({ body }) => {
      const { page, pageSize } = body;
      return {
        data: {
          businessCode: "business_demands_record",
          elements: [
            {
              id: "11",
              publish_time: "1696576613",
              title:'22'
            }, {
              id: "1",
              title:'11',
              publish_time: "1696576702",

            },{
              id: "1",
              title:'11',
              publish_time: "1696576",
            },{
              id: "1",
              title:'11',
            },{
              id: "1",
              title:'11',
            },{
              id: "1",
              title:'11',
            },{
              id: "1",
              title:'11',
            },{
              id: "1",
              title:'11',
            },{
              id: "1",
              title:'11',
            }, {
              id: "1",
              title:'11',
            }, {
              id: "1",
              title:'11',
            }, {
              id: "1",
              title:'11',
            }, 
          ],
          pageSize:10,
          total:25,
        },
        status: "0",
        msg: "请求成功",
      };
    },
  },
  
  {
    
    url: '/mock/admin/login',
    method: "post",
    response: ({ body }) => {
      const { page, pageSize } = body;
      return {
        result:{
          token:'2222',
        },
        data:{
          token:'2222',
          username: "15868129000",
          name: "15868129000",
        },
        token:'2222',
       
        status: "0",
        msg: "请求成功",
      };
    },
  },
];
