<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="/favicon.ico" />
  <link rel="stylesheet" href="//at.alicdn.com/t/font_2570680_gkyjimtz1d.css">
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>
  </title>
</head>

<body>
  <!-- <div id="mine">
  <div id="app">
  <div id="app2"></div>
  </div>
  </div> -->

  <div id="wujie">
    <!-- <div id="app"> -->
    <div id="app1"></div>
  </div>
  <script type="module" src="/src/main.ts"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.7.107/pdf.min.js"></script>
</body>
<script>
  // 浏览器获取网页时，会对网页中每一个对象（脚本文件、样式表、图片文件等等）发出一个HTTP请求。而通过window.performance.getEntries方法，则可以以数组形式，返回这些请求的时间统计信息，每个数组成员均是一个PerformanceResourceTiming对象！

  // function performanceGetEntries(){
  // 判断浏览器是否支持
  //     if (!window.performance && !window.performance.getEntries) {
  //         return false;
  //     }
  //     var result = [];
  //     // 获取当前页面所有请求对应的PerformanceResourceTiming对象进行分析
  //     window.performance.getEntries().forEach((item) => {
  //         result.push({
  //             'url': item.name,
  //             'entryType': item.entryType,
  //             'type': item.initiatorType,
  //             'duration(ms)': item.duration
  //         });
  //     });
  //     // 控制台输出统计结果
  //     console.table(result); // 表示已经加载的资源

  // 	//  然后把整个资源的数量减去已经加载好的资源，剩下的就是没有加载出来的资源的数量。
  // }
  /**
    * 监控页面静态资源加载报错
    */
  //   function loadResourceError() {
  //    window.addEventListener('error', function(e) {
  //    console.log(e, '错误捕获===');
  //    if(e){
  //      let target = e.target || e.srcElement;
  //      let isElementTarget = target instanceof HTMLElement;
  //      if (!isElementTarget) {
  //        // js错误
  //        console.log('js错误===');
  //        // js error处理
  //        let {filename, message, lineno, colno, error} = e;
  //        let { message: ErrorMsg, stack } = error;
  //      }else{
  //        // 页面静态资源加载错误处理
  //        console.log('资源加载错误===');
  //        let { type, timeStamp, target } = e;
  //        let { localName, outerHTML, tagName, src } = target;
  //        let typeName = target.localName;
  //        let sourceUrl = "";
  //        if (typeName === "link") {
  //          sourceUrl = target.href;
  //        } else if (typeName === "script") {
  //          sourceUrl = target.src;
  //        }
  //        alert('资源加载失败，请刷新页面或切换网络重试。('+sourceUrl+')')
  //      }
  //    }
  //  // 设为true表示捕获阶段调用，会在元素的onerror前调用,在window.addEventListener('error')后调用
  //  },true);
  //  }
  //  // 我们根据e.target的属性来判断它是link标签，还是script标签。目前只关注只监控了css，js文件加载错误的情况。

  // performanceGetEntries()
  // loadResourceError()
</script>

</html>