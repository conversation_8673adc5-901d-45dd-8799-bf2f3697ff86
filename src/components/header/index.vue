<template>
  <div class="header">
    <div style="display: flex; align-items: center">
      <!-- <div class="icon"></div>
        园区智服 -->
    </div>

    <div class="jump">
      <el-dropdown :hide-on-click="false" @command="handleCommand">
        <span class="el-dropdown-link">
          {{ userInfo?.realName || "" }}<i class="sfont system-xiala"></i>
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <!-- <el-dropdown-item command="3">驾驶舱</el-dropdown-item>
            <el-dropdown-item
              v-if="
                route.path !== '/userCenter' && route.path !== '/accountCenter'
              "
              command="4"
            >
              个人中心</el-dropdown-item
            >
            <el-dropdown-item
              v-if="route.path === '/userCenter' && state === 'user'"
              command="5"
            >
              在线办事</el-dropdown-item
            >

            <el-dropdown-item command="6">统一用户管理</el-dropdown-item> -->
            <!-- <el-dropdown-item command="1"> 工作台</el-dropdown-item> -->
            <el-dropdown-item command="2"> 退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>
  
  <script lang="ts" setup>
import { onMounted, onBeforeUnmount, ref, computed } from "vue";
import { useStore } from "vuex";
import { useRoute, useRouter } from "vue-router";
const userInfo = computed(() =>
  JSON.parse(localStorage.getItem("userInfo") || "{}")
);
const store = useStore();
const router = useRouter();
const route = useRoute();
let state = computed(() => localStorage.getItem("userState"));

const handleCommand = (command: string | number | object) => {
  // 1 工作台
  if (command === "1") {
    router.push("/workbench/building");
  } else if (command === "2") {
    // 2退出登陆
    store.dispatch("user/loginOut");
    router.push("/login");
  } else if (command === "3") {
    router.push("/");
  } else if (command === "4") {
    router.push("/accountCenter");

    // router.push("/userCenter")
  } else if (command === "5") {
    // 在线办事
    router.push("/onLineService");
  } else if (command === "6") {
    // 统一用户管理
    //router.push("/onLineService")
  }
};
</script>
  <style scoped lang="scss">
.header {
  height: 48px;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0px 1px 4px 0px rgba(0, 21, 41, 0.12);
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  width: 100%;
  padding: 0 20px;
  font-size: 14px;

  font-weight: bold;
  color: rgba(0, 0, 0, 0.65);
  line-height: 48px;
}
.jump {
  display: flex;
}

.icon {
  width: 22px;
  height: 20px;
  background: no-repeat url("@/assets/images/online/icon13.png");
  background-size: contain;
}
</style>
  