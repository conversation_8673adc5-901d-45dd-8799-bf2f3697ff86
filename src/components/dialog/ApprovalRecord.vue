<template>
    <div>
        <el-dialog v-model="dialogVisible" :before-close="canleDig" title="审批记录" width="600">
            <div v-if="dataList.length > 0" class="record">
                <div v-for="(item, index) in dataList" :key="index" class="single">
                    <div class="head">
                        <span class="time">{{ item.gmtCreate }}</span> <span class="operation">{{ item.targetStatus
                        }}</span>
                    </div>
                    <div class="box">
                        <div v-for="(value, key) in item.data" :key="key">
                            <div class="dataBox" v-if="key == '附件'">{{ key }}：
                                <span v-if="value.length > 0" class="attachmentImgBox">
                                    <el-image v-for="(i, ind) in value" :key="ind" :src="i" class="attachmentImg"
                                        :preview-src-list="value"
                                        show-progress fit="cover" />
                                </span>
                            </div>
                            <div class="dataBox" v-else>{{ key }}：{{ value }}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div style="height: 200px;" v-if="dataList.length == 0 && !showNodata">
            </div>
            <div class="nodataBox" v-if="dataList.length == 0 && showNodata">
                <img class="nodata" src="https://static.idicc.cn/cdn/aiChat/applet/nodata.png" alt="">
                <span class="text">暂无审批记录</span>
            </div>
        </el-dialog>
    </div>
</template>
<script setup>
import { ref, onMounted, defineEmits } from 'vue'
import { approvalListAPI } from '@/api/user'

const props = defineProps({
    dialogVisible: {
        type: Boolean,
        required: true
    },
    data: {
        type: Object,
        required: true
    }
});
const dataList = ref([])
const showNodata = ref(false)
const emit = defineEmits(['canleDig']);
const dialogVisible = ref(props.dialogVisible);
const canleDig = () => {
    emit('canleDig')
}
onMounted(() => {
    // console.log(dialogVisible.value, 'dialogVisible');
    if (dialogVisible.value) {
        // console.log(props.data, 'dialogVisible');
        approvalListAPI(props.data).then((res) => {
            // console.log(res)
            showNodata.value = true
            if (res.status === '0') {
                dataList.value = res.data
            }
        })
    }
    // console.log(props.data, 'dialogVisible');

});

</script>
<style scoped lang="scss">
.nodataBox {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    font-size: 18px;
    color: #000;
    position: relative;
    margin-bottom: 60px;

    .nodata {
        width: 375px;
        height: 368px;
    }

    .text {
        position: absolute;
        top: 260px;
    }
}

.record {
    margin-left: 16px;
    margin-top: 16px;
    max-height: 600px;
    overflow: scroll;

    .dataBox {
        display: flex;
        white-space: nowrap;
    }

    .attachmentImgBox {
        display: flex;
        width: 100%;
        flex-wrap: wrap;

        .attachmentImg {
            width: 100px;
            height: 100px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
    }

    .head {
        display: flex;
        height: 19px;

        .operation {
            margin-left: 25px;
        }

        .time {
            margin-left: 25px;
            font-size: 13px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #4e5969;
        }

        .letter {
            font-size: 14px;
            font-family: PingFang SC-Regular, PingFang SC;
            font-weight: 400;
            color: #1d2129;
        }
    }

    .box {
        margin-left: 25px;
        margin-top: 12px;
        margin-bottom: 25px;
        height: auto;
        //background: #f7f8fa;
        //padding: 20px 18px;
    }
}

.single {
    position: relative;
}

.single:not(:last-child)::before {
    background-color: #f0f0f0;
    content: "";
    position: absolute;
    left: 7px;
    z-index: 5;
    width: 2px;
    height: calc(100% + 25px);
}

.head::after {
    background-color: #3e80ff;
    content: "";
    position: absolute;
    //left: 65px;
    border: 4px solid #bedaff;
    width: 16px;
    height: 16px;
    z-index: 6;
    border-radius: 50%;
}
</style>