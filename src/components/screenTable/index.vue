<template>
    <div class="tableClass">
        <div class="row" v-for="data, index in tableData" :key="index" @click="change(data)"
            :class="{ active: activeKey === data.id && !!innerVisible }">
            <div class="rowLeft" :style="{ width: data.image_url ? 'calc(100% - 124px)' : '100%' }">
                <div class="title">
                    <span class="tip" v-if="data.type === '2'">政策</span>
                    <span class="tip2" v-if="data.type === '6'">资讯</span>
                    <!-- <el-tooltip  :content=" data.title" > -->
                    <span class="text"> {{ data.title }}</span>
                    <!-- </el-tooltip> -->
                </div>
                <div class="time">
                    {{ data?.publish_time && dayjs(data?.publish_time).format('YYYY/MM/DD') || '' }}
                </div>
                <!--   {{ data?.publish_time&&dayjs(data?.publish_time).format('YYYY/MM/DD')||'' }} -->
                <!-- <span class="tip"  v-if="data.type===‘2">{{ ['', '公告', '政策', '党建要闻', '党政法规', '活动',][] }}</span>  -->
            </div>
            <div v-if="data.image_url" class="rowRight">
                <!-- {{ dayjs('1694793600000').format('YYYY-MM-DD hh:mm:ss') }}  -->
                <!-- {{ data.publish_time&&dayjs(Number(data.publish_time)).format('YYYY-MM-DD')||'' }} -->
                <img :src="data.image_url" class="leftimg">
            </div>
        </div>

    </div>
    <el-dialog custom-class="screenTable padding0Dialog" class="screenTable padding0Dialog" v-model="innerVisible"
        width="30%" append-to-body>
        <DialogDetail :innerVisible="innerVisible" @changeInner="changeInner" :detailData="detailData"
            :title="headerTitle" />
    </el-dialog>
</template>
  
<script lang="ts" setup>
import { ref, defineProps } from 'vue'
import Dialog from './dialog/index.vue'
import dayjs from 'dayjs'

import DialogDetail from './dialog/detail.vue'
const props = defineProps(['tableData', 'showDetail', 'title'])
const centerDialogVisible = ref(false)
const innerVisible = ref(false)
const detailData = ref()
const activeKey = ref()
const headerTitle = ref()
interface Data {
    title: string
    id: string

}
const change = (data: Data) => {
    if (data?.input_type == 2) {
        window.open(`${data?.link_url}`, '_blank');
    } else {
        headerTitle.value = data.title;
        activeKey.value = data.id;
        innerVisible.value = true
        detailData.value = data
    }

}
const changeInner = () => {
    innerVisible.value = true
}
</script>
<style lang="scss" >
.el-message.el-message--error,
.el-message.el-message--warning,
.el-message.el-message--success {
    z-index: 100000 !important;
}

.el-select__popper,
.el-overlay {
    // z-index: 99999 !important;
}

.padding0Dialog {
    .el-dialog__body {
        padding: 0 30px !important;
    }
}

.screenTable {
    border: 1px solid rgba(64, 129, 203, 0.7);
    width: 1100px;
    height: 700px;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 101% 101%;
    border-radius: 5px;
    background-image: url("@/assets/images/view/home/<USER>");

    .el-dialog__headerbtn {
        // right: 0px;
        // padding: 05px;
        right: -7px;
        top: -1px;
        padding: 0;
    }
}

.screenTablemini {
    border: 1px solid rgba(64, 129, 203, 0.7);
    width: 600px;
    height: 220px;
    margin-top: 10%;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 101% 101%;
    border-radius: 5px;
    background-image: url("@/assets/images/view/home/<USER>");

    .el-dialog__headerbtn {
        // right: 0px;
        // padding: 05px;
        right: -7px;
        top: -1px;
        padding: 0;
    }

    .el-dialog__title {
        font-size: 1.25rem;
        font-weight: bold;
        font-style: italic;
        color: #C5D4ED !important;
        line-height: 2.8125rem;
    }
}
</style>
<style lang="scss"  scoped>
.leftimg {
    min-width: 104px;
    margin-left: 20px;
    height: 78px;
}

.tableClass {
    padding: 0px 0;

    .row {
        width: 385px;
        height: 95px;
        margin-bottom: 8px;
        // padding-bottom: 8px;
        margin-left: 11px;
        // padding-left: 11px;
        background: rgb(0, 138, 255, 0.05);
        display: flex;
        flex-wrap: nowrap;
        cursor: pointer;
        color: rgb(178, 195, 223, 0.5);

        line-height: 20px;

        padding: 4px 12px;

        /*         &:nth-child(2n) {
            background: #0f2a4a !important;
            // background: rgba(44,112,191,0.5) !important;
        }
 */
        &:nth-child(2n-1) {}

        .rowLeft {
            font-size: 14px;

            color: #B2C3DF;
            display: flex;
            flex-direction: column;

            //align-items: center;
            //justify-content: flex-start;
            .title {
                max-width: 3450px;
                height: 56px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
                text-overflow: ellipsis;
                //font-size: 14px;
                //font-family: Alibaba PuHuiTi;
                //font-weight: 300;
                color: #B2C3DF;
                line-height: 28px;

                .text {
                    font-size: 14px;
                }
            }

            .time {
                margin-top: 8px;
                font-size: 12px;
            }

        }

        .rowRight {
            width: 104px;
            display: flex;
            align-items: center;
        }

        &:hover {
            color: #FFFFFF;
            background: rgba(44, 112, 191, 0.2) !important;
        }

        &.active {
            border: 1px solid rgba(44, 112, 191, );
            color: #FFFFFF;
            // box-shadow: 0px 0px 5PX -2px rgb(253 251 251) inset;
        }
    }

}

.tip {
    border: 1px solid #EB9113;
    border-radius: 2px;
    font-size: 12px;
    padding: 1px 2px;
    color: #EB9113;
    margin-right: 5px;
    // margin-left: 10px;
    transform: scale(0.75);
}

.tip2 {
    border: 1px solid #53D41B;
    border-radius: 2px;
    font-size: 12px;
    padding: 1px 2px;
    color: #53D41B;
    margin-right: 5px;
    // margin-left: 10px;
    transform: scale(0.75);
}</style>
 
  