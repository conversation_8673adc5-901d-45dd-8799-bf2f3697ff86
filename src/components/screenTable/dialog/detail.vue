
<template>
    <div class="tableClass">
        <!-- <div v-if="!state"> -->
            <div class="tableSearch">
            <!---->
            <div 
            :class="state?'noBg':'bgText'" 
            >
               <div class="titled">{{ title }}</div>
                <span class="tip"  v-if="showData?.type==='data.type'">政策 </span>  
                <!-- <span class="tip" >{{ ['', '公告', '政策', '党建要闻', '党政法规', '活动'][showData?.type] }}</span> -->
            </div>

        </div>
        <div  :class="state?'noBgDes':'des'" >
            <div>发布机构：江苏省数字交通产业园</div>
            <div>发布日期： {{ showData?.publish_time && dayjs(showData?.publish_time).format('YYYY-MM-DD') }}</div>
            <!-- <div @click="getCollect"> 收藏</div> -->
        </div>
        <div class="detail">
            <div class="titleImg" :style="{ backgroundImage: `url(${showData?.image_url})` }" v-if="image_url">
            </div>
                <div class="content" ref="contentDom">
                </div>
                <!-- {{ showData?.content || '' }} -->
 
            <!-- v-else-if="showData?.link_url"  -->
            <div class="content"  :class="state?'noBgUrl':'url'"  @click="open(showData?.link_url)">
                {{ showData?.link_url }}
            </div>
         
        </div>
    </div>
</template>
<script setup>
import { defineProps, onMounted, watch, ref } from 'vue'
import { getHomeEvenDetail,collect } from '@/api/dashboard'
import dayjs from 'dayjs'

const props = defineProps(['detailData', 'innerVisible', 'title','state']);
const showData = ref();
let contentDom=ref()
watch(() => props.innerVisible, (newVal) => {
    if (newVal === false) {
        showData.value = ''
    } else {
        getDetail()
    }
}, { deep: true })

onMounted(() => {
    getDetail()
})
// const detailData = ref()
const getDetail = () => {
    getHomeEvenDetail({
        id: props.detailData?.id
    }).then((result) => {
        showData.value = result.data
        contentDom.value.innerHTML=showData.value?.content?showData.value?.content:''
    }).catch((err) => {

    })
}
const open = (url) => {
    window.open(url)
}

const getCollect=()=>{

}
</script>

<style scoped lang="scss">
@import './index.scss';
.tableClass{


::-webkit-scrollbar {
    /* 对应纵向滚动条的宽度 */
    width: 5px;
    /* 对应横向滚动条的宽度 */
    height: 0px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
   background-color: #3241563c;
    border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
    display: none;
}}
.titled{
    display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1; /* 显示一行 */
  overflow: hidden; /* 隐藏溢出部分 */
}
.content {
    font-size: 14px;
    color:#94a6c7; 
   
    line-height: 32px;

    &.url,&.noBgUrl {
        cursor: pointer;
    }
}
 .url {
    color: var(--system-primary-color)
 }
 .noBgUrl {
    color:   var(--system-primary-color);
 }
.des,.noBgDes {
    width: 100%;
    display: flex;
height: 40px;
    div:first-child {
        margin-right: 45px;
    }

}

.titleImg {
    width: 100%;
    height: 300px;
    background: center / contain no-repeat;
    // url('@/assets/images/view/tab2/bg1.png');

}

.detail {
    width: 100%;
    height: 550px;
    overflow-y: scroll;
    font-size: 14PX;
    .content.url{
        font-size: 14PX;
    }
}

</style>
