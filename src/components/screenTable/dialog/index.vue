<template>
    <div class="tableClass">
        <div class="tableSearch">
            <div class="bg">{{ title }}</div>
            <el-input class="w-50 m-2 tableInput" v-model="input" placeholder="输入关键字搜索" :suffix-icon="Search"
                @change="changeData" />
        </div>
        <div class="tables">
        <el-table :data="dialogData.elements" stripe style="width: 100%" :show-header="false" @row-click="change"
          
            >
            <!--   :row-class-name="tableRowClassName" -->
            <!-- <el-table-column type="index" width="50" /> -->
            <el-table-column prop="title" label="title" width="530" >
                <template #default="scope">
                 <span class="subTitle"> {{ scope.row.title }}  </span>  
                <span class="tip"  v-if="scope.row?.type==='2'">政策 </span>  
                <span class="tip2"  v-if="scope.row?.type==='6'">资讯 </span>  
                <!-- <span class="tip" >{{ ['', '公告', '政策', '党建要闻', '党政法规', '活动'][scope.row.type] }}</span> -->
            </template>
            </el-table-column>
            <el-table-column prop="publish_time" label="publish_time">
                <template #default="scope">
                    {{scope.row?.publish_time&&dayjs(scope.row?.publish_time).format('YYYY/MM/DD')||'' }}
                   <!-- {{ dayjs(1694793600000)}} -->
                   <!-- {{ scope.row.publish_time&&dayjs(Number(scope.row.publish_time)).format('YYYY-MM-DD')||'' }} -->
                    <!-- {{ dayjs(scope.row.publish_time&&scope.row.publish_time) }} -->
                </template>
            </el-table-column>
        </el-table>
    </div >
        <el-pagination background v-model:current-page="currentPage1" :page-size='12'
        
            layout="total, prev, pager, next" :total="Number(dialogData.total)" @size-change="handleSizeChange"
            @current-change="handleCurrentChange" />
    </div>
</template>
  
<script lang="ts" setup>
import { ref, defineProps, defineEmits, onMounted, onBeforeUnmount, watch } from 'vue'
import { Search } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
const props = defineProps(['dialogData', 'centerDialogVisible','title'])
const emit = defineEmits(['changeInner', 'getData'])
const currentPage1 = ref(1)
const input = ref('')
watch(() => props.centerDialogVisible, (newVal: any) => {
    if (newVal === false) {
        input.value = ''
        currentPage1.value=1
    }else{
        currentPage1.value=1
    }
}, { deep: true })
onMounted(() => {
    currentPage1.value=1
})
interface User {
    date: string
    name: string
    address: string
}
// const tableRowClassName = ({
//     row,
//     rowIndex,
// }: {
//     row: User
//     rowIndex: number
// }) => {
//     if (rowIndex === 1) {
//         return 'warning-row'
//     } else if (rowIndex === 3) {
//         return 'success-row'
//     }
//     return ''
// }

const change = (e) => {
    emit('changeInner', e)
}

const handleSizeChange = (val: number) => {
    emit('getData', {
        "pageSize": 12,
        "pageNum": val
    })
}
const handleCurrentChange = (val: number) => {
    currentPage1.value=val
    emit('getData', {
        "pageSize": 12,
        "pageNum": val,
        conditions: [
            {
                "compareType": "like",
                "key": "title",
                "value":  input.value 
            }
        ]
    })
}
const changeData = (e) => {
    input.value = e
    emit('getData', {
        "pageSize": 12,
        "pageNum": 1,
        conditions: [
            {
                "compareType": "like",
                "key": "title",
                "value": e
            }
        ]
    })

}
</script>

<style scoped lang="scss">
@import './index.scss';
</style>

<style lang="scss">
.tableClass{


::-webkit-scrollbar {
    /* 对应纵向滚动条的宽度 */
    width: 5px;
    /* 对应横向滚动条的宽度 */
    height: 0px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
    background-color: #324156;
    border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
    display: none;
}}
.tableInput {

    .el-input__wrapper {
        background: #0d346a;
        box-shadow: 0 0 0 1px rgba(64, 129, 203, 0.8) inset;
    }
}

.tableClass {

    .el-table__inner-wrapper::before {
        background: transparent;

    }

    .el-table {
        background: transparent;

        td.el-table__cell {
            border-bottom: 1px solid rgba(44, 112, 191, 0.5);
            // border-bottom: 0px;
        }
    }

    .el-table__row {
        background: transparent;
        color: #B2C3DF;

        &:hover>td.el-table__cell {
            background: #163a6e !important;
        }

        &:hover {
            background: #163a6e !important;
        }
    }

    .el-table__row--striped {
        .el-table__cell {
            background: transparent !important;
            color: #B2C3DF;

            // background: #0f2a4b !important;
            &:hover {
                background: red;
            }

        }
    }
    .el-table__cell {
            .cell{
                height: 32px;
                line-height: 32px;
                display: flex;
    justify-content: flex-start;
    align-items: center;
            }
        }
    .el-table .warning-row {
        .el-table__cell {

            // border:1px solid  red !important;
        }

        color: #FFFFFF;
    }

    .el-table .success-row {
        --el-table-tr-bg-color: yellow;
    }

    .el-pagination {
        padding-top: 15px;
        justify-content: center;
        position: absolute;
        bottom: 20px;
        width: 95%;
    }

    .btn-prev,
    .btn-next {
        background: transparent !important;
        color: white !important;
    }

    .number,
    .more {
        background: transparent !important;
        border: 1px solid rgba(44, 112, 191, 0.5) !important;
        color: rgba(255, 255, 255, 0.4);

    }

    .number.is-active,
    .more.is-active {
        background: #163a6e !important;
        color: white !important;
        border: 1px solid rgba(44, 112, 191, 0.7) !important;

    }
}
.tables{
    height: 540px;
    margin-top: 20px;
    overflow-y: scroll;
}
</style>
  