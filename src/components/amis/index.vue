<template>
    <div id="amis"></div>
</template>

<script lang="ts" setup>
// import { onMounted,watch } from "vue";
import { onMounted, defineProps, watch, ref, defineExpose } from "vue"
import { ElMessage } from 'element-plus'
import axios from "axios";
import request from '@/utils/system/request';
import {  token } from '@/utils/utils';

import store from '@/store'
const props = defineProps({
    amisjson: {
        type: Object,
        required: true
    }
})

watch((() => props.amisjson), ((newVal) => {
    getDom()
}), { deep: true })
onMounted(() => {
    getDom()
})
var amis = amisRequire('amis/embed');
let amisScoped = null;

const getDom = () => {
    // @ts-ignore
    let theme = 'antd'
    amisScoped = amis.embed(
        '#amis',
        props.amisjson,
        {
            updateLocation: (to, replace) => { },
        },
        {
            // 下面三个接口必须实现
            fetcher: ({
                url, // 接口地址
                method, // 请求方法 get、post、put、delete
                data, // 请求数据
                responseType,
                config, // 其他配置
                headers,// 请求头
                updateLocation
            }) => {
                config = config || {};
                config.withCredentials = true;

                // 设置接口地址
                config.baseURL = import.meta.env.VITE_BASE_URL;

                responseType && (config.responseType = responseType);

                if (config.cancelExecutor) {
                    config.cancelToken = new (axios).CancelToken(
                        config.cancelExecutor
                    );
                }
                config.headers = headers || {};
                // 设置token
                const isToken = (config.headers || {}).token === false
                if (!isToken) {
                    config.headers['token'] =token()// 自行实现逻辑
                }
                return request({
                    url,
                    method,
                    data
                })
                // return (axios )[method](url, data, config);
            },
            // requestAdaptor: (rest) => {
            //     console.log(rest);
            // },
            // response:(res)=>{
            //     console.log(pres);

            // },
            // adaptor:  (payload, response, api, context)=> {
            //               console.log(payload, response, api, context);
            //               },
            isCancel: (value) => (axios).isCancel(value),
            copy: content => {
                copy(content);
                ElMessage.success('内容已复制到粘贴板');
            },
            theme
        }
    )
}

// 暴露刷新方法
const reload = () => {
    console.log('reload called, amisScoped:', amisScoped);
    if (amisScoped) {
        try {
            // 方法一：通过新的ID获取组件
            const crudComponent = amisScoped.getComponentById && amisScoped.getComponentById('property-crud-table');
            console.log('crudComponent by new id:', crudComponent);
            
            if (crudComponent && crudComponent.reload) {
                console.log('Successfully found CRUD component, calling reload');
                crudComponent.reload();
                return;
            }
            
            // 方法二：通过 name 获取
            const crudComponentByName = amisScoped.getComponentByName && amisScoped.getComponentByName('tab-s');
            console.log('crudComponent by name:', crudComponentByName);
            
            if (crudComponentByName && crudComponentByName.reload) {
                console.log('Found component by name, calling reload');
                crudComponentByName.reload();
                return;
            }
            
            // 方法三：查找所有 CRUD 类型的组件
            if (amisScoped.getComponents) {
                const allComponents = amisScoped.getComponents();
                console.log('All components count:', allComponents.length);
                
                for (let i = 0; i < allComponents.length; i++) {
                    const comp = allComponents[i];
                    if (comp.props && comp.props.type === 'crud') {
                        console.log('Found CRUD component at index', i, comp);
                        if (comp.reload) {
                            comp.reload();
                            return;
                        }
                    }
                }
            }
            
            // 方法四：通过 amis 内部 API 强制刷新页面
            if (amisScoped.updateProps) {
                console.log('Using updateProps to refresh');
                amisScoped.updateProps(props.amisjson);
                return;
            }
            
            // 方法五：重新初始化整个组件
            console.log('All specific reload methods failed, re-initializing');
            getDom();
            
        } catch (error) {
            console.error('Reload failed:', error);
            // 出错时重新初始化
            getDom();
        }
    } else {
        console.log('amisScoped is null, re-initializing');
        getDom();
    }
}

// 强制重新渲染的方法
const forceRefresh = () => {
    console.log('forceRefresh called');
    getDom();
}

defineExpose({
    reload,
    forceRefresh
})

</script>