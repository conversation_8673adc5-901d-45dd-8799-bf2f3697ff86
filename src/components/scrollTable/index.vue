<template>
  <div v-if="listData?.length > 0">
    <ScrollBoard
      class="scrollTable"
      ref="scrollBoard"
      :config="{
        ...config,
        data: listData,
      }"
      :style="style"
      @mouseover="mouseoverHandler"
      @click="clickHandler"
    />
  </div>
  <div v-else class="noneData">暂无数据</div>
</template>
<script lang="ts" setup>
import {
  defineComponent,
  ref,
  reactive,
  computed,
  watch,
  defineProps,
  onMounted,
} from 'vue';
import { ScrollBoard, Decoration2 } from '@kjgl77/datav-vue3';
const props = defineProps(['style', 'propData', 'showHeader', 'type']);
const config = reactive({
  data: [],
  // index: false,
  // columnWidth: [50],
  align: ['center'],
  oddRowBGC: 'rgba(25,57,98,.3)',
  evenRowBGC: 'rgb(0,138,255,.05)',
  headerBGC: 'rgb(0,138,255,.05)',
  rowNum: 5,
  header: null,

  // index:true,
  // waitTime:null,
  columnWidth: [50, 300, 150],
});
const listData = ref();
watch(
  () => props.propData,
  (newVal) => {
    getList(newVal);
  },
  { deep: true }
);
onMounted(() => {
  getList(props.propData);
});
const getList = (val) => {
  let list = null;
  if (props?.type === 'policy') {
    config.header = ['政策名称', '申报次数', '通过数'];
    config.columnWidth = [240, 90, 70];
    list = val?.map((item: any, index: number) => {
      return [item.policyName, item.declareCount, item.passCount];
    });
  } else {
    list = val?.map((item: any, index: number) => {
      return [index + 1, item.name, item.value];
    });
  }

  listData.value = list;
};
const mouseoverHandler = (e: any) => {
  // console.log(e)
};

const clickHandler = (e: any) => {
  // console.log(e)
};

const updateRows = () => {
  config.data.push([
    `行${config.data.length + 1}列1`,
    `行${config.data.length + 1}列2`,
    `行${config.data.length + 1}列3`,
  ]);
};
</script>
<style lang="scss">
.scrollTable {
  width: 100%;
  height: 100%;
  .header-item,
  .ceil {
    color: #b2c3df;
  }
  .row-item {
    .ceil {

      min-width: 50px !important;
    }
  }
  .ceil {
    text-align: left;
  }
}
.noneData {
  width: 400px;
  height: 200px;
  line-height: 200px;
  text-align: center;
  color: #939191;
  font-size: 14px;
}
</style>
