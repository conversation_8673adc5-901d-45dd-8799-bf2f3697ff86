<template>
  <header>
    <div class="left-box">
      <!-- 收缩按钮 -->
      <div class="menu-icon" @click="opendStateChange">
        <!--   class="sfont head-fold" -->
        <span
          :class="isCollapse ? 'system-s-unfold' : 'system-s-fold'"
        ></span>
      </div>
      <Breadcrumb />
    </div>
    <div class="right-box">
      <!-- 用户信息 -->
      <div class="user-info">
        <el-dropdown>
          <span class="el-dropdown-link">
            {{ userInfo?.realName || "" }}
            <i class="sfont system-xiala"></i>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <!-- <el-dropdown-item @click="showPasswordLayer">{{ $t('message.system.changePassword') }}</el-dropdown-item> -->
              <!-- <el-dropdown-item @click="goScreen"> 数字驾驶舱</el-dropdown-item> -->
              <!-- 分路由 -->
              <!-- <el-dropdown-item @click="goHome">
                {{
                  state === "admin" ? "园区主页" : "在线办事"
                }}</el-dropdown-item
              >
             -->
              <!-- <el-dropdown-item @click="goHome"> 首页</el-dropdown-item> -->
              <!-- <el-dropdown-item
                v-if="
                  route.path !== '/userCenter' &&
                  route.path !== '/accountCenter'
                "
                @click="goCenter"
              >
                个人中心</el-dropdown-item
              >
              <el-dropdown-item @click="loginOuts"
                >统一用户管理</el-dropdown-item
              > -->
              <!-- <el-dropdown-item @click="goWork">首页</el-dropdown-item> -->
              <el-dropdown-item @click="loginOut">退出登录</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      <password-layer :layer="layer" v-if="layer.show" />
    </div>
  </header>
</template>

<script lang="ts">
import { defineComponent, computed, reactive, ref } from "vue";
import { useStore } from "vuex";
import { useRouter, useRoute } from "vue-router";
import FullScreen from "../../components/functionList/fullscreen.vue";
import Word from "../../components/functionList/word.vue";
import SizeChange from "../../components/functionList/sizeChange.vue";
import Github from "../../components/functionList/github.vue";
import Theme from "../../components/functionList/theme.vue";
import Breadcrumb from "./Breadcrumb.vue";
import PasswordLayer from "../../components/passwordLayer.vue";
import { ElMessage } from "element-plus";

export default defineComponent({
  components: {
    FullScreen,
    Breadcrumb,
    Word,
    SizeChange,
    Github,
    Theme,
    PasswordLayer,
  },
  setup() {
    const store = useStore();
    const router = useRouter();
    const route = useRoute();
    const userInfo = computed(() =>
      JSON.parse(localStorage.getItem("userInfo") || "{}")
    );
    const layer = reactive({
      show: false,
      showButton: true,
    });
    let state = ref(localStorage.getItem("userState"));
    const isCollapse = computed(() => store.state.app.isCollapse);
    // isCollapse change to hide/show the sidebar
    const opendStateChange = () => {
      store.commit("app/isCollapseChange", !isCollapse.value);
    };

    const loginOuts = () => {
      // 去登录
      this.$router.push({
        path: "/login",
      });
    };
    // login out the system
    const loginOut = () => {
      store.dispatch("user/loginOut");
      router.push("/login");
    };
    const goScreen = () => {
      router.push("/dashboard?tab=3");
    };
    const goHome = () => {
      return router.push("/onLineService");
    };
    const goCenter = () => {
      return router.push("/userCenter");
    };
    const goWork = () => {
      router.push("/");
    };
    const showPasswordLayer = () => {
      layer.show = true;
    };
    return {
      isCollapse,
      layer,
      opendStateChange,
      loginOut,
      loginOuts,
      showPasswordLayer,
      goScreen,
      goHome,
      userInfo,
      goCenter,
      route,
      state,
      goWork,
    };
  },
});
</script>

<style lang="scss" scoped>
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  background-color: var(--system-header-background);
  padding-right: 22px;
}
.left-box {
  height: 100%;
  display: flex;
  align-items: center;
  .menu-icon {
    width: 60px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 25px;
    font-weight: 100;
    cursor: pointer;
    margin-right: 10px;
    &:hover {
      background-color: var(--system-header-item-hover-color);
    }
    i {
      color: var(--system-header-text-color);
    }
  }
}
.right-box {
  display: flex;
  justify-content: center;
  align-items: center;
  .function-list {
    display: flex;
    .function-list-item {
      width: 30px;
      display: flex;
      justify-content: center;
      align-items: center;
      :deep(i) {
        color: var(--system-header-text-color);
      }
    }
  }
  .user-info {
    margin-left: 20px;
    .el-dropdown-link {
      color: var(--system-header-breadcrumb-text-color);
    }
  }
}
.head-fold {
  font-size: 20px;
}
 .system-s-unfold{
  width: 18px;
  height: 18px;
  background: center / contain
    url('https://static.idicc.cn/cdn/jinghai/system-s-unfold.webp') no-repeat;

 }
.system-s-fold{
  width: 18px;
  height: 18px;
  background: center / contain
    url('https://static.idicc.cn/cdn/jinghai/system-s-unfold.webp') no-repeat;
  transform: rotate(180deg);

}
</style>
