<template>
  <el-container style="height: 100vh ;background: #E9F0FF;">
    <div
      class="mask"
      v-show="!isCollapse && !contentFullScreen"
      @click="hideMenu"
    ></div>
    <!-- v-show="!contentFullScreen" -->
    <el-aside
      v-show="!contentFullScreen"
      :width="isCollapse ? '78px' : '230px'"
      :class="isCollapse ? 'hide-aside' : 'show-side'"
    >
      <Logo v-if="showLogo" />
      <Menu />
    </el-aside>
    <el-container style="border-radius: 20px 0px 0px 20px;
    background: #FAFCFF;">
      <!-- <div class="contentHeader"> -->
      <!--  -->
      <el-header v-show="!contentFullScreen">
        <Header />
      </el-header>
      <!-- </div> -->

      <!-- <Tabs v-show="showTabs" /> -->
      <!-- <div class="main"> -->
      <el-main>
        <div :class="!contentFullScreen ? 'mainContent' : 'padding0'" style="">
          <router-view v-slot="{ Component, route }">
            <!-- <transition :name="route.meta.transition || 'fade-transform'" mode="out-in"> -->
            <keep-alive
              v-if="keepAliveComponentsName"
              :include="keepAliveComponentsName"
            >
              <component :is="Component" :key="route.fullPath" />
            </keep-alive>
            <component v-else :is="Component" :key="route.fullPath" />
            <!-- </transition> -->
          </router-view>
        </div>
      </el-main>
    </el-container>
  </el-container>
  <el-dialog
    v-model="forgetShow"
    width="400px"
    custom-class="forget"
    class="forget"
    title=""
    @close="closeForget"
    :close-on-click-modal="false"
  >
    <Forget ref="forgetRef"></Forget>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent, computed, onBeforeMount, ref, onMounted } from "vue";
import { useStore } from "vuex";
import { useEventListener } from "@vueuse/core";
import Menu from "../components/Menu/index.vue";
import Logo from "../components/Logo/index.vue";
import Header from "./Header/index.vue";
import Tabs from "./Tabs/index.vue";
import { ElMessageBox, ElMessage } from "element-plus";
import { useRoute, useRouter } from "vue-router";
// import Login from '@/views/system/login.vue'
// import Forget from "@/views/system/forget.vue";
import router from "../../router/index";
export default defineComponent({
  components: {
    Menu,
    Logo,
    Header,
    Tabs,
    // Login,
    // Forget,
  },
  setup() {
    const route = useRoute();
    const forgetRef = ref();
    const store = useStore();
    // const dialogVisible =
    //   computed(() =>
    //   localStorage.getItem('isLogin')==='true'
    //   // store.state.app.isLogin ß
    //   );
    const forgetShow = computed(() => {
      // localStorage.getItem('isLogin')==='true'
      return store.state.app.isForgetShow;
    });

    // computed
    const isCollapse = computed(() => store.state.app.isCollapse);
    const contentFullScreen = computed(() => store.state.app.contentFullScreen);
    const showLogo = computed(() => store.state.app.showLogo);
    const showTabs = computed(() => store.state.app.showTabs);
    const keepAliveComponentsName = computed(
      () => store.getters["keepAlive/keepAliveComponentsName"]
    );
    // 页面宽度变化监听后执行的方法
    const resizeHandler = () => {
      if (document.body.clientWidth <= 1000 && !isCollapse.value) {
        store.commit("app/isCollapseChange", true);
      } else if (document.body.clientWidth > 1000 && isCollapse.value) {
        store.commit("app/isCollapseChange", false);
      }
    };
    // 初始化调用
    resizeHandler();
    // beforeMount
    onBeforeMount(() => {
      // 监听页面变化
      useEventListener("resize", resizeHandler);
    });
    onMounted(() => {
      setTimeout(() => {
        if (route.path !== "/agreement") {
          let userInfo = JSON.parse(localStorage.getItem("userInfo") || "{}");
          if ((userInfo && userInfo === "") || !userInfo) {
            ElMessage({
              type: "warning",
              message: "暂无登录用户信息，请重新登录",
            });
            router.push("/login");
          }
        }
      }, 2000);
      store.commit("app/stateChange", {
        name: "isForgetShow",
        value: false,
      });
      sessionStorage.setItem("isLogin", "false");
      // store.commit('app/stateChange', {
      //   name: 'isLogin',
      //   value: false
      // })
    });
    // methods
    // 隐藏菜单
    const hideMenu = () => {
      store.commit("app/isCollapseChange", true);
    };
    const handleClose = (done: () => void) => {
      ElMessageBox.confirm("确定关闭吗", {
        cancelButtonText: "取消",
        confirmButtonText: "确认",
      })
        .then(() => {
          sessionStorage.setItem("isLogin", "false");
          // done()
          //     store.commit('app/stateChange', {
          //   name: 'isLogin',
          //   value: false
          // })
          // store.commit("app/stateChange", {
          //     name: "isForgetShow",
          //     value: false,
          //   });
        })
        .catch(() => {
          // catch error
        });
    };
    const closeForget = () => {
      // console.log( forgetRef.value)
      forgetRef.value.cancel();
      store.commit("app/stateChange", {
        name: "isForgetShow",
        value: false,
      });
    };
    return {
      isCollapse,
      showLogo,
      showTabs,
      contentFullScreen,
      keepAliveComponentsName,
      hideMenu,
      // dialogVisible,
      handleClose,
      forgetShow,
      closeForget,
      forgetRef,
    };
  },
});
</script>

<style lang="scss" scoped>
.el-header {
  padding-left: 0;
  padding-right: 0;
  // box-shadow: 0px 2px 4px 0px #EEF1F8;
  // box-shadow: 0px 4px 0.75rem 0px #EEF1F8;
  z-index: 11;
}

.el-aside {
  display: flex;
  flex-direction: column;
  transition: 0.2s;
  overflow-x: hidden;
  transition: 0.3s;
    background: #e9f0ff !important;
  &::-webkit-scrollbar {
    width: 0 !important;
  }
}

.el-main {
  background-color: var(--system-container-background);
  height: 100%;
  padding: 0px;
  overflow-x: hidden;
  background-color: var(--system-container-background);

  .padding0 {
    height: 100%;
    background: white;
  }

  .mainContent {
    // margin: 20px;
    height: 100%;
  }
}

:deep(.el-main-box) {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
  background: #fafcff;
}

@media screen and (max-width: 1000px) {
  .el-aside {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;

    &.hide-aside {
      left: -250px;
    }
  }

  .mask {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 999;
    background: rgba(0, 0, 0, 0.5);
  }
}

.main {
  height: 100%;
  padding: 0px;
  overflow-x: hidden;
  background-color: var(--system-container-background);
  display: block;
  flex: 1;
  flex-basis: auto;
  overflow: auto;
  box-sizing: border-box;
}
</style>
<style  lang="scss">
.login.loginDialog {
  .el-dialog__body {
    .el-input__wrapper {
      background: #fff;
      box-shadow: 0 0 0 0px;
      border: 0px solid;
      border-bottom: 1px solid var(--el-border-color);
    }
  }
}
.loginDialog {
  background: transparent;
  .el-dialog__header,
  .el-dialog__body {
    padding: 0;
    background: transparent;
    .el-input__wrapper {
    }
  }

  .el-dialog__header {
    width: 700px;
    z-index: 100;
    position: absolute;
    height: 30px;

    .el-dialog__headerbtn {
      margin-top: -10px;
      /* margin-left: 17px; */
      right: -8px;
    }
  }
}
.contentHeader {
  //   width: 100%;
  // height: 48px;
  // // background: #FFFFFF;
  // box-shadow: 0px 1px 4px 0px rgba(0,21,41,0.12);
  // border-radius: 0px 0px 0px 0px;
  // opacity: 1;
}
</style>