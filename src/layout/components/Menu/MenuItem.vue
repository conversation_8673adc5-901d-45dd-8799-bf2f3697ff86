<template>
  <template v-if="!menu.hideMenu">
    <el-sub-menu
      class="el-sub-menus"
      v-if="showMenuType === 2"
      :index="pathResolve"
      :show-timeout="0"
      :hide-timeout="0"
    >
      <template #title>
        <div
          :class="menu.meta.icon ? 'el-menu-item-bg' : 'el-menu-item-bg-none'"
        >
          <i :class="[menu.meta.icon, 'leftIcon']" v-if="menu.meta.icon"></i>
        </div>

        <!-- <el-icon v-if="menu.meta.icon" :size="18" color="#000">
          <component :is="menu.meta.icon" />
        </el-icon> -->
        <span class="menu-title">
          {{ menu.meta.title }}
        </span>
      </template>
      <menu-item
        v-for="(item, key) in menu.children"
        :key="key"
        :menu="item"
        :basePath="pathResolve"
      />
    </el-sub-menu>
    <app-link v-else-if="showMenuType === 1" :to="pathResolve">
      <el-menu-item
        :index="pathResolve"
        v-if="
          !menu.children[0].children || menu.children[0].children.length === 0
        "
      >
        <div
          :class="menu.meta.icon ? 'el-menu-item-bg' : 'el-menu-item-bg-none'"
        >
          <i :class="[menu.meta.icon, 'leftIcon']" v-if="menu.meta.icon"></i>
        </div>

        <!-- <el-icon
          v-if="menu.children[0].meta.icon || menu.meta.icon"
          :size="20"
          color="#000"
        >
          <component :is="menu.meta.icon" />
        </el-icon> -->
        <template #title>
          <span class="menu-title"> {{ menu.children[0].meta.title }} </span>
        </template>
      </el-menu-item>
      <el-sub-menu
        v-else
        :index="pathResolve"
        :show-timeout="0"
        :hide-timeout="0"
      >
        <template #title>
          <div
            :class="menu.meta.icon ? 'el-menu-item-bg' : 'el-menu-item-bg-none'"
          >
            <i
              :class="[menu.meta.icon, 'leftIcon']"
              v-if="menu.children[0].meta.icon || menu.meta.icon"
            ></i>
          </div>
          <!-- <el-icon
            v-if="menu.children[0].meta.icon || menu.meta.icon"
            :size="20"
            color="#000"
          >
            <component :is="menu.children[0].meta.icon || menu.meta.icon" />
          </el-icon> -->
          <span class="menu-title">
            {{ menu.children[0].meta.title }}
            <!-- {{ isBackMenu ? menu.children[0].meta.title : $t(menu.children[0].meta.title) }} -->
          </span>
        </template>
        <menu-item
          v-for="(item, key) in menu.children[0].children"
          :key="key"
          :menu="item"
          :basePath="pathResolve"
        />
      </el-sub-menu>
    </app-link>
    <app-link v-else :to="pathResolve">
      <el-menu-item :index="pathResolve">
        <div
          :class="menu.meta.icon ? 'el-menu-item-bg' : 'el-menu-item-bg-none'"
        >
          <i :class="[menu.meta.icon, 'leftIcon']" v-if="menu.meta.icon"></i>
        </div>
        <template #title>
          <span class="menu-title"> {{ menu.meta.title }} </span>
          <!-- {{ isBackMenu ? menu.meta.title : $t(menu.meta.title) }} -->
        </template>
      </el-menu-item>
    </app-link>
  </template>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';
import appLink from './Link.vue';
import { isBackMenu } from '@/config';
export default defineComponent({
  name: 'menu-item',
  props: {
    menu: {
      type: Object,
      required: true,
    },
    basePath: {
      type: String,
      default: '',
    },
  },
  components: {
    appLink,
  },
  setup(props) {
    const menu = props.menu;
    // todo: 优化if结构
    const showMenuType = computed(() => {
      // 0: 无子菜单， 1：有1个子菜单， 2：显示上下级子菜单
      if (
        menu.children &&
        (menu.children.length > 1 ||
          (menu.children.length === 1 && menu.alwayShow))
      ) {
        return 2;
      } else if (
        menu.children &&
        menu.children.length === 1 &&
        !menu.alwayShow
      ) {
        return 1;
      } else {
        return 0;
      }
    });
    // todo: 优化多层if
    const pathResolve = computed(() => {
      let path = '';
      if (showMenuType.value === 1) {
        if (menu.children[0].path.charAt(0) === '/') {
          path = menu.children[0].path;
        } else {
          let char = '/';
          if (menu.path.charAt(menu.path.length - 1) === '/') {
            char = '';
          }
          path = menu.path + char + menu.children[0].path;
        }
      } else {
        path = menu.path;
      }
      path = props.basePath ? props.basePath + '/' + path : path;
      return path;
    });
    return {
      showMenuType,
      pathResolve,
      isBackMenu,
    };
  },
});
</script>

<style lang="scss" scoped>
.el-menu.el-menu--collapse {
  :hover {
    .el-menu-item-bg {
      background: linear-gradient(
        174deg,
        #aec6ff -123%,
        #1f61ff 135%
      ) !important;
    }
    .OfficeBuilding {
      background: center / contain
        url('https://static.idicc.cn/cdn/jinghai/OfficeBuildingLight.webp')
        no-repeat;
    }
    .User {
      background: center / contain
        url('https://static.idicc.cn/cdn/jinghai/UserLight.webp') no-repeat;
    }
    .Refrigerator {
      background: center / contain
        url('https://static.idicc.cn/cdn/jinghai/RefrigeratorLight.webp')
        no-repeat;
    }
    .Monitor {
      background: center / contain
        url('https://static.idicc.cn/cdn/jinghai/MonitorLight.webp') no-repeat;
    }
    .Setting {
      background: center / contain
        url('https://static.idicc.cn/cdn/jinghai/SettingLight.webp') no-repeat;
    }
    //  background: linear-gradient(174deg, #AEC6FF -123%, #1F61FF 135%) !important;
  }
}
.el-menu-item-bg-none {
  width: 20px;
  height: 40px;
}
.el-menu-item-bg {
  width: 40px;
  border-radius: 6px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.el-sub-menu {
  text-align: left;
}
.el-menu-item {
  text-align: left;
}
.menu-title {
  font-size: 16px;
  margin-left: 14px;
}
.el-menu-item i,
.el-sub-menu__title i {
  // margin-right: 14px;
  width: 20px;
  height: 20px;
  display: inline-block;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.OfficeBuilding {
  background: center / contain
    url('https://static.idicc.cn/cdn/jinghai/OfficeBuilding.webp') no-repeat;
}
.User {
  background: center / contain
    url('https://static.idicc.cn/cdn/jinghai/User.webp') no-repeat;
}
.Refrigerator {
  background: center / contain
    url('https://static.idicc.cn/cdn/jinghai/Refrigerator.webp') no-repeat;
}
.Monitor {
  background: center / contain
    url('https://static.idicc.cn/cdn/jinghai/Monitor.webp') no-repeat;
}
.Setting {
  background: center / contain
    url('https://static.idicc.cn/cdn/jinghai/Setting.webp') no-repeat;
}
</style>
<style lang="scss">
// .el-sub-menus.el-sub-menu{
//  display: flex;
//   justify-content: center;
.el-sub-menu__title.el-tooltip__trigger,
.el-menu-tooltip__trigger.el-tooltip__trigger {
  min-height: 40px !important;
  min-width: 40px !important;
  padding: 0px !important;
  // display: flex;
  // justify-content: center;
}
.jh-menu--popup {
  border-radius: 8px;
  border: none !important;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2) !important;
  // display: inline-block !important;
  ul {
    border-radius: 8px;
    li {
      .menu-title {
        margin-left: 20px !important;
      }
    }
    a {
      .menu-title {
        margin-left: 0px !important;
      }
    }
  }
}
// }
</style>
