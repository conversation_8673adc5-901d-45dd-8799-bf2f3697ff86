<template>
  <el-scrollbar>
    <el-menu
      :default-active="activeMenu"
      class="layout-menu system-scrollbar"
      :mode="mode"
      :class="isCollapse ? 'collapse' : ''"
      :collapse="isCollapse"
      :collapse-transition="true"
      :unique-opened="expandOneMenu"
      @select="handleSelect"
      popper-class="jh-menu--popup"
    >
      <menu-item v-for="(menu, key) in allRoutes" :key="key" :menu="menu" />
    </el-menu>
  </el-scrollbar>
</template>

<script lang="ts">
import { defineComponent, computed, onMounted, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useStore } from 'vuex';
import MenuItem from './MenuItem.vue';
export default defineComponent({
  props: {
    mode: {
      type: String,
      default: 'vertical',
    },
  },
  components: {
    MenuItem,
  },
  setup(props) {
    const { mode } = props;
    const store = useStore();
    const isCollapse = computed(() => store.state.app.isCollapse);
    const expandOneMenu = computed(() => store.state.app.expandOneMenu);
    const allRoutes = useRouter().options.routes;
    const route = useRoute();
    const router = useRouter();
    const activeMenu: any = computed(() => {
      const { meta, path } = route;
      if (meta.activeMenu) {
        return meta.activeMenu;
      }
      return path;
    });
    onMounted(() => {});
    const handleSelect = (key: string) => {
      if (key === '/home') {
        let env = import.meta.env.VITE_STATIC_URL_JH;
        if (!env || window.location.hostname === 'localhost') {
          env = 'https://localhost:3001/';
        }
        window.open(`${env}#${key}`);
        // 路由不跳转
        setTimeout(() => {
          // 返回上级路由
          router.go(-1);
        }, 300);
        return;
      } else {
        router.push(key);
      }
    };
    return {
      isCollapse,
      expandOneMenu,
      allRoutes,
      activeMenu,
      mode,
      handleSelect,
    };
  },
});
</script>

<style lang="scss" scoped>
.el-scrollbar {
  // background-color: #e9f0ff !important;
  flex: 1;
}
.layout-menu {
  width: 100%;
  border: none;
  padding: 10px;
  box-sizing: border-box;
  background: transparent !important;

  &.collapse {
    margin-left: 0px;
  }
}
::v-deep {
  .layout-menu.el-menu {
    padding-left: 10px;
  }
  .layout-menu.el-menu.el-menu--collapse {
    padding-left: 16px;
  }
  .el-menu,
  .el-menu-item,
  .el-sub-menu {
    background-color: transparent !important;
  }

  .el-menu-item i,
  .el-menu-item-group__title,
  .el-sub-menu__title i {
    color: var(--system-menu-text-color);
  }

  .el-menu-item,
  .el-sub-menu__title {
    &.is-active {
      background: linear-gradient(153deg, #aec6ff -19%, #1f61ff 84%) !important;
      color: #ffffff !important; // 白色文字
      border-radius: 8px;
      // margin: 0 15px;
      box-sizing: border-box;
      i {
        color: #ffffff !important; // 图标也变为白色
      }
      &:hover {
        background: linear-gradient(
          153deg,
          #aec6ff -19%,
          #1f61ff 84%
        ) !important;
        color: #ffffff !important;
      }
    }

    &:hover {
      background-color: var(--system-menu-hover-background) !important;
    }
  }

  .el-sub-menu {
    &.is-active {
      > .el-sub-menu__title,
      > .el-sub-menu__title i {
        color: var(--system-menu-submenu-active-color) !important;
      }
    }

    .el-menu-item {
      background-color: var(--system-menu-children-background) !important;

      &.is-active {
        color: #ffffff !important; // 白色文字
        border-radius: 8px;
        // margin: 0 15px;
        box-sizing: border-box;
        background: linear-gradient(
          153deg,
          #aec6ff -19%,
          #1f61ff 84%
        ) !important;
        i {
          color: #ffffff !important; // 图标也变为白色
        }
        &:hover {
          background: linear-gradient(
            153deg,
            #aec6ff -19%,
            #1f61ff 84%
          ) !important;
          color: #ffffff !important;
        }
      }
      &:hover {
        background-color: var(--system-menu-hover-background) !important;
      }
    }

    .el-sub-menu__title {
      background-color: var(--system-menu-children-background) !important;

      &:hover {
        background-color: var(--system-menu-hover-background) !important;
      }

      // 展开箭头样式
      .el-submenu__icon-arrow {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: transform 0.3s;
      }

      &.is-opened .el-submenu__icon-arrow {
        transform: rotate(90deg);
      }
    }
  }

}

</style>
