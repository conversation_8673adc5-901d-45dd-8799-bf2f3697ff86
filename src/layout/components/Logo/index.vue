<template>
  <div class="logo-container">
    <img class="icon" src="@/assets/logo.png" alt="" />
    <h1 v-if="!isCollapse">{{ $t(systemTitle) }}</h1>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed } from "vue";
import { useStore } from "vuex";
import { systemTitle } from "@/config";
export default defineComponent({
  setup() {
    const store = useStore();
    const isCollapse = computed(() => store.state.app.isCollapse);
    return {
      isCollapse,
      systemTitle,
    };
  },
});
</script>

<style lang="scss" scoped>
.logo-container {
  height: 60px;
  display: flex;
  align-items: center;
  // justify-content: center;
  padding-left: 15px;
  width: 100%;
  // background-color: #e9f0ff !important;
  h1 {
    font-size: 18px;
    white-space: nowrap;
    color: var(--system-logo-color);
  }
  .icon {
    width: 34px;
    height: 28px;
    margin-right: 10px;
  }
}
</style>