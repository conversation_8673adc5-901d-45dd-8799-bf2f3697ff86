import request from '@/utils/system/request'

// 未被锁定楼层树
export function getNoLeaseBuildingTree (params:any) {
  return request({
    url: '/lease/getNoLeaseBuildingTree',
    method: 'get',
    params
  })
}
// 所有楼层树
export function TreeAPI (params:any) {
  return request({
    url: '/houses/getBuildingTree',
    method: 'get',
    params
  })
}
// 获取数据api
export function getpageList (data:object) {
  return request({
    url: '/lease/page',
    method: 'post',
    data
  })
}
// 获取unicode
// 获取数据api
export function getEnterpriseId (params:object) {
  return request({
    url: 'enterprise/listAllEnterprise',
    method: 'get',
    params
  })
}
// 获取unicode
// 租金：获取缴纳时间
export function geRentTime (params:object) {
  return request({
    url: 'lease/rent/alreadyPaid',
    method: 'get',
    params
  })
}
// 获取unicode
// 物业费：获取缴纳时间
export function getPropertyTime(params:object) {
  return request({
    url: 'lease/property/alreadyPaid',
    method: 'get',
    params
  })
}
// 获取unicode
// 物业费： 账单详情
export function getLeaseBillDetail(params:object) {
  return request({
    url: 'lease/queryLeaseBillDetail',
    method: 'get',
    params
  })
}
 