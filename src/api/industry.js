import request from '@/utils/system/request'
import {  token } from '@/utils/utils'

/**
 * 产业协会/产业基金/园区荣誉数据列表
 * @param {*} params 
 * @returns 
 */
export function industryOrParkHonorListAPI(params) {
  return request({
    url: '/data/manager/industryOrParkHonor/list',
    method: 'get',
    params
  })
}
/**
 * 产业协会/产业基金/园区荣誉数据新增
 * @param {*} data 
 * @returns 
 */
export function addindustryOrParkHonorAPI(data) {
  return request({
    url: '/data/manager/industryOrParkHonor/add',
    method: 'post',
    data
  })
}
/**
 * 产业协会/产业基金/园区荣誉数据删除
 * @param {*} data 
 * @returns 
 */
export function deleteindustryOrParkHonorAPI(params) {
  return request({
    url: '/data/manager/industryOrParkHonor/delete',
    method: 'get',
    params
  })
}
/**
 * 产业协会/产业基金/园区荣誉数据编辑
 * @param {*} data 
 * @returns 
 */
export function updateindustryOrParkHonorAPI(data) {
  return request({
    url: '/data/manager/industryOrParkHonor/update',
    method: 'post',
    data
  })
}
/**
 * 政策申报列表
 * @param {*} data 
 * @returns 
 */
export function policyDeclarelistAPI(params) {
  return request({
    url: '/data/manager/policyDeclare/list',
    method: 'get',
    params
  })
}
/**
 * 政策申报删除
 * @param {*} data 
 * @returns 
 */
export function policydeleteAPI(params) {
  return request({
    url: '/data/manager/policyDeclare/delete',
    method: 'get',
    params
  })
}
/**
 * 政策申报新增
 * @param {*} data 
 * @returns 
 */
export function addpolicyDeclareAPI(data) {
  return request({
    url: '/data/manager/policyDeclare/add',
    method: 'post',
    data
  })
}
/**
 * 政策申报编辑
 * @param {*} data 
 * @returns 
 */
export function updatepolicyDeclareAPI(data) {
  return request({
    url: '/data/manager/policyDeclare/update',
    method: 'post',
    data
  })
}
/**
 * 人才申报/园区概况/园区成果详情
 * @param {*} data 
 * @returns 
 */
export function parkMessagedetailAPI(params) {
  return request({
    url: '/data/manager/parkMessage/detail',
    method: 'get',
    params
  })
}
/**
 * 人才申报/园区概况/园区成果新增或编辑
 * @param {*} data 
 * @returns 
 */
export function addOrUpdateAPI(data) {
  return request({
    url: '/data/manager/parkMessage/addOrUpdate',
    method: 'post',
    data
  })
}
/**
 * 科技申报列表
 * @param {*} data 
 * @returns 
 */
export function scienceDeclarelistAPI(params) {
  return request({
    url: '/data/manager/scienceDeclare/list',
    method: 'get',
    params
  })
}
/**
 * 科技申报新增
 * @param {*} data 
 * @returns 
 */
export function addscienceDeclareAPI(data) {
  return request({
    url: '/data/manager/scienceDeclare/add',
    method: 'post',
    data
  })
}
/**
 * 科技申报编辑
 * @param {*} data 
 * @returns 
 */
export function updatescienceDeclareAPI(data) {
  return request({
    url: '/data/manager/scienceDeclare/update',
    method: 'post',
    data
  })
}
/**
 * 科技申报删除
 * @param {*} data 
 * @returns 
 */
export function deletescienceDeclareAPI(params) {
  return request({
    url: '/data/manager/scienceDeclare/delete',
    method: 'get',
    params
  })
}
/**
 * 年度招商情况列表
 * @param {*} data 
 * @returns 
 */
export function annualInvestmentSituationlistAPI(params) {
  return request({
    url: '/data/manager/annualInvestmentSituation/list',
    method: 'get',
    params
  })
}
/**
 * 年度招商情况新增
 * @param {*} data 
 * @returns 
 */
export function addannualInvestmentSituationAPI(data) {
  return request({
    url: '/data/manager/annualInvestmentSituation/add',
    method: 'post',
    data
  })
}
/**
 * 年度招商情况编辑
 * @param {*} data 
 * @returns 
 */
export function updataannualInvestmentSituationAPI(data) {
  return request({
    url: '/data/manager/annualInvestmentSituation/update',
    method: 'post',
    data
  })
}
/**
 * 年度招商情况删除
 * @param {*} data 
 * @returns 
 */
export function deleteannualInvestmentSituationAPI(params) {
  return request({
    url: '/data/manager/annualInvestmentSituation/delete',
    method: 'get',
    params
  })
}
/**
 * 年度招商情况编辑
 * @param {*} data 
 * @returns 
 */
export function getenterprisePageAPI(data) {
  return request({
    url: '/board/enterprisePage',
    method: 'post',
    data
  })
}

// 产业渠道 导出

    export function downloadIndustryChannel() {
        return request({
          url: `industry/channels/download`,
          method: 'post',
     data:{},
          headers: {
              'Content-Type': 'application/json',
              'token': token()
            },
            responseType: 'blob' || '',
        })
      }

 