import request from '@/utils/system/request'
const baseURL = "https://pangudev.idicc.cn/sso".slice(0, -4);

/**
 * 产业小艾-台账列表
 * @param {*} data
 * @returns
 */
export function listBillingsAPI(data) {
  return request({
    url: baseURL + "/ai/billings/listBillings",
    method: "POST",
    data,
  });
}
/**
 * 产业小艾-用户列表
 * @param {*} data
 * @returns
 */
export function smartAIUserPage(data) {
  return request({
    url: "/admin/user/smartAIUserPage",
    method: "post",
    data,
    baseURL: "/sso",
  });
}
/**
 * 产业小艾-用户列表
 * 赠送会员券
 * @param {*} data
 * @returns
 */
export function giveVip(data) {
  return request({
    url: "/admin/user/giveVip",
    method: "post",
    data,
  });
}
/**
 * 产业小艾-用户列表
 * 赠送会员
 * @param {*} data
 * @returns
 */

export function saveMember(data) {
  return request({
    url: baseURL + "/ai/user/center/vipAddManual",
    method: "post",
    data,
  });
}
/**
 * 产业小艾-用户列表
 * 可购买的产业链
 * @param {*} data
 * @returns
 */

export function getKnowledgeLibrary() {
  return request({
    url: baseURL + "/ai/knowledgeLibrary/listAllPurchaseChain",
    method: "get",
  });
}
/**
 * 产业小艾-更新用户状态
 * @param {*} data
 * @returns
 */
export function updateAIUserStatus(data) {
  return request({
    url: "/admin/user/updateAIUserStatus",
    method: "post",
    data,
  });
}
/**
 * 查询用户的前三个订单
 * @param {*} data
 * @returns
 */
export function queryUserBillings(params) {
  return request({
    url: baseURL + "/ai/billings/queryUserBillings",
    method: "GET",
    params,
  });
}
/**
 * 查询能力库列表
 * @param {*} data
 * @returns
 */
export function queryAbilityLib(params) {
  return request({
    url: baseURL + "/ai/capability/list",
    method: "GET",
    params,
  });
}

export function addAbilityLib(data) {
  return request({
    url: baseURL + "/ai/capability/add",
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data,
  });
}

export function updateAbilityLib(data) {
  return request({
    url: baseURL + "/ai/capability/update",
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data,
  });
}
/**
 * 数据产品列表
 * @param {*} data
 * @returns
 */
export function productListAPI(params) {
  return request({
    url: baseURL + "/ai/data/product/productList",
    method: "GET",
    params,
  });
}
/**
 * 数据产品数量
 * @param {*} data
 * @returns
 */
export function productCountAPI(params) {
  return request({
    url: baseURL + "/ai/data/product/productCount",
    method: "GET",
    params,
  });
}
/**
 * 新增数据产品
 * @param {*} data
 * @returns
 */
export function addProductAPI(data) {
  return request({
    url: baseURL + "/ai/data/product/addProduct",
    method: "post",
    data,
  });
}
/**
 * 编辑数据产品
 * @param {*} data
 * @returns
 */
export function updateProductAPI(data) {
  return request({
    url: baseURL + "/ai/data/product/updateProduct",
    method: "post",
    data,
  });
}
/**
 * 企业课程主题类型
 * @param {*} data
 * @returns
 */
export function typenumberListAPI(params) {
  return request({
    url: baseURL + "/ai/enterprise/class/type/number",
    method: "GET",
    params,
  });
}
/**
 * 企业课程主题类型修改
 * @param {*} data
 * @returns
 */
export function typeupdateAPI(data) {
  return request({
    url: baseURL + "/ai/enterprise/class/type/update",
    method: "POST",
    data,
  });
}
/**
 * 后台管理根据主题查询课程
 * @param {*} data
 * @returns
 */
export function listByThemeAPI(params) {
  return request({
    url: baseURL + "/ai/enterprise/class/listByTheme",
    method: "GET",
    params,
  });
}
/**
 * 后台 新增企业课程
 * @param {*} data
 * @returns
 */
export function addcourseAPI(data) {
  return request({
    url: baseURL + "/ai/enterprise/class/addClass",
    method: "POST",
    data,
  });
}
/**
 * 后台 新增企业课程
 * @param {*} data
 * @returns
 */
export function updacourseAPI(data) {
  return request({
    url: baseURL + "/ai/enterprise/class/updateClass",
    method: "POST",
    data,
  });
}
/**
 * 待支付列表
 * @param {*} data
 * @returns
 */
export function waitPaymentListAPI(params) {
  return request({
    url: baseURL + "/ai/entrust/admin/waitPaymentList",
    method: "GET",
    params,
  });
}
/**
 * 改为已支付
 * @param {*} data
 * @returns
 */
export function paymentAPI(data) {
  return request({
    url: baseURL + "/ai/entrust/admin/payment",
    method: "POST",
    data,
  });
}
/**
 * 委托单列表
 * @param {*} data
 * @returns
 */
export function followListAPI(params) {
  return request({
    url: baseURL + "/ai/entrust/admin/list",
    method: "GET",
    params,
  });
}
/**
 * 委托单详情
 * @param {*} data
 * @returns
 */
export function entrustdetailAPI(params) {
  return request({
    url: baseURL + "/ai/entrust/admin/detail",
    method: "GET",
    params,
  });
}
/**
 * 管理员 跟进审核
 * @param {*} data
 * @returns
 */
export function followCheckAPI(data) {
  return request({
    url: baseURL + "/ai/entrust/admin/adminReview",
    method: "POST",
    data,
  });
}
/**
 * 管理员 申诉审核
 * @param {*} data
 * @returns
 */
export function complainCheckAPI(data) {
  return request({
    url: baseURL + "/ai/entrust/admin/complainCheck",
    method: "POST",
    data,
  });
}
// 城市 - json 数据
export function getCityJson(code, areaType, cityData) {
  const cityUrl = baseURL + "/admin/orgIndustryChainRelation/geo/data";
  let params = "";
  if (code == 110100) {
    params = `110000_full`;
  } else if (code == 310100) {
    params = `310000_full`;
  } else if (code == 120100) {
    params = `120000_full`;
  } else if (code == 500100) {
    params = `500000_full`;
  } else {
    // 台湾
    if (code == 710000) {
      params = `${code}`;
      // 东莞
    } else if (code == 441900) {
      params = `${code}`;
    } else if (
      areaType === "1" ||
      areaType === "2" ||
      ["110100"].includes(code)
    ) {
      params = `${code}_full`;
    } else if (areaType === "3" && cityData.length == 3) {
      params = `${code}_full`;
    } else {
      params = `${code}`;
    }
  }
  return request({
    url: cityUrl,
    params: { code: params },
    method: "GET",
  });
}
/**
 * 管理员提现列表
 * @param {*} data
 * @returns
 */
export function adminWithdrawalListAPI(params) {
  return request({
    url: baseURL + "/ai/user/account/adminWithdrawalList",
    method: "GET",
    params,
  });
}
/**
 * 管理员提现列表
 * @param {*} data
 * @returns
 */
export function remitpaymentAPI(data) {
  return request({
    url: baseURL + "/ai/user/account/payment",
    method: "POST",
    data,
  });
}
/**
 * 管理员  接单成交统计+顾问佣金排名
 * @param {*} data
 * @returns
 */
export function getInvestmentReportAPI(params) {
  return request({
    url: baseURL + "/ai/entrust/admin/getInvestmentReport",
    method: "GET",
    params,
  });
}
/**
 * 管理员 结单总金额总单量访问量 废单比 跟进情况 顾问列表
 * @param {*} data
 * @returns
 */
export function getInvestmentAllCountAPI(params) {
  return request({
    url: baseURL + "/ai/entrust/admin/getInvestmentAllCount",
    method: "GET",
    params,
  });
}
/**
 * 管理员 招商实时跟进情况
 * @param {*} data
 * @returns
 */
export function getInvestmentRealTimeAPI(params) {
  return request({
    url: baseURL + "/ai/entrust/admin/getInvestmentRealTime",
    method: "GET",
    params,
  });
}
/**
 * 会员详情
 * @param {*} data
 * @returns
 */
export function getVipList(data) {
  return request({
    url: baseURL + "/ai/user/center/vipList",
    method: "GET",
    params: data,
  });
}

/**
 * 产业政策列表
 * @param {*} data
 * @returns
 */
export function getPolicyInfo(params) {
  return request({
    url: baseURL + "/ai/industry/policyInfo/list",
    method: "GET",
    params,
  });
}

export function updatePolicyStatus(data) {
  return request({
    url: baseURL + "/ai/industry/policyInfo/updateStatus",
    method: "POST",
    data,
  });
}

export function createPolicyInfo(data) {
  return request({
    url: baseURL + "/ai/industry/policyInfo/create",
    method: "POST",
    data,
  });
}

export function deletePolicyInfo(data) {
  return request({
    url: baseURL + "/ai/industry/policyInfo/delete",
    method: "POST",
    data,
  });
}

export function updatePolicyInfo(data) {
  return request({
    url: baseURL + "/ai/industry/policyInfo/update",
    method: "POST",
    data,
  });
}

/**
 * 政策汇编列表
 * @param {*} data
 * @returns
 */
// export function getPolicyList(params) {
//   return request({
//     url: baseURL + "/ai/oss/policy/list",
//     method: "GET",
//     params,
//   });
// }
export function policyUpdate(data) {
  return request({
    url: baseURL + "/ai/oss/policy/updatePdf",
    method: "POST",
    data,
  });
}
export function getAddressList(data) {
  return request({
    url: baseURL + "/admin/administrativeDivision/list",
    method: "GET",
    params: data,
  });
}

export function updateCode(data) {
  return request({
    url: baseURL + "/ai/user/center/updateCode",
    method: "POST",
    data,
  });
}

export function updateCompletionAPI(data) {
  return request({
    url: baseURL + "/ai/user/center/updateCompletion",
    method: "POST",
    data,
  });
}

/**
 * 获取省市
 * @param {*} data
 * @returns
 */
export function getAllAddress(params) {
  return request({
    url: baseURL + "/admin/administrativeDivision/getAll",
    method: "GET",
    params,
  });
}

export function getPolicyList(params) {
  return request({
    url: baseURL + "/ai/industry/policyFile/list",
    method: "GET",
    params,
  });
}

// 批量新建

export function createMutiPolicyFile(data) {
  return request({
    url: baseURL + "/ai/industry/policyFile/create",
    method: "POST",
    data,
  });
}

// 批量上架

export function uploadPolicyFile(data) {
  return request({
    url: baseURL + "/ai/industry/policyFile/upload",
    method: "POST",
    data,
  });
}

// 更新汇编
export function updatePolicyFile(data) {
  return request({
    url: baseURL + "/ai/industry/policyFile/recordCreate",
    method: "POST",
    data,
  });
}

//历史记录
export function historyPolicyFileRecord(params) {
  return request({
    url: baseURL + "/ai/industry/policyFile/recordList",
    method: "GET",
    params,
  });
}

//替换历史记录
export function replaceHistoryRecord(data) {
  return request({
    url: baseURL + "/ai/industry/policyFile/replace",
    method: "POST",
    data,
  });
}

// 所有产业链列表

export function getAllChain() {
  return request({
    url: baseURL + "/ai/industryChain/allChain",
    method: "GET",
  });
}

//获取文件流

export function getFile(data) {
  return request({
    url: baseURL + "/ai/industry/policyFile/getFile",
    method: "POST",
    data,
    responseType: "blob",
  });
}

/**
 * 机构列表
 * @param {*} data
 * @returns
 */
export function getAllAPI(data) {
  return request({
    url: baseURL + "/sso/admin/institution/getOwnAll",
    method: "get",
    params: data,
  });
}
/**
 * 根据机构code查询用户和余额
 * @param {*} data
 * @returns
 */
export function getUserListByOrgCodeAPI(data) {
  return request({
    url: baseURL + "/ai/entrust/admin/getUserListByOrgCode",
    method: "GET",
    params: data,
  });
}
/**
 * 机构充值记录
 * @param {*} data
 * @returns
 */
export function getRechargeListByOrgCodeAPI(data) {
  return request({
    url: baseURL + "/ai/entrust/admin/getRechargeListByOrgCode",
    method: "GET",
    params: data,
  });
}
/**
 * 用户机构支付委托单列表
 * @param {*} data
 * @returns
 */
export function getEntrustListByOrgCodeAPI(data) {
  return request({
    url: baseURL + "/ai/entrust/admin/getEntrustListByOrgCode",
    method: "GET",
    params: data,
  });
}
/**
 * 充值
 * @param {*} data
 * @returns
 */
export function rechargeOrgCodeAPI(data) {
  return request({
    url: baseURL + "/ai/entrust/admin/rechargeOrgCode",
    method: "post",
    data,
  });
}
/**
 * 管理端顾问分页列表
 * @param {*} data
 * @returns
 */
export function workerListPageAPI(data) {
  return request({
    url: baseURL + "/ai/invitationRecord/admin/workerListPage",
    method: "GET",
    params: data,
  });
}
/**
 * 管理端交易历史
 * @param {*} data
 * @returns
 */
export function adminSelfOrderHistoryAPI(data) {
  return request({
    url: baseURL + "/ai/invitationRecord/admin/adminSelfOrderHistory",
    method: "GET",
    params: data,
  });
}
/**
 * 管理端交易历史
 * @param {*} data
 * @returns
 */
export function adminOrderHistoryAPI(data) {
  return request({
    url: baseURL + "/ai/invitationRecord/admin/adminOrderHistory",
    method: "GET",
    params: data,
  });
}
/**
 * 管理端顾问详情
 * @param {*} data
 * @returns
 */
export function adminWorkerDetailAPI(data) {
  return request({
    url: baseURL + "/ai/invitationRecord/admin/adminWorkerDetail",
    method: "GET",
    params: data,
  });
}
/**
 * 管理端顾问详情
 * @param {*} data
 * @returns
 */
export function topDownListAPI(data) {
  return request({
    url: baseURL + "/ai/invitationRecord/admin/topDownList",
    method: "GET",
    params: data,
  });
}


//管理员需求列表
export function getDemandListAPI(data) {
  return request({
    url: baseURL + "/ai/entrust/admin/demand/list",
    method: "GET",
    params: data,
  })
}

//管理员反馈列表
export function getRecommandListAPI(data) {
  return request({
    url: baseURL + "/ai/entrust/admin/recommend/list",
    method: "GET",
    params: data
  })
}

//审核反馈
export function recommendCheckAPI(data) {
  return request({
    url: baseURL + "/ai/entrust/admin/recommendCheck",
    method: "POST",
    data
  })
}
//用户钱包管理
export function getUserlistAPI(params) {
  return request({
    url: baseURL + "/ai/user/account/getUserList",
    method: "get",
    params
  })
}
//佣金明细
export function commissionBreakdownAPI(params) {
  return request({
    url: baseURL + "/ai/user/account/incomeFlowList",
    method: "GET",
    params
  })
}
//提现记录
export function balanceFlowListAPI(params) {
  return request({
    url: baseURL + "/ai/user/account/balanceFlowList",
    method: "get",
    params
  })
}
//意见反馈列表
export function feedbackPageAPI(data) {
  return request({
    url: baseURL + "/ai/feedback/page",
    method: "POST",
    data
  })
}
//删除反馈
export function deletefeedbackAPI(params) {
  return request({
    url: baseURL + "/ai/feedback/delete",
    method: "get",
    params
  })
}
//内容管理列表
export function smartAIUserPageWithDialogueCountAPI(data) {
  return request({
    url: "/admin/user/smartAIUserPageWithDialogueCount",
    method: "POST",
    data
  })
}
//用户历史记录
export function historyByUserAPI(data) {
  return request({
    url: baseURL + "/ai/context/historyByUser",
    method: "POST",
    data
  })
}

//---------------------------即时资讯发布api----------------------------------
export function offLineData(params) {//下线
  return request({
    url: baseURL + '/admin/user/deleted',
    method: 'GET',
    params
  })
}


export function deleteData(data) {//删除
  return request({
    url: baseURL + '/ai/news/admin/delete',
    method: 'POST',
    data
  })
}
export function infoList(data) {//获取列表
  return request({
    url: baseURL + "/ai/news/admin/page",
    method: "post",
    data,
  });
  //下线
}

export function creteNews(data) {
  return request({
    url: baseURL + "/ai/news/admin/add",
    method: "POST",
    data,
  });
}




export function updateNews(data) {
  return request({
    url: baseURL + "/ai/news/admin/modify",
    method: "POST",
    data,
  });
}

export function getNewsDetail(params) {
  return request({
    url: baseURL + "/ai/news/admin/detail",
    method: "GET",
    params,
  });
}

//广告发布
export function advertAdd(data) {
  return request({
    url: baseURL + "/ai/advert/admin/add",
    method: "POST",
    data,
  });
}
//删除
export function avertDelete(data) {//删除
  return request({
    url: baseURL + '/ai/advert/admin/delete',
    method: 'POST',
    data
  })
}

export function avertUpdate(data) {//删除
  return request({
    url: baseURL + '/ai/advert/admin/modify',
    method: 'POST',
    data
  })
}

export function avertQuery(data) {//查询
  return request({
    url: baseURL + '/ai/advert/admin/page',
    method: 'POST',
    data
  })
}

export function queryAvertDetail(params) {//查询
  return request({
    url: baseURL + '/ai/advert/admin/detail',
    method: 'GET',
    params
  })
}

export function uploadAPI(data) {//查询
  const fm = new FormData()
  fm.append('file', data.file)
  return request({
    url: baseURL + '/jh/upload',
    method: 'post',
    data: fm
  })
}
//查询上级名称
export function parentListAPI(data) {//查询
  return request({
    url: baseURL + '/ai/invitationRecord/worker/parent/list',
    method: 'POST',
    data
  })
}
// 智推监控记录

export function monitorListAPI(params) {
  return request({
    url: baseURL + "/ai/invest/report/monitor/list",
    method: "GET",
    params
  });
}
// 是否可编辑招商属地

export function checkIsDefaultOrNullAPI(params) {
  return request({
    url: baseURL + "/ai/user/center/checkIsDefaultOrNull",
    method: "GET",
    params
  });
}