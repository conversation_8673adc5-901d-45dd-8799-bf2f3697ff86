import request from "@/utils/system/request";
/** 获取第一个大屏 */
export function getVieData() {
  return request({
    url: "/board/pcOverview",
    method: "get",
  });
}
/** 获取第一个大屏弹窗 */

export function getSecondVieData(data: Object) {
  return request({
    url: "/board/pcBuildingOverview",
    method: "post",
    data,
  });
}
/** 获取楼栋信息 */

export function getFloorData(data: Object) {
  return request({
    url: "/billund/getOne",
    method: "post",
    data,
  });
}

// 单个楼栋
export function getBuilding(data: Object) {
  return request({
    url: "/billund/getOne",
    method: "post",
    // baseURL: '/api',
    data,
  });
}

export function getEnterpriseInsight(data: Object) {
  return request({
    url: "/board/pcEnterpriseInsight",
    method: "post",
    data,
  });
}
// 园区公告

export function getHomeAnnouncement(data: Object) {
  return request({
    url: "/billund/getListPage",
    method: "post",
    data: {
      businessCode: "information",
      conditions: [
        {
          "compareType": "in",
          "key": "type",
          "values": ["1","2","6"]
      },
      {
          "compareType": "eq",
          "key": "status",
          "value": "2"
      },
      {
          "compareType": "orderBy",
          "key": "publish_time",
          "isDesc": true
      },
        ...data?.conditions,
      ],
      pageSize: data.pageSize,
      pageNum: data.pageNum,
    },
  });
}
// 园区活动
interface HomeEven {
  id: string;
  pageSize: number;
  pageNum: number;
  conditions?: Object;
}
export function getHomeEven(data: HomeEven) {
  return request({
    url: "/billund/getListPage",
    method: "post",
    data: {
      businessCode: "information",
      conditions: [
        {
          "compareType": "eq",
          "key": "type",
          "value":  data?.id,
      },
      {
          "compareType": "eq",
          "key": "status",
          "value": "2"
      },
      {
          "compareType": "orderBy",
          "key": "publish_time",
          "isDesc": true
      },
        ...data?.conditions,
      ],
      pageSize: data.pageSize,
      pageNum: data.pageNum,
    },
  });
}
// 园区活动&园区公告 详情

export function getHomeEvenDetail(data: Object) {
  return request({
    url: "/billund/getOne",
    method: "post",
    data: {
      businessCode: "information",
      conditions: [
        {
          "compareType":"eq",
          "key":"id",
          "value":data.id
      }
      ],
      
    },
  });
}
// 园区风采查询接口

export function getParkStyle(data: Object) {
  return request({
    url: "/parkStyle/index",
    method: "post",
    data: data,
  });
}

// 园区载体配套
export function getSupporting(data: Object) {
  return request({
    url: "/board/pcParkService/supporting",
    method: "get",
    data: data,
  });
}

// 未登录政策申报

export function NoTokengetPolicy(data: Object) {
  return request({
    //url: `/board/pcParkService/policy`,
    url: `/data/manager/policyDeclare/listByDashboard`,
    method: "get",
    params:{
      year:data.year,
      pageNum:1,
      pageSize:1000,
    }
  });
}
// 政策申报

export function getPolicy(data: Object) {
  return request({
    //url: `/board/pcParkService/policy`,
    url: `/data/manager/policyDeclare/list`,
    method: "get",
    params:{
      year:data.year,
      pageNum:1,
      pageSize:1000,
    }
  });
}


 
// 人才申报


export function getTalent(data: Object) {
  return request({
    //url: `/board/pcParkService/talent`,
    url: `/data/manager/parkMessage/detail`,
    method: "get",
    params:{
      year:data.year,
    }
  });
}
//未登录的人才申报
export function NoTokengetTalent(data: Object) {
  return request({
    //url: `/board/pcParkService/talent`,
    url: `/data/manager/parkMessage/detailByDashboard`,
    method: "get",
    params:{
      year:data.year,
    }
  });
}
// 科技申报
export function getTech(data: Object) {
  return request({
    //url: `/board/pcParkService/tech`,
    url: `/data/manager/scienceDeclare/bestNew`,
    method: "get",
    params:{
      year:data.year,
    }
  });
}
// // 第三个大屏 能耗监测
export function getThirdLis(data: Object) {
  return request({
    url: `/board/energy/overview`,
    method: "post",
    // baseURL:'mock',
    data
  });
}
// // // 园区简介 
export function getDetail() {
  return request({
    url: `/billund/getList`,
    method: "post",
    data:{
      "businessCode": "park_intro",
      "conditions": []
  }
  });
}
// 园区简介 编辑

export function formSubmit(data: Object) {
  return request({
    url: "/billund/formSubmit",
    method: "post",
    data: data,
  });
}
// 园区风采 编辑

export function demeanourEditor(data: Object) {
  return request({
    url: "/parkStyle/saveOrUpdate",
    method: "post",
    data: data,
  });
}


export function  collect(){
  return request({
    url: "/parkStyle/saveOrUpdate",
    method: "post",
    data: data,
  });
}
//  企业 单个查询
export function getEnterpriseData(data: object) {
  return request({
    url: '/enterprise/getOne',
    method: 'post',
    data
  })
}
// 政策申报数量

export function getPolicyNum(data: Object) {
  return request({
    url: `/data/manager/policyDeclare/statistics`,
    method: "get",
    params:{
      year:data.year,
    }
  });
}

// 增加人才统计接口
  export function getTalentStat( ) {
  return request({
    url: `/enterprise/getTalentStat`,
    method: "get",
    // params: data
  });
}