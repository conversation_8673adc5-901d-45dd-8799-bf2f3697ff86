import request from '@/utils/system/request'

/** 我的收藏 */
export function getMyCollect(data: object) {
  return request({
    url: '/collect/pageMyCollect',
    method: 'post',
    data
  })
}

/** 企业主页-办事记录
 */
export function getMyRecord(data: object) {
  return request({
    url: '/enterprise/done/history/homePage',
    method: 'post',
    data
  })
}
/** 企业主页- 我的消息
 */
export function getMyMessage(data: object) {
  return request({
    url: '/message/myPage',
    method: 'post',
    data
  })
}
// /message/myPage
/** 企业主页-  全部标记已读
 */
export function setMyPage(data: object) {
  return request({
    url: '/message/readAll',
    method: 'get',
    data
  })
}
/** 企业主页-  全部标记已读
 */
// /bill/myPageList
export function getMyCheck(data: object) {
  return request({
    url: '/lease/myPage',
    method: 'post',
    data:{
    status:'1',
      pageSize:1,
      pageNum:1,
    }
  })
}

// onLineService

// /message/myPage
/**未读消息总数

 */
export function countUnRead(data: object) {
  return request({
    url: '/message/countUnRead',
    method: 'get',
    data
  })
}

/** 企业主页- 我的消息
 */
export function getMessageOnline(data: object) {
  return request({
    url: '/message/home',
    method: 'get',
    params:{limit:5}
  })
}

