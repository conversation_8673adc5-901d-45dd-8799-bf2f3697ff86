import request from '@/utils/system/request'
import {  token } from '@/utils/utils'
// 房源列表一键导出
export function downloadAll(data: object) {
  return request({
    url: `houses/downloadAll`,
    method: 'get',
    data,
    headers: {
        'Content-Type': 'application/json',
        'token': token()
      },
      responseType: 'blob' || '',
  })
}
//企业数据导出
// export function enterprisedownload(data: object) {
//     return request({
//       url: `enterprise/downloadAll`,
//       method: 'get',
//       data,
//       headers: {
//           'Content-Type': 'application/json',
//           'token': token()
//         },
//         responseType: 'blob' || '',
//     })
//   }
  //企业上报数据下载模板
  export function downloadTemplate(data: object) {
    return request({
      url: `enterprise/business/downloadTemplate`,
      method: 'get',
      data,
      headers: {
          'Content-Type': 'application/json',
          'token': token()
        },
        responseType: 'blob' || '',
    })
  }
    //企业上报数据导出
    export function enterprisebusiness(params: object) {
        return request({
          url: `enterprise/business/downloadAll`,
          method: 'get',
          params,
          headers: {
              'Content-Type': 'application/json',
              'token': token()
            },
            responseType: 'blob' || '',
        })
      }





  // 物业管理
  //数据导出
  export function exportEnterpriseList(data: object) {
    let url = `lease/rent/enterprise/export`;
    if (data.type == '0'){
      url = `lease/property/enterprise/export`;
    }
    return request({
      url,
      method: 'get',
      params:data,
      headers: {
          'Content-Type': 'application/json',
          'token': token()
        },
        responseType: 'blob' || '',
    })
  }
    //统计数据导出
    export function exportStatisticsList(data: object) {
      return request({
        url: `org/statistics/export`,
        method: 'get',
        params: data,
        headers: {
            'Content-Type': 'application/json',
            'token': token()
          },
          responseType: 'blob' || '',
      })
    }


//企业数据导出
export function downloadByCondition(data: object) {
    return request({
      url: `/enterprise/downloadByCondition`,
      method: 'post',
      data,
      headers: {
          'Content-Type': 'application/json',
          'token': token()
        },
        responseType: 'blob' || '',
    })
  }