import request from '@/utils/system/request'
/** 登录api */
export function loginApi(data: object) {
  return request({
    url: '/admin/login',
    method: 'post',
    data,
    baseURL: '/sso'
  })
}

export function orgApi() {
  return request({
    url: '/admin/institution/ownList',
    method: 'get',
    baseURL:'/sso'
  })
}

/** 获取用户信息Api */
export function getInfoApi(data: object) {
  return request({
    url: '/user/info',
    method: 'post',
    baseURL: '/mock',
    data
  })
}

/** 退出登录Api */
 
export function loginOutApi() {
  return request({
    url: '/admin/logout',
    method: 'post',
  })
}

/** 获取用户信息Api */
export function passwordChange(data: object) {
  return request({
    url: '/user/passwordChange',
    method: 'post',
    baseURL: '/mock',
    data
  })
}

/** 获取登录后需要展示的菜单 */
export function getMenuApi() {
  return request({
    url: '/sso/admin/role/acl/roleAclTree',
    // pangudev.idicc.cn/sso/admin/role/acl/roleAclTree
    method: 'get',
    baseURL: 'mock'
  })
}
//注册
export function signApi(data: object) {
  return request({
    //url: '/enterpriseRegister/formSubmit',
    url: '/enterpriseRegister/register',
    method: 'post',
    data
  })
}
export function publicKey( ) {
  return request({
    url: '/common/user/login/publicKey',
    method: 'get',
 
  })
}
export function getCode(data :object) {
  return request({
    url: '/common/sms/log/sendVerificationCode',
    method: 'post',
    data
  })
}

/**
 * 获取登陆密码rsa加密的公钥
 * @returns 
 */
export function encryptionAPI() {
  return request({
    url:'/admin/user/login/publicKey',
    method: 'get',
    baseURL: '/sso'

  })
}
export function setPasswordAPI2(data) {
  return request({
    url: '/admin/user/setPassword',
    method: 'POST',
    data,
    baseURL: '/sso'
  })
}
/**
 * 账号设置-设置密码
 * @param {*} data 
 * @returns 
 */
export function setPasswordAPI(data:any) {
  return request({
      url: '/common/user/setPassword',
      method: 'post',
      data,
      // baseURL: '/pangu'
  })
}
/**
 * 账号信息
 * @param {*} data 
 * @returns 
 */
export function getUser(data:any) {
  return request({
      url: '/accountInfo/personalInfo',
      method: 'get',
      data
  })
}
/**
 * 账号 修改头像
 * @param {*} data 
 * @returns 
 */
export function setImg(data:any) {
  return request({
      url: '/accountInfo/updateHeadImg',
      method: 'get',
      params:data
  })
}

/**
 * 账号 修改 邮箱
 * @param {*} data 
 * @returns 
 */
export function setEmail(data:any) {
  return request({
      url: '/accountInfo/updateUserInfo',
      method: 'post',
    data  
  })
}

/**
 * 账号 修改 企业信息
 * @param {*} data 
 * @returns 
 */
export function setIndustry(data:any) {
  return request({
      url: '/enterprise/formSubmit',
      method: 'post',
    data  
  })
}


/**
 * 账号 修改 企业信息
 * @param {*} data 
 * @returns 
 */
export function checkVerificationCode(data:any) {
  return request({
      url: '/common/sms/log/checkVerificationCode',
      method: 'post',
    data  
  })
}

/**
 * 账号 修改 企业信息
 * @param {*} data 
 * @returns 
 */
export function forgetPassword(data:any) {
  return request({
      url: '/common/user/forgetPassword',
      method: 'post',
    data  
  })
}
/**
 * 获取用户登录信息
 * @param {*} data 
 * @returns 
 */
export function getueser(token:any) {
  return request({
      url: '/common/user/getLoginUserInfo',
      method: 'get',
      params:{},
      headers:{
        token: token
      }
  })
}

/**
 * 获取用户登录信息
 * @param {*} data 
 * @returns 
 */
export function jurisdiction(url:any,params:any,token:any) {
  return request({
      url,
      method: 'get',
      params,
      headers:{
        token: token
      }
  })
}
/**
 * 根据企业社会统一信用代码获取机构code列表
 * @param {*} data 
 * @returns 
 */
export function getRegisterChannelKeyByUniCodeAPI(params:any) {
  return request({
      url: '/enterpriseRegister/getRegisterChannelKeyByUniCode',
      method: 'get',
      params,
  })
}
/**
 * 我的消息列表
 * @param {*} data
 * @returns
 */
export function messageMyPageAPI(data:any) {
  return request({
      url: '/message/myPage',
      method: 'post',
      data
  })
}
/**
 * 删除消息
 * @param {*} data 
 * @returns 
 */
export function messageRemoveSingleAPI(params:any) {
  return request({
      url: '/message/removeSingle',
      method: 'get',
      params,
  })
}
/**
 * 租金到期提醒
 * @param {*} data 
 * @returns 
 */
export function leaseRentRemindAPI(params:any) {
  return request({
      url: '/sms/leaseRentRemind',
      method: 'get',
      params,
  })
}
/**
 * 租金逾期提醒
 * @param {*} data 
 * @returns 
 */
export function leaseRentOverdueAPI(params:any) {
  return request({
      url: '/sms/leaseRentOverdue',
      method: 'get',
      params,
  })
}
/**
 * 物业费到期提醒
 * @param {*} data 
 * @returns 
 */
export function leasePropertyRemindAPI(params:any) {
  return request({
      url: '/sms/leasePropertyRemind',
      method: 'get',
      params,
  })
}

/**
 * 物业费逾期提醒
 * @param {*} data 
 * @returns 
 */
export function leasePropertyOverdueAPI(params:any) {
  return request({
      url: '/sms/leasePropertyOverdue',
      method: 'get',
      params,
  })
}
/**
 * 未读消息数量
 * @param {*} data 
 * @returns 
 */
export function messageCountUnReadAPI(params:any) {
  return request({
      url: '/message/countUnRead',
      method: 'get',
      params,
  })
}
/**
 * 我的消息列表
 * @param {*} data
 * @returns
 */
export function approvalListAPI(data:any) {
  return request({
      url: '/approve/flow/record',
      method: 'post',
      data
  })
}
/**
 * 校验验证码是否正确
 * @param {*} params 
 * @returns 
 */
export function checkVerificationCodeAPI(data) {
  return request({
    url:'/common/sms/log/checkVerificationCode',
    method: 'post',
    data,baseURL: '/sso'
  })
}
/**
 * 发送验证码之前，校对手机号码是否存在数据库中
 * @param {*} params 
 * @returns 
 */
export function codeBeforeCheckNumberAPI(data) {
  return request({
    url:'/admin/user/codeBeforeCheckNumber',
    method: 'post',
    data,baseURL: '/sso'
  })
}
/**
 * 忘记密码
 * @param {*} params 
 * @returns 
 */
export function forgetPasswordAPI(data) {
  return request({
    //url:mockUrl+'/user/logout',
    url:'/common/user/forgetPassword',
    method: 'post',
    data,baseURL: '/sso'
  })
}
/**
 * 获取验证码
 * @param {*} params 
 * @returns 
 */
export function sendVerificationCodeAPI(data) {
  return request({
    //url:mockUrl+'/user/logout',
    url:'/common/sms/log/sendVerificationCode',
    method: 'post',
    data,
    baseURL: '/sso'
  })
}

export function queryResourceByType() {
  return request({
    url: '/admin/roleResource/queryResourceByType',
    method: 'GET',
    params: {},
    baseURL: '/sso',
  })
}
export function queryResourceByTypeBtn() {
  return request({
    url: '/admin/roleResource/queryResourceByType',
    method: 'GET',
    params: {
      type: 3
    },
    baseURL: '/sso',
  })
}

const apiDomain = '';
export const chartApi = {
  deptTree: apiDomain + '/admin/user/dept/deptTree', // 部门树
  deptSave: apiDomain + '/admin/user/dept/save', // 新增部门
  deptDelete: apiDomain + '/admin/user/dept/delete', // 删除部门
  deptEdit: apiDomain + '/admin/user/dept/edit', // 编辑部门
  listUserByDept: apiDomain + '/admin/user/listUserByDept', // 查询部门关联的用户
  deptUserList: apiDomain + '/admin/user/dept/deptUserList', // 部门列表
  pageUserByDept: apiDomain + '/admin/user/pageUserByDept', // 人员列表 
  userDetail: apiDomain + '/admin/user/userDetail', // 获取用户详情 
  batchUpdateDept: apiDomain + '/admin/user/batchUpdateDept', // 获取用户详情 
  listDeptByOrgCode: apiDomain + '/admin/user/dept/listDeptByOrgCode', // 获取用户详情 
  orgDeptTree: apiDomain + '/admin/user/dept/orgDeptTree', // 获取用户详情 
  listRolesByOrgCode: apiDomain + '/admin/role/listRolesByOrgCode', // 根据机构获取角色 
  queryByDeptId: apiDomain + '/admin/user/dept/queryByDeptId', // 根据部门id查询部门及子部门
}
// 查询部门关联的用户
export function listUserByDept(data) {
  return request({
    url: chartApi.listUserByDept,
    method: 'GET',
    params: { ...data },
    baseURL:'/sso',
  })
}
// 查询部门关联的用户
export function userDetailAPI(data) {
  return request({
    url: chartApi.userDetail,
    method: 'GET',
    params:data,
    baseURL:'/sso',
  })
}
// 删除部门
export function deptDelete(data) {
  return request({
    url: chartApi.deptDelete,
    method: 'GET',
    params: { ...data },
    baseURL:'/sso',
  })
}
// 部门树
export function deptTree(data) {
  return request({
    url: chartApi.deptTree,
    method: 'POST',
    data,
    baseURL:'/sso',
  })
}
// 新增部门
export function deptSave(data) {
  return request({
    url: chartApi.deptSave,
    method: 'POST',
    data,
    baseURL:'/sso',
  })
}

// 编辑部门
export function deptEdit(data) {
  return request({
    url: chartApi.deptEdit,
    method: 'POST',
    data,
    baseURL:'/sso',
  })
}


// 部门list
export function deptUserList(data) {
  return request({
    url: chartApi.deptUserList,
    method: 'POST',
    data,
    baseURL:'/sso',
  })
}
// 查询部门关联的用户
export function pageUserByDeptAPI(data) {
  return request({
    url: chartApi.pageUserByDept,
    method: 'POST',
    data
  })
}
// 批量移除部门
export function batchUpdateDeptAPI(data) {
  return request({
    url: chartApi.batchUpdateDept,
    method: 'POST',
    data,
    baseURL:'/sso',
  })
}
// 根据机构获取部门
export function listDeptByOrgCodeAPI(data) {
  return request({
    url: chartApi.listDeptByOrgCode,
    method: 'GET',
    params: { ...data },
    baseURL:'/sso',
  })
}
// 根据机构获取角色列表
export function listRolesByOrgCodeAPI(data) {
  return request({
    url: chartApi.listRolesByOrgCode,
    method: 'get',
    params: { ...data },
    baseURL:'/sso',
  })
}
// 根据机构code 获取树
export function orgDeptTreeAPI(data) {
  return request({
    url: chartApi.orgDeptTree,
    method: 'GET',
    params: { ...data },
    baseURL:'/sso',
  })
}
// 根据部门id查询部门及子部门
export function queryByDeptIdAPI(data) {
  return request({
    url: chartApi.queryByDeptId,
    method: 'GET',
    params: { ...data },
    baseURL:'/sso',
  })
}

/**
 * 账号设置-重置密码
 * @param {*} data 
 * @returns 
 */
export function resetPasswordAPI(params) {
  return request({
      url: '/admin/user/resetPassword',
      method: 'GET',
      params,
      baseURL:'/sso',
  })
}

export function getListBasicRoles() {
  return request({
    url: '/admin/role/listBasicRoles',
    method: 'GET',
    baseURL:'/sso',
  })
}

export  function getMenuTree() {
  return request({
    url: '/admin/roleResource/queryUserMenuResourceTree',
    method: 'GET',
    params:{
      routePreUrl: '/jh'
    },
    baseURL:'/sso',
  })
}
export function updateAIUserStatus(data) {
  return request({
    url: "/admin/user/updateAIUserStatus",
    method: "post",
    data,
    baseURL:'/sso',
  });
}