import request from '@/utils/system/request'

/**
 * 获取登陆人的角色列表
 * @param {*} data 
 * @returns 
 */
export function rolesAPI(data:any) {
  return request({
      url: '/admin/role/roles',
      method: 'POST',
      data,
      baseURL: '/sso',
  })
} 
/**
 * 角色管理列表
 * @param {*} data 
 * @returns 
 */
export function rolepageAPI(data:any) {
  return request({
      url: '/admin/role/page',
      method: 'POST',
      data,
      baseURL: '/sso',
  })
} 

/**
 * 添加角色
 * @param {*} data 
 * @returns 
 */
export function rolesAddAPI(data:any) {
  return request({
      url: '/admin/role/add',
      method: 'POST',
      data,
      baseURL: '/sso',
  })
} 
/**
 * 编辑角色
 * @param {*} data 
 * @returns 
 */
export function rolesEditAPI(data:any) {
  return request({
      url: '/admin/role/edit',
      method: 'POST',
      data,
      baseURL: '/sso',
  })
} 
/**
 * 删除角色
 * @param {*} data 
 * @returns 
 */
export function rolesDeleteAPI(params:any) {
  return request({
      url: '/admin/role/delete',
      method: 'GET',
      params,
      baseURL: '/sso',
  })
} 
/**
 * 获取用户管理的权限
 * @param {*} data 
 * @returns 
 */
export function listAclByUserIdAPI(params:any) {
  return request({
      url: '/admin/role/acl/roleAclTree',
      method: 'GET',
      params,
      baseURL: '/sso',
  })
}
/**
 * 新增/编辑角色获取权限树
 * @param {*} data 
 * @returns 
 */
export function getAclTreeAPI(params:any) {
  return request({
      url: '/admin/appResource/queryAppResourceTreeByChannelKey',
      method: 'GET',
      params,
      baseURL: '/sso',
  })
}
/**
 * 权限管理获取权限树
 * @param {*} data 
 * @returns 
 */
export function getAclTreeByRoleTypeAPI(data:any) {
  return request({
      url: '/admin/acl/getAclTree',
      method: 'POST',
      data,
      baseURL: '/sso',
  })
}

/**
 * 根据角色获取已有权限
 * @param {*} data 
 * @returns 
 */
export function roleAclTreeAPI(params:any) {
  return request({
    url: '/admin/roleResource/listRoleBindResourceIds',
      method: 'GET',
      params,
      baseURL: '/sso',
  })
}
/**
 * 添加成员
 * @param {*} data 
 * @returns 
 */
export function rolesaveAPI(data:any) {
  return request({
      url: '/admin/user/role/save',
      method: 'POST',
      data,
      baseURL: '/sso',
  })
}
/**
 * 查看成员
 * @param {*} data 
 * @returns 
 */
export function listByRoleIdAPI(data:any) {
  return request({
      url: '/admin/user/role/pageByRoleIds',
      method: 'POST',
      data,
      baseURL: '/sso',
  })
}
/**
 * 移除成员
 * @param {*} data 
 * @returns 
 */
export function deleteRoleUserAPI(params:any) {
  return request({
      url: '/admin/user/role/deleteRoleUser',
      method: 'GET',
      params,
      baseURL: '/sso',
  })
}
/**
 * 已绑定用户
 * @param {*} data 
 * @returns 
 */
export function listRoleUserAPI(params:any) {
  return request({
      url: '/admin/user/role/listRoleUser',
      method: 'GET',
      params,
      baseURL: '/sso',
  })
}
/**
 * 用户部门树
 * @param {*} data 
 * @returns 
 */
export function deptUserTreeAPI(params:any) {
  return request({
      url: '/admin/user/dept/deptUserTree',
      method: 'GET',
      params,
      baseURL: '/sso',
  })
}
/**
 * 设置角色权限类型
 * @param {*} data 
 * @returns 
 */
export function setRoleTypeAclAPI(data:any) {
  return request({
      url: '/admin/role/acl/setRoleTypeAcl',
      method: 'POST',
      data,
      baseURL: '/sso',
  })
}
/**
 * 角色权限类型回显
 * @param {*} data 
 * @returns 
 */
export function listRoleTypeAclAPI(params:any) {
  return request({
      url: '/admin/role/acl/listRoleTypeAcl',
      method: 'GET',
      params,
      baseURL: '/sso',
  })
}
/**
 * 拖拽
 * @param {*} data 
 * @returns 
 */
export function moveAclAPI(data:any) {
  return request({
      url: '/admin/acl/moveAcl',
      method: 'POST',
      data,
      baseURL: '/sso',
  })
}

/**
 * 联想词
 * @param {*} data 
 * @returns 
 */
export function autoSearchAPI(data:any) {
    return request({
        url:"/admin/institution/autoSearch",
        method:'POST',
        data,
        baseURL: '/sso',
    })
}