import request from '@/utils/system/request'

// 获取指定待办租约的跟进记录列表
export function getListAPI(data: object,url: string) {
  // console.log(url);
  return request({
    //url: baseUrl.replace("/api", ""),
    url: `${url}admin/orgIndustryChainRelation/list`,
    method: 'post',
    data
  })
}
/**
 * 获取绑定机构列表
 * @param {*} data 
 * @returns 
 */
export function ownList(url: string) {
  return request({
      url: `/sso/admin/institution/ownList`,
      method: 'get',
      params:''
  })
}

