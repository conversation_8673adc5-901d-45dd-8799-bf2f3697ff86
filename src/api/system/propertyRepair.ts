import request from '@/utils/system/request'

// 获取指定待办租约的跟进记录列表
export function getListAPI(data: object) {
  return request({
    url: '/billund/getList',
    method: 'post',
    data
  })
}
// 添加待办租约跟进记录
export function addFollowRecord(data: object) {
  return request({
    url: '/pendingLease/addFollowRecord',
    method: 'post',
    data
  })
}
//物业报修记录详情
export function propertyRepairAPI(params: [String]) {
  return request({
    url: '/propertyRepair/detail',
    method: 'get',
    params,
  })
}
// 获取企业list
export function getenterpriseListAPI(data: object) {
  return request({
    url: '/enterprise/getList',
    method: 'post',
    data
  })
}
// 获取非挂载类型企业列表
export function listNoMountEnterpriseAPI(params: [String]) {
  return request({
    url: '/enterprise/listNoMountEnterprise',
    method: 'get',
    params
  })
}
// 新增租约
export function addleaseAPI(data: object) {
  return request({
    url: '/lease/add',
    method: 'post',
    data
  })
}
// 新增合作咨询记录
export function conaddAPI(data: object) {
  return request({
    url: '/coo/con/add',
    method: 'post',
    data
  })
}
// 租约详情
export function getOneAPI(data: object) {
  return request({
    url: '/billund/getOne',
    method: 'post',
    data
  })
}
// 续约租约详情
export function contractgetOneAPI(params: [String]) {
  return request({
    url: '/lease/renewalDetail',
    method: 'get',
    params
  })
}
// 租约详情
export function detailAPI(params: [String]) {
  return request({
    url: '/lease/detail',
    method: 'get',
    params
  })
}
// 更新租约
export function updateAPI(data: object) {
  return request({
    url: '/lease/update',
    method: 'post',
    data
  })
}
// 获取合作咨询跟进记录列表
export function pageListAPI(data: object) {
  return request({
    url: '/coo/pro/page',
    method: 'post',
    data
  })
}

// 添加合作咨询跟进记录
export function addAPI(data: object) {
  return request({
    url: '/coo/pro/add',
    method: 'post',
    data
  })
}
// 合作资讯查看
export function getByIdAPI(data: object) {
  return request({
    url: '/coo/con/getById',
    method: 'post',
    data
  })
}
//招商管理跟进记录列表
export function followListAPI(params: [String]) {
  return request({
    url: '/investment/management/followList',
    method: 'get',
    params,
  })
}
//招商管理跟进记录列表
export function followAPI(data: object) {
  return request({
    url: '/investment/management/follow',
    method: 'post',
    data,
  })
}
//消息详情
export function queryByIdAPI(params: [String]) {
  return request({
    url: '/information/queryById',
    method: 'get',
    params,
  })
}
//变更信息
export function formSubmitAPI(data: object) {
  return request({
    url: '/billund/formSubmit',
    method: 'post',
    data,
  })
}
//审核
export function auditAPI(data: object) {
  return request({
    url: '/information/audit',
    method: 'post',
    data,
  })
}
//风采列表
export function mienList(data: object) {
  return request({
    url: '/billund/getList',
    method: 'post',
    data,
  })
}
//变更展示视频
export function setLargeScreenVideoAPI(params: [String]) {
  return request({
    url: '/parkStyle/setLargeScreenVideo',
    method: 'get',
    params,
  })
}
//编辑园区风采
export function saveOrUpdateAPI(data: object) {
  return request({
    url: '/parkStyle/saveOrUpdate',
    method: 'post',
    data,
  })
}
//物业费租约
export function insertOrUpdateAPI(data: object) {
  return request({
    url: '/lease/property/agree/insertOrUpdate',
    method: 'post',
    data,
  })
}

