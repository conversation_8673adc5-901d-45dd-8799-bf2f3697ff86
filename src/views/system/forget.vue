<template>
  <div class="forgetTab">
    <div class="title" v-if="showSecond !== '3'">忘记密码</div>
    <div class="phone">
      {{ ["", "验证手机号", "请设置新密码"][showSecond] }}
    </div>
    <div v-if="showSecond === '1'">
      <el-form
        class="form"
        :model="form"
        label-width="0px"
        size="default"
        ref="formRef"
      >
        <el-form-item
          label=""
          prop="phone"
          :rules="[
            { required: true, message: '请输入联系人手机号码' },
            {
              pattern: /^1[3456789]\d{9}$/,
              message: '请输入正确手机号',
              trigger: 'blur',
            },
          ]"
        >
          <el-input
            v-model="form.phone"
            placeholder="请输入联系人手机号"
            type="text"
            maxlength="50"
            class="phones"
            size="default"
          >
            <template #append>
              <div @click="sentCode">
                <!-- <el-divider direction="vertical" /> -->
                <span class="cursor-pointer">
                  {{ timer === 0 ? "获取验证码" : `${timer}s后重新获取` }}</span
                >
              </div>

              <!-- | <span class="getCode">获取验证码</span> -->
            </template></el-input
          >
        </el-form-item>
        <el-form-item
          label=""
          prop="verification_code"
          :rules="[
            { required: true, message: '请输入验证码' },
            {
              pattern: /^[0-9]*$/,
              message: '请输入正确验证码',
              trigger: 'blur',
            },
            {
              min: 6,
              max: 6,
              message: '验证码长度为6位',
              trigger: 'blur',
            },
            //   { type: 'number', message: '验证码为数字' },
          ]"
        >
          <el-input
            v-model="form.verification_code"
            placeholder="请输入验证码"
            size="default"
            class="phones"
          />
        </el-form-item>
        <div class="submitBtn">
          <div class="submitBtnCenter">
            <el-button
              size="default"
              type="primary"
              :loading="form.loading"
              @click="submit(formRef)"
              class="onOk"
            >
              下一步
            </el-button>
          </div>
        </div>
      </el-form>
    </div>
    <div v-if="showSecond === '2'">
      <el-form
        class="form"
        :model="formSecond"
        label-width="0px"
        ref="formSecondRef"
      >
        <el-form-item
          label=""
          prop="newPassword"
          :rules="[
            { required: true, message: '请输入新密码' },
            { min: 8, message: '密码需要八位以上', trigger: 'blur' },
            { validator: validatePass, trigger: 'blur' },
          ]"
        >
          <el-input
            v-model="formSecond.newPassword"
            placeholder="请输入新密码"
            type="text"
            maxlength="50"
            class="phones"
          >
          </el-input>
        </el-form-item>
        <el-form-item
          label=""
          prop="checkPassword"
          :rules="[
            { required: true, message: '请再次输入新密码' },
            { validator: validatePass2, trigger: 'blur' },
          ]"
        >
          <el-input
            v-model="formSecond.checkPassword"
            placeholder="请再次输入新密码"
            type="text"
            maxlength="50"
            class="phones"
          >
          </el-input>
        </el-form-item>
        <div class="submitBtn">
          <div class="submitBtnCenter">
            <el-button
              size="default"
              type="primary"
              :loading="form.loading"
              @click="next(formSecondRef)"
              class="onOk"
            >
              下一步
            </el-button>
          </div>
        </div>
      </el-form>
    </div>
    <div class="successIcon" v-if="showSecond === '3'">
      <div class="success"></div>
      <div class="successText">密码设置成功！</div>

      <!-- <div class=""> -->
      <el-button
        size="default"
        type="primary"
        :loading="form.loading"
        @click="login()"
        class="onOk"
      >
        去登录
      </el-button>
      <!-- </div> -->
    </div>
  </div>
</template>
<script setup>
import {
  defineComponent,
  ref,
  reactive,
  defineEmits,
  onMounted,
  computed,
  watch,
} from "vue";
import { useIntervalFn } from "@vueuse/core";
import { getCode, checkVerificationCode, forgetPassword } from "@/api/user";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
import { publicKey, setPasswordAPI } from "@/api/user";
import { JSEncrypt } from "jsencrypt";
import { debounce } from "lodash";
const store = useStore();
const formRef = ref();
const formSecondRef = ref();
const forgetShow = computed(() => {
  // localStorage.getItem('isLogin')==='true'
  return store.state.app.isForgetShow;
});
const timer = ref(0);
const showSecond = ref("1");
const result = ref();
const formSecond = reactive({
  newPassword: "",
  checkPassword: "",
  loading: false,
});

const form = reactive({
  phone: "",
  verification_code: "",
  loading: false,
});
const validatePass = (rule, value, callback) => {
  var passwordreg = /(?=.*\d)(?=.*[a-zA-Z]).{8,20}/;
  if (value === "") {
    callback(new Error("请输入密码"));
  } else if (!passwordreg.test(value)) {
    callback(new Error("密码8-20位,包含字母数字"));
  } else {
    // if (formSecond.newPassword !== "") {
    //   formRef.value.validateField("newPassword");
    // }
    callback();
  }
};
const validatePass2 = (rule, value, callback) => {
  if (value === "") {
    callback(new Error("请再次输入密码"));
  } else if (value !== formSecond.newPassword) {
    callback(new Error("两次输入密码不一致!"));
  } else {
    callback();
  }
};
watch(
  () => forgetShow,
  (newVal) => {
    showSecond.value = "1";
  },
  { deep: true }
);
onMounted(() => {
  encryption();
});
const encryption = async () => {
  const res = await publicKey();
  result.value = res.result;
};
// 发送验证码倒计时
// useIntervalFn(定时的回调，回调的时间间隔，控制回调的调用方式)
// 参数三 {immediate: true, immediateCallback: false}
// 3-1) immediate 首次运行useIntervalFn函数时，是否立刻开启定时任务（默认值true表示默认开启）
// 3-2）immediateCallback 执行useIntervalFn函数立刻执行回调（在延时时间的前或者后调用），默认值是false，如果修改为true，表示为不延时，立刻启动定时任务（不要使用pause方法）
// pause暂停；resume启动
const { pause, resume } = useIntervalFn(
  () => {
    if (timer.value <= 0) {
      // 停止定时任务
      pause();
    } else {
      // 单次定时任务执行的回调
      timer.value--;
    }
  },
  1000,
  {
    // 默认不开启定时任务
    immediate: false,
  }
);
const sentCode = () => {
  const mobile = form.phone;

  formRef.value.validateField("phone", (valid) => {
    if (valid) {
      // 发送验证码
      // 开启倒计时效果
      if (timer.value === 0) {
        timer.value = 60;
        resume();
      } else {
        return;
      }
      getCode({ type: 1, mobile: mobile }).then((response) => {
        if (response.code === "SUCCESS") {
          ElMessage({
            message: "发送成功",
            type: "success",
          });
        } else {
          ElMessage({
            message: response.data.msg,
            type: "error",
          });
        }
      });
    }
  });
};
const submit = (formEl) => {
  if (!formEl) return;
  formEl.validate((valid) => {
    if (valid) {
      let { loading, ...prps } = form;

      checkVerificationCode({
        verificationCode: form.verification_code,
        mobile: form.phone,
      }).then((res) => {
        ElMessage.success({
          message: "验证成功",
          type: "success",
        });
        showSecond.value = "2";
        // formRef.value.resetFields();
        // formSecondRef.value.resetFields();
      });
    }
  });
};
const next = (formEl) => {
  if (!formEl) return;

  formEl.validate((valid) => {
    // console.log(valid)
    if (valid) {
      let { loading } = formSecond;
      let encrypt = new JSEncrypt();
      encrypt.setPublicKey(result.value);
      forgetPassword({
        code: form.verification_code,
        phoneNumber: form.phone,
        newPassword: encrypt.encrypt(formSecond.newPassword),
        checkPassword: encrypt.encrypt(formSecond.checkPassword),
      }).then((res) => {
        ElMessage.success({
          message: "验证成功",
          type: "success",
        });
        showSecond.value = "3";
      });
    }
  });
};
const login = () => {
  store.commit("app/stateChange", {
    name: "isForgetShow",
    value: false,
  });

  setTimeout(() => {
    store.commit("app/stateChange", {
      name: "isLogin",
      value: true,
    });
  }, 500);
};
const cancel = () => {
  if (showSecond.value === "1") {
    formRef.value.resetFields();
  } else if (showSecond.value === "2") {
    formSecondRef.value.resetFields();
  }
};
defineExpose({
  cancel,
});
</script>
<style lang="scss">
.phones {
  .el-input__wrapper {
    height: 45px;
    border-right: 0px;

    border-radius: 4px 0 0 4px !important;
  }
}

.forget .el-dialog__body {
  padding: 20px;

  // padding-bottom: 20px;
  .el-form-item {
    margin-bottom: 30px;
  }
}
</style>
<style scoped lang="scss">
.forgetTab {
  padding: 0px 30px;
}

.title {
  text-align: center;
  width: 100%;
  font-size: 26px;
  font-weight: 500;
  color: #006afe;
  padding-bottom: 20px;
}

.phone {
  padding-bottom: 30px;

  text-align: center;

  width: 100%;

  font-size: 14px;
  font-weight: 400;
  color: #999999;
}

.getCode {
  color: white;
  padding-left: 10px;

  &:hover {
    color: #2f89cd;
  }
}

.success {
  width: 70px;
  height: 70px;
  margin-bottom: 30px;
  background: center / contain no-repeat url("@/assets/login/forget.png");
}

.onOk {
  width: 100%;
  height: 40px;
  margin: 20px 0 30px;
}

.successIcon {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.successText {
  font-size: 14px;
  font-weight: 500;
  padding: 10px 0 50px;
  width: 100%;
  text-align: center;
}

.cursor-pointer {
  cursor: pointer;
}
</style>
