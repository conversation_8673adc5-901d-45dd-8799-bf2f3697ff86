<template>
  <div style="width: 100%; height: 100%">
    <div class="login-container">
      <div class="form">
        <div class="login-form">
          <div class="title-container">
            <div class="title">重置密码</div>
            <div class="t2">
              {{ greeting }}
            </div>
          </div>
          <el-form
            ref="ruleForm"
            style="margin-top: 31px"
            :model="ruleForm"
            :rules="rules"
            label-position="top"
            :hide-required-asterisk="true"
          >
            <el-form-item
              v-if="firststep"
              style="margin-top: 17px"
              prop="cellPhoneNumber"
            >
              <el-input
                ref="username"
                v-model="ruleForm.cellPhoneNumber"
                placeholder="请输入手机号"
                name="username"
                type="text"
                maxlength="11"
                tabindex="1"
                style="z-index: 100"
                autocomplete="on"
              />
              <el-button
                size="default"
                v-if="verify"
                v-loading="codeLoading"
                type="primary"
                class="btnyz"
                :disabled="!islegal"
                @click.stop="verificationCode"
              >
                获取验证码
              </el-button>
              <el-button
                size="default"
                v-else
                type="primary"
                class="btnyz"
                disabled
              >
                {{ i }}秒后重试
              </el-button>
            </el-form-item>
            <el-form-item v-if="firststep" prop="verificationCode">
              <el-input
                v-model="ruleForm.verificationCode"
                type="text"
                placeholder="请输入验证码"
              />
            </el-form-item>
            <el-form-item v-if="!firststep" prop="pass">
              <el-input
                v-model="ruleForm.pass"
                show-password
                type="password"
                autocomplete="off"
                placeholder="请输新入新密码"
                @keyup="trimLR1"
              />
            </el-form-item>
            <el-form-item
              v-if="!firststep"
              style="margin-top: 17px"
              label=""
              prop="checkPass"
            >
              <el-input
                ref="password"
                v-model.trim="ruleForm.checkPass"
                show-password
                type="password"
                placeholder="请再次输入新密码"
              />
            </el-form-item>
            <el-button
              size="default"
              v-if="firststep"
              :loading="affirmLoading"
              type="primary"
              class="login-btn"
              @click.prevent="codeverify"
            >
              下 一 步
            </el-button>
            <el-button
              size="default"
              v-else
              :loading="affirmLoading"
              type="primary"
              class="login-btn"
              @click.prevent="confirm"
            >
              确 认
            </el-button>
          </el-form>
          <div class="agreement">
            <span>重置密码</span> <el-divider direction="vertical" />
            <span class="goagreement" @click="$router.back()">返回登录</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  encryptionAPI,
  sendVerificationCodeAPI,
  forgetPasswordAPI,
  codeBeforeCheckNumberAPI,
  checkVerificationCodeAPI,
} from "@/api/user";
import { JSEncrypt } from "jsencrypt";
export default {
  data() {
    var validatePass = (rule, value, callback) => {
      var passwordreg = /(?=.*\d)(?=.*[a-zA-Z]).{8,20}/;
      if (value === "") {
        callback(new Error("请输入密码"));
      } else if (!passwordreg.test(value)) {
        callback(new Error("密码8-20位,包含字母数字"));
      } else {
        if (this.ruleForm.checkPass !== "") {
          this.$refs.ruleForm.validateField("checkPass");
        }
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.ruleForm.pass) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      firststep: true,
      ruleForm: {
        cellPhoneNumber: "",
        verificationCode: "",
        pass: "",
        checkPass: "",
      },
      loading: false,
      islegal: false,
      resultstandby:
        "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC27tAIp7i+qhyunO+O5xvk5ilVR9npOkrfeJB69OtafL1i3ZjXNct0gxZF09WRCzWCOkLzG0rcKzWaFieWRscci5pAHqSzfld5Qob6e3BVgI+BtJcge4NOGtMN8ASEMXBUdzhuo1ud0VQUqVjDcBMqQMKtsyIpBh+onZH8jqXz/wIDAQAB",
      rules: {
        cellPhoneNumber: [
          { required: true, message: "手机号不能为空", trigger: "blur" },
          {
            pattern: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/,
            message: "请输入合法手机号码",
            trigger: "blur",
          },
        ],
        verificationCode: [
          { required: true, message: "验证码不能为空", trigger: "blur" },
        ],
        pass: [
          { required: true, message: "密码不能为空", trigger: "blur" },
          { min: 8, message: "密码需要八位以上", trigger: "blur" },
          { validator: validatePass, trigger: "blur" },
        ],
        checkPass: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          { validator: validatePass2, trigger: "blur" },
        ],
      },
      i: 60,
      verify: true,
      affirmLoading: false,
      codeLoading: false,
      greeting: "验证手机号",
    };
  },
  watch: {
    "ruleForm.cellPhoneNumber"(newValue) {
      const regExp = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
      this.islegal = regExp.test(newValue);
    },
  },
  methods: {
    //校验输入的验证码是否正确
    async codeverify() {
      if (this.affirmLoading == true) {
        return;
      }
      await this.$refs.ruleForm.validate();
      try {
        this.affirmLoading = true;
        const res = await checkVerificationCodeAPI({
          verificationCode: this.ruleForm.verificationCode,
          mobile: this.ruleForm.cellPhoneNumber,
        });
        if (res.result == true) {
          this.firststep = false;
          this.greeting = "设置新密码";
          this.$message.success("验证码验证通过，请设置新密码");
        } else {
          this.$message.error("验证码不正确");
        }
      } finally {
        this.affirmLoading = false;
      }
    },
    //添加
    async confirm() {
      if (this.affirmLoading == true) {
        return;
      }
      try {
        this.affirmLoading = true;
        await this.$refs.ruleForm.validate();
        await this.encryption();
        let encrypt = new JSEncrypt();
        encrypt.setPublicKey(this.resultstandby);
        const password = encrypt.encrypt(this.ruleForm.pass);
        const checkPassword = encrypt.encrypt(this.ruleForm.checkPass);
        let data = {
          phoneNumber: this.ruleForm.cellPhoneNumber,
          code: this.ruleForm.verificationCode,
          newPassword: password,
          checkPassword: checkPassword,
        };
        const res = await forgetPasswordAPI(data);
        this.$message.success("操作成功！请重新登陆");
        this.$router.push("/login");
      } catch (error) {
      } finally {
        this.affirmLoading = false;
      }
    },
    //ras加密
    async encryption() {
      const res = await encryptionAPI();
      if (res.result) {
        this.resultstandby = res.result;
      }
    },
    //发送验证码
    async verificationCode() {
      if (this.codeLoading == true) {
        return 
      }
      try {
        this.codeLoading = true;
        await codeBeforeCheckNumberAPI({
          phoneNumber: this.ruleForm.cellPhoneNumber,
        });
        const res = await sendVerificationCodeAPI({
          mobile: this.ruleForm.cellPhoneNumber,
          type: 1,
        });
        this.$message.success("验证码发送成功！");
        //console.log(res);
        this.verify = false;
        var timer = setInterval(() => {
          this.i--;
          if (this.i < 1) {
            this.verify = true;
            this.i = 60;
            clearInterval(timer);
            timer = null;
          }
        }, 1000);
      } catch (error) {
        //console.log(error);
      } finally {
        this.codeLoading = false;
      }
    },
  },
};
</script>

<style lang="scss">
$bg: #283443;
$light_gray: #fff;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    width: 100%;
    //border: 1px solid rgba(255, 255, 255, 0.1);
    // background: rgba(0, 0, 0, 0.1);
    //height: 66px;
    height: 54px;
    display: flex;
    align-items: center;
    background: #ffffff;
    // box-shadow: 0px 10spx 40px 0px rgba(174, 174, 174, 0.2);
    border-radius: 10px 10px 10px 10px;
    opacity: 1;
    // border: 1px solid #eaeaea;
    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      height: 54px;
      color: #333333;
      height: 47px;
      caret-color: #333333;

      &:-webkit-autofill {
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.agreement {
  display: flex;
  margin-top: 20px;
  height: 30px;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #333333;
  line-height: 16px;
  .goagreement {
    cursor: pointer;
    font-size: 15px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #1a5bff;
    line-height: 16px;
  }
}
::v-deep {
  .el-loading-mask {
    display: flex;
    align-items: center;
    justify-content: center;
    top: 27px;
  }
  .el-loading-spinner .circular {
    color: #fff !important;
    width: 12px !important;
    height: 12px !important;
  }
}
.btnyz {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  color: #fff;
  font-size: 12px;
  bottom: 14px;
  right: 10px;
  z-index: 999;
  width: 82px;
  height: 26px;
  background: #437bff;
  border: 1px solid #437bff;
  border-radius: 15px 15px 15px 15px;
  opacity: 1;
  cursor: pointer;
}
.btnyz:hover {
  background: #437bff;
  border: 1px solid #437bff;
  color: #fff;
}
.btnyz:active {
  background: #437bff;
  border: 1px solid #437bff;
  color: #fff;
}
.btnyz:focus {
  background: #437bff;
  border: 1px solid #437bff;
  color: #fff;
}
.kj {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
  font-size: 15px;
  font-family: Plus Jakarta Sans-Regular, Plus Jakarta Sans;
  font-weight: 400;
  color: #1a5bff;
}
.svgicon {
  margin-top: 20px;
}
.login-btn {
  margin-left: 0px !important;
  width: 100%;
  height: 54px;
  margin-top: 33px;
  background: linear-gradient(135deg, #8daeff 0%, #1a5bff 100%);
  box-shadow: 0px 10px 40px 0px rgba(174, 174, 174, 0.2);
  border-radius: 10px 10px 10px 10px;
  opacity: 1;
  border: 1px solid #eaeaea;
  font-size: 16px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
}
.login-btn:hover {
  margin-left: 0px !important;
  width: 100%;
  height: 54px;
  margin-top: 33px;
  background: linear-gradient(135deg, #8daeff 0%, #1a5bff 100%);
  box-shadow: 0px 10px 40px 0px rgba(174, 174, 174, 0.2);
  border-radius: 10px 10px 10px 10px;
  opacity: 1;
  border: 1px solid #eaeaea;
  font-size: 16px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
}
.login-btn:active {
  margin-left: 0px !important;
  width: 100%;
  height: 54px;
  margin-top: 33px;
  background: linear-gradient(135deg, #8daeff 0%, #1a5bff 100%);
  box-shadow: 0px 10px 40px 0px rgba(174, 174, 174, 0.2);
  border-radius: 10px 10px 10px 10px;
  opacity: 1;
  border: 1px solid #eaeaea;
  font-size: 16px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
}
.login-btn:focus {
  margin-left: 0px !important;
  width: 100%;
  height: 54px;
  margin-top: 33px;
  background: linear-gradient(135deg, #8daeff 0%, #1a5bff 100%);
  box-shadow: 0px 10px 40px 0px rgba(174, 174, 174, 0.2);
  border-radius: 10px 10px 10px 10px;
  opacity: 1;
  border: 1px solid #eaeaea;
  font-size: 16px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
}
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;

.login-container {
  display: flex;
  min-width: 1024px;
  min-height: 700px;
  margin: 0;
  padding: 0;
  font-family: sans-serif;
  background: url("https://static.idicc.cn/cdn/SSO/zhifuBgc.png") no-repeat
    center 0px;
  background-size: cover;
  background-position: center 0;
  background-repeat: no-repeat;
  background-attachment: fixed;
  -webkit-background-size: cover;
  -o-background-size: cover;
  -moz-background-size: cover;
  -ms-background-size: cover;
  background-repeat: no-repeat;
  width: 100%;
  overflow: hidden;
  height: 100vh;
  .form {
    position: absolute;
    top: 48%;
    left: 80%;
    transform: translate(-50%, -50%);
  }
  .login-form {
    border-radius: 20px 20px 20px 20px;
    //border: 1px solid rgba(255,255,255,0.15);
    //background: rgba(0,23,58,0.6);
    position: relative;
    // width: 538px;
    //height: 605px;
    width: 440px;
    height: 468px;
    //height: 548px;
    background: #ffffff;
    box-shadow: 0px 4px 53px 0px rgba(37, 81, 147, 0.25);
    border-radius: 16px 16px 16px 16px;
    opacity: 1;
    padding: 40px 32px;
    margin: 0 auto;
    p {
      text-align: center;
      margin-top: 80px;
      font-size: 15px;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.25);
    }
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;
    .title {
      font-size: 30px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 600;
      color: #000000;
      margin: 5px auto 10px auto;
      //text-align: center;
      //font-weight: bold;
    }
    .t2 {
      font-size: 20px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #000000;
      font-weight: 400;
      color: #000000;
    }
    /*  img{
      margin-left: 165px;
      width: 100px;
      height: 100px;
    } */
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 2px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

  @media only screen and (max-width: 470px) {
    .thirdparty-button {
      display: none;
    }
  }
}
.text {
  float: left;
  cursor: pointer;
  font-size: 14px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #333333;
}
footer {
  margin-top: 25px;
  display: flex;
  width: 100%;
  bottom: 63px;
  font-size: 12px;
  color: #ffffff66;
  span {
    max-width: 1200px;
    margin: auto;
    display: block;
    width: 100%;
    text-align: center;
  }
  img {
    width: 20px;
    display: inline-block;
    height: 20px;
    margin-right: 12px;
  }
  .imgs {
    height: 20px;
    margin-right: 3px;
  }
}
</style>
