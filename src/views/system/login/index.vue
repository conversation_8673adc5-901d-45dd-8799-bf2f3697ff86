<template>
  <div style="width: 100%; height: 100%">
    <div v-show="show" class="login-container">
      <div class="form">
        <div class="login-form">
          <div class="title-container">
            <div class="title">登录</div>
            <div class="t2">
              {{ greeting }}
            </div>
          </div>
          <el-form
            ref="loginForm"
            style="margin-top: 31px"
            :model="loginForm"
            :rules="loginRules"
            autocomplete="on"
            label-position="top"
            :hide-required-asterisk="true"
          >
            <el-form-item style="margin-top: 17px" label="" prop="username">
              <el-input
                ref="username"
                v-model="loginForm.username"
                placeholder="请输入账号/手机号"
                name="username"
                type="text"
                tabindex="1"
                autocomplete="on"
              />
            </el-form-item>
            <!-- <el-tooltip
              v-model="capsTooltip"
              content="Caps lock is On"
              placement="right"
              manual
            > -->
            <el-form-item style="margin-top: 17px" prop="password" label="">
              <el-input
                :key="passwordType"
                ref="password"
                v-model="loginForm.password"
                :type="passwordType"
                placeholder="请输入密码"
                name="password"
                tabindex="2"
                autocomplete="on"
                @keyup="checkCapslock"
                @blur="capsTooltip = false"
                @keyup.enter="handleLogin"
              />
              <span class="show-pwd" @click="showPwd">
                <el-icon class="svgicon" v-if="passwordType === 'password'"
                  ><View
                /></el-icon>
                <el-icon class="svgicon" v-else><Hide /></el-icon>
              </span>
            </el-form-item>
            <!-- </el-tooltip> -->
            <div class="text" @click="$router.push('/retrievePassword')">
              忘记密码?
            </div>
            <el-button
              :loading="loading"
              type="primary"
              class="login-btn"
              @click.prevent="handleLogin"
            >
              登 录
            </el-button>
            <!--  <p>为保证您的正常使用，若手机号变更，请联系管理员及时调整</p> -->
          </el-form>
          <!--  <span class="kj">快捷登录</span> -->
          <div class="agreement">
            <el-checkbox v-model="checked" style="margin-right: 5px" />
            阅读并同意<span class="goagreement" @click="shows(false)"
              >《用户使用协议》</span
            >
          </div>
        </div>
      </div>
    </div>
    <changePassDig
      v-if="changePass"
      :changePass="changePass"
      :passToken="passToken"
      @changesu="changesu"
    />
    <div v-show="!show" style="width: 100%; height: 100%">
      <agreement @show="shows" />
    </div>
  </div>
</template>

<script>
import { systemTitle, systemSubTitle } from "@/config";
import {
  encryptionAPI,
  loginApi,
  publicKey,
  orgApi,
  queryResourceByTypeBtn,
  codeBeforeCheckNumberAPI,
} from "@/api/user";
import { getToken, removeToken } from "@/utils/auth"; // get token from cookie
import { JSEncrypt } from "jsencrypt";
import agreement from "./agreement.vue";
import changePassDig from "./changePass.vue";
export default {
  name: "UserLogin",
  components: {
    agreement,
    changePassDig,
  },
  data() {
    const validateUsername = (rule, value, callback) => {
      var reg_tel =
        ///^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/; //11位手机号码正则
        /^1[0-9]{10}$/;
      if (!reg_tel.test(value)) {
        callback(new Error("请输入手机号"));
      } else {
        callback();
      }
    };
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请输入密码"));
      } else {
        callback();
      }
    };
    return {
      result: "",
      resultstandby:
        "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC27tAIp7i+qhyunO+O5xvk5ilVR9npOkrfeJB69OtafL1i3ZjXNct0gxZF09WRCzWCOkLzG0rcKzWaFieWRscci5pAHqSzfld5Qob6e3BVgI+BtJcge4NOGtMN8ASEMXBUdzhuo1ud0VQUqVjDcBMqQMKtsyIpBh+onZH8jqXz/wIDAQAB",
      // 控制滑块验证码
      SliderVerificationCode: false,
      checked: false,
      // 滑块验证是否成功
      verification: true,
      show: true,
      loginForm: {
        //username: '13381801205',
        //password: '123'
        //username: '13958032060',//测试
        username: "", //开发
        password: "",
      },
      loginRules: {
        username: [
          {
            required: true,
            trigger: "blur",
            validator: validateUsername,
            message: "请输入正确的手机号",
          },
        ],
        password: [
          { required: true, trigger: "blur", validator: validatePassword },
        ],
      },
      passwordType: "password",
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      changePass: false,
      passToken: "",
      otherQuery: {},
      source: "",
      greeting: "欢迎登录园区智服管理平台",
    };
  },
  watch: {
    $route: {
      handler: function (route) {
        const query = route.query;
        if (query) {
          this.redirect = query.redirect;
          this.otherQuery = this.getOtherQuery(query);
        }
      },
      immediate: true,
    },
  },
  created() {
    document.title = `${systemTitle}`;
    this.checked = sessionStorage.getItem("checked") === "0";
    localStorage.clear();
    console.log('已清空');
  },
  mounted() {
    localStorage.removeItem("JH_TOKEN");
    removeToken();
    // localStorage.setItem("userInfo", "{}");
    if (this.loginForm.username === "") {
      this.$refs.username.focus();
    } else if (this.loginForm.password === "") {
      this.$refs.password.focus();
    }
  },
  destroyed() {},
  methods: {
    getPublicKey() {
      publicKey().then((res) => {
        key.value = res.result;
      });
    },
    changesu() {
      this.changePass = false;
      this.loginForm.password = "";
    },
    shows(i) {
      this.show = i;
    },
    checkCapslock(e) {
      const { key } = e;
      this.capsTooltip = key && key.length === 1 && key >= "A" && key <= "Z";
    },
    showPwd() {
      if (this.passwordType === "password") {
        this.passwordType = "";
      } else {
        this.passwordType = "password";
      }
      this.$nextTick(() => {
        this.$refs.password.focus();
      });
    },
    async encryption() {
      const res = await encryptionAPI();
      if (res.result) {
        this.resultstandby = res.result;
      }
    },
    handleLogin() {
      this.$refs.loginForm.validate(async (valid) => {
        if (valid) {
          if (!this.checked) {
            return this.$message.warning("请先勾选用户协议");
          }
          this.loading = true;
          await this.encryption();
          let encrypt = new JSEncrypt();
          encrypt.setPublicKey(this.resultstandby);
          const password = encrypt.encrypt(this.loginForm.password);
          const code = await codeBeforeCheckNumberAPI({
            phoneNumber: this.loginForm.username,
          });
          if (code.result == true) {
            loginApi({
              username: this.loginForm.username,
              password,
            })
              .then((res) => {
                this.loading = false;
                // 是否重置密码
                if (res.result?.isChangedPassword == "0") {
                  sessionStorage.setItem("checked", "0");
                  this.passToken = res.result.token;
                  this.changePass = true;
                } else {
                  sessionStorage.setItem("JH_TOKEN", res?.result.token);
                  sessionStorage.setItem("checked", "0");
                  const hasToken = getToken();
                  localStorage.setItem(
                    "JH_TOKEN",
                    JSON.stringify({
                      expire: new Date().getTime(),
                      value: hasToken,
                    })
                  );
                  let userInfo = res.result;
                  orgApi().then((org) => {
                    let mine = org.result.selected;
                    userInfo.orgCode = mine?.orgCode;
                    userInfo.orgName = mine?.orgName;
                    if (mine?.basicRoles?.length) {
                      if (mine?.basicRoles?.[0]?.roleName.indexOf("园区端") > -1) {
                        localStorage.setItem("userState", "admin");
                      } else if (
                        mine?.basicRoles?.[0]?.roleName.indexOf("企业端") > -1
                      ) {
                        localStorage.setItem("userState", "user");
                      }
                    }
                    localStorage.setItem("userInfo", JSON.stringify(userInfo));
                    sessionStorage.setItem(
                      "userInfo",
                      JSON.stringify(userInfo)
                    );
                    this.getPermission();
                  });
                }
              })
              .catch(() => {
                this.loading = false;
              })
              .finally(() => {
                this.loading = false;
              });
          } else {
            this.$message.error("该手机号未注册");
            this.loading = false;
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    // 获取权限
    getPermission() {
      queryResourceByTypeBtn().then((res) => {
        localStorage.setItem(
          "resourceCode",
          JSON.stringify(
            res.result.map((item) => {
              return item.resourceCode;
            })
          )
        );
        this.$router.push({ path: `/` });
      });
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== "redirect") {
          acc[cur] = query[cur];
        }
        return acc;
      }, {});
    },
  },
};
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$bg: #283443;
$light_gray: #fff;
$cursor: #fff;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.login-container {
  // ::v-deep {
  // .el-form-item__content {
  //   border: none !important;
  //   background: transparent;
  // }
  // }
  .el-input {
    width: 100%;
    //border: 1px solid rgba(255, 255, 255, 0.1);
    // background: rgba(0, 0, 0, 0.1);
    //height: 66px;
    height: 54px;
    display: flex;
    align-items: center;
    background: #ffffff;
    // box-shadow: 0px 10px 40px 0px rgba(174, 174, 174, 0.2);
    border-radius: 10px 10px 10px 10px;
    opacity: 1;
    // border: 1px solid #eaeaea;

    input {
      background: transparent;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      height: 54px;
      color: #333333;
      height: 47px;
      caret-color: #333333;

      &:-webkit-autofill {
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.agreement {
  display: flex;
  margin-top: 20px;
  height: 30px;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #333333;
  line-height: 16px;

  .goagreement {
    cursor: pointer;
    font-size: 14px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: #1a5bff;
    line-height: 16px;
  }
}

.kj {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 30px;
  font-size: 15px;
  font-family: Plus Jakarta Sans-Regular, Plus Jakarta Sans;
  font-weight: 400;
  color: #1a5bff;
}

.svgicon {
  margin-top: 20px;
}

.login-btn {
  margin-left: 0px !important;
  width: 100%;
  height: 54px;
  margin-top: 33px;
  background: linear-gradient(135deg, #8daeff 0%, #1a5bff 100%);
  box-shadow: 0px 10px 40px 0px rgba(174, 174, 174, 0.2);
  border-radius: 10px 10px 10px 10px;
  opacity: 1;
  border: 1px solid #eaeaea;
  font-size: 16px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
}

.login-btn:hover {
  margin-left: 0px !important;
  width: 100%;
  height: 54px;
  margin-top: 33px;
  background: linear-gradient(135deg, #8daeff 0%, #1a5bff 100%);
  box-shadow: 0px 10px 40px 0px rgba(174, 174, 174, 0.2);
  border-radius: 10px 10px 10px 10px;
  opacity: 1;
  border: 1px solid #eaeaea;
  font-size: 16px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
}

.login-btn:active {
  margin-left: 0px !important;
  width: 100%;
  height: 54px;
  margin-top: 33px;
  background: linear-gradient(135deg, #8daeff 0%, #1a5bff 100%);
  box-shadow: 0px 10px 40px 0px rgba(174, 174, 174, 0.2);
  border-radius: 10px 10px 10px 10px;
  opacity: 1;
  border: 1px solid #eaeaea;
  font-size: 16px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
}

.login-btn:focus {
  margin-left: 0px !important;
  width: 100%;
  height: 54px;
  margin-top: 33px;
  background: linear-gradient(135deg, #8daeff 0%, #1a5bff 100%);
  box-shadow: 0px 10px 40px 0px rgba(174, 174, 174, 0.2);
  border-radius: 10px 10px 10px 10px;
  opacity: 1;
  border: 1px solid #eaeaea;
  font-size: 16px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #ffffff;
}

$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;

.login-container {
  display: flex;
  min-width: 1024px;
  min-height: 700px;
  margin: 0;
  padding: 0;
  font-family: sans-serif;
  background: url("https://static.idicc.cn/cdn/SSO/zhifuBgc.png") no-repeat
    center 0px;
  background-size: cover;
  background-position: center 0;
  background-repeat: no-repeat;
  background-attachment: fixed;
  -webkit-background-size: cover;
  -o-background-size: cover;
  -moz-background-size: cover;
  -ms-background-size: cover;
  background-repeat: no-repeat;
  width: 100%;
  overflow: hidden;
  height: 100vh;

  .form {
    position: absolute;
    top: 48%;
    left: 80%;
    transform: translate(-50%, -50%);
  }

  .login-form {
    border-radius: 20px 20px 20px 20px;
    //border: 1px solid rgba(255,255,255,0.15);
    //background: rgba(0,23,58,0.6);
    position: relative;
    // width: 538px;
    //height: 605px;
    width: 440px;
    height: 495px;
    //height: 548px;
    background: #ffffff;
    box-shadow: 0px 4px 53px 0px rgba(37, 81, 147, 0.25);
    border-radius: 16px 16px 16px 16px;
    opacity: 1;
    padding: 40px 32px;
    margin: 0 auto;

    p {
      text-align: center;
      margin-top: 80px;
      font-size: 15px;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.25);
    }
  }

  .tips {
    font-size: 14px;
    color: #fff;
    margin-bottom: 10px;

    span {
      &:first-of-type {
        margin-right: 16px;
      }
    }
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $dark_gray;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;

    .title {
      font-size: 30px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 600;
      color: #000000;
      margin: 5px auto 10px auto;
      //text-align: center;
      //font-weight: bold;
    }

    .t2 {
      font-size: 20px;
      font-family: Source Han Sans CN-Regular, Source Han Sans CN;
      font-weight: 400;
      color: #000000;
      font-weight: 400;
      color: #000000;
    }

    /*  img{
      margin-left: 165px;
      width: 100px;
      height: 100px;
    } */
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 2px;
    font-size: 16px;
    color: $dark_gray;
    cursor: pointer;
    user-select: none;
  }

  .thirdparty-button {
    position: absolute;
    right: 0;
    bottom: 6px;
  }

  @media only screen and (max-width: 470px) {
    .thirdparty-button {
      display: none;
    }
  }
}

.text {
  float: left;
  cursor: pointer;
  font-size: 14px;
  font-family: Source Han Sans CN-Regular, Source Han Sans CN;
  font-weight: 400;
  color: #333333;
}

footer {
  margin-top: 25px;
  display: flex;
  width: 100%;
  bottom: 63px;
  font-size: 12px;
  color: #ffffff66;

  span {
    max-width: 1200px;
    margin: auto;
    display: block;
    width: 100%;
    text-align: center;
  }

  img {
    width: 20px;
    display: inline-block;
    height: 20px;
    margin-right: 12px;
  }

  .imgs {
    height: 20px;
    margin-right: 3px;
  }
}
</style>
