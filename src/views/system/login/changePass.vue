<template>
  <div>
    <el-dialog
      width="40%"
      title="密码设置"
      v-model="show"
      :show-close="false"
      @close="cancel"
    >
      <div class="head">
        <span class="t2"
          >根据《中华人民共和国网络安全法》要求，以及为了保障您的账户安全，请修改初始密码</span
        >
      </div>
      <el-form
        ref="form"
        label-position="left"
        :model="ruleForm"
        :rules="rules"
        label-width="95px"
        class="demo-ruleForm"
      >
        <el-form-item label="原密码:" prop="oldPassword">
          <el-input
            v-model="ruleForm.oldPassword"
            type="password"
            show-password
            placeholder="请输入原密码"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item label="新密码:" prop="newPassword">
          <el-input
            v-model="ruleForm.newPassword"
            type="password"
            show-password
            placeholder="请输入新密码,新密码8-20位,包含字母数字"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item label="确认密码:" prop="confirmNewPassword">
          <el-input
            v-model="ruleForm.confirmNewPassword"
            type="password"
            autocomplete="off"
            show-password
            placeholder="请再次输入新密码,新密码8-20位,包含字母数字"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="default" @click="confirm"> 确认 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { encryptionAPI, setPasswordAPI2 } from "@/api/user";
import { JSEncrypt } from "jsencrypt";
import { token } from "@/utils/utils";
export default {
  name: "ExcelErrorwd",
  props: {
    changePass: {
      type: Boolean,
      default: false,
    },
    passToken: {
      type: String,
      default: "",
    },
  },
  data() {
    var validatePass = (rule, value, callback) => {
      var passwordreg = /(?=.*\d)(?=.*[a-zA-Z]).{8,20}/;
      if (value === "") {
        callback(new Error("请输入密码"));
      } else if (!passwordreg.test(value)) {
        callback(new Error("密码8-20位,包含字母数字"));
      } else {
        if (this.ruleForm.confirmNewPassword !== "") {
          this.$refs.form.validateField("confirmNewPassword");
        }
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.ruleForm.newPassword) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      show: false,
      ruleForm: {
        oldPassword: "",
        newPassword: "",
        confirmNewPassword: "",
      },
      rules: {
        oldPassword: [
          { required: true, message: "原密码不能为空", trigger: "blur" },
        ],
        newPassword: [
          { required: true, message: "密码不能为空", trigger: "blur" },
          { min: 8, message: "密码需要八位以上", trigger: "blur" },
          { validator: validatePass, trigger: "blur" },
        ],
        confirmNewPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          { validator: validatePass2, trigger: "blur" },
        ],
      },
      result: "",
    };
  },
  created() {
    this.encryption();
    this.show = this.changePass;
  },
  methods: {
    cancel() {
      this.show = false;
      this.$emit("changesu");
    },
    async encryption() {
      const res = await encryptionAPI();
      this.result = res.result;
    },
    async confirm() {
      let encrypt = new JSEncrypt();
      encrypt.setPublicKey(this.result);
      const oldPassword = encrypt.encrypt(this.ruleForm.oldPassword);
      const newPassword = encrypt.encrypt(this.ruleForm.newPassword);
      const confirmNewPassword = encrypt.encrypt(
        this.ruleForm.confirmNewPassword
      );
      await this.$refs.form.validate();
      // 更改密码
      const res = await setPasswordAPI2({
        oldPassword,
        newPassword,
        confirmNewPassword,
        token: this.passToken,
      });
      if (res.code == "SUCCESS") {
        this.$message.success("修改密码成功！请重新登陆");
        this.cancel();
      }
    },
  },
};
</script>
        
<style scoped lang="scss">
::v-deep {
  .el-dialog__header {
    background: #fff !important;
  }
}
.head {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}
.t1 {
  font-size: 20px;
  font-family: PingFang SC, PingFang SC;
  font-weight: 600;
  color: #1d2129;
  line-height: 28px;
  margin-right: 10px;
}
.t2 {
  color: #b5b5b5;
  font-size: 12px;
  margin-top: 24px;
}
</style>