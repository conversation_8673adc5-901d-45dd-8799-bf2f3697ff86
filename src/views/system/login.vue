<template>
  <div></div>
  <div class="container">
    <div class="box">
      <div class="login-content-left"></div>

      <div class="box-inner">
        <div class="welcome">账户密码登录</div>
        <el-form class="form">
          <el-input
            size="large"
            v-model="form.name"
            placeholder="请输入手机号/邮箱"
            type="text"
            maxlength="50"
          >
            <template #prepend>
              <i class="sfont system-xingmingyonghumingnicheng"></i>
            </template>
          </el-input>
          <el-input
            size="large"
            ref="password"
            v-model="form.password"
            :type="passwordType"
            placeholder="请输入登录密码"
            name="password"
            maxlength="50"
          >
            <template #prepend>
              <i class="sfont system-mima"></i>
            </template>
            <template #append>
              <i
                class="sfont password-icon"
                :class="passwordType ? 'system-yanjing-guan' : 'system-yanjing'"
                @click="passwordTypeChange"
              ></i>
            </template>
          </el-input>
          <div class="forgot">
            <span @click="forget">忘记密码？</span>
            <span @click="signUp">立即注册</span>
          </div>
          <el-button
            class="submit"
            type="primary"
            :loading="form.loading"
            @click="submit"
            style="width: 100%"
            size="default"
          >
            {{ $t("message.system.login") }}
          </el-button>
        </el-form>
        <div class="tips">
          <el-checkbox v-model="checked2"> </el-checkbox>
          <div class="tipsLabel" @click="changeCheckState">
            我已阅读并同意
            <span @click="goAgreement">《客户使用规范内容及相关协议》</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- <el-dialog
    v-model="signDialogVisible"
    width="1200px"
    :before-close="handleClose"
    custom-class="screenTable"
    class="screenTable2"
    :close-on-click-modal="false"
    center
  >
    <SignUp
      :signDialogVisible="'signDialogVisible'"
      @handleClose="handleClose"
      ref="signRef"
    />
  </el-dialog> -->
</template>

<script lang="ts" setup>
import { systemTitle, systemSubTitle } from "@/config";
import {
  ref,
  reactive,
  onMounted,
  onBeforeMount,
  defineProps,
  watch,
  computed,
} from "vue";
import { useStore } from "vuex";
import { useRouter, useRoute } from "vue-router";
// import SignUp from "./signUp.vue";
import type { RouteLocationRaw } from "vue-router";
import { getAuthRoutes } from "@/router/permission";
import { ElMessage } from "element-plus";
// import { Iphone } from '@element-plus/icons-vue'
// import selectLang from '@/layout/components/functionList/word.vue'
import { publicKey } from "@/api/user";
import { JSEncrypt } from "jsencrypt";
// import { ssoURL } from "@/utils/utils";
const forgetShow = ref(false);
const props = defineProps(["dialogVisible"]);
const signDialogVisible = ref();
const signRef = ref();
// let loginState = ref(localStorage.getItem("userState"));
// const forgetShow =
//       computed(() =>
//       // localStorage.getItem('isLogin')==='true'
//       store.state.app.isForgetShow
//       );
watch(
  () => props.dialogVisible,
  (newVal: any) => {
    checked2.value = sessionStorage.getItem("checked") === "0";
    // if(newVal){
    //   checked2.value=false
    // }
  },
  { deep: true }
);
let checked2 = ref(false);

onBeforeMount(() => {
  getPublicKey();
  goSSo();
});
onMounted(() => {
  // console.log(
  //   localStorage.getItem("checked"),
  //   localStorage.getItem("checked") === "0"
  // );
  checked2.value = sessionStorage.getItem("checked") === "0";

  // getPublicKey()
});
const goSSo = () => {
  var test = window.location.href;
  let lastIndex = test.lastIndexOf("/");
  let result = test.substring(0, lastIndex + 1);
  //console.log(result, "result");
  const url = `${ssoURL}?ly=${result}`;
  //const baseURL = 'https://staticdev.idicc.cn/static/sso/index.html'
  window.location.href = url;
};
const getPublicKey = () => {
  publicKey().then((res) => {
    // console.log(res.result)
    key.value = res.result;
  });
};
let key = ref(
  "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC27tAIp7i+qhyunO+O5xvk5ilVR9npOkrfeJB69OtafL1i3ZjXNct0gxZF09WRCzWCOkLzG0rcKzWaFieWRscci5pAHqSzfld5Qob6e3BVgI+BtJcge4NOGtMN8ASEMXBUdzhuo1ud0VQUqVjDcBMqQMKtsyIpBh+onZH8jqXz/wIDAQAB"
);
const store = useStore();
const router = useRouter();
const route = useRoute();
const goAgreement = () => {
  // store.commit('app/stateChange', {
  //       name: 'isLogin',
  //       value: false
  //     })
  // router.push('/agreement')
  window.open("/static/jh/index.html#/agreement");
};
const form = reactive({
  // name: '15868129000',
  // password: '129000',
  name: "",
  password: "",
  loading: false,
});
const passwordType = ref("password");
const passwordTypeChange = () => {
  passwordType.value === ""
    ? (passwordType.value = "password")
    : (passwordType.value = "");
};
const checkForm = () => {
  if (!checked2.value) {
    ElMessage.warning({
      message: "请先勾选协议",
      type: "warning",
    });
    return;
  }
  return new Promise((resolve, reject) => {
    if (form.name === "") {
      ElMessage.warning({
        message: "用户名不能为空",
        type: "warning",
      });
      return;
    }
    if (form.password === "") {
      ElMessage.warning({
        message: "密码不能为空",
        type: "warning",
      });
      return;
    }
    resolve(true);
  });
};
const submit = () => {
  checkForm().then(() => {
    form.loading = true;
    let encrypt = new JSEncrypt();
    encrypt.setPublicKey(key.value);
    const password = encrypt.encrypt(form.password);
    let params = {
      username: form.name,
      password: password,
      // 'Qg9CBdwkIl5FCY/4/UcOaTFqZ6RbfTrMYp0VIGFblJCRFpeGexETsrEY78gZniyvR3JgBN8/KonwRiECs72G84nk4UiDFwXjazVP04pHduv/y/SopB7KUZUyCpUqtAHLLxr62j6eB9U8vnzAJhhkMyHc4SnQBa5IvO9nHr3f+ao='
      //
    };
    store
      .dispatch("user/login", params)
      .then(() => {
        ElMessage.success({
          message: "登录成功",
          type: "success",
          showClose: true,
          duration: 1000,
        });
        sessionStorage.setItem("checked", "0");
        store.commit("app/stateChange", {
          name: "isLogin",
          value: false,
        });
        //  await getAuthRoutes()
        getAuthRoutes().then((res) => {
          //   setTimeout(() => {
          //     sessionStorage.getItem("userState")
          // === "admin"
          //   ? router.push("/homepage")
          //   : sessionStorage.getItem("userState")
          // === "user"? router.push("/onLineService"):router.push('/')
          form.loading = false;
          //   }, 1000);
        });
      })
      .finally(() => {
        form.loading = false;
      });
  });
};
const signUp = () => {
  signDialogVisible.value = true;
  // router.push('/signUp')
};
const handleClose = () => {
  signDialogVisible.value = false;
  signRef.value.clear();
};

const changeCheckState = () => {
  checked2.value = !checked2.value;
};

const forget = () => {
  store.commit("app/stateChange", {
    name: "isLogin",
    value: false,
  });
  forgetShow.value = true;

  setTimeout(() => {
    store.commit("app/stateChange", {
      name: "isForgetShow",
      value: true,
    });
  }, 500);
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  width: 100%;
  height: 420px;
  background: transparent;
  // url('@/assets/login/bg.png') no-repeat center;
  // overflow: hidden;
  background-size: cover;
  cursor: pointer;
  user-select: none;

  .box {
    width: 700px;
    // display: flex;
    position: relative;
    // left: 50%;
    // top: 50%;
    background: transparent;
    border-radius: 8px;
    // transform: translate(-50%, -50%);
    height: 420px;
    // overflow: hidden;
    // box-shadow: 0 6px 20px 5px rgba(152, 152, 152, 0.1),
    //   0 16px 24px 2px rgba(117, 117, 117, 0.14);

    .login-content-left {
      width: 418px;
      height: 320px;
      float: left;
      // background: blue;
      margin: 50px 0;
      background: center/contain no-repeat url("@/assets/login/bg1.png");
    }

    .box-inner {
      width: 360px;
      height: 440px;
      float: right;
      right: 0;
      float: right;
      position: absolute;
      right: 0;
      background: white;
      box-shadow: -7px 7px 10px 0px rgba(0, 0, 0, 0.2);
      border-radius: 5px;

      .welcome {
        margin-top: 50px;
        text-align: center;
        font-size: 26px;
        font-weight: 500;
        color: #006afe;
      }

      .form {
        width: 80%;
        margin: 30px auto 15px;

        .el-input {
          margin-bottom: 20px;
        }

        .password-icon {
          cursor: pointer;
          color: #437bff;
        }
      }

      .fixed-top-right {
        position: absolute;
        top: 10px;
        right: 10px;
      }

      .forgot {
        padding: 0px 10px 35px;
        font-size: 12px;
        color: #999999;
        display: flex;
        justify-content: space-between;
        span {
          color: #3a88f7;
        }
      }
      .text {
        display: flex;
        justify-content: space-between;
      }
      .tips {
        padding: 0 31px;
        font-size: 12px;
        color: #999999;
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        .tipsLabel {
          padding-left: 10px;
        }
        span {
          color: #3a88f7;
        }
      }

      .submit {
        width: 302px;
        height: 45px;
        background: linear-gradient(90deg, #006afe, #4391ff);
        border-radius: 4px;

        span {
          font-size: 18px;

          color: #ffffff;
        }
      }
    }
  }
}
</style>
<style lang="scss">
.box-inner {
  .el-input-group__append,
  .el-input-group__prepend {
    padding: 0 10px;
    background: white;
    border-bottom: 1px solid var(--el-border-color);
    // border-top: 0px;
    // border-left: 0px;
    // border-right: 0px;
    border-radius: 0;
    box-shadow: 0 0 0 0;
  }

  .el-input__wrapper {
    // border-bottom: 1px solid var(--el-border-color);
    border-radius: 0;
    box-shadow: 0 0 0 0;
  }
}
.screenTable2 {
  height: 550px;
}
.screenTable,
.screenTable2 {
  .el-dialog__header {
    width: 100% !important;
    padding: 0 10px;
    margin-top: 10px !important;
    // padding: 30px 30px !important;
  }
}
</style>
