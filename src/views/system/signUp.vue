<template>
  <div class="container signInForm" v-if="!successState">
    <div class="box">
      <div class="box-inner">
        <div class="singUpBg">注册账号</div>
        <div class="contentForm">
          <el-form
            class="form"
            :model="form"
            label-width="120px"
            ref="formRef"
            label-position="top"
          >
            <!-- 一栏 -->
            <el-row>
              <el-col :span="11">
                <el-form-item
                  label="企业名称"
                  prop="enterprise_name"
                  :rules="[
                    { required: true, message: '请输入企业名称' },
                    // { type: 'number', message: '验证码为数字' },
                  ]"
                >
                  <el-input
                    v-model="form.enterprise_name"
                    placeholder="请输入企业名称"
                    type="text"
                    maxlength="50"
                  />
                </el-form-item>
                <el-form-item
                  label="统一社会信用代码"
                  prop="uni_code"
                  :rules="[
                    { required: true, message: '请输入统一社会信用代码' },
                    // { type: 'number', message: '验证码为数字' },
                  ]"
                >
                  <el-input
                    v-model="form.uni_code"
                    placeholder="请输入统一社会信用代码"
                    type="text"
                    maxlength="50"
                  />
                </el-form-item>
                <el-form-item
                  label="电子证照"
                  prop="business_license"
                  :rules="[
                    { required: true, message: '请上传电子证照' },
                    // { type: 'number', message: '验证码为数字' },
                  ]"
                >
                  <el-upload
                    class="avatar-uploader"
                    :action="url"
                    :show-file-list="false"
                    :on-success="handleAvatarSuccess"
                    :before-upload="beforeAvatarUpload"
                  >
                    <img
                      v-if="form.business_license"
                      :src="showbusiness"
                      class="bgImg"
                    />
                    <el-icon v-else class="avatar-uploader-icon singInUpload">
                      <Plus />
                    </el-icon>
                  </el-upload>
                </el-form-item>
              </el-col>
              <!-- 二栏 -->
              <el-col :span="11">
                <el-form-item
                  label="企业联系人"
                  prop="contact"
                  :rules="[
                    { required: true, message: '请输入企业联系人' },
                    // { type: 'number', message: '验证码为数字' },
                  ]"
                >
                  <el-input
                    v-model="form.contact"
                    placeholder="请输入企业联系人"
                    type="text"
                    maxlength="50"
                  />
                </el-form-item>
                <el-form-item
                  label="联系人手机号码"
                  prop="phone"
                  :rules="[
                    { required: true, message: '请输入联系人手机号码' },
                    {
                      pattern: /^1[3456789]\d{9}$/,
                      message: '请输入正确手机号',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-input
                    v-model="form.phone"
                    placeholder="请输入联系人手机号"
                    type="text"
                    maxlength="50"
                    class="phone"
                  >
                    <template #append>
                      <div @click="sentCode">
                        <!-- <el-divider direction="vertical" /> -->
                        <span class="hover:cursor-pointer">
                          {{
                            timer === 0 ? "获取验证码" : `${timer}s后重新获取`
                          }}</span
                        >
                      </div>

                      <!-- | <span class="getCode">获取验证码</span> -->
                    </template></el-input
                  >
                </el-form-item>
                <el-form-item
                  label="验证码"
                  prop="verification_code"
                  :rules="[
                    { required: true, message: '请输入验证码' },
                    {
                      pattern: /^[0-9]*$/,
                      message: '请输入正确验证码',
                      trigger: 'blur',
                    },
                    {
                      min: 6,
                      max: 6,
                      message: '验证码长度为6位',
                      trigger: 'blur',
                    },
                    //   { type: 'number', message: '验证码为数字' },
                  ]"
                >
                  <el-input
                    v-model="form.verification_code"
                    placeholder="请输入验证码"
                  />
                </el-form-item>
                <el-form-item
                  label="联系人邮箱"
                  prop="email"
                  :rules="[
                    {
                      required: true,
                      message: '请输入联系人邮箱',
                      trigger: 'blur',
                    },
                    {
                      type: 'email',
                      message: '请输入正确的联系人邮箱',
                      trigger: ['blur', 'change'],
                    },
                    // { type: 'number', message: '验证码为数字' },
                  ]"
                >
                  <el-input
                    v-model="form.email"
                    placeholder="请输入联系人邮箱"
                    type="text"
                    maxlength="50"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <div class="submitBtn">
              <div class="submitBtnCenter">
                <el-button
                  size="default"
                  type="primary"
                  :loading="form.loading"
                  @click="MechanismCheck(formRef)"
                  style="width: 80px"
                  class="onOk"
                >
                  提交
                </el-button>
                <el-button
                  size="default"
                  type="default"
                  :loading="form.loading"
                  @click="cancel"
                  style="width: 80px"
                  class="onCancel"
                >
                  取消
                </el-button>
              </div>

              <!--   <div class="submitBtnText">
                已有账号? <span @click="cancel">去登录</span>
              </div> -->
            </div>
          </el-form>
          <!-- <div class="fixed-top-right">
          <select-lang />
        </div> -->
        </div>
      </div>
    </div>
  </div>
  <div class="successState" v-else>
    <div class="title">账号注册提交成功!</div>
    <div class="tips">
      <span>
        {{ form.enterprise_name }}
      </span>
      ，您的注册信息已提交录入完成，系统正在审核中，我们将在24小时内短信通知您审核结果，请保持通讯顺畅!
    </div>

    <el-button size="default" type="primary" @click="cancel">确定</el-button>
  </div>
  <el-dialog
    v-model="organizationdig"
    custom-class="screenTablemini"
    width="30%"
    title="机构选择"
    :close-on-click-modal="false"
  >
    <el-form
      label-position="right"
      label-width="100px"
      :model="organizationCode"
      style="max-width: 460px"
    >
      <el-form-item label="请选择机构">
        <el-select
          multiple
          collapse-tags
          v-model="organizationCode"
          placeholder="请选择机构"
        >
          <el-option
            v-for="item in orgList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="submitBtn">
      <div class="submitBtnCenter">
        <el-button
          size="default"
          type="primary"
          :loading="form.loading"
          @click="submit(formRef)"
          style="width: 80px"
          class="onOk"
        >
          确定
        </el-button>
        <el-button
          size="default"
          type="default"
          :loading="form.loading"
          @click="abolish"
          style="width: 80px"
          class="onCancel"
        >
          取消
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import {
  ref,
  reactive,
  defineEmits,
  watch,
} from "vue";
import { useStore } from "vuex";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import type { FormInstance } from "element-plus";
import { Plus } from "@element-plus/icons-vue";
import { useIntervalFn } from "@vueuse/core";
import type { UploadProps } from "element-plus";
import { getCode } from "@/api/user";
import { baseEvn } from "@/utils/utils";
import { getRegisterChannelKeyByUniCodeAPI } from "@/api/user";
const timer = ref(0);
const showbusiness = ref("");
const organizationCode = ref([]); //用户选择的机构code
const organizationdig = ref(false); //机构选择弹层
const orgList = ref([]); //机构选择弹层
const emit = defineEmits(["handleClose"]);
let props = defineProps(["signDialogVisible"]);
const successState = ref(false);
watch(
  () => props.signDialogVisible,
  (newVal) => {
    //   form =  {
    //   enterprise_name: "",
    //   uni_code: "",
    //   contact: "",
    //   phone: "",
    //   verification_code: null,
    //   loading: false,
    //   business_license: "",
    //   email: "",
    // }
  },
  { deep: true }
);
// 发送验证码倒计时
// useIntervalFn(定时的回调，回调的时间间隔，控制回调的调用方式)
// 参数三 {immediate: true, immediateCallback: false}
// 3-1) immediate 首次运行useIntervalFn函数时，是否立刻开启定时任务（默认值true表示默认开启）
// 3-2）immediateCallback 执行useIntervalFn函数立刻执行回调（在延时时间的前或者后调用），默认值是false，如果修改为true，表示为不延时，立刻启动定时任务（不要使用pause方法）
// pause暂停；resume启动
const { pause, resume } = useIntervalFn(
  () => {
    if (timer.value <= 0) {
      // 停止定时任务
      pause();
    } else {
      // 单次定时任务执行的回调
      timer.value--;
    }
  },
  1000,
  {
    // 默认不开启定时任务
    immediate: false,
  }
);

const url = `${baseEvn}upload`;

const handleAvatarSuccess: UploadProps["onSuccess"] = (
  response,
  uploadFile
) => {
  showbusiness.value = URL.createObjectURL(uploadFile.raw!);
  form.business_license = response?.data?.value;
};

const beforeAvatarUpload: UploadProps["beforeUpload"] = (rawFile) => {
  //   if (rawFile.type !== 'image/jpeg/png/PNG/JPG/JPEG/') {
  //     ElMessage.error ("只能上传")
  //     return false
  //   } else
  if (rawFile.size / 1024 / 1024 > 5) {
    ElMessage.error("上传图片大小不能超过 5MB!");
    return false;
  }
  return true;
};
const formRef = ref<FormInstance>();
const store = useStore();
const router = useRouter();
const route = useRoute();
let form = reactive({
  enterprise_name: "",
  uni_code: "",
  contact: "",
  phone: "",
  verification_code: null,
  loading: false,
  business_license: "",
  email: "",
});

const passwordType = ref("password");
const passwordTypeChange = () => {
  passwordType.value === ""
    ? (passwordType.value = "password")
    : (passwordType.value = "");
};
const checkForm = () => {
  return new Promise((resolve, reject) => {
    if (form.name === "") {
      ElMessage.warning({
        message: "用户名不能为空",
        type: "warning",
      });
      return;
    }
    if (form.password === "") {
      ElMessage.warning({
        message: "密码不能为空",
        type: "warning",
      });
      return;
    }
    resolve(true);
  });
};
const MechanismCheck = (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  formEl.validate((valid) => {
    if (valid) {
      getRegisterChannelKeyByUniCodeAPI({ uniCode: form.uni_code }).then(
        (res: any) => {
          if (res.data) {
            let data = res.data;
            if (data.length == 1) {
              organizationCode.value.push(data[0].channelKey);
              submit(formRef.value);
            } else {
              data = res.data.filter(
                (item: any) => item.isUniCodeRegister !== true
              );
              if (data.length == 1) {
                organizationCode.value.push(data[0].channelKey);
                submit(formRef.value);
                return;
              } else if (data.length == 0) {
                organizationCode.value.push(res.data[0].channelKey);
                submit(formRef.value);
                return;
              } else {
                orgList.value = data.map((it: any) => {
                  return {
                    value: it.channelKey,
                    label: it.orgName,
                  };
                });
                //打开一个弹层 让用户选data
                organizationdig.value = true;
              }
            }
          }
        }
      );
    }
  });
};
const submit = (formEl: FormInstance | undefined) => {
  if (!organizationCode.value) {
    return ElMessage({
      message: "您还未选择机构",
      type: "warning",
      duration: 1000,
    });
  }
  if (!formEl) return;
  formEl.validate((valid) => {
    if (valid) {
      let params = {
        businessLicense: form.business_license,
        contact: form.contact,
        email: form.email,
        enterpriseName: form.enterprise_name,
        phone: form.phone,
        uniCode: form.uni_code,
        verificationCode: form.verification_code,
        channelKeyList: organizationCode.value,
      };
      store
        .dispatch("user/signUp", params)
        .then(async () => {
          successState.value = true;
          abolish();
          ElMessage.success({
            message: "注册成功",
            type: "success",
            showClose: true,
            duration: 1000,
          });
        })
        .catch(() => {})
        .finally(() => {});
    }
  });
};

const sentCode = () => {
  const mobile = form.phone;
  formRef.value?.validateField("phone", (valid) => {
    if (valid) {
      // 发送验证码
      // 开启倒计时效果
      if (timer.value === 0) {
        timer.value = 60;
        resume();
      } else {
        return;
      }
      getCode({ type: 1, mobile: mobile }).then((response: any) => {
        if (response.code === "SUCCESS") {
          ElMessage({
            message: "发送成功",
            type: "success",
          });
        } else {
          ElMessage({
            message: response.data.msg,
            type: "error",
          });
        }
      });
    }
  });
};
const clear = () => {
  formRef.value?.resetFields();
  successState.value = false;
};
const cancel = () => {
  Object.assign(form, {
    enterprise_name: "",
    uni_code: "",
    contact: "",
    phone: "",
    verification_code: null,
    loading: false,
    business_license: "",
    email: "",
  });
  organizationCode.value = []; //用户选择的机构code
  clear();
  emit("handleClose");
};
const abolish = () => {
  organizationdig.value = false;
  organizationCode.value = []; //用户选择的机构code
  orgList.value = []; //机构选择弹层
};
defineExpose({
  clear,
});
</script>

<style lang="scss" scoped>
.bgImg {
  width: 200px;
  height: 130px;
}

.singInUpload {
  font-size: 28px;
  color: #8c939d;
  width: 200px;
  height: 130px;
  text-align: center;
  border: 1px solid rgba(64, 129, 203, 0.7);
  border-radius: 4px;
}

.submitBtn {
  width: 80%;
  margin: 0 auto;

  .submitBtnCenter,
  .submitBtnText {
    margin-top: 15px;
    display: flex;
    justify-content: center;

    span {
      color: white;
      padding-left: 15px;
    }
  }

  .onCancel {
    background: rgba(42, 102, 176, 0);
    border: 1px solid #2f89cd;
    opacity: 0.6;
    border-radius: 4px;
    font-size: 16px;
    color: #ffffff;
  }

  .onOk {
    background: rgba(42, 102, 176, 0.7);
    border: 1px solid #2f89cd;

    font-size: 16px;
    color: #ffffff;
  }
}

.singUpBg {
  width: 100%;
  height: 50px;
  background: left 0 top 5px / contain no-repeat
    url("@/assets/images/view/home/<USER>");
  font-size: 20px;
  font-weight: bold;
  font-style: italic;
  color: #c5d4ed;
  line-height: 45px;
  padding-left: 20px;
  // line-height: 60px;
}
</style>
<style lang="scss">
.signInForm {
  .box-inner .is-error .el-input__wrapper {
    border: 1px solid var(--el-color-danger);

    box-shadow: 0 0 0 0px var(--el-color-danger) inset;
  }

  .box-inner .el-input__wrapper {
    background: transparent !important;
    // box-shadow: 0 0 0 1px rgba(64, 129, 203, 0.7) inset;
    // border: 0;
    border: 1px solid rgba(64, 129, 203, 0.7) !important;
    border-radius: 4px !important;
  }

  .el-form-item__label {
    color: rgba(178, 195, 223, 1);
  }

  .phone {
    .el-input__wrapper {
      border-right: 0px;

      border-radius: 4px 0 0 4px !important;
    }
  }

  .getCode {
    color: white;
    padding-left: 10px;

    &:hover {
      color: #2f89cd;
    }
  }

  .el-input__inner {
    color: white;
  }

  //   .is-error{

  //   }
  .box-inner .el-input-group__append {
    background: transparent !important;
    border: 1px solid rgba(64, 129, 203, 0.7);
    border-left: 0px;
    border-radius: 0 4px 4px 0;
  }

  .el-row {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
  }

  .el-col:last-child {
    margin-left: 25px;
  }
}

.contentForm {
  padding: 25px;
}

.successState {
  padding: 120px 30px;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;

  .title {
    font-size: 22px;
    width: 100%;
    padding: 15px;
    text-align: center;
    color: #fff;
  }

  .tips {
    width: 100%;
    text-align: center;
    color: #bfbfbf;
    padding: 25px 15px;

    span {
      color: #2f89cd;
    }
  }

  button {
    width: 100px;
    height: 32px;
    margin: 50px auto;
  }
}
</style>
