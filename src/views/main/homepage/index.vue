<template>
  <Header />
  <!-- <div class="mainContent"> -->

  <div class="bgc">
    <div class="bgcInner">
      <div class="headerTitle">用大数据和AI打造智能园区</div>
      <div class="des">您好，欢迎来到江苏省数字交通产业园综合服务平台</div>

      <div class="con">
        <div class="one">
          <div @click="skip(4, '')" class="top">
            <div class="aicon"></div>
            <span>产业洞察</span>
          </div>
          <div @click="skip(4, '')" class="middle">
            <span>{{ one }}</span>
          </div>
          <div class="industry">
            <div
              @click="skip(1, item)"
              v-for="(item, index) in inList"
              :key="index"
              class="list"
            >
              <!--   <img  class="industryImg" :src="item.icon" alt=""> -->
              <div :class="'industryImg' + item.i"></div>
              <span>{{ item.chainName.replace("产业金脑·", "") }}</span>
            </div>
          </div>
        </div>
        <div class="right">
          <div style="display: flex">
            <div @click="skip(2, '')" class="two">
              <div class="top">
                <div class="bicon"></div>
                <span>数字产研</span>
              </div>
              <div class="middle">
                <span>{{ two }}</span>
              </div>
            </div>
            <div @click="skip(3, '')" class="three">
              <div class="top">
                <div class="cicon"></div>
                <span>数智招商</span>
              </div>
              <div class="middle">
                <span>{{ three }}</span>
              </div>
            </div>
          </div>
          <div class="pt">
            <div class="bt">综合服务平台</div>
            <div class="list">
              <div class="jsc" @click="go(1)"><span>园区驾驶舱</span></div>
              <div class="gzt" @click="go(2)"><span>工作台</span></div>
              <div class="ht" @click="go(3)"><span>管理后台</span></div>
              <div class="yh" @click="go(4)"><span>统一用户管理</span></div>
            </div>
          </div>
        </div>
        <!-- </div> -->
      </div>
    </div>
  </div>
</template>
<script  lang="ts" setup>
import Header from "@/components/header/index.vue";

import { skipURL, token } from "@/utils/utils";
import { onMounted, onBeforeUnmount, ref, computed, reactive } from "vue";
import { useStore } from "vuex";
import { getListAPI } from "@/api/idicc";
import { useRoute, useRouter } from "vue-router";
import { ElMessageBox, ElMessage } from "element-plus";
const userInfo = computed(() =>
  JSON.parse(localStorage.getItem("userInfo") || "{}")
);
const store = useStore();
const router = useRouter();
const route = useRoute();
const one = ref(
  "精确定位，客观评价，实现数字交通企业与产业链细分环节精准匹配，打造产业全景一张图；以驾驶舱视角观趋势、研形势，系统化洞察区域数字交通产业发展态势。"
);
const two = ref(
  "基于多维度产业数字底座，全面分析全国数字交通产业布局，客观研判产业发展实力，实时追踪产业发展动态，定期发布产业发展白皮书，定向提供产业技术专利导航。"
);
const three = ref(
  "实时追踪产业链企业、技术、产品动态，智能匹配本地现状，捕获潜在招商线索，提供强链补链招商建议，招商线索共享协作，招商进度实时跟踪，实现招商全流程精细化管理。"
);
const inId = ref(null);
onMounted(() => {
  // console.log(route)

  setFullScreen();
  getList();
});

interface ChainItem {
  chainName: string;
  i: number;
}
const inList: Ref<ChainItem[]> = ref([]);
onBeforeUnmount(() => {
  store.commit("app/contentFullScreenChange", false);
});
const setFullScreen = () => {
  store.commit("app/contentFullScreenChange", true);
};
const getList = async () => {
  let data = {
    isAll: 1,
  };
  const res = await getListAPI(data, skipURL);
  if (res.result?.length > 0) {
    inId.value = res.result[0].id;
    // console.log(inId, 'inde');
  }
  inList.value = res.result;
  inList.value.forEach((item) => {
    if (item.chainName == "数字轨交") {
      item.i = 1;
    } else if (item.chainName == "数字港口") {
      item.i = 2;
    } else if (item.chainName == "数字道桥") {
      item.i = 3;
    } else if (item.chainName == "车路协同") {
      item.i = 4;
    } else {
      item.i = 0;
    }
  });
};
const skip = (i: any, item: any) => {
  /*  ElMessage({
     message: '功能开发中..',
     type: 'warning',
   }) */
  if (inId.value !== null) {
    if (i == 2) {
      window.open(`${skipURL}#/idicc/idicc-scan?token=${token()}`, "_blank");
    } else if (i == 3) {
      window.open(
        `${skipURL}#/attract/IntelligentSearch?token=${token()}`,
        "_blank"
      );
    } else if (i == 1) {
      window.open(
        `${skipURL}#/dashboard?id=${item.id}&token=${token()}`,
        "_blank"
      );
    } else {
      window.open(
        `${skipURL}#/dashboard?id=${inId.value}&token=${token()}`,
        "_blank"
      );
    }
  } else {
    ElMessage({
      message: "暂无产业链权限",
      type: "error",
    });
  }
};
const go = (i: any) => {
  if (i == 1) {
    router.push("/dashboard");
  } else if (i == 2) {
    router.push("workbench/building");
  } else if (i == 4) {
    router.push("home");
  } else {
    // 统一用户管理
    router.push("backstage/release");
  }
};
</script>

<style scoped lang="scss">
::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 3px;
  /* 对应横向滚动条的宽度 */
  height: 5px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
  background-color: #d9d9d9;
  border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  display: none;
}
.icon {
  width: 22px;
  height: 20px;
  background: no-repeat url("../homepage/img/icon.png");
  background-size: contain;
}

.header {
  height: 48px;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0px 1px 4px 0px rgba(0, 21, 41, 0.12);
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  width: 100%;
  padding: 0 20px;
  font-size: 14px;

  font-weight: bold;
  color: rgba(0, 0, 0, 0.65);
  line-height: 48px;
}

.jump {
  display: flex;
}

.industry {
  width: 100%;
  display: flex;
  margin-top: 80px;
  overflow: auto;
  //flex-wrap: wrap;
  //justify-content:center;

  .list {
    flex: 0 0 25%; /* 设置宽度为父容器的25% */
    display: flex;
    align-items: center;
    cursor: pointer;
    flex-direction: column;
    padding: 10px;
  }

  .industryImg {
    width: 70px;
    height: 70px;
  }
  .industryImg0 {
    width: 70px;
    height: 70px;
    background: no-repeat url("./img/icon0.png");
    background-size: contain;
  }
  .industryImg1 {
    width: 70px;
    height: 70px;
    background: no-repeat url("./img/icon1.png");
    background-size: contain;
  }
  .industryImg2 {
    width: 70px;
    height: 70px;
    background: no-repeat url("./img/icon2.png");
    background-size: contain;
  }
  .industryImg3 {
    width: 70px;
    height: 70px;
    background: no-repeat url("./img/icon3.png");
    background-size: contain;
  }
  .industryImg4 {
    width: 70px;
    height: 70px;
    background: no-repeat url("./img/icon4.png");
    background-size: contain;
  }
  .industryImg5 {
    width: 70px;
    height: 70px;
    background: no-repeat url("./img/icon4.png");
    background-size: contain;
  }
  span {
    margin-top: 5px;
    font-size: 14px;
    font-family: Source Han Sans CN-Regular, Source Han Sans CN;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.65);
  }
}

.head {
  width: 100%;
  height: 48px;
  background: #ffffff;
  box-shadow: 0px 1px 4px 0px rgba(0, 21, 41, 0.12);
  border-radius: 0px 0px 0px 0px;
  opacity: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;

  span {
    font-size: 14px;
    font-family: Source Han Sans CN-Bold, Source Han Sans CN;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.65);
    line-height: 22px;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .left {
    display: flex;
    padding-left: 20px;

    .icon {
      width: 22px;
      height: 20px;
      background: no-repeat url("./img/icon.png");
      background-size: contain;
    }
  }
}

.bgc {
  width: 100%;
  height: calc(100vh - 60px);
  position: absolute;
  padding: 60px 0px 0;
  z-index: 0;
  background: #f8f9fc top left 100% / contain no-repeat
    url("@/assets/images/online/bg1.png");
}
.bgcInner {
  width: 82%;
  margin: 0 auto;
}
.con {
  z-index: 99;
  position: relative;
  margin-top: 75px;
  // padding: 0  ;
  display: flex;
  width: 100%;
  // height: 300px;

  .one:hover {
    box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.45);
  }

  .one {
    overflow: auto;
    width: 33%;
    height: 428px;
    padding-top: 20px;
    padding-left: 19px;
    padding-right: 19px;
    background: #ffffff;
    box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.25);
    border-radius: 6px 6px 6px 6px;
    opacity: 1;

    .top {
      height: 50px;
      width: 100%;
      display: flex;
      cursor: pointer;
      align-items: center;

      span {
        margin-left: 15px;
        font-size: 20px;
        font-family: Source Han Sans CN-Medium, Source Han Sans CN;
        font-weight: 800;
        color: #000000;
        line-height: 22px;
      }
    }

    .middle {
      margin-top: 12px;
      padding-left: 16px;
      padding-right: 16px;
      font-size: 14px;
      font-family: Source Han Sans CN-Medium, Source Han Sans CN;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.65);
      line-height: 22px;
    }

    .aicon {
      width: 50px;
      height: 50px;
      background: no-repeat url("./img/1.png");
      background-size: contain;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    // height: 400px;
    width: 67%;
    .pt {
      // width: 995px;
      height: 210px;
      margin-left: 15px;
      margin-top: 16px;
      background: #ffffff;
      box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.25);
      border-radius: 6px 6px 6px 6px;
      opacity: 1;

      .bt {
        padding-left: 24px;
        padding-top: 21px;
        font-size: 24px;
        font-weight: 700;
        color: rgba(0, 0, 0, 0.85);
      }

      .list {
        padding: 20px 20px;
        // margin-left: 32px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        div {
          width: 30%;
          height: 100px;
          cursor: pointer;
          background-size: 100% 100%;
          span {
            line-height: 100px;
            padding-left: 20px;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.85);
          }
        }
        .jsc {
          background: center/100% 100% no-repeat url("./img/Frame <EMAIL>");
          width: 24%;
        }

        .gzt {
          background: center/100% 100% no-repeat url("./img/Frame <EMAIL>");
          width: 24%;
        }

        .ht {
          background: center/100% 100% no-repeat url("./img/Frame <EMAIL>");
          width: 24%;
        }
        .yh {
          background: center/100% 100% no-repeat url("./img/yh.png");
          width: 24%;
        }
      }
    }

    .two:hover {
      box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.45);
    }

    .two {
      cursor: pointer;
      padding-top: 20px;
      padding-left: 19px;
      padding-right: 19px;
      margin-left: 15px;
      // width: 490px;
      height: 200px;
      background: #ffffff;
      box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.25);
      border-radius: 6px 6px 6px 6px;
      opacity: 1;

      .top {
        height: 50px;
        width: 100%;
        display: flex;
        align-items: center;

        span {
          margin-left: 15px;
          font-size: 20px;
          font-family: Source Han Sans CN-Medium, Source Han Sans CN;
          font-weight: 800;
          color: #000000;
          line-height: 22px;
        }
      }

      .middle {
        margin-top: 12px;
        padding-left: 16px;
        padding-right: 16px;
        font-size: 14px;
        font-family: Source Han Sans CN-Medium, Source Han Sans CN;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
      }

      .bicon {
        width: 50px;
        height: 50px;
        background: no-repeat url("./img/2.png");
        background-size: contain;
      }
    }

    .three:hover {
      box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.45);
    }

    .three {
      padding-top: 20px;
      padding-left: 19px;
      margin-left: 15px;
      cursor: pointer;
      // width: 490px;
      height: 200px;
      background: #ffffff;
      box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.25);
      border-radius: 6px 6px 6px 6px;
      opacity: 1;

      .top {
        height: 50px;
        width: 100%;
        display: flex;
        align-items: center;

        span {
          margin-left: 15px;
          font-size: 20px;
          font-family: Source Han Sans CN-Medium, Source Han Sans CN;
          font-weight: 800;
          color: #000000;
          line-height: 22px;
        }
      }

      .middle {
        margin-top: 12px;
        padding-left: 16px;
        padding-right: 16px;
        font-size: 14px;
        font-family: Source Han Sans CN-Medium, Source Han Sans CN;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
      }

      .cicon {
        width: 50px;
        height: 50px;
        background: no-repeat url("./img/3.png");
        background-size: contain;
      }
    }
  }
}
.mainContent {
  width: 100%;
  // padding: 0 120px;
}
.headerTitle {
  font-size: 30px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
  line-height: 22px;
  margin-bottom: 22px;
}
.des {
  font-size: 16px;
  color: rgba(0, 0, 0, 0.65);
  line-height: 22px;
}
</style>