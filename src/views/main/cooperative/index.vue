<script  setup>
// import HelloWorld from './components/HelloWorld.vue'
// import TheWelcome from './components/TheWelcome.vue'
import AmisComponent from "@/components/amis/index.vue";
import { ref } from "vue";
import { perFix, baseEvn, token } from "@/utils/utils";
import { useRoute, useRouter } from "vue-router";

const router = useRouter();
function handleButtonClick(e) {
  // "link": "#/workbench/lease/addlease",
  router.push(e);
}
// 合作咨询列表
const amisjson2 = {
  type: "page",
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: "0px 0px 0px 0px transparent",
  },
  body: [
    {
      type: "tpl",
      inline: true,
      wrapperComponent: "",
      syncLocation: false,
      id: "u:3345e187f2df",
      style: {
        boxShadow: "0px 0px 0px 0px transparent",
        fontWeight: "500",
        lineHeight: "1.7",
        fontSize: "20px",
      },
    },
    {
      type: "crud",
      syncLocation: false,
      name: "tab-s",
      api: {
        method: "post",
        url: `coo/con/page`,

        requestAdaptor: (rest) => {
          // console.log(rest, 'rest');
          let { gmtCreate, ...other } = rest.data;
          // console.log(rest.data.housesIds, 'rest.data.housesIds');
          let datas = {
            ...other,
            startTimeStamp: rest.data.gmtCreate?.split(",")[0] * 1000,
            endTimeStamp: rest.data.gmtCreate?.split(",")[1] * 1000,
          };
          return {
            ...rest,
            data: datas,
          };
        },
        data: {
          clientName: "${clientName}",
          phone: "${phone}",
          enterpriseName: "${enterpriseName}",
          followStatus: "${followStatus}",
          pageNum: "${page}",
          pageSize: "${perPage}",
          gmtCreate: "${gmtCreate}",
        },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: "table",
      //列表
      columns: [
        {
          name: "enterpriseName",
          label: "企业名称",
          type: "text",
        },
        {
          name: "clientName",
          label: "姓名",
          type: "text",
        },
        {
          name: "jobTitle",
          label: "职务",
          type: "text",
        },
        {
          name: "cooperationAppeal",
          label: "合作诉求",
          type: "text",
        },
        {
          name: "phone",
          label: "联系方式",
          type: "text",
        },
        {
          name: "gmtCreate",
          label: "提交时间",
          type: "date",
          valueFormat: "x",
        },
        {
          name: "followStatus",
          label: "跟进状态",
          type: "text",
        },
        {
          name: "followMan",
          label: "跟进人",
          type: "text",
        },
        {
          name: "gmtModify",
          label: "跟进时间",
          type: "date",
          valueFormat: "x",
        },
        {
          type: "operation",
          label: "操作",
          width: 200,
          buttons: [
            {
              label: "查看",
              type: "button",
              // "actionType": "link",
              // "link": "#/workbench/cooperative/follow?id=$id",
              onClick: (e, item) => {
                handleButtonClick(
                  `/workbench/cooperative/follow?id=${item?.data?.id}`
                );
              },
            },
            {
              type: "button",
              label: "跟进",
              actionType: "dialog",
              dialog: {
                title: "跟进",
                actions: [
                  {
                    type: "button",
                    actionType: "cancel",
                    label: "取消",
                    level: "default",
                    primary: true,
                  },
                  {
                    label: "确认",
                    actionType: "confirm",
                    primary: true,
                    //"reload": "tab-s",
                    close: true,
                    type: "button",
                    api: {
                      method: "post",
                      url: `coo/pro/add`,
                      messages: {
                        success: "操作成功！",
                      },

                      data: {
                        cooperationId: "$id",
                        note: "$note",
                      },
                    },
                  },
                ],
                body: [
                  {
                    type: "form",
                    body: [
                      {
                        name: "note",
                        type: "textarea",
                        label: "跟进描述",
                        required: true,
                        showCounter: true,
                        maxLength: 300,
                        placeholder: "添加跟进记录,不超过300字",
                      },
                    ],
                  },
                ],
              },
            },
          ],
        },
      ],
      alwaysShowPagination: true,
      // "autoFillHeight": false,
      autoFillHeight: {
        height: 450,
      },
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      //筛选
      filter: {
        title: "",
        submitText: "搜索",
        controls: [
          {
            type: "text",
            name: "enterpriseName",
            size: "md",
            label: "企业名称",
            placeholder: "请输入企业名称",
          },
          {
            type: "text",
            name: "clientName",
            size: "md",
            label: "姓名",
            placeholder: "请输入姓名",
          },
          {
            type: "text",
            name: "phone",
            size: "md",
            label: "联系方式",
            placeholder: "请输入联系方式",
          },
          {
            type: "input-date-range",
            name: "gmtCreate",
            size: "md",
            label: "联系时间",
          },

          {
            type: "select",
            name: "followStatus",
            size: "md",
            label: "跟进状态",
            placeholder: "请选择状态",
            value: "",
            options: [
              { label: "全部", value: "" },
              { label: "未跟进", value: "未跟进" },
              { label: "已跟进", value: "已跟进" },
            ],
          },
          {
            type: "reset",
            label: "重置",
          },
          {
            type: "submit",
            label: "搜索",
            primary: true,
          },
        ],
        onSubmit: "reload",
        actions: [
          /*           {
                      "type": "button",
"size": "md",
                      "label": "一键导出",
                      "actionType": "download",
                      "api": `/enterprise/business/downloadTemplate`
                    } */
        ],
      },
    },
  ],
};
</script>

<template>
  <div class="publicTableStyle">
    <AmisComponent :amisjson="amisjson2" />
  </div>
</template>
<style scoped lang="scss">
.tab {
  padding-top: 20px;
  padding-left: 10px;
}

.on {
  background-color: #169bd5;
  color: #fff;
}
</style>

