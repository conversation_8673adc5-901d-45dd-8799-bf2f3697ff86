  .menuTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;

  }

  .addBtn {
      margin-bottom: 0px !important;
  }

  .letTab {
      padding-bottom: 20px;
  }

  .searchTab {
      display: flex;
      justify-content: space-between;
      align-items: center;
  }

  .rightSearch {
      //   padding: 0 10px 0 20px;
  }

  .dialog-footer {
      float: right;
      margin-right: 60px;
  }

  .pickers {
      width: 300px;
  }

  .ye {
      position: absolute;
      bottom: 20px;
      right: 10px;
  }

  .custom-tree-node {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 14px;
      padding-right: 20px;

      .el-icon-plus {
          margin-right: 10px;
      }
  }

  //   .jurisdiction {
  //       .operation {
  //           span {
  //               &:first-child {
  //                   font-weight: 600;
  //                   color: #1D2129;
  //                   display: block;

  //                   font-size: 16px;
  //               }
  //           }
  //       }
  //   }



  ::v-deep {
      .deptTree {
          height: calc(100% - 35px);
          background: #FAFCFF;
          padding: 10px 8px 0;
          border-radius: 6px;
      }
  }


  .menu {
      span {
          &:first-child {
              font-weight: 600;
              color: #1D2129;
              display: block;
              //   padding-bottom: 20px;
              font-size: 16px;
              min-width: 80px;
          }
      }
  }

  .filterTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-bottom: 10px;

      &:first-child {
          font-size: 16px;
          font-weight: 600;
          color: #1D2129;
      }

      .reset {
          font-size: 14px;
          color: #3370FF;
          cursor: pointer;
      }
  }

  .searchPopover {
      border-radius: 10px;
      padding: 16px;
  }

  .checks {
      background: #FAFCFF;
      border-radius: 4px 4px 4px 4px;
      padding: 16px;
      display: flex;
      flex-wrap: nowrap;

      .checksList {

          &:first-child {
              border-right: 1px solid #E6EBF0;
              width: 48%;
          }

          &:last-child {
              padding-left: 5%;
              width: 52%;
          }
      }

      .title {

          font-size: 12px;
          font-weight: 400;
          color: #3F4A59;
          margin-bottom: 8px
      }
  }



  .moreBtn {
      margin-left: 10px;
  }

  .filterBtn {
      font-size: 14px;
      color: #3F4A59;

      .filter {
          color: #3F4A59;
      }
  }

  .deleteUserDisabled {
      color: #C0C4CC;
      cursor: not-allowed;
  }

  .deleteUser {
      color: #FC474C;
  }

  .operation {
    height: 100%;
    overflow: scroll;
    // border-top: 1px solid #f2f3f4;
    //   height: 55px;
    //   margin-bottom: 10px
  }