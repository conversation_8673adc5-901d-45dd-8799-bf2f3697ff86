<template>
  <div ref="msMain" class="vue-ms-main vue-ms-main-sel personal personalMargin">
    <el-card class="box-card">
      <div class="formCard">
        <el-form
          ref="form"
          :model="form"
          :inline="true"
          label-position="right"
          label-width="120px"
          size="default"
        >
          <div class="title">
            <span style="font-size: 20px"
              class="el-icon-edit"
              >账户信息
              <el-icon
                :size="22"
                v-if="isredact"
              
                @click="compile"
                style="
                  color: #3370ff;
                  cursor: pointer;
                  margin-left: 10px;
                  padding-top: 5px;
                "
              >
                <Edit /> </el-icon
            ></span>
          </div>
          <div class="formContent">
           
              <el-form-item label="姓名：">
                <span v-if="isredact" class="labelText">{{
                  form.realName
                }}</span>
                <el-input v-else v-model="form.realName" :disabled="isredact" />
              </el-form-item>
              <el-form-item label="手机号：">
                <span class="labelText">{{ form.mobile }}</span>
              </el-form-item>
            </div>
          
          <div class="saveBtn">
            <el-form-item v-if="!isredact">
              <el-button size="default" class="btn" @click="resetField">
                取消
              </el-button>
              <el-button
                size="default"
                :disabled="isredact"
                class="btn"
                type="primary"
                @click="preserve"
              >
                保存
              </el-button>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </el-card>
  </div>
</template>
<script>
import { getAccountAPI, editAccountInfoAPI } from '@/api/userset';
// import cloneDeep from "lodash/cloneDeep";
export default {
  name: 'AccountInformation',
  components: {},
  filters: {},
  data() {
    return {
      isredact: true,
      isDrawer: false,
      resetFields: {},
      form: {
        realName: '',
        mobile: '',
      },
      usercontent: {},
      rowId: '',
      total: '',
    };
  },
  created() {
    this.personalDetails();
  },
  methods: {
    resetField() {
      this.isredact = !this.isredact;
      this.form = this.resetFields;
    },
    // 编辑
    compile() {
      this.isredact = !this.isredact;
      // let data = cloneDeep(this.form);
      this.resetFields = this.form;
    },
    // 保存
    async preserve() {
      await editAccountInfoAPI({
        id: this.usercontent.id,
        realName: this.form.realName,
      });
      this.$message.success('个人信息编辑成功');
      this.isredact = true;
    },
    // 回显
    async personalDetails() {
      const res = await getAccountAPI();
      this.usercontent = res.result;
      this.form.realName = this.usercontent.realName; //姓名
      this.form.mobile = this.usercontent.mobile; //手机号
    },
  },
};
</script>

<style scoped lang="scss">
// @import "~@/styles/variables.scss";
#app1 .editBtn.el-button.is-circle {
  margin-left: 10px;
  border: 0px;
  border-radius: 20px !important;
}

.listBtn {
  margin-left: 10px;
  margin-top: -2px;
  border: 0px !important;
  border-radius: 20px;
  padding: 5px 8px;
}
</style>
<style lang="scss">
.personal {
  // margin-top: 55px;
  .resourceNum.labelText {
    display: inline;
    span {
      padding: 0px 50px;
    }
  }
  .labelText {
    width: 200px;
    display: block;
    padding: 0 0.9375rem;
    // margin-right: 80px;
    color: #1d2129;
  }

  .el-input {
    width: 200px;
    // margin-right: 60px;
  }
  .el-form-item {
    margin-bottom: 10px !important;
  }
  .el-form-item__label {
    font-weight: 400;
    color: #3f4a59;
  }

  .el-form {
    width: 100%;
  }

  .title {
    height: 30px;
    margin-bottom: 16px;

    span {
      line-height: 30px;
      font-size: 16px;
      font-weight: 600;
      color: black;
    }
  }

  .formContent {
    padding-top: 10px;
    background: #fafcff;
    border-radius: 6px 6px 6px 6px;
    margin-bottom: 8px;
    display: flex;
    flex-wrap: wrap;
    flex-direction: column;
  }

  .formCard {
    width: 100%;
  }

  .saveBtn {
    margin-top: 20px;
    width: 100%;
    display: flex;
    padding-left: 120px;
    justify-content: flex-start;
  }
  .box-card {
    margin-bottom: 16px;
  }
}
.el-icon-edit {
  font-size: 24px;
  align-items: start;
  display: flex;
  justify-content: left;
}
</style>
