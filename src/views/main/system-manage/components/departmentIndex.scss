.el-card__body,
.menu,
.tree,
.bmtree,
.el-tree {
  width: 100%;
  height: 100%;
}


.jurisdiction {
  height: calc(100vh - 80px);
  display: flex;
  justify-content: space-between;
}

.box-card1 {
  height: 99%;
  width: 15%;
  overflow-x: scroll;
  margin: 20px 0 20px 20px;

}

.box-card2 {
  height: 99%;
  width: 85%;
    margin: 20px 0 20px 20px;
}

.el-message-box__title,
.el-drawer__header {
  font-weight: 500;
  padding: 10px 24px !important;
  margin: 0 !important;
}

.el-drawer__header {
  color: #000 !important;
  font-size: 16px !important;
  border-bottom: 1px solid #E6EBF0;
}


.el-tree {
  width: 100%;
  height: 100%;
}

.el-tree {

  background: #FAFCFF !important;
  border-radius: 6px 6px 6px 6px;

  .el-tree-node__content {
    height: 32px;
    padding-right: 10px;
    color: #1D2129;

    &:hover {
      background: #E6EBF0;
      border-radius: 4px;
    }

  }

  .is-current,
  .is-checked {
    >.el-tree-node__content {
      color: #3370FF;
      background: #e6eeff !important;
      border-radius: 4px 4px 4px 4px;
    }

  }
}

.el-dialog__header {
  font-weight: 500;
}

.uni-drawer__content {
  height: 90vh;
  overflow-y: scroll;
  padding: 20px 20px;
  background: #FFF;
}

.checkboxGroup {
  .el-checkbox-group {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;

    label {
      width: 100%;
      margin-bottom: 10px
    }
  }
}
.tableStatus {
  display: flex;
  justify-content: center;
  align-items: center;

  div {
      width: 5px;
      height: 5px;
      border-radius: 5px;
      margin-right: 5px;
  }

  .status1 {

    background: #19B21E;
}

.status2,
.status3 {

    background: #FC474C;
}
}

.el-table__fixed-right::before{
  background-color: transparent !important;
}

.split {
  display: flex;
  flex-direction: row;
}

.gutter {
  background-repeat: no-repeat;
  background-position: 50%;
}

.gutter.gutter-horizontal {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+bMfxAGAgYYmwGrIIiDjrELjpo5aiZeMwF+yNnOs5KSvgAAAABJRU5ErkJggg==');
  cursor: col-resize;
}
.pagination{
  margin-top: 16px;
}