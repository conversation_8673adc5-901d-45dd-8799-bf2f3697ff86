<template>
  <div
    ref="msMain"
    class="vue-ms-main vue-ms-main-sel personalPassWorld passwordSetting personalMargin"
  >
    <!-- <table-layout :tab-name-list="['密码设置']">
      <template slot="selBtn" />
    </table-layout> -->
    <el-card class="box-card">
      <div class="formCard">
        <el-form
          ref="form"
          label-position="right"
          :model="ruleForm"
          :inline="true"
          :rules="rules"
          label-width="120px"
          class="demo-ruleForm"
            size="default"
        >
          <div class="title">
            <span>密码设置</span>
          </div>
          <div class="formContent">
            <div>
              <el-form-item label="原密码" prop="oldPassword" >
                <el-input
                  v-model="ruleForm.oldPassword"
                  type="password"
                  placeholder="请输入原密码"
                  autocomplete="off"
                
                />
              </el-form-item>
            </div>
            <div>
              <el-form-item label="新密码" prop="newPassword">
                <el-input
                  v-model="ruleForm.newPassword"
                  type="password"
                  placeholder="请输入新密码,新密码8-20位,包含字母数字"
                  autocomplete="off"
                />
              </el-form-item>
            </div>
            <div>
              <el-form-item label="确认密码" prop="confirmNewPassword">
                <el-input
                  v-model="ruleForm.confirmNewPassword"
                  type="password"
                  autocomplete="off"
                  placeholder="请再次输入新密码,新密码8-20位,包含字母数字"
                />
              </el-form-item>
            </div>
          </div>
          <div class="saveBtn">
            <el-form-item>
              <el-button size="default" class="btn" @click="resetField">
                取消
              </el-button>
              <el-button
                size="default"
                class="btn"
                style="float: right"
                type="primary"
                @click="confirm"
              >
                确定
              </el-button>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </el-card>
  </div>
</template>
  
  <script>
// import TableLayout from "@/common/components/table-layout";
import { encryptionAPI, setPasswordAPI } from "@/api/user";
import { JSEncrypt } from "jsencrypt";
export default {
  name: "PasswordSetting",
  components: {
    // TableLayout
  },
  data() {
    var validatePass = (rule, value, callback) => {
      var passwordreg = /(?=.*\d)(?=.*[a-zA-Z]).{8,20}/;
      if (value === "") {
        callback(new Error("请输入密码"));
      } else if (!passwordreg.test(value)) {
        callback(new Error("密码8-20位,包含字母数字"));
      } else {
        if (this.ruleForm.confirmNewPassword !== "") {
          this.$refs.form.validateField("confirmNewPassword");
        }
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.ruleForm.newPassword) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      ruleForm: {
        oldPassword: "",
        newPassword: "",
        confirmNewPassword: "",
      },
      rules: {
        oldPassword: [
          { required: true, message: "原密码不能为空", trigger: "blur" },
        ],
        newPassword: [
          { required: true, message: "密码不能为空", trigger: "blur" },
          { min: 8, message: "密码需要八位以上", trigger: "blur" },
          { validator: validatePass, trigger: "blur" },
        ],
        confirmNewPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          { validator: validatePass2, trigger: "blur" },
        ],
      },
      i: 60,
      verify: true,
      result: "",
      resetFields: {},
    };
  },
  created() {
    this.encryption();
  },
  methods: {
    resetField() {
      this.$refs["form"].resetFields();
    },
    async encryption() {
      const res = await encryptionAPI();
      this.result = res.result;
    },
    async confirm() {
      let encrypt = new JSEncrypt();
      encrypt.setPublicKey(this.result);
      const oldPassword = encrypt.encrypt(this.ruleForm.oldPassword);
      const newPassword = encrypt.encrypt(this.ruleForm.newPassword);
      const confirmNewPassword = encrypt.encrypt(
        this.ruleForm.confirmNewPassword
      );
      await this.$refs.form.validate();
      // 更改密码
      await setPasswordAPI({
        oldPassword,
        newPassword,
        confirmNewPassword,
        //oldPassword:this.ruleForm.oldPassword,
        //newPassword:this.ruleForm.newPassword,
        //confirmNewPassword:this.ruleForm.confirmNewPassword,
      });
      this.$message.success("修改密码成功！");
      this.ruleForm = {
        oldPassword: "",
        newPassword: "",
        confirmNewPassword: "",
      };
    },
  },
};
</script>
 <style  lang="scss">
.passwordSetting.personalPassWorld {
  .el-input {
    width: 400px !important;
    // margin-right: 60px;
  }
  .saveBtn {
    margin-top: 20px;
    width: 100%;
    display: flex;
    margin-left: 120px;
    //justify-content: center;
  }
  .title {
    height: 30px;
    margin-bottom: 16px;

    span {
      line-height: 30px;
      font-size: 20px;
      font-weight: 600;
      color: black;
    }
  }
}
</style>