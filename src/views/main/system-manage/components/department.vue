<template>
  <div>
    <div class="jurisdiction">
      <el-card id="leftDiv" class="box-card1">
        <div class="menu">
          <div class="menuTitle letTab">
            <span>组织架构</span>
          </div>
          <DeptTree
            ref="deptTreeRef"
            type="1"
            :tree-id="treeId"
            :orgCode="orgCode"
            :user-type="userType"
            @gaindeptId="gaindeptId"
          />
        </div>
      </el-card>
      <el-card id="rightDiv" class="box-card2">
        <div class="operation">
          <!-- <span>人员列表</span> -->
          <div class="menuTitle">
            <div>
              <el-button
                size="default"
                v-if="resourceCode?.includes('UserManagementUserAdd')"
                class="btn addBtn"
                type="primary"
                @click="addUser"
              >
                <template #icon><Plus /></template>
                新增用户
              </el-button>
            </div>
            <div class="searchTab">
              <el-input
                v-model.trim="searchKey"
                placeholder="请输入内容"
                prefix-icon="el-icon-search"
                clearable
                @input="inputChange"
              />
              <!-- <div class="rightSearch">
                <el-popover
                  width="310"
                  trigger="click"
                  placement="bottom-start"
                  class="searchPopover"
                >
                  <div>
                    <div class="filterTitle">
                      <div>筛选</div>
                      <div class="reset" @click="reset">重置筛选条件</div>
                    </div>
                    <div class="checks">
                      <div class="checksList">
                        <div class="title">账号状态</div>
                        <div class="checkboxGroup">
                          <el-checkbox-group
                            v-model="statusChecked"
                            @change="handleSelectStatus"
                          >
                            <el-checkbox label="1"> 正常 </el-checkbox>
                            <el-checkbox label="2"> 禁用 </el-checkbox>
                            <el-checkbox label="3"> 过期 </el-checkbox>
                          </el-checkbox-group>
                        </div>
                      </div>
                      <div class="checksList">
                        <div class="title">角色</div>
                        <div class="checkboxGroup" v-if="rolesListFilter">
                          <el-checkbox-group
                            v-model="roleChecked"
                            @change="handleSelectRole"
                          >
                            <el-checkbox
                              v-for="item in rolesListFilter"
                              :key="item.id"
                              :label="item.id"
                            >
                              {{ item.roleName }}
                            </el-checkbox>
                          </el-checkbox-group>
                        </div>
                      </div>
                    </div>
                  </div>
                  <template #reference>
                    <el-button size="default"  class="filterBtn" type="text">
                      <svg-icon class-name="filter" icon-class="filter" />
                      筛选
                    </el-button>
                  </template>
                </el-popover>
              </div> -->
            </div>
          </div>
          <el-table
            v-loading="listLoading"
            :data="tableData"
            :row-key="getRowKeys"
            tooltip-effect="dark"
            size="lg"
            style="width: 100%, overflow-y: scroll;display: block;margin-top: 10px;  "
            @selection-change="handleSelectStockChange"
          >
            <el-table-column
              prop="realName"
              align="center"
              label="姓名"
              fixed="left"
              width="200"
            />
            <el-table-column
              prop="username"
              label="账号"
              align="center"
              width="200"
            />
            <el-table-column
              prop="roleName"
              width="200"
              align="center"
              label="账户角色"
            />

            <el-table-column
              prop="accountStatus"
              label="账号状态"
              align="center"
              v-if="resourceCode?.includes('UserManagementResetStatus')"
            >
              <template v-slot="{ row }">
                <el-switch
                  v-model="row.status"
                  active-value="1"
                  inactive-value="0"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  :before-change="() => changeswitch(row)"
                />
              </template>
            </el-table-column>
            <el-table-column
              prop="accountStatus"
              label="账号状态"
              align="center"
              v-else
            >
              <template v-slot="{ row }">
                <div class="tableStatus">
                  <div :class="'status' + row.accountStatus" />
                  {{ ['', '正常', '禁用', '过期'][row.accountStatus] }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="gmtCreate"
              width="200"
              align="center"
              label="账号创建时间"
            />

            <el-table-column
              align="center"
              label="操作"
              :width="250"
              fixed="right"
            >
              <template v-slot="{ row }" >
                <el-button
class="btnSize"
                  type="primary"
                  v-if="resourceCode?.includes('UserManagementUserEdit')"
                  @click="allocation(row)"
                >
                  编辑
                </el-button>
                <el-button
                class="btnSize"
                  type="primary"
                  v-if="resourceCode?.includes('UserManagementResetPassword')"
                  @click="resetPasswordsFn(row)"
                >
                  重置密码
                </el-button>
                <el-button
             class="btnSize"
                  type="danger"
                  v-if="resourceCode?.includes('UserManagementUserDelete')"
                  @click="userdeleted(row)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
            <template #empty>
              <el-empty
                class="emptyCustomize"
                :image-size="50"
                :image="empty"
              />
            </template>
          </el-table>
          <div v-if="tableData?.length > 0" class="pagination">
            <el-pagination
              size="default"
              :current-page="pageSize.pageNum"
              :page-sizes="[5, 10, 20, 50]"
              :page-size="pageSize.pageSize"
              :total="+total"
              layout="sizes,total,  prev, pager, next, jumper"
              @size-change="pageSizeChange"
              @current-change="pageNumberChange"
            />
          </div>
        </div>
      </el-card>
    </div>
    <div>
      <AddUser
        v-if="dialogFormVisible"
        :dialogFormVisible="dialogFormVisible"
        @update:dialogFormVisible="dialogFormVisible = $event"
        @userList="userList"
      />
      <EditUser
        v-if="editUser"
        :edit-info="editInfo"
        :row-id="rowId"
        :edit-user="editUser"
        @update:edit-user="editUser = $event"
        @userList="userList"
      />
    </div>
    <ResetPassword
      :id="rowId"
      :dialog-visible="dialogPassWorldVisible"
      @cancelPasswordsFn="cancelPasswordsFn"
    />
  </div>
</template>

<script>
import DeptTree from './component/deptTree.vue';
import {
  batchUpdateDeptAPI,
  queryByDeptIdAPI,
  listRolesByOrgCodeAPI,
  updateAIUserStatus,
} from '@/api/user';
import { Plus } from '@element-plus/icons-vue';
import { userPageAPI, userdeletedAPI } from '@/api/userset';
import empty from '@/assets/images/empty.png';
import AddUser from './component/adduser.vue';
import EditUser from './component/edituser.vue';
import ResetPassword from './component/resetPassword.vue';
import debounce from 'lodash/debounce';

export default {
  name: 'user',
  components: {
    DeptTree,
    AddUser,
    EditUser,
    ResetPassword,
    Plus,
  },
  data() {
    return {
      userType: '',
      options: [],
      tableData: [],
      deptList: [],
      optionProps: {
        value: 'id',
        label: 'userDeptName',
        children: 'childDeptList',
        checkStrictly: true,
      },
      type: 1,
      pageSize: {
        pageNum: 1,
        pageSize: 10,
      },
      rules: {
        value: [{ required: true, message: '目标部门必选', trigger: 'blur' }],
      },
      total: 0,
      multipleSelection: [],
      centerDialogVisible: false,
      form: {
        value: [],
      },
      listLoading: false,
      treeId: [],
      empty,
      dialogFormVisible: false,
      rowId: '',
      editUser: false,
      dialogPassWorldVisible: false,
      user: null,
      editInfo: {},
      // rolesList: [],
      // userDeptList: [],
      industryVisible: false, //产业链配置
      searchKey: '',
      selectKey: '',
      chainList: [],
      roleChecked: [],
      statusChecked: [],
      editState: 'add',
      rolesListFilter: [], //角色筛选 右上角
      orgCode: '', //机构code
      orgCodeInfo: {}, //机构信息
      changeState: '', //使用设置or 产业链配置
      industryTitle: '',
      resourceCode: [],
    };
  },

  watch: {
    async centerDialogVisible(value) {
      if (value) {
        const res = await queryByDeptIdAPI({
          deptId: this.treeId,
        });
        this.deptList = res;
      } else {
        this.deptList = [];
      }
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    async changeswitch(row) {
      const text = row.status == 0 ? '启用' : '禁用';
      try {
        await this.$confirm(`确定${text}该用户？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        });
        await updateAIUserStatus({
          uid: row.id,
          status: row.status == 0 ? 1 : 0,
        });
        this.$message.success('操作成功!');
        return true;
      } catch (err) {
        return false;
      }
    },
    init() {
      let resourceCode = JSON.parse(
        localStorage.getItem('resourceCode') || '[]'
      );
      this.resourceCode = resourceCode;

      let org = JSON.parse(localStorage.getItem('userInfo') || '{}');
      this.orgCodeInfo = org;
      this.orgCode = org?.orgCode;
      this.getRoleList();
    },
    getRoleList() {
      listRolesByOrgCodeAPI({ orgCode: this.orgCode }).then((rs) => {
        this.rolesListFilter = rs.result;
      });
      this.userList();
    },
    // 获取列表 用户
    async userList() {
      try {
        this.loading = true;
        let data = {
          pageNum: this.pageSize.pageNum,
          pageSize: this.pageSize.pageSize,
          keyword: this.searchKey,
        };
        let res = await userPageAPI(data);
        if (this.pageSize.pageNum != 1 && res.result.records.length == 0) {
          this.pageSize.pageNum--;
          this.userList();
          return;
        }
        this.tableData = res.result.records;
        this.total = Number(res.result.total);
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    // 部门列表
    // listAllChildDeptUsersAPI() {
    //   deptUserList().then(res => {
    //     this.userDeptList = res
    //   })
    // },
    // // 角色列表
    // rolesAPI() {
    //   rolesAPI().then(res => {
    //     this.rolesList = res.result;
    //   })
    // },
    // 新增
    async addUser() {
      this.dialogFormVisible = true;
      this.editState = this.userType === 'admin' ? 'add' : 'edit';
    },
    // 编辑部门
    async allocation(e) {
      this.editUser = true;
      this.rowId = e.id;
      this.user = e;
    },
    // 删除
    async userdeleted(row) {
      this.$confirm('是否确认删除用户？', '删除用户', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: '',
      }).then(async () => {
        await userdeletedAPI({
          id: row.id,
        });
        // 刷新产业链
        this.userList();
        this.$message({
          type: 'success',
          message: '删除用户成功!',
        });
      });
    },
    // 重置密码
    async resetPasswordsFn(e) {
      this.rowId = e.id;
      this.dialogPassWorldVisible = true;
      this.user = e;
    },
    setIndustryVisible() {
      this.industryVisible = false;
    },
    cancelPasswordsFn() {
      this.dialogPassWorldVisible = false;
    },
    getRowKeys(row) {
      return row.id;
    },
    // 组织架构id 查询人员
    async gaindeptId(value) {
      this.treeId = value;
      this.pageSize.pageNum = 1;
      this.pageSize.pageSize = 10;
      this.multipleSelection = [];
      this.userList();
    },
    handleSelectStockChange(val) {
      let stockSelectlist = [];
      val.forEach((el) => {
        stockSelectlist.push(el.id);
      });
      this.multipleSelection = stockSelectlist;
    },
    close() {
      // $emit(this, 'close', 1)
    },
    // 确认调整部门
    async transfer() {
      await this.$refs.form.validate();
      let arr = [];
      arr[0] = this.multipleSelection;
      let fromValue = this.form.value.pop();
      await batchUpdateDeptAPI({
        deptId: fromValue,
        userIds: arr,
      });
      this.$message.success('调整部门成功');
      this.cancel();
      this.gaindeptId(this.treeId);
    },
    // 取消调整部门
    cancel() {
      this.form.value = [];
      this.centerDialogVisible = false;
    },
    // 调整部门
    changeConfig(e, state) {
      this.industryVisible = true;
      this.user = e;
      this.changeState = state;
      // this.multipleSelection = row
      // this.centerDialogVisible = true;
    },
    pageSizeChange(e) {
      this.pageSize.pageSize = e;
      this.pageSize.pageNum = 1;
      this.userList();
    },
    pageNumberChange(e) {
      this.pageSize.pageNum = e;
      this.userList();
    },
    reset() {
      this.pageSize.pageNum = 1;
      this.pageSize.pageSize = 10;
      this.roleChecked = [];
      this.statusChecked = [];
      // 获取ueserList
      this.userList();
    },
    handleSelectRole(e) {
      this.roleChecked = e;
      // 获取ueserList

      this.userList();
    },
    handleSelectStatus(e) {
      this.statusChecked = e;
      // 获取ueserList
      this.userList();
    },
    inputChange(e) {
      this.searchKey = e;
      this.pageSize.pageNum = 1;
      this.pageSize.pageSize = 10;
      // 获取ueserList
      this.onSearch();
    },
    // 定义一个防抖的函数进行请求接口
    onSearch: debounce(function () {
      // 请求接口
      // console.log(this.searchKey,'debounce')
      this.userList();
    }, 1000),
  },
};
</script>

<style scoped lang="scss">
@import './variables.scss';
@import './department.scss';
.btnSize{
  height: 29px;
}
.pagination{
  float: right;
}
</style>

<style lang="scss">
.emptyCustomize {
  .el-empty__description {
    font-size: 16px;
    font-weight: 600;
    color: #86909c;
    line-height: 24px;
    margin-top: 0;
  }
}
@import './departmentIndex.scss';
</style>
