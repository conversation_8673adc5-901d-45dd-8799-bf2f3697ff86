<template>
  <div>
    <el-drawer
      size="30%"
      title="新增用户"
      :model-value="dialogFormVisible"
      :close-on-click-modal="false"
      :wrapper-closable="false"
      @close="cancel"
    >
      <div class="uni-drawer__content">
        <div class="baseInfo">
          <div class="baseInfoTitle">基本信息</div>
          <div class="cards">
            <el-form
              ref="form"
              label-position="left"
              :model="form"
              :rules="rules"
              label-width="110px"
            >
              <el-form-item prop="realName" label="姓名">
                <el-input v-model="form.realName" placeholder="请输入姓名" />
              </el-form-item>
              <el-form-item prop="mobile" label="账号（手机）">
                <el-input
                  v-model="form.mobile"
                  placeholder="请输入联系人手机号码"
                  class="miniInput"
                />
              </el-form-item>
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="form.email" placeholder="请输入邮箱" />
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="baseInfo">
          <div class="baseInfoTitle">部门信息</div>
          <div class="cards">
            <el-form
              ref="departmentForm"
              label-position="left"
              :model="departmentForm"
              class="form"
              :rules="departmentRules"
              label-width="110px"
            >
              <el-form-item
                label="部门名称"
                prop="deptIds"
                class="appendToBodyFalse"
              >
                <el-cascader
                  v-model="departmentForm.deptIds"
                  :append-to-body="false"
                  placeholder="请选择部门"
                  :options="deptListFn"
                  :props="optionProps"
                  class="cascaderList"
                />
              </el-form-item>

              <el-form-item
                label="角色"
                prop="roleType"
                class="appendToBodyFalse"
              >
                <el-select
                  v-model="departmentForm.roleType"
                  placeholder="请选择"
                  style="width: 90px"
                  :popper-append-to-body="false"
                  disabled
                  v-show="false"
                >
                  <el-option
                    v-for="item in roleTypeList"
                    :key="item.id"
                    :label="item.roleName"
                    :value="item.id"
                  />
                </el-select>
                <el-form-item
                  label=""
                  prop="roleIds"
                  class="appendToBodyFalse roleIds"
                >
                  <el-select
                    v-model="departmentForm.roleIds"
                    multiple
                    placeholder="请选择"
                    class="cascaderList"
                    :popper-append-to-body="false"
                  >
                    <el-option
                      v-for="item in rolesList"
                      :key="item.id"
                      :label="item.roleName"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-form-item>

              <el-form-item label="职位">
                <el-input
                  v-model="departmentForm.position"
                  placeholder="请输入职位"
                />
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
      <div class="demo-drawer__footer">
        <el-button size="default" @click="cancel"> 取 消 </el-button>
        <el-button size="default" class="btn" type="primary" @click="preserve">
          确 认
        </el-button>
      </div>
    </el-drawer>
  </div>
</template>
  
<script>
import { getAccountInfoAPI } from "@/api/userset";
import {
  listDeptByOrgCodeAPI,
  listRolesByOrgCodeAPI,
  getListBasicRoles,
} from "@/api/user";

import cloneDeep from "lodash/cloneDeep";
export default {
  name: "AddUser",
  components: {},
  props: {
    dialogFormVisible: {
      type: Boolean,
      default: false,
    },
    compileEcho: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      form: {
        realName: "",
        mobile: "",
        email: "",
      },
      departmentForm: {
        deptIds: "",
        roleType: "1", //角色
        roleIds: [], //角色
        position: "",
      },
      // optionProps: [],
      activeName: "first",
      addindustry: "",
      optionProps: {
        value: "id",
        label: "userDeptName",
        children: "childDeptList",
        checkStrictly: true,
      },
      departmentRules: {
        deptIds: [{ required: true, message: "部门名称必选", trigger: "blur" }],
        roleIds: [{ required: true, message: "角色必选", trigger: "blur" }],
        roleType: [{ required: true, message: "角色必选", trigger: "blur" }],
      },
      rules: {
        realName: [
          { required: true, message: "姓名不能为空", trigger: "blur" },
        ],

        email: [
          {
            pattern:
              /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
            message: "邮箱格式不符合规则",
            trigger: "blur",
          },
        ],
        mobile: [
          { required: true, message: "手机号码不能为空", trigger: "blur" },
          {
            pattern: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/,
            message: "手机号不符合规则",
            trigger: "blur",
          },
        ],
      },
      deptListFn: [],
      autoList: [],
      rolesList: [],
      roleTypeList: [],
      addUserUni: [],
      addUserUniLength: 0,
    };
  },
  watch: {
    async addindustry(value) {
      //console.log(value);
      if (!value) {
        if (this.form.orgCode) {
          const res = await listDeptByOrgCodeAPI({
            orgCode: this.form.orgCode,
          });
          this.deptListFn = this.getTreeData(res.result);
          this.form.deptId = "";
        }
      }
    },
  },
  created() {
    this.orgCode = JSON.parse(
      localStorage.getItem("userInfo") || "{}"
    )?.orgCode;
    this.departmentList();
    this.getRoles();
  },
  methods: {
    async getRoles() {
      let res = await getListBasicRoles();
      this.roleTypeList = res.result;
    },

    getTreeData(data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].childDeptList.length < 1) {
          data[i].childDeptList = undefined;
        } else {
          this.getTreeData(data[i].childDeptList);
        }
      }
      return data;
    },
    handleClick() {},
    async departmentList() {
      const res = await listDeptByOrgCodeAPI({
        orgCode: this.orgCode || null,
      });
      this.deptListFn = this.getTreeData(res.result);
      const arr = await listRolesByOrgCodeAPI({
        orgCode: this.orgCode || null,
      });
      this.rolesList = arr.result;
    },
    cancel() {
      this.$emit("update:dialogFormVisible", false);
    },
    async preserve() {
      //  this.$refs.addUserUni0.formUni
      this.addUserUni.map((e) => {
        //console.log(e, this.$refs);
        // this.$refs[`addUserUni${e.index}`][0].validate()
        this.$refs[`addUserUni${e.index}`][0].validateForm();
        // [0].$refs?.[`addUserUni${e.index}`].validate()
      });
      // //console.log(this.$refs, this.$refs.addUserUni0[0].$refs.formUni.validate())
      await this.$refs.form.validate();
      await this.$refs.departmentForm.validate();

      try {
        let { email, mobile, realName } = this.form;
        let { deptIds, roleIds, roleType, position } = this.departmentForm;
        let deptId = cloneDeep(deptIds);
        // //console.log(deptIds,deptId,'deptIds')

        let saveData = {
          appId: 15,
          realName,
          username: mobile,

          email,
          relations: [
            {
              orgCode: JSON.parse(localStorage.getItem("userInfo") || "{}")
                ?.orgCode,
              deptId: Array.isArray(deptIds) ? deptId.pop() : deptIds,
              basicRoleId: roleType,
              position,
              roleIds,
            },
          ],
        };
        await getAccountInfoAPI(saveData);
        this.$emit("userList");
        this.cancel();
        this.$message.success("新增用户成功");
        // this.$emit("industryList");
      } catch (error) {
        //console.log(error);
      }
    },
    async department() {
      //this.$emit('departmentFn',this.form.orgCode)
      this.addindustry = true;
    },
  },
};
</script>
  
<style scoped lang="scss">
.demo-drawer__footer {
  z-index: 9999;
}

.uni-drawer__content {
  height: 80vh;
  overflow-y: scroll;
  padding: 0 20px 20px;
  background: #fff;
  margin-bottom: 61px !important;
  // margin: 20px;
}

.baseInfo {
  .baseInfoTitle {
    font-size: 14px;
    font-weight: 600;
    color: #1d2129;
    padding: 25px 16px;
  }
}

.cards {
  background: #fafcff;
  border-radius: 6px 6px 6px 6px;
  border: 1px solid rgba(51, 112, 255, 0.1);
  padding: 25px;
}

.roleIds {
  // position: absolute;
  // top: 0;
  // left: 90px;
  width: 100%;
}
</style>
<style lang="scss">
.appendToBodyFalse {
  height: auto;
}
.appendToBodyFalse .el-picker-panel {
  position: absolute !important;
  top: 30px !important;
}

.appendToBodyFalse .el-select-dropdown {
  position: absolute !important;
  top: 30px !important;
  left: 0 !important;
}

.appendToBodyFalse .el-cascader__dropdown {
  position: absolute !important;
  top: 30px !important;
  left: 0 !important;
  max-width: 300px;
  overflow-x: scroll;

  // &::-webkit-scrollbar-track {
  //   background: rgb(239, 239, 239);
  //   border-radius: 2px;
  // }

  // &::-webkit-scrollbar-thumb {
  //   background: #bfbfbf;
  //   border-radius: 10px;
  // }

  // &::-webkit-scrollbar-thumb:hover {
  //   background: #333;
  // }

  // &::-webkit-scrollbar-corner {
  //   background: #179a16;
  // }
}

.cascaderList {
  width: 100%;
}
</style>