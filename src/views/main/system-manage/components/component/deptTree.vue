<template>
  <div class="deptTree">
    <div class="tree" :class="type == 1 ? 'on' : ''">
      {{ type ? "" : "请选择新增部门:" }}
      <div class="bmtree deptTreeChange">
        <el-tree
          v-loading="treeLoading"
          :show-checkbox="type === '1'"
          :data="deptTreeData"
          node-key="id"
          :indent="10"
          :props="defaultProps"
          :default-expanded-keys="keyList"
          :default-checked-keys="checkedList"
          @check="gainList"
        >
          <template v-slot="{ node, data }">
            <div class="custom-tree-node">
              <el-input
                v-if="inputName === node.id || !node.label"
                v-model="deptName"
                placeholder="请输入部门名称"
              />
              <span v-else>{{ node.label }}</span>
              <div class="content none">
                <div v-if="inputName === node.id || !node.label">
                  <i class="icons" @click.stop="blurEvent(node.label)">
                    <el-icon :size="15">
                      <Check />
                    </el-icon>
                  </i>
                  <i
                    class="icons el-icon-close"
                    @click.stop="cancelEvent(node, data)"
                  >
                    <el-icon :size="15">
                      <Close />
                    </el-icon>
                  </i>
                </div>
                <div v-else>
                  <span
                    v-if="
                      resourceCode?.includes('UserManagementEditDepartment')
                    "
                    @click.stop="editEvent(node)"
                  >
                   <img class="editIcon" src="https://static.idicc.cn/cdn/zhaoShang/edit.png"></img>
                  </span>
                  <i
                    v-if="resourceCode?.includes('UserManagementAddDepartment')"
                    @click.stop="add(data)"
                  >
                      <img class="editIcon" src="https://static.idicc.cn/cdn/zhaoShang/add.png"></img>
                  </i>
                  <i
                    v-if="
                      node.data.parentId != 0 &&
                      resourceCode?.includes('UserManagementDeleteDepartment')
                    "
                    @click.stop="deleteFn(node, data)"
                    ><img class="editIcon" src="https://static.idicc.cn/cdn/zhaoShang/del.png"></img>
                  </i>
                </div>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
    </div>
  </div>
</template>

<script>
import {
  deptTree,
  deptSave,
  deptDelete,
  deptEdit,
  orgDeptTreeAPI,
} from "@/api/user";
export default {
  name: "DeptTree",
  props: {
    addindustry: {
      type: Boolean,
      default: false,
    },
    type: {
      type: String,
      default: "",
    },
    orgCode: {
      type: String,
      default: "",
    },
    userType: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      defaultProps: {
        children: "childDeptList",
        label: "userDeptName",
      },
      deptTreeData: [], //部门树
      inputName: "", // 选中那个编辑
      deptName: "", //
      deptOpt: "", // 选中节点数据
      treeLoading: false,
      userId: "", // 用户ID --

      status: "", // 当前状态
      userDeptLevel: 0,
      keyList: [],
      checkedList: [],
      newOrgCode: "",
      resourceCode: [],
      addData: {},
    };
  },
  mounted() {
    this.newOrgCode = this.orgCode;
    let resourceCode = JSON.parse(localStorage.getItem("resourceCode") || "[]");
    //console.log("resourceCode", resourceCode);

    this.resourceCode = resourceCode;
    this.getDeptTree();
  },
  methods: {
    // 根据部门获取人员列表
    async gainList(data, checked) {
      this.$emit("gaindeptId", checked.checkedKeys);
    },
    // 获取部门树
    getDeptTree() {
      this.treeLoading = true;
      deptTree({ orgCode: this.orgCode })
        .then((res) => {
          res.noDel = true;
          let treeData = res.result;
          this.deptTreeData = treeData;
          this.$emit("gaindeptId", []);
        })
        .finally(() => {
          this.treeLoading = false;
        });
    },
    editEvent(node) {
      if (this.status == "add") {
        this.$message.error("请修改完成后再添加");
        return false;
      }
      this.inputName = node.id;
      this.deptName = node.label;
      this.userId = node.data.id;
      this.status = "add";
    },
    cancelEvent() {
      this.status = "";
      this.inputName = "";
      this.getDeptTree();
    },
    blurEvent(nodeLabel) {
      if (nodeLabel === this.deptName) {
        this.$message.warning("请修改后再保存");
        return false;
      }
      let val = this.deptName;
      this.inputName = "";
      let data = {
        id: this.userId,
        userDeptName: val,
        parentId: this.deptOpt.id,
        userDeptLevel: +this.userDeptLevel + 1,
      };
      if (!data.userDeptName) {
        this.$message.warning("部门名称不能为空");
        return;
      }
      if (data.id) {
        // 修改部门
        deptEdit(data)
          .then(() => {
            this.$message.success("修改成功！");
            this.getDeptTree();
            this.status = "";
            this.userDeptLevel = 0;
          })
          .catch(() => {
            this.status = "";
          });
      } else {
        // 新增部门
        deptSave(data).then(() => {
          this.$message.success("新增成功！");
          this.getDeptTree();
          this.status = "";
          this.userDeptLevel = 0;
        });
      }
    },
    add(data) {
      this.keyList.push(data.id);
      if (this.status == "add") {
        this.$message.warning("请修改完成后再添加");
        return false;
      }
      this.deptOpt = data;
      this.userDeptLevel = data.userDeptLevel;
      const newChild = {
        id: +new Date(),
        type: 1,
        label: "name",
        childDeptList: [],
      };
      if (!data.childDeptList) {
        this.$set(data, "childDeptList", []);
      }
      data.childDeptList.push(newChild);
      this.addData = data;
      this.status = "add";
      this.deptName = "";
      this.userId = "";
    },
    // eslint-disable-next-line no-unused-vars
    deleteFn(node, data) {
      if (data.type == 1) {
        this.inputName = "";
        this.deptName = "";
        const parent = node.parent;
        const children = parent.data.childDeptList || parent.data;
        const index = children.findIndex((d) => d.id === data.id);
        children.splice(index, 1);
        this.status = "";
        return;
      }
      this.$confirm("确定删除该部门？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        deptDelete({ id: node.data.id }).then(() => {
          this.getDeptTree();
          this.$message({
            type: "success",
            message: "删除部门成功!",
          });
        });
        this.status = "";
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.editIcon{
  width: 16px;
  height: 16px;
  margin-left: 16px;
}
.deptTree {
  .tree {
    // width: 80%;
    // margin-left: 10%;
    &.on {
      margin-left: 0;

      .bmtree {
        // padding-top: 20px;
      }
    }

    .bmtree {
      //       width: 322px;
      // height: 760px;
      background: #fafcff;
      border-radius: 6px 6px 6px 6px;
      // width: 100%;
      // border: 1px solid #ededed;
      // margin-top: 20px;
      // height: 450px;
      // display: block;
      overflow-y: scroll;
    }
  }

  .none {
    display: none;
  }

  ::v-deep {
    .el-tree-node {
      .el-tree-node__children {
        // overflow: scroll !important;
        // color: #3370FF;
        // background: #e6eeff !important;
        // border-radius: 4px 4px 4px 4px;
      }
    }

    .custom-tree-node {
      .el-input--mini {
        width: 120px;

        .el-input__inner {
          height: 25px;
          line-height: 20px;
        }
      }

      &:hover {
        .none {
          display: block;
        }
      }
    }

    .is-checked {
      .el-tree-node__children,
      .custom-tree-node {
        background: #e6eeff !important;
      }
    }
  }

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    // min-width: 180px;
    // overflow-x: scroll;
    height: 32px;
    padding-right: 10px;
    border-radius: 4px 4px 4px 4px;

    // display: block;
    &:hover {
      background: #e6ebf0;
    }

    .icons {
      margin-right: 5px;
    }

    &:hover {
      .none {
        display: block;
      }
    }
  }
}
</style>

<style scoped lang="scss">
// .deptTreeChange
</style>