<template>
  <div>
    <el-drawer
      width="50%"
      title="编辑用户"
      :model-value="editUser"
      :close-on-click-modal="false"
      :wrapper-closable="false"
      @close="cancel"
  class="demo-drawer__body"
    >
      <div class="uni-drawer__content">
        <div class="baseInfo">
          <div class="baseInfoTitle">基本信息</div>
          <div class="cards">
            <el-form
              ref="form"
              label-position="left"
              :model="form"
              class="form"
              :rules="rules"
              label-width="110px"
                size="default"
            >
              <el-form-item prop="realName" label="姓名">
                <el-input v-model="form.realName" placeholder="请输入姓名"  size="default" />
              </el-form-item> 
              <el-form-item prop="mobile" label="账号（手机）">
                <el-input
                  v-model="form.mobile"
                  placeholder="请输入联系人手机号码"
                  class="miniInput"
                   size="default"
                />
              </el-form-item>
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="form.email" placeholder="请输入邮箱"   size="default"/>
              </el-form-item>
            </el-form>
          </div>
        </div>
        <div class="baseInfo">
          <div class="baseInfoTitle">部门信息</div>
          <div class="cards">
            <el-form
              ref="departmentForm"
              label-position="left"
              :model="departmentForm"
              class="form"
              :rules="departmentRules"
              label-width="110px"
            >
              <el-form-item
                label="部门名称"
                prop="deptId"
                class="appendToBodyFalse"
              >
                <el-cascader
                  v-model="departmentForm.deptId"
                  :append-to-body="false"
                  placeholder="请选择部门"
                  :options="deptListFn"
                  :props="optionProps"
                  class="cascaderList"
                   size="default"
                />
              </el-form-item>

              <el-form-item
                label="角色"
                prop="roleType"
                class="appendToBodyFalse"
                v-if="roleTypeList.length"
              >
                <el-select
                  v-model="departmentForm.roleType"
                  placeholder="请选择"
                  :popper-append-to-body="false"
                  disabled
                  v-show="false"
                   size="default"
                >
                  <el-option
                    v-for="item in roleTypeList"
                    :key="item?.id || ''"
                    :label="item?.roleName"
                    :value="item?.id"
                     size="default"
                  />
                </el-select>
                <el-form-item
                  label=""
                  prop="roleIds"
                  class="appendToBodyFalse roleIds"
                  v-if="rolesList.length"

                >
                  <el-select
                    v-model="departmentForm.roleIds"
                    multiple
                    placeholder="请选择"
                    :popper-append-to-body="false"
                    class="cascaderList"
                     size="default"
                  >
                    <el-option
                      v-for="item in rolesList"
                      :key="item.id"
                      :label="item.roleName"
                      :value="item.id"
                       size="default"
                    />
                  </el-select>
                </el-form-item>
              </el-form-item>
              <el-form-item label="职位">
                <el-input
                  v-model="departmentForm.position"
                  placeholder="请输入职位"
                   size="default"
                />
              </el-form-item>
            </el-form>
          </div>
        </div>
           <div class="demo-drawer__footer">
        <el-button size="default" @click="cancel"> 取 消 </el-button>
        <el-button size="default" class="btn" type="primary" @click="preserve">
          确 认
        </el-button>
      </div>
      </div>
   
    </el-drawer>
  </div>
</template>
    
    <script>
import { usereditAPI } from "@/api/userset";
import {
  userDetailAPI,
  listDeptByOrgCodeAPI,
  listRolesByOrgCodeAPI,
  getListBasicRoles,
} from "@/api/user";
import cloneDeep from "lodash/cloneDeep";
import remove from "lodash/remove";
export default {
  name: "AddUser",
  props: {
    editUser: {
      type: Boolean,
      default: false,
    },
    compileEcho: {
      type: Object,
      default: () => {},
    },
    rowId: {
      type: String,
      default: null,
    },
    editInfo: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      form: {
        orgName: "", //机构名称
        realName: "", // 姓名
        username: "", //手机号/账号

        email: "", //邮箱
        mobile: "",
      },
      departmentForm: {
        deptId: "", // 部门id
        roleType: "1", //角色
        roleIds: [], //角色
        position: "", //职位
      },
      departmentRules: {
        deptId: [{ required: true, message: "部门名称必选", trigger: "blur" }],
        roleIds: [{ required: true, message: "角色必选", trigger: "blur" }],
        roleType: [{ required: true, message: "角色必选", trigger: "blur" }],
      },
      activeName: "first",
      formLoading: false,
      deptListFn: [],
      rolesList: [],
      roleTypeList: [],

      optionProps: {
        value: "id",
        label: "userDeptName",
        children: "childDeptList",
        checkStrictly: true,
      },
      rules: {
        deptId: [
          { required: true, message: "部门名称必选", trigger: "change" },
        ],
        realName: [
          { required: true, message: "姓名不能为空", trigger: "blur" },
        ],
        roleIds: [{ required: true, message: "角色必选", trigger: "change" }],
        email: [
          {
            pattern:
              /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
            message: "邮箱格式不符合规则",
            trigger: "blur",
          },
        ],
        username: [
          { required: true, message: "手机号码不能为空", trigger: "blur" },
          {
            pattern: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/,
            message: "手机号不符合规则",
            trigger: "blur",
          },
        ],
      },
      addUserUni: [],
      addUserUniLength: 0,
    };
  },
  created() {
    this.editFn();
    this.getRoles();
  },
  methods: {
    async getRoles() {
      let res = await getListBasicRoles();
      this.roleTypeList = res.result;
    },
    addUserUnis() {
      let id = this.addUserUniLength + 1;
      this.addUserUni.unshift({
        id,
        index: id,
        deptId: "",
        orgCode: "",
        roleIds: [],
        roleType: "",
      });
      this.$refs[`addUserUni${id}`]?.[0].$refs.formUni.resetFields();
      this.addUserUniLength = id;
      setTimeout(() => {
        this.addUserUni?.map((e) => {
          this.$refs[`addUserUni${e.index}`][0].validateForm();
        });
      });
    },
    removeUserUnis(id) {
      let data = cloneDeep(this.addUserUni);
      remove(data, function (n) {
        return n.id === id;
      });
      this.addUserUni = data;
    },
    async editFn() {
      try {
        this.formLoading = true;
        const resData = await userDetailAPI({
          userId: this.rowId,
        });
        let res = resData.result;
        //console.log("87878778", res);
        (this.form.realName = res.realName), // 姓名
          // (this.form.deptId = res.deptId), // 部门id
          (this.form.mobile = res.username), //手机号/账号
          (this.form.email = res.email), //邮箱
          // (this.departmentForm.roleIds = res.roleIds); //角色 */
          // (this.form.id = res.id); //角色 */
          (this.departmentForm.deptId = res?.relations?.[0]?.deptId),
          (this.departmentForm.roleType =
            res?.relations?.[0]?.basicRoleIds?.[0]),
          (this.departmentForm.roleIds = res?.relations?.[0]?.roleIds),
          (this.departmentForm.position = res?.relations?.[0]?.position), //职位
          this.departmentList(res.orgCode);
      } catch (error) {
        console.log(error);
      } finally {
        this.formLoading = false;
      }
    },
    getTreeData(data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].childDeptList.length < 1) {
          data[i].childDeptList = undefined;
        } else {
          this.getTreeData(data[i].childDeptList);
        }
      }
      return data;
    },
    async departmentList(val) {
      const res = await listDeptByOrgCodeAPI({
        orgCode:
          val || JSON.parse(localStorage.getItem("userInfo") || "{}")?.orgCode,
      });
      const arr = await listRolesByOrgCodeAPI({
        orgCode:
          val || JSON.parse(localStorage.getItem("userInfo") || "{}")?.orgCode,
      });
      this.deptListFn = this.getTreeData(res.result);
      this.rolesList = arr.result;
    },
    cancel() {
      this.$emit("update:editUser", false);
    },
    async preserve() {
      await this.$refs.form.validate();
      await this.$refs.departmentForm.validate();
      try {
        let { email, mobile, realName } = this.form;
        let { deptId, roleIds, roleType, position } = this.departmentForm;
        let deptIds = cloneDeep(deptId);
        // console.log(deptIds,deptId,'deptIds')

        let saveData = {
          id: this.rowId,
          realName,
          username: mobile,
          mobile,

          email,
          relations: [
            {
              orgCode: JSON.parse(localStorage.getItem("userInfo") || "{}")
                ?.orgCode,
              deptId: Array.isArray(deptId) ? deptIds.pop() : deptId,
              basicRoleId: roleType,
              position,
              roleIds,
            },
          ],
        };
        await usereditAPI(saveData);
        this.$emit("userList");
        this.cancel();
        this.$message.success("编辑用户成功");
        // this.$emit("industryList");
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>
    
<style scoped lang="scss">

.demo-drawer__footer {
  z-index: 9999;
}
.uni-drawer__content {
 padding:0  20px 20px 20px 20px;
  // display: flex;
  height: 100%;
  // justify-content: flex-start;
  position: relative;
  overflow-y: scroll;
  
  background: #fff;
}

.baseInfo {
  .baseInfoTitle {
    font-size: 14px;
    font-weight: 600;
    color: #1d2129;
    padding: 25px 16px;
  }
}
.cards {
  background: #fafcff;
  border-radius: 6px 6px 6px 6px;
  border: 1px solid rgba(51, 112, 255, 0.1);
  padding: 25px;
}
.roleIds {
  // position: absolute;
  // top: 0;
  // left: 90px;
  width: 100% !important;
}
.cascaderList {
  width: 100%;
}
.appendToBodyFalse {
  width: 100%;
  height: auto;
}
.appendToBodyFalse .el-picker-panel {
  position: absolute !important;
  top: 30px !important;
}

.appendToBodyFalse .el-select-dropdown {
  position: absolute !important;
  top: 30px !important;
  left: 0 !important;
}

.appendToBodyFalse .el-cascader__dropdown {
  position: absolute !important;
  top: 30px !important;
  left: 0 !important;
  max-width: 300px;
  overflow-x: scroll;
}
.demo-drawer__footer {
  padding: 10px;
  position: absolute;
  z-index: 999;
  bottom: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  left: 0;
  background: #fff;
box-shadow: 0px -4px 10px 0px rgba(0, 0, 0, 0.1);
border: 0;

}
</style>