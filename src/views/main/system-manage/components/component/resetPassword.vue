<template>
  <el-dialog :model-value="dialogVisible" width="22%" :before-close="cancel">
    <template #header>
      <span :class="reset ? 'dialog-titleCenter' : 'dialog-titleLeft'">
        {{ reset ? "重置密码成功" : "重置密码" }}
      </span>
    </template>

    <span v-if="!reset" class="tipTitle">是否确认重置密码？</span>
    <div v-else>
      <div class="success">
        <div>
          账号：<span>{{ resetPasswords.username }} </span>
        </div>
        <div>
          新密码：<span>{{ resetPasswords.password }}</span>
        </div>
      </div>
      <el-button
        size="default"
        type="primary"
        class="copyPassword"
        @click="copy(resetPasswords.password)"
      >
        复制密码
      </el-button>
    </div>
    <template #footer>
      <span v-if="!reset" class="dialog-footer">
        <el-button size="default" @click="cancel">取 消</el-button>
        <el-button size="default" type="primary" @click="submit"
          >确 定</el-button
        >
      </span>
    </template>
  </el-dialog>
</template>


<script>
import { resetPasswordAPI } from "@/api/user";
export default {
  props: {
    dialogVisible: {
      type: Boolean,
    },
    id: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      reset: false,
      resetPasswords: "",
    };
  },
  methods: {
    cancel() {
      (this.reset = false), this.$emit("cancelPasswordsFn");
    },
    async submit() {
      const res = await resetPasswordAPI({
        userId: this.id, //已选择id,
      });
      this.resetPasswords = res.result;
      this.$message({
        type: "success",
        message: "重置密码成功！",
      });
      this.reset = true;
    },
    copy(val) {
      var cInput = document.createElement("input");
      cInput.value = val;
      document.body.appendChild(cInput);
      cInput.select();
      document.execCommand("copy");
      document.body.removeChild(cInput);
      this.$message.success("复制成功");
    },
  },
};
</script>
<style scoped lang="scss">
.success {
  text-align: center;
  height: 110px;
  background: #fafcff;
  border-radius: 6px 6px 6px 6px;
  border: 1px solid rgba(51, 112, 255, 0.1);
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  padding: 25px;

  div {
    width: 100%;
    font-size: 14px;
    color: #4e5969;

    span {
      padding-left: 10px;
      color: #1d2129;
    }
  }
}

.dialog-titleCenter {
  display: block;
  width: 100%;
  font-size: 18px;
  font-weight: 600;
  color: #1d2129;
  text-align: center;
}
.dialog-titleLeft {
  width: 100%;
  font-size: 18px;
  font-weight: 600;
  color: #1d2129;
}

.copyPassword {
  width: 30%;
  margin-left: 35%;
  margin-top: 20px;
  margin-bottom: 20px;
}
.tipTitle {
  font-size: 14px;
  font-weight: 400;
  color: #86909c;
}
</style> 