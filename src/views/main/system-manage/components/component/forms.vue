<template>
  <el-form
    :ref="formRefs"
    label-position="left"
    :model="formItem"
    class="form formList"
    :rules="rulesUni"
    label-width="110px"
  >
    <div>
      <el-form-item prop="orgCode" label="机构名称" class="appendToBodyFalse">
        <el-select
          v-model="formItem.orgCode"
          placeholder="请选择"
          :popper-append-to-body="false"
        >
          <!--  -->
          <el-option
            v-for="item in autoList"
            :key="item.id"
            :label="item.orgName"
            :value="item.orgCode"
            @click="departmentClick(item)"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="部门名称" prop="deptId" class="appendToBodyFalse">
        <!-- <el-select v-model="formItem.deptId" placeholder="请选择部门" style="width: 280px;"   :popper-append-to-body="false">
                    <el-option v-for="item in formItem.deptListFn" :key="item.id" :label="item.userDeptName" :value="item.id" />
                </el-select> -->

        <el-cascader
          v-model="formItem.deptId"
          placeholder="请选择部门"
          :options="formItem.deptListFn"
          :show-all-levels="false"
          :props="optionProps"
          :append-to-body="false"
        />
      </el-form-item>
      <el-form-item label="职位">
        <el-input v-model="formItem.position" placeholder="请输入职位" />
      </el-form-item>
      <el-form-item label="角色" prop="roleType" class="appendToBodyFalse">
        <el-select
          v-model="formItem.roleType"
          placeholder="请选择"
          style="width: 90px"
          :popper-append-to-body="false"
        >
          <el-option
            v-for="item in roleTypeList"
            :key="item.id"
            :label="item.roleName"
            :value="item.id"
            :disabled="item.roleType === '1'"
          />
        </el-select>
        <el-form-item label="" prop="roleIds" class="roleIds appendToBodyFalse">
          <el-select
            v-model="formItem.roleIds"
            multiple
            placeholder="请选择"
            :popper-append-to-body="false"
          >
            <el-option
              v-for="item in formItem.rolesList"
              :key="item.id"
              :label="item.roleName"
              :value="item.id"
              :disabled="item.roleType === '1'"
            />
          </el-select>
        </el-form-item>
      </el-form-item>
    </div>
  </el-form>
</template>
<script>
export default {
  name: "BoxRecursion",
  props: {
    roleTypeList: {
      type: Array,
      default: null,
    },
    autoList: {
      type: Array,
      default: null,
    },
    // rolesList: {
    //     type: Array,
    //     default: null
    // },
    formRefs: {
      type: Number,
      default: null,
    },
    formItem: {
      type: Object,
    },
  },

  data() {
    return {
      rolesList: [], //角色list
      deptListFn: [], //部门list
      optionProps: {
        value: "id",
        label: "userDeptName",
        children: "childDeptList",
        checkStrictly: true,
      },

      rulesUni: {
        orgCode: [{ required: true, message: "机构名称必选", trigger: "blur" }],
        deptId: [{ required: true, message: "部门名称必选", trigger: "blur" }],
        roleIds: [{ required: true, message: "角色必选", trigger: "blur" }],
        roleType: [{ required: true, message: "角色必选", trigger: "blur" }],
      },
    };
  },
  watch: {
    "formItem.orgCode"() {
      this.validateForm();
    },
    "formItem.deptId"() {
      this.validateForm();
    },
    "formItem.roleIds"() {
      this.validateForm();
    },
    "formItem.roleType"() {
      this.validateForm();
    },
  },
  mounted() {},
  methods: {
    clearValidateForm() {
      this.$refs?.[this.formRefs].clearValidate();
    },
    validateForm() {
      return this.$refs?.[this.formRefs]?.validate();
    },
    departmentClick() {
      this.formItem.deptId = "";
      this.formItem.roleIds = [];
      this.$emit("departmentList", this.formItem.orgCode, this.formItem.id);
    },
  },
};
</script>
<style scoped lang="scss">
.roleIds {
  position: absolute;
  top: 0;
  left: 90px;
  width: calc(100% - 90px);
}

.formList {
  ::v-deep {
    .el-select,
    .el-cascader {
      width: 100%;
    }
  }
}
</style>