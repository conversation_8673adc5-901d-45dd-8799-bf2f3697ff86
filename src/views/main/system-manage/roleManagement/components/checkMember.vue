<template>
  <div>
    <el-drawer
      title="查看成员"
      :model-value="ischeckMember"
      :before-close="handleClose"
      :wrapper-closable="false"
      size="50%"
    >
      <!-- <el-divider /> -->
      <div class="demo-drawer__content">
        <div class="searchArea">
          <el-button
            size="default"
            class="btn"
            @click="add"
            type="primary"
            
          >
            添加成员
          </el-button>
          <el-input
            v-model="Membername"
            placeholder="请输入人员名称"
            icon="el-icon-search"
            class="searchInput"
          >
            <i
              class="el-input__icon el-icon-search"
              style="cursor: pointer"
              @click="industryList"
            />
          </el-input>
        </div>

        <el-table
          v-loading="listLoading"
          :data="tableData"
          style="width: 100%; margin-top: 20px"
          size="default"
        >
          <el-table-column
            prop="realName"
            label="姓名"
            width="110"
            align="center"
          />
          <el-table-column
            prop="username"
            label="账号"
            width="150"
            align="center"
          />
          <el-table-column
            prop="orgName"
            label="机构"
            width="160"
            align="center"
          />
          <el-table-column prop="deptName" label="部门" align="center" />
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button
                size="default"
                type="text"
                @click="ischeckMemberFn(scope)"
              >
                移除
              </el-button>
            </template>
          </el-table-column>
          <template>
            <el-empty
              class="emptyCustomize"
              :image-size="100"
              :image="empty"
            ></el-empty>
          </template>
        </el-table>
        <div class="ye">
          <el-pagination
            :current-page="form.pageNum"
            :page-sizes="[5, 10, 20, 50]"
            :page-size="form.pageSize"
            :total="+total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="sizeChange"
            @current-change="currentChange"
          />
        </div>
      </div>
    </el-drawer>
  </div>
</template>
      
<script>
import empty from "@/assets/images/empty.png";
import { listByRoleIdAPI, deleteRoleUserAPI } from "@/api/role";
export default {
  name: "DetailedLIstRight",
  props: {
    ischeckMember: {
      type: Boolean,
      default: false,
    },
    compileindustryId: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      form: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      listLoading: false,
      Membername: "",
      tableData: [],
      empty,
    };
  },
  computed: {},
  watch: {},
  created() {
    this.industryList();
  },
  methods: {
    sizeChange(val) {
      this.form.pageSize = val;
      this.industryList();
    },
    currentChange(val) {
      this.form.pageNum = val;
      this.industryList();
    },
    add() {
      this.$emit("addition", this.compileindustryId);
    },
    // 获取列表
    async industryList() {
      try {
        this.listLoading = true;
        let data = {
          pageNum: this.form.pageNum,
          pageSize: this.form.pageSize,
          param: this.Membername,
          roleId: this.compileindustryId,
        };
        const res = await listByRoleIdAPI(data);
        this.tableData = res.result.records;
        this.total = res.result.total;
      } catch (error) {
        console.log(error);
      } finally {
        this.listLoading = false;
      }
    },
    ischeckMemberFn(row) {
      this.$confirm("确定删除该成员吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "",
      }).then(async () => {
        await deleteRoleUserAPI({
          id: row.row.id,
        });
        this.industryList();
        this.$message({
          type: "success",
          message: "移除成功!",
        });
      });
    },
    handleClose() {
      this.$emit("update:ischeckMember", false);
    },
  },
};
</script>
      
<style scoped lang="scss">
.ye {
  margin-top: 20px;
  display: flex;
  justify-content: end;
}
.demo-drawer__content {
  height: 88vh;
  background: transparent;
  padding: 12px;
}
.searchArea {
  display: flex;
  justify-content: space-between;
  .searchInput {
    width: 260px;
  }
}
</style>
  