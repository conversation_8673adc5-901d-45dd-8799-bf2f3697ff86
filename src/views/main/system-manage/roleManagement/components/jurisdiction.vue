<template>
  <div>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 20px; margin-left: 20px">权限管理</span>
        <el-button
          size="default"
          style="float: right; padding: 3px 0"
          type="text"
        >
          <i class="el-icon-close" @click="close" />
        </el-button>
      </div>
      <div class="jurisdiction">
        <div class="role">
          <span>角色类型</span>
          <br />
          <el-radio-group v-model="type">
            <div class="radio">
              <el-radio :label="1"> 运营端 </el-radio>
            </div>
            <div class="radio">
              <el-radio :label="2"> 用户端 </el-radio>
            </div>
          </el-radio-group>
        </div>
        <div class="menu">
          菜单权限
          <div
            style="
              width: 80%;
              border: 1px solid #ededed;
              height: 340px;
              display: block;
              overflow-y: scroll;
              margin-top: 30px;
            "
          >
            <el-tree
              ref="refTree"
              v-loading="treeLoading"
              :data="data"
              :props="defaultProps"
              default-expand-all
              show-checkbox
              :check-strictly="isCheck"
              node-key="id"
              @node-drop="finishFn"
            />
          </div>
          <div class="button">
            <el-button size="default" @click="close"> 取消 </el-button>
            <el-button
              size="default"
              v-loading="buttonloading"
              class="btn"
              @click="preserve"
            >
              保存
            </el-button>
          </div>
        </div>
        <!--   <div class="operation">
          操作权限
          <div
            style="
                width: 80%;
                border: 1px solid #ededed;
                height: 340px;
                display: block;
                overflow-y: scroll;
                margin-top: 30px;
              "
          >
            <el-tree
              ref="refTree"
              :data="data"
              :props="defaultProps"
              default-expand-all
              show-checkbox
              :check-strictly="isCheck"
              node-key="label"
              @check-change="checked"
            />
          </div>

        </div>  -->
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  getAclTreeByRoleTypeAPI,
  setRoleTypeAclAPI,
  listRoleTypeAclAPI,
  moveAclAPI,
} from "@/api/role";
export default {
  name: "JurisDiction",
  data() {
    return {
      type: 1,
      data: [],
      defaultProps: {
        children: "aclDTOList",
        label: "aclName",
      },
      isCheck: false,
      treeLoading: false,
      buttonloading: false,
    };
  },
  watch: {
    type() {
      this.getAclTree();
    },
  },
  created() {
    this.getAclTree();
  },
  methods: {
    async finishFn(Node, ev) {
      await moveAclAPI({
        id: Node.data.id,
        parentId: ev.parent.data.id,
      });
      this.getAclTree();
      //console.log(Node.data.aclName);
      //console.log(ev.parent.data.aclName);
    },

    async getAclTree() {
      try {
        this.treeLoading = true;
        const res = await getAclTreeByRoleTypeAPI();
        this.data = res.result;
        const res2 = await listRoleTypeAclAPI({
          roleType: this.type,
        });
        this.$refs.refTree.setCheckedKeys([]);
        if (res2.result != null) {
          this.$nextTick(() => {
            const nodes = [];
            res2.result.forEach((item) => {
              const node = this.$refs.refTree.getNode(item);
              if (node?.isLeaf) {
                nodes.push(item);
              }
            });
            this.$refs.refTree.setCheckedKeys(nodes, true);
          });
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.treeLoading = false;
      }
    },
    close() {
      this.$emit("close", 1);
    },
    async preserve() {
      try {
        this.buttonloading = true;
        this.treeLoading = true;
        const arr1 = this.$refs.refTree.getCheckedKeys(); //获取已选中
        const arr2 = this.$refs.refTree.getHalfCheckedKeys(); //获取半选
        const arr = [...arr1, ...arr2];
        //const arr = this.$refs.refTree.getCheckedKeys()
        //console.log(arr,arr1);
        await setRoleTypeAclAPI({
          roleType: this.type,
          aclIds: arr,
        });
        this.$message.success("保存成功");
        const res2 = await listRoleTypeAclAPI({
          roleType: this.type,
        });
        this.$refs.refTree.setCheckedKeys([]);
        if (res2.result != null) {
          this.$nextTick(() => {
            const nodes = [];
            res2.result.forEach((item) => {
              const node = this.$refs.refTree.getNode(item);
              if (node.isLeaf) {
                nodes.push(item);
              }
            });
            this.$refs.refTree.setCheckedKeys(nodes, true);
          });
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.buttonloading = false;
        this.treeLoading = false;
      }
    },
  },
};
</script>

<style scoped lang="scss">
.jurisdiction {
  display: flex;
  width: 95%;
  margin-left: 2.5%;
  .role {
    width: 20%;
    span {
      font-size: 14px;
    }
    .radio {
      margin-top: 30px;
    }
  }
  .menu {
    width: 30%;
    .button {
      float: right;
      margin-right: 20%;
      margin-top: 30px;
    }
  }
  .operation {
    width: 30%;
    .button {
      float: right;
      margin-right: 20%;
      margin-top: 30px;
    }
  }
}
.box-card {
  height: 550px;
}
</style>