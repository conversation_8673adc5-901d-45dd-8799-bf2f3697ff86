<template>
  <div>
    <el-drawer
      :title="compileindustryId !== '' ? ' 编辑角色' : '新增角色'"
      :model-value="isDrawer"
      :before-close="handleClose"
      :wrapper-closable="false"
      size="30%"
      class="demo-drawer__body"
    >
      <!-- <el-divider /> -->
      <div class="demo-drawer__content">
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          label-width="80px"
          class="demo-ruleForm"
          size="default"
        >
          <el-form-item label="角色名称" prop="name">
            <div class="treeInput">
              <el-input v-model="ruleForm.name"  placeholder="请输入角色名称" />
            </div>
          </el-form-item>
          <el-form-item label="功能权限">
            <div class="treeContent">
              <!-- check-strictly 在显示复选框的情况下，是否严格的遵循父子不互相关联的做法，默认为 false  此时必须设置node-key-->
              <el-tree
                ref="refTree"
                size="default"
                v-loading="treeLoading"
                :check-on-click-node="checkStrictly"
                :data="data"
                default-expand-all
                show-checkbox
                :check-strictly="checkStrictly"
                :default-checked-keys="defaultCheckedKeys"
                node-key="id"
                :props="defaultProps"
                @check="checked"
              />
            </div>
          </el-form-item>
          
        </el-form>
     <div class="demo-drawer__footer">
            <el-form-item>
              <el-button size="default" @click="handleClose"> 取消 </el-button>
              <el-button
                size="default"
                v-loading="btnLoading"
                class="btn"
                type="primary"
                @click="preserve"
              >
                保存
              </el-button>
            </el-form-item>
          </div>
      </div>
    </el-drawer>
  </div>
</template>
  
<script>
import { getTreeLength3 } from "@/utils/dateFormat";
import {
  rolesAddAPI,
  rolesEditAPI,
  getAclTreeAPI,
  roleAclTreeAPI,
} from "@/api/role";
export default {
  name: "DetailedLIstRight",
  props: {
    isDrawer: {
      type: Boolean,
      default: false,
    },
    compileindustryId: {
      type: String,
      default: null,
    },
    echoObj: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      checkStrictly: true,
      // 父子节点有无关联关系
      isCheck: false,
      btnLoading: false,
      treeLoading: false,
      data: [],
      defaultProps: {
        children: "childList",
        label: "resourceName",
      },
      ruleForm: {
        name: "",
        type: "1",
      },
      rules: {
        name: [{ required: true, message: "请输入角色名称", trigger: "blur" }],
        type: [
          {
            required: true,
            message: "请至少选择一个角色类型",
            trigger: "change",
          },
        ],
      },
      // 数据权限
      options: [],
      value: "",
      // 全选
      isAllChecked: false,
      selected: 0,
      defaultCheckedKeys: [],
      checkedNodes: [],
    };
  },
  computed: {
    // 计算总数
    sum() {
      if (this.data) {
        return getTreeLength3(this.data);
      } else {
        return 0;
      }
    },
  },
  watch: {
    /* "ruleForm.type"() {
      this.$refs.refTree.setCheckedKeys([]);
      this.treeList();
      if (this.isAllChecked == true) {
        this.isAllChecked = !this.isAllChecked;
      }
    }, */
  },
  async mounted() {
    if (this.compileindustryId) {
      this.init();
    } else {
      this.changeTree();
    }
  },
  methods: {
    async changeTree() {
      if (this.compileindustryId !== "" && this.echoObj.orgCode === "") return;
      const res = await getAclTreeAPI({
        channelKey: this.echoObj.orgCode,
      });
      this.data = res?.result?.map((item) => {
        return {
          resourceName: item.appName,
          id: item.appName,
          disabled: true,
          childList: item.childList,
        };
      });
    },
    // 获取数据回显
    async init() {
      try {
        this.treeLoading = true;
        this.ruleForm.name = this.echoObj.name;
        await this.changeTree();
        const res = await roleAclTreeAPI({
          roleId: this.compileindustryId,
        });
        this.defaultCheckedKeys = res?.result || [];
      } catch (error) {
        console.log(error);
      } finally {
        this.treeLoading = false;
      }
    },
    // 计算已勾选
    checked(e, node) {
      // console.log(e,node,'322')
      this.checkedNodes = node.checkedNodes;
      this.defaultCheckedKeys = node.checkedKeys;
    },
    // 关闭
    handleClose() {
      this.$emit("update:isDrawer", false);
    },
    // // 全选
    // handleAllCheckedChange() {
    //   if (this.isAllChecked) {
    //     const keys = [];
    //     const nodes = this.$refs.refTree.store.nodesMap;
    //     console.log(nodes, 'nodes');
    //     for (let key in nodes) {
    //       keys.push(key);
    //     }
    //     this.$refs.refTree.setCheckedKeys(keys);
    //     console.log(keys, 'keys');
    //     // this.$refs.refTree.setCheckedNodes(this.data);
    //   } else {
    //     this.$refs.refTree.setCheckedKeys([]);
    //   }
    // },
    // 保存
    async preserve() {
      try {
        this.btnLoading = true;
        let arr = this.defaultCheckedKeys;
        if (arr?.length === 0) {
          return this.$message.error("请至少勾选一项权限");
        }
        await this.$refs.ruleForm.validate();
        let data = {
          roleName: this.ruleForm.name, //角色名称
          roleType: this.ruleForm.type, //角色类型
          resourceIds: arr,
          channelKey: this.echoObj.orgCode,
        };
        let datas = {
          id: this.compileindustryId,
          roleName: this.ruleForm.name, //角色名称
          resourceIds: arr,
          channelKey: this.echoObj.orgCode,
        };
        this.compileindustryId
          ? await rolesEditAPI(datas)
          : await rolesAddAPI(data);
        this.compileindustryId
          ? this.$message.success("编辑成功")
          : this.$message.success("添加成功");
        this.handleClose();
        this.$emit("industryList");
      } catch (error) {
        console.log(error);
      } finally {
        this.btnLoading = false;
      }
    },
  },
};
</script>
  
<!-- <style scoped lang="scss"></style> -->
<style scoped lang="scss">
.treeContent {
  border: 1px solid #ced4db;
  background: #fafcff;
  border-radius: 4px 4px 4px 4px;
  height: calc(80vh - 50px);
  display: block;
  overflow-y: scroll;
  padding: 10px;
  width: 100%;
}
.treeInput{
  width: 100%;
}
.demo-drawer__content {
  height: 100%;
  overflow-y: scroll;
  padding:  20px;
  position: relative;
}
 
.demo-drawer__footer {
  padding: 10px;
  position: absolute;
  z-index: 999;
  bottom: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  left: 0;
  background: #fff;
box-shadow: 0px -4px 10px 0px rgba(0, 0, 0, 0.1);
border: 0;
  .el-form-item {
    margin-bottom: 0px !important;
  }

  .el-form-item__content {
    margin-left: 0px !important;
  }
}

.selectOrg {
  width: 100%;
}

</style>
<style lang="scss">


</style>