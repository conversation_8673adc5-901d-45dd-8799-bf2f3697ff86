<template>
  <div ref="msMain" class="vue-ms-main vue-ms-main-sel">
    <div>
      <table-layout :tab-name-list="['角色管理']">
        <!-- 查询内容 -->
        <template #elForm>
          <el-form
            ref="params"
            label-width="82px"
            class="demo-form-inline"
            :model="staform"
            size="default"
          >
            <el-form-item label="角色">
              <el-input
              class="roleInput"
                v-model="staform.roleName"
                placeholder="请输入角色名称"
                size="default"
              />
            </el-form-item>

            <el-form-item>
              <el-button size="default" class="btn" @click="reset">
                重置
              </el-button>
              <el-button
                size="default"
                v-loading="loading"
                class="btn"
                @click="search"
                type="primary"
              >
                查询
              </el-button>
              <el-button
                size="default"
                class="btn addBtn"
                @click="addMemberFn"
                type="primary"
                :icon="Plus"
                v-if="resourceCode?.includes('RoleManageAdd')"
              >
                新增角色
              </el-button>
            </el-form-item>
          </el-form>
        </template>
        <template #selTable>
          <div>
            <el-table
              v-loading="loading"
              :data="tableData"
              style="width: 100%"
              size="default"
            >
              <el-table-column prop="roleName" align="center" label="角色" />
              <el-table-column
                prop="resourceNum"
                align="center"
                label="功能权限"
              />
              <el-table-column prop="userNum" align="center" label="成员数" />
              <el-table-column
                align="center"
                label="操作"
                fixed="right"
                width="400"
              >
                <template v-slot="{ row }">
                  <el-button
                    class="btnSize"
                    type="primary"
                    @click="allocation(row)"
                    v-if="resourceCode?.includes('RoleManageEdit')"
                  >
                    编辑
                  </el-button>
                  <!-- <el-divider
                    direction="vertical"
                    v-if="resourceCode?.includes('RoleManageEdit')"
                  /> -->
                  <el-button
                    class="btnSize"
                    type="primary"
                    @click="addMembers(row.id)"
                    v-if="resourceCode?.includes('RoleManageAddMember')"
                  >
                    添加成员
                  </el-button>
                  <!-- <el-divider
                    direction="vertical"
                    v-if="resourceCode?.includes('RoleManageSeeMember')"
                  /> -->
                  <el-button
                    class="btnSize"
                    type="primary"
                    @click="ischeckMemberFn(row.id)"
                    v-if="resourceCode?.includes('RoleManageSeeMember')"
                  >
                    查看成员
                  </el-button>
                  <!-- <el-divider
                    direction="vertical"
                    v-if="resourceCode?.includes('RoleManageDelete')"
                  /> -->
                  <el-button
                    class="btnSize"
                    type="danger"
                    @click="deleteFn(row)"
                    v-if="resourceCode?.includes('RoleManageDelete')"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
              <template>
                <el-empty
                  class="emptyCustomize"
                  :image-size="100"
                  :image="empty"
                ></el-empty>
              </template>
            </el-table>
            <div class="ye pagination">
              <el-pagination
                v-model:current-page="staform.pageNum"
                size="default"
                :page-sizes="[5, 10, 20, 50]"
                :page-size="staform.pageSize"
                :total="+total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="changeSize"
                @current-change="industryList"
              />
            </div>
          </div>
        </template>
      </table-layout>
    </div>

    <!-- <div v-if="isList == 2">
      <table-layout :tab-name-list="['角色管理']" />
       <jurisdiction @close="close" />
    </div> -->
    <addRole
      v-if="isDrawer"
      :isDrawer="isDrawer"
      @update:is-drawer="isDrawer = $event"
      :compileindustry-id="compileindustryId"
      :echo-obj="echoObj"
      @industryList="industryList"
    />
    <addMember
      v-if="isMember"
      :is-member="isMember"
      @update:is-member="isMember = $event"
      :compileindustry-id="compileindustryId"
      @industryList="industryList"
    />
    <checkMember
      v-if="ischeckMember"
      :ischeck-member="ischeckMember"
      @update:ischeck-member="ischeckMember = $event"
      :compileindustry-id="compileindustryId"
      @addition="addition"
    />
  </div>
</template> 

<script>
import addMember from './components/addMember.vue';
// import jurisdiction from "./components/jurisdiction.vue"; //权限管理
import addRole from './components/addRole.vue'; //添加/编辑成员
import checkMember from './components/checkMember.vue';
import TableLayout from '@/common/components/table-layout.vue';
import {
  rolesAPI,
  rolesDeleteAPI,
  rolepageAPI,
  autoSearchAPI,
} from '@/api/role';
import empty from '@/assets/images/empty.png';
import { size } from 'lodash';

export default {
  name: 'RoleManagement',
  components: {
    TableLayout,
    addRole,
    // jurisdiction,
    addMember,
    checkMember,
  },
  filters: {
    type(value) {
      if (value == 1) {
        return '运营端';
      } else if (value == 2) {
        return '用户端';
      } else {
        return '';
      }
    },
  },
  data() {
    return {
      isList: 1,
      // 添加成员/编辑成员抽屉
      isDrawer: false,
      //添加角色抽屉
      isMember: false,
      // 查看抽屉
      ischeckMember: false,
      echoObj: {
        type: '',
        name: '',
        orgCode: '',
      },
      // 列表
      tableData: [],
      // 表单提交
      staform: {
        pageNum: 1,
        pageSize: 10,
        roleId: '',
        type: '',
        orgName: '',
        roleName: '',
      },
      // 总数
      total: 0,
      // 编辑角色所需id
      compileindustryId: '',
      // 列表loading
      loading: false,
      // 按钮loading
      listLoading: false,
      // 机构名称列表
      organizationName: [],
      // 角色列表
      options: [],
      // 角色类型
      roleType: [
        {
          label: '全部',
          value: '',
        },
        {
          label: '运营端',
          value: '1',
        },
        {
          label: '用户端',
          value: '2',
        },
      ],
      empty,
      resourceCode: [],
    };
  },
  mounted() {
    let resourceCode = JSON.parse(localStorage.getItem('resourceCode') || '[]');
    this.resourceCode = resourceCode;

    this.staform.orgName = JSON.parse(
      localStorage.getItem('userInfo') || '{}'
    ).orgName;
    this.echoObj.orgCode = JSON.parse(
      localStorage.getItem('userInfo') || '{}'
    ).orgCode;
    this.industryList();
    this.roleList();
  },
  methods: {
    changeSize(val) {
      this.staform.pageSize = val;
      this.industryList();
    },
    async querySearch(queryString, cb) {
      if (this.staform.orgName !== '') {
        const res = await autoSearchAPI({
          orgName: this.staform.orgName,
        });
        var results = res.result.searchHits;
        let dataList = [];
        for (let i = 0; i <= results.length - 1; i++) {
          dataList[i] = {
            value: results[i].content.orgName,
          };
        }
        cb(dataList);
      }
    },
    addition(value) {
      this.compileindustryId = value;
      this.ischeckMember = false;
      this.isMember = true;
    },
    // 编辑角色
    allocation(row) {
      this.compileindustryId = row.id;
      this.echoObj.type = row.type;
      this.echoObj.name = row.roleName;
      this.echoObj.orgCode = row.orgCode;
      this.isDrawer = true;
    },
    // 获取可选角色列表
    async roleList() {
      const res = await rolesAPI();
      this.options = res.result;
    },
    // 获取角色列表
    async industryList() {
      try {
        this.loading = true;
        const res = await rolepageAPI(this.staform);
        this.tableData = res.result.records;
        this.total = res.result.total;
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
    // 删除角色
    deleteFn(row) {
      this.$confirm('确定删除该角色？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
    
      }).then(async () => {
        await rolesDeleteAPI({
          id: row.id,
        });
        // 刷新产业链
        this.staform.pageNum = 1;
        this.industryList();
        this.$message({
          type: 'success',
          message: '删除角色成功!',
        });
      });
    },
    // 查询列表
    search() {
      this.staform.pageNum = 1;
      this.industryList();
    },
    // 重置列表
    reset() {
      this.staform = {
        pageNum: 1,
        pageSize: 10,
        roleId: '',
        type: '',
        orgName: '',
        roleName: '',
      };
      this.industryList();
    },
    // 添加角色
    addMemberFn() {
      this.isDrawer = true;
      this.compileindustryId = null;
      this.compileindustryId = '';
    },
    // 添加成员
    addMembers(id) {
      this.compileindustryId = id;
      this.isMember = true;
    },
    // 查看成员
    ischeckMemberFn(id) {
      this.compileindustryId = id;
      this.ischeckMember = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.btnSize {
  height: 29px;
}
.ye {
  margin-top: 20px;
  display: flex;
  justify-content: end;
}

.demo-form-inline {
  margin: 0px !important;
  padding: 16px 0px 0px 0px !important;

  overflow: hidden;
  display: flex;
  .el-form-item {
    display: flex;
  }
  ::v-deep {
    .el-form-item {
      width: auto !important;
    }
    .el-date-editor--daterange.el-input__inner {
      width: 250px !important;
    }
  }
}
.roleInput{
  width: 220px;
}
</style>
