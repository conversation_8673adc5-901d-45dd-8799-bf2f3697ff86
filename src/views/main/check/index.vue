<script  setup>
// import HelloWorld from './components/HelloWorld.vue'
// import TheWelcome from './components/TheWelcome.vue'
import AmisComponent from "@/components/amis/index.vue";
import { ref } from "vue";
import { perFix, baseEvn, token } from "@/utils/utils";
// 账单管理
const amisjson2 = {
  type: "page",
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: "0px 0px 0px 0px transparent",
  },
  body: [
    {
      type: "tpl",
      inline: true,
      wrapperComponent: "",
      syncLocation: false,
      id: "u:3345e187f2df",
      style: {
        boxShadow: "0px 0px 0px 0px transparent",
        fontWeight: "500",
        lineHeight: "1.7",
        fontSize: "20px",
      },
    },
    {
      type: "crud",
      syncLocation: false,
      name: "tab-s",
      api: {
        method: "post",
        url: `bill/pageList`,

        requestAdaptor: (rest) => {
          // console.log(rest, 'rest');
          let { gmtCreate, ...other } = rest.data;
          let datas = {
            ...other,
            startTimeStamp: rest.data.gmtCreate?.split(",")[0] * 1000,
            endTimeStamp: rest.data.gmtCreate?.split(",")[1] * 1000,
          };
          return {
            ...rest,
            data: datas,
          };
        },
        data: {
          enterpriseName: "${enterpriseName}",
          type: "${type}",
          pageNum: "${page}",
          pageSize: "${perPage}",
          gmtCreate: "${gmtCreate}",
        },
      },
      errors: {
        ajax: {
          handler: "handleAjaxError",
        },
      },
      functions: {
        handleAjaxError:
          "function(ctx) { console.log('接口请求失败:', ctx.error); }",
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: "table",
      //列表
      columns: [
        {
          name: "enterpriseName",
          label: "企业名称",
          type: "text",
        },
        /*   {
          "name": "moveInAddress",
          "label": "入驻地址",
          "type": "text"
        }, */
        {
          type: "tpl",
          name: "moveInAddress",
          label: "入驻地址",
          tpl: "${moveInAddress|truncate:18}",
          popOver: {
            trigger: "hover",
            position: "left-top",
            showIcon: false,
            body: {
              type: "tpl",
              tpl: "${moveInAddress}",
            },
          },
        },
        {
          name: "${type==1 ? '租金' : (type==2 ? '物业费' : (type==3 ? '电费' : '水费' ) )}",
          label: "账单类型",
          type: "text",
        },

        {
          name: "${billAmount / 100}",
          label: "账单金额",
          type: "text",
        },
        {
          name: "billDate",
          label: "账单日期",
          type: "text",
        },
        {
          name: "${status==0 ? '待缴费' : (status==1 ? '已缴费' : '欠费')}",
          label: "账单状态",
          type: "text",
        },
        {
          name: "paymentDate",
          label: "缴费日期",
          type: "text",
        },
        {
          name: "${paymentAmount / 100}",
          label: "缴费金额",
          type: "text",
        },
        {
          type: "operation",
          label: "操作",
          fixed: "right", // 固定在右侧
          width: 200,
          buttons: [
            {
              type: "button",
              label: "编辑账单",
              actionType: "dialog",
              dialog: {
                title: "编辑",
                actions: [
                  {
                    type: "button",
                    actionType: "cancel",
                    label: "取消",
                     level: "default",
                    primary: true,
                  },
                  {
                    label: "确认",
                    actionType: "confirm",
                    primary: true,
                    //"reload": "tab-s",
                    close: true,
                    type: "button",
                    api: {
                      method: "post",
                      url: `bill/update`,
                      messages: {
                        success: "操作成功！",
                      },

                      requestAdaptor: (rest) => {
                        // console.log(rest, 'rest');
                        let { gmtCreate, ...other } = rest.data;
                        // console.log(rest.data, 'rest.data');
                        let datas = {
                          ...other,
                          billAmount:
                            parseFloat(rest.data.billAmount).toFixed(2) * 100,
                          paymentAmount:
                            parseFloat(rest.data.paymentAmount).toFixed(2) *
                            100,
                        };
                        return {
                          ...rest,
                          data: datas,
                        };
                      },
                      data: {
                        uniCode: "${uniCode}",
                        type: "${type}",
                        billAmount: "${billAmountStr}",
                        billDate: "${billDateStamp}",
                        paymentDate: "${paymentDateStamp}",
                        paymentAmount: "${paymentAmountStr}",
                        id: "${id}",
                      },
                    },
                  },
                ],
                //表格
                body: [
                  {
                    type: "form",
                    rules: {},
                    body: [
                      {
                        type: "select",
                        name: "uniCode",
                        label: "企业名称",
                        required: true,
                        labelClassName: "text-muted",
                        inline: true,
                        source: {
                          url: `enterprise/getList`,
                          method: "post",

                          data: {
                            businessCode: "enterprise",
                          },
                          adaptor: function (payload, response, api, context) {
                            // console.log(payload);
                            let list = payload?.elements.map((item) => {
                              return {
                                value: item.uni_code,
                                label: item.name,
                              };
                            });
                            return {
                              data: list,
                              msg: "请求成功",
                              status: 0,
                            };
                          },
                        },
                      },
                      {
                        type: "select",
                        name: "type",
                        label: "账单类型",
                        required: true,
                        options: [
                          { label: "租金", value: "1" },
                          { label: "物业费", value: "2" },
                          { label: "电费", value: "3" },
                          { label: "水费", value: "4" },
                        ],
                      },
                      {
                        type: "input-text",
                        name: "billAmountStr",
                        label: "账单金额(元)",
                      },
                      {
                        type: "input-date",
                        name: "billDateStamp",
                        required: true,
                        label: "账单日期",
                        valueFormat: "x",
                      },
                      {
                        type: "input-date",
                        name: "paymentDateStamp",
                        required: true,
                        label: "缴费日期",
                        valueFormat: "x",
                      },
                      {
                        type: "input-text",
                        name: "paymentAmountStr",
                        label: "缴费金额(元)",
                      },
                    ],
                  },
                ],
              },
            },
            {
              type: "button",
              actionType: "ajax",
              label: "删除",
              level: "danger",
              confirmText: "是否确认删除",
              api: {
                method: "get",
                url: `bill/delete`,

                data: {
                  id: "${id}",
                },
              },
            },
          ],
        },
      ],
      alwaysShowPagination: true,
      // "autoFillHeight": false,
      autoFillHeight: {
        height: 450,
      },
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      //筛选
      filter: {
        title: "",
        submitText: "搜索",
        controls: [
          {
            type: "text",
            name: "enterpriseName",
            label: "企业名称",
            size: "md",
            placeholder: "请输入企业名称",
          },
          {
            type: "select",
            name: "type",
            size: "md",
            label: "账单类型",
            placeholder: "请选择状态",
            value: "",
            options: [
              { label: "全部", value: "" },
              { label: "租金", value: "1" },
              { label: "物业费", value: "2" },
              { label: "电费", value: "3" },
              { label: "水费", value: "4" },
            ],
          },
          {
            type: "input-date-range",
            name: "gmtCreate",
            size: "md",
            label: "账单时间",
          },

          {
            type: "reset",
            label: "重置",
          },
          {
            type: "submit",
            label: "搜索",
            primary: true,
          },
          /*  {
             "type": "button",
"size": "md",
             "label": "一键发送所有账单",
             "actionType": "download",
             "api": `/enterprise/business/downloadTemplate`
           }, */
          {
            type: "button",
            label: "新增账单",
            actionType: "dialog",
            dialog: {
              title: "新增",
              data: {},
              actions: [
                {
                  type: "button",
                  actionType: "cancel",
                  label: "取消",
                   level: "default",
                  primary: true,
                },
                {
                  label: "确认",
                  actionType: "confirm",
                  primary: true,
                  reload: "tab-s",
                  close: true,
                  type: "button",
                  api: {
                    method: "post",
                    url: `bill/add`,

                    data: [
                      {
                        uniCode: "${uniCode}",
                        type: "${type}",
                        billAmount: "${billAmount * 100}",
                        billDate: "${billDate}",
                        paymentDate: "${paymentDate}",
                        paymentAmount: "${paymentAmount * 100}",
                      },
                    ],
                  },
                },
              ],
              //表格
              body: [
                {
                  type: "form",
                  rules: {},
                  body: [
                    {
                      type: "select",
                      name: "uniCode",
                      label: "企业名称",
                      required: true,
                      labelClassName: "text-muted",
                      inline: true,
                      source: {
                        url: `enterprise/getList`,
                        method: "post",

                        data: {
                          businessCode: "enterprise",
                        },
                        adaptor: function (payload, response, api, context) {
                          // console.log(payload);
                          let list = payload?.elements.map((item) => {
                            return {
                              value: item.uni_code,
                              label: item.name,
                            };
                          });
                          return {
                            data: list,
                            msg: "请求成功",
                            status: 0,
                          };
                        },
                      },
                    },
                    {
                      type: "select",
                      name: "type",
                      label: "账单类型",
                      required: true,
                      options: [
                        { label: "租金", value: "1" },
                        { label: "物业费", value: "2" },
                        { label: "电费", value: "3" },
                        { label: "水费", value: "4" },
                      ],
                    },
                    {
                      type: "input-text",
                      name: "billAmount",
                      label: "账单金额(元)",
                    },
                    {
                      type: "input-date",
                      name: "billDate",
                      required: true,
                      label: "账单日期",
                      valueFormat: "x",
                    },
                    {
                      type: "input-date",
                      name: "paymentDate",
                      // "required": true,
                      label: "缴费日期",
                      valueFormat: "x",
                    },
                    {
                      type: "input-text",
                      name: "paymentAmount",
                      label: "缴费金额(元)",
                    },
                  ],
                },
              ],
            },
          },
        ],
        onSubmit: "reload",
        // "actions": [

        // ]
      },
    },
  ],
};
</script>

<template>
  <div class="publicTableStyle">
    <AmisComponent :amisjson="amisjson2" />
  </div>
</template>
<style scoped lang="scss">
.tab {
  padding-top: 20px;
  padding-left: 10px;
}

.on {
  background-color: #169bd5;
  color: #fff;
}
</style>

