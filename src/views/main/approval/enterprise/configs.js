import { perFix, publicConfig, baseEvn, token } from '@/utils/utils';
import { transformData } from '@/utils/formData';
import { enterpriseData, transformObject } from '@/utils/enterpriseFormData';

import dayjs from 'dayjs';

const handleData = (title, state) => {
  return {
    title: title,
    size: 'md',
    actions: [
      {
        type: 'button',
        actionType: 'cancel',
        label: '取消',
        level: 'default',
        primary: true,
      },
      {
        label: '确认',
        actionType: 'confirm',
        primary: true,
        reload: 'tab-s',
        close: true,
        type: 'button',
        api: {
          method: 'post',
          url: `approve/submit/approval`,
          messages: {
            success: '操作成功！',
          },
          headers: {
            token: `${token()}`,
          },
          data: {
            approveId: '${id|default:undefined}',
            status: '${approvalStatus|default:undefined}',
            note: '${note|default:undefined}',
            attachments: '${attachments|default:undefined}',
          },
        },
      },
    ],
    body: [
      {
        type: 'form',
        // columnCount: 1,
        rules: {},
        body: [
          {
            type: 'select',
            name: 'approvalStatus',
            label: '审批结果',
            size: 'md',
            placeholder: '请选择审批结果',
            value: '6', // 添加默认值为"通过"
            // = 0 ? '异常' :'警告'}",
            options: [
              { label: '通过', value: '6' },
              { label: '不通过', value: '7' },
            ],
          },
          {
            label: '审批说明',
            type: 'textarea',
            name: 'note',
            id: 'u:ea2bfdba9bea',
            // "size": "",
            required: false,
            requiredOn: "approvalStatus === '7'", // 当选择不通过时必填
            validations: {
              notEmptyString: {
                message: '选择不通过时，审批说明为必填项',
              },
            },
            validationErrors: {
              notEmptyString: '选择不通过时，审批说明为必填项',
            },
            minRows: 6,
          },
          {
            label: '审批人',
            type: 'input-text',
            name: 'user',
            id: 'u:ea2bfdba9bea',
            value: `${
              JSON.parse(localStorage.getItem('userInfo') || '{}')?.realName ||
              ''
            }`,
            disabled: true,
            required: true,
            minRows: 6,
          },
          {
            type: 'input-image',
            name: 'attachments',
            label: '附件',
            useChunk: false,
            downloadUrl: false,
            maxSize: 20990000,
            accept: '.jpg,.png',
            //"required": true,
            multiple: true,
            maxLength: 3,
            receiver: {
              method: 'POST',
              url: `upload`,
            },
          },
        ],
      },
    ],
  };
};
const viewDraw = {
  type: 'page', 
  "size": "lg",
  title: '企业详情',
  className:'enterpriseDetail ',
  actions: [],
  body: [
    {
      type: 'form',
      columnCount: 2,
      labelWidth: 100,
         className: 'form-b-sm',
      initApi: {
        method: 'get',
        url: '/approve/detail',
        data: {
          id: '${id}',
          operateType: '${operateType}', // 将表格行的operateType传递给抽屉页面
        },
        adaptor: function (payload, response, api, context) {
          // console.log(api.query.operateType !== '3');
          const transformedObj = transformObject(
            api.query.operateType !== '3'
              ? payload.afterData.element
              : payload.beforeData
          );
          // console.log(transformedObj);
          let data = transformedObj;
          data.rent_status = transformedObj.rent_status == 1 ? 'true' : 'false';

          return {
            data,
            msg: '请求成功',
            status: 0,
          };
        },
      },
      disabledOn: 'true',
      rules: [],
      body: enterpriseData('preview'),
    },
  ],
};
export const getColums = (haveApprovel, openApprovel) => {
  let defaultBtns = [
    {
      type: 'button',
      label: '查看',
      level: 'primary',
      actionType: 'drawer',

      drawer: viewDraw,
    },
    {
      // "icon": "fa fa-edit",
      type: 'button',
      label: '审批记录',
      level: 'primary',
      onClick: (e, item) => {
        openApprovel(item);
      },
    },
  ];
  const approvel = [
    {
      // "icon": "fa fa-edit",
      type: 'button',
      level: 'primary',
      label: '审批',
      actionType: 'dialog',
      dialog: handleData('审批', 'insert'),
    },
  ];
  const defaultColums = [
    {
      type: 'operation',
      label: '企业名称',
      width: 200,
      fixed: 'left',
      buttons: [
        {
          type: 'button',
          level: 'link',
          size: 'md',
          label: '${enterpriseName}',
          // "disabledOn": "operateType === '3'",
          actionType: 'drawer',

          drawer: viewDraw,
        },
      ],
    },
    {
      name: 'enterpriseName',
      label: '企业名称',
      type: 'text',
    },
    {
      name: 'enterpriseUniCode',
      label: '社会信用代码',
      type: 'text',
    },
    // {
    //   name: "${energy_type == 0 ? '水' :'电'}",
    //   label: "企业类型",
    //   type: "text",
    // },
    {
      name: "${enterpriseType?[ '其他', '独资' ,'合资' ,'民营' ,'国有','股份制'][enterpriseType]:'其他'}",
      label: '企业类型',
      type: 'tpl',
      tpl: '<span class="inline-block px-2 py-1 text-xs font-medium rounded-full ${enterpriseType==1 ? "bg-blue-100 text-blue-800" : enterpriseType==2 ? "bg-green-100 text-green-800" : enterpriseType==3 ? "bg-yellow-100 text-yellow-800" : enterpriseType==4 ? "bg-purple-100 text-purple-800" : enterpriseType==5 ? "bg-red-100 text-red-800" : "bg-gray-100 text-gray-800"}">${enterpriseType?[ "其他", "独资" ,"合资" ,"民营" ,"国有","股份制"][enterpriseType]:"其他"}</span>',
    },
    {
      name: 'createUserName',
      label: '操作人',
      type: 'text',
    },
    {
      name: 'gmtCreate',
      label: '操作时间',
      type: 'text',
    },
    {
      name: "${['默认', '新增' ,'编辑' ,'删除' ,'重新提交','待审批','审批通过','审批不通过','执行失败'][operateType]||'-'}",
      label: '执行操作',
      type: 'tpl',
      tpl: '<span class="inline-block px-2 py-1 text-xs font-medium rounded-full ${operateType==1 ? "bg-green-100 text-green-800" : operateType==2 ? "bg-blue-100 text-blue-800" : operateType==3 ? "bg-red-100 text-red-800" : operateType==4 ? "bg-orange-100 text-orange-800" : operateType==5 ? "bg-yellow-100 text-yellow-800" : operateType==6 ? "bg-green-100 text-green-800" : operateType==7 ? "bg-red-100 text-red-800" : operateType==8 ? "bg-gray-100 text-gray-800" : "bg-gray-100 text-gray-800"}">${["默认", "新增" ,"编辑" ,"删除" ,"重新提交","待审批","审批通过","审批不通过","执行失败"][operateType]||"-"}</span>',
    },

    {
      name: "${['默认', '新增' ,'编辑' ,'删除' ,'重新提交','待审批','审批通过','审批不通过','执行失败'][status]||'-'}",

      label: '审批状态',
      type: 'tpl',
      tpl: '<span class="inline-block px-2 py-1 text-xs font-medium rounded-full ${status==5 ? "bg-yellow-100 text-yellow-800" : status==6 ? "bg-green-100 text-green-800" : status==7 ? "bg-red-100 text-red-800" : status==8 ? "bg-gray-100 text-gray-800" : "bg-gray-100 text-gray-800"}">${["默认", "新增" ,"编辑" ,"删除" ,"重新提交","待审批","审批通过","审批不通过","执行失败"][status]||"-"}</span>',
    },
    {
      type: 'operation',
      label: '操作',
      width: 200,
      buttons: haveApprovel ? [...defaultBtns, ...approvel] : defaultBtns,
    },
  ];
  return haveApprovel ? [...defaultColums] : defaultColums;
};
export const approvedAPi = {
  method: 'post',
  url: `approve/enterprise/page`,
  requestAdaptor: (rest) => {
    let {
      gmtCreate,
      enterpriseName,
      enterpriseUniCode,
      createUser,
      operateType,
      statusList,
      pageNum,
      pageSize,
    } = rest.data;
    let time = gmtCreate?.split(',');
    let gmtCreateStart =
      (time[0] &&
        dayjs(Number(time[0]) * 1000).format('YYYY-MM-DD HH:mm:ss')) ||
      undefined;
    let gmtCreateEnd =
      (time[1] &&
        dayjs(Number(time[1]) * 1000).format('YYYY-MM-DD HH:mm:ss')) ||
      undefined;
    let datas = {
      enterpriseName: enterpriseName || undefined,
      enterpriseUniCode: enterpriseUniCode || undefined,
      createUser: createUser || undefined,
      operateType: operateType || undefined,
      gmtCreateStart,
      gmtCreateEnd,
      statusList: statusList ? statusList : [6, 7, 8],
      pageNum: pageNum,
      pageSize: pageSize,
    };
    return {
      ...rest,
      data: datas,
    };
  },
  data: {
    gmtCreate: "${gmtCreate|default:''}",
    enterpriseName: "${enterpriseName|default:''}",
    enterpriseUniCode: "${enterpriseUniCode|default:''}",
    createUser: "${createUser|default:''}",
    operateType: "${operateType|default:''}",
    statusList: "${statusList|default:''}",
    pageNum: '${page|default:1}',
    pageSize: '${perPage|default:10}',
  },
  responseData: {
    '&': '$$',
    items: 'records',
  },
};
export const approvedFilter = {
  title: '',
  submitText: '',
  controls: [
    {
      type: 'text',
      name: 'enterpriseName',
      label: '企业名称',
      size: 'md',
      placeholder: '请输入企业名称',
    },
    {
      type: 'text',
      name: 'enterpriseUniCode',
      label: '企业信用代码',
      size: 'md',
      placeholder: '请输入企业信用代码',
    },
    {
      type: 'text',
      name: 'createUser',
      label: '操作人',
      size: 'md',
      placeholder: '请输入操作人',
    },
    {
      type: 'select',
      name: 'statusList',
      label: '审批状态',
      size: 'md',
      placeholder: '请选择审批状态',
      // = 0 ? '异常' :'警告'}",
      options: [
        { label: '全部', value: [6, 7, 8] },
        { label: '通过', value: [6] },
        { label: '不通过', value: [7] },
      ],
    },
    {
      type: 'select',
      name: 'operateType',
      label: '执行操作',
      size: 'md',
      placeholder: '请选择执行操作',
      // 0 ? '未处理' :'已处理'}",
      options: [
        { label: '全部', value: '' },
        { label: '新增', value: '1' },
        { label: '编辑', value: '2' },
        { label: '删除', value: '3' },

        // { label: "重新提交", value: "4" },
      ],
    },
    {
      type: 'input-date-range',
      name: 'gmtCreate',
      label: '操作时间',
    },
    {
      type: 'reset',
      label: '重置',
      //   "name": "gmtCreate",
      //   "onEvent": {
      //     "click": {
      //       "actions": [
      //         {
      //           "enterpriseName": "",
      //           "house_id": "",
      //           "warn_status": "",
      //           "handle_status": "",
      //           "energy_type": "",
      //         }
      //       ]
      //     }
      //   },
    },
    {
      type: 'submit',
      label: '搜索',
      primary: true,
    },
  ],
  onSubmit: 'reload',
  // "actions": [
  //   {
  //     "type": "reset",
  //     "label": "重置"
  //   },
  //   {
  //     "type": "submit",
  //     "label": "搜索",
  //     "primary": true
  //   },

  // ]
};

export const pendindAPi = {
  method: 'post',
  url: `approve/enterprise/page`,
  requestAdaptor: (rest) => {
    let {
      gmtCreate,
      enterpriseName,
      enterpriseUniCode,
      createUser,
      operateType,
      pageNum,
      pageSize,
    } = rest.data;
    let time = gmtCreate?.split(',');
    let gmtCreateStart =
      (time[0] &&
        dayjs(Number(time[0]) * 1000).format('YYYY-MM-DD HH:mm:ss')) ||
      undefined;
    let gmtCreateEnd =
      (time[1] &&
        dayjs(Number(time[1]) * 1000).format('YYYY-MM-DD HH:mm:ss')) ||
      undefined;
    let datas = {
      enterpriseName: enterpriseName || undefined,
      enterpriseUniCode: enterpriseUniCode || undefined,
      createUser: createUser || undefined,
      operateType: operateType || undefined,
      gmtCreateStart,
      gmtCreateEnd,
      statusList: [5],
      pageNum,
      pageSize,
    };
    return {
      ...rest,
      data: datas,
    };
  },
  data: {
    gmtCreate: "${gmtCreate|default:''}",
    enterpriseName: "${enterpriseName|default:''}",
    enterpriseUniCode: "${enterpriseUniCode|default:''}",
    createUser: "${createUser|default:''}",
    operateType: "${operateType|default:''}",
    pageNum: '${page|default:1}',
    pageSize: '${perPage|default:10}',
  },
  responseData: {
    '&': '$$',
    items: 'records',
  },
};

export const pendingFilter = {
  title: '',
  submitText: '',
  controls: [
    {
      type: 'text',
      name: 'enterpriseName',
      label: '企业名称',
      size: 'md',
      placeholder: '请输入企业名称',
    },
    {
      type: 'text',
      name: 'enterpriseUniCode',
      label: '企业信用代码',
      size: 'md',
      placeholder: '请输入企业信用代码',
    },
    {
      type: 'text',
      name: 'createUser',
      label: '操作人',
      size: 'md',
      placeholder: '请输入操作人',
    },
    // {
    //   type: "select",
    //   name: "warn_status",
    //   label: "审批状态",
    //   size: "md",
    //   placeholder: "请选择审批状态",
    //   // = 0 ? '异常' :'警告'}",
    //   options: [
    //     { label: "通过", value: "0" },
    //     { label: "不通过", value: "1" },
    //   ],
    // },
    {
      type: 'select',
      name: 'operateType',
      label: '执行操作',
      size: 'md',
      placeholder: '请选择执行操作',
      // 0 ? '未处理' :'已处理'}",
      options: [
        { label: '全部', value: '' },
        { label: '新增', value: '1' },
        { label: '编辑', value: '2' },
        { label: '删除', value: '3' },

        // { label: "重新提交", value: "4" },
      ],
    },
    {
      type: 'input-date-range',
      name: 'gmtCreate',
      label: '操作时间',
    },
    {
      type: 'reset',
      label: '重置',
      //   "name": "gmtCreate",
      //   "onEvent": {
      //     "click": {
      //       "actions": [
      //         {
      //           "enterpriseName": "",
      //           "house_id": "",
      //           "warn_status": "",
      //           "handle_status": "",
      //           "energy_type": "",
      //         }
      //       ]
      //     }
      //   },
    },
    {
      type: 'submit',
      label: '搜索',
      primary: true,
    },
  ],
  onSubmit: 'reload',
};
