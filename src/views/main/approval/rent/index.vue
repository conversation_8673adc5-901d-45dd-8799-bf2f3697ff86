<template>
  <div>
    <!-- 已申报 -->
    <div v-if="!!amisjson" class="zhengce publicTableStyle">
      <AmisComponent :amisjson="amisjson" />
    <ApprovalRecord v-if="dialogVisible" :dialogVisible="dialogVisible" @canleDig="canleApprovel" :data="approvalRecordData">
</ApprovalRecord>

    </div>
  </div>
</template>
<script setup>

import { onBeforeMount, onMounted, ref } from 'vue'
import AmisComponent from '@/components/amis/index.vue'
import { perFix, publicConfig, baseEvn, token } from '@/utils/utils';
import { useStore } from 'vuex'
import { useRoute, useRouter } from 'vue-router';
import { approvedAPi, approvedFilter, pendingFilter,pendindAPi ,getColums} from './configs'
import ApprovalRecord from '@/components/dialog/ApprovalRecord.vue'
import { transformData} from '@/utils/formData'
const route = useRoute()
const router = useRouter()
const dialogVisible = ref(false)
const approvalRecordData = ref([])
const canleApprovel = () => {
  approvalRecordData.value = []
  dialogVisible.value = false
}
// const openApprovel =  (item) => {
//   const data = {
//     "businessScope": "enterprise",//类型 企业、租金、租约
//     "approveId": item.data.id,//消息id
//     //"relationId": item.data.id//原id
//   }
//   // const res = await approvalListAPI(data)
//   approvalRecordData.value = data
//   dialogVisible.value = true
// }

 
let amisjson = ref(null)
onMounted(() => {
  init()

})
const pendingColumns = (state) => {
  const openApprovel =  (item) => {
  const data = {
    "businessScope": "lease_rent",//类型 企业、租金、租约
    "approveId": item.data.id,//消息id
    //"relationId": item.data.id//原id
  }
  // const res = await approvalListAPI(data)
  approvalRecordData.value = data
  dialogVisible.value = true
}
  return getColums(state, openApprovel)
}
const init = () => {
    let tabs = {
    "type": "tabs",
      // "tabsMode": "radio",
    unmountOnExit:true,
    "tabs": [
      {
        "title": "待审批",
        "body": publicConfig({ title: '', columns:pendingColumns(true), api:pendindAPi, filter:pendingFilter }),
        "id": "u:319a552aa436"
      },
      {
        "title": "已审批",
        "body": publicConfig({ title: '', columns:  pendingColumns(false), api: approvedAPi, filter: approvedFilter }),
        // publicConfig({title:'能耗监测', columns, api, filter }),
        // 
        "id": "u:4a07b35df9ef"
      }
    ],
    "id": "u:238ba61a4079",
    "className": "tabsClass",
    "linksClassName": "tabsTitle"
  }
  // let data = publicConfig({ columns, api, filter })
  amisjson.value = tabs
  
}
</script>
<style lang="scss">
.boxf{
  background-color: #fff;
  padding: 10px;
  margin-bottom: 20px;
}
.amis-scope .headerTitle {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  text-align: right;
}

.amis-scope .leftTitle {
  line-height: 33px;
  width: 100px;
}
.zhengce{

.test{
  display: inline-block;
  font-size:16px;
  text-align: center;
  width: 100%;
  font-weight: 500;

}
.title{
  font-size:18px;
  width: 100%;
  font-weight: 500;
}
// .p-border{
// border: 1px solid #eee;
// margin:10px 50px ;
// }
.input-number{
  margin: 0 15px;
}
}
 


</style>