.cardsItem {
    .icons0 {
        width: 50px;
        height: 50px;
        margin: 0 auto;
        background: center / contain no-repeat;
        background-size: contain;
        background: center/contain no-repeat url("@/assets/images/online/icon1.png");
    }

    .icons1 {
        width: 50px;
        height: 50px;
        margin: 0 auto;
        background: center / contain no-repeat;
        background-size: contain;
        background: center/contain no-repeat url("@/assets/images/online/icon2.png");
    }

    .icons2 {
        width: 50px;
        height: 50px;
        margin: 0 auto;
        background: center / contain no-repeat;
        background-size: contain;
        background: center/contain no-repeat url("@/assets/images/online/icon3.png");
    }

    .icons3 {
        width: 50px;
        height: 50px;
        margin: 0 auto;
        background: center / contain no-repeat;
        background-size: contain;
        background: center/contain no-repeat url("@/assets/images/online/icon4.png");
    }

    .icons5 {
        width: 50px;
        height: 50px;
        margin: 0 auto;
        background: center / contain no-repeat;
        background-size: contain;
        background: center/contain no-repeat url("@/assets/images/online/icon6.png");
    }

    .icons4 {
        width: 50px;
        height: 50px;
        margin: 0 auto;
        background: center / contain no-repeat;
        background-size: contain;
        background: center/contain no-repeat url("@/assets/images/online/icon5.png");
    }
}

.nodata {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .noimg {
        width: 116px;
        height: 96px;
        background: no-repeat url("@/assets/cooperation/no-data.png");
        background-size: contain;
    }
}


.onlineService {
    width: 100%;
    // height: calc(100vh - 50px);
overflow: scroll;

    padding: 60px 20px 60px;
    background: #fafcff top left 100% / contain no-repeat url("@/assets/images/online/bg1.png");

    .option {
        display: flex;
        width:100%;
        height: auto;
        margin-top: 15px;

        span.title {
            padding: 16px;
            font-size: 16px;
            display: flex;
            font-family: Source Han Sans CN-Medium, Source Han Sans CN;
            font-weight: 500;
            color: #000000;
        }
.message{
    overflow: scroll;
}
        .left {
            
            width: 50%;
            height: 200px;
            background: #ffffff;
            box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.25);
            border-radius: 6px 6px 6px 6px;
            opacity: 1;
           
        }

        .right {
            width: 50%;
            height: 200px;
            background: #ffffff;
            box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.25);
            border-radius: 6px 6px 6px 6px;
            opacity: 1;
            margin-left: 15px;
        }

        .content {
            padding: 0px 16px;
        }

    }

    .head {
        // position: absolute;
        // margin-top: 80px;
        // margin-left: 16px;
        font-size: 16px;
        font-weight: 500;
        color: #000;
    }

    .headerTitle {
        font-size: 30px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
        line-height: 22px;
        margin-bottom: 22px;

     
    }
    .des {
        font-size: 16px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 22px;
    }
    .cards {
        // width: 995px;
        height: 200px;
        background: #ffffff;
        box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.25);
        border-radius: 6px 6px 6px 6px;
        opacity: 1;
        display: flex;
        justify-content: space-around;
        align-items: flex-start;
        margin-top: 60px;
        flex-wrap: wrap;

        .cardsMain {
            width: 100%;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 0 50px;
        }

        .head {
            padding: 16px;
            text-align: left;
            width: 100%;
        }

        .cardsItem {
            text-align: center;

            .icons {
                width: 50px;
                height: 50px;
                margin: 0 auto;
                background: center / contain no-repeat;
                //  url("@/assets/images/online/icon1.png");
                // background: red;
            }

            // width: 150px;

            .text {
                margin: 5px 0;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.65);
            }
        }
    }
.record1{
    width: calc(100% - 490px);
}
    .record {
        width: 490px;
        margin-top: 60px;
        margin-left: 15px;
        height: 629px;
        background: #ffffff;
        box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.25);
        border-radius: 6px 6px 6px 6px;
        opacity: 1;

        span.title {
            // padding: 16px;
            font-size: 16px;
            display: flex;
            font-family: Source Han Sans CN-Medium, Source Han Sans CN;
            font-weight: 500;
            color: #000000;
        }

        .recordContent {
            padding: 16px;
        }

        // .recordList {
        //     position: relative;
        //     width: 100%;
        //     height: 75px;
        //     background: #F7F9FD;
        //     border-radius: 4px 4px 4px 4px;
        //     padding: 13px;
        //     color: black;
        //     display: flex;
        //     align-content: space-around;
        //     flex-wrap: wrap;

        //     .showText {
        //         width: 100%;
        //     }

        //     .tips {
        //         right: 0;
        //         top: 0;
        //         position: absolute;
        //         // float: right;
        //         padding: 0px 12px;
        //         // width: 50px;
        //         background: #437BFF;
        //         border-radius: 0px 4px 0px 4px;
        //         color: white;
        //         text-align: center;
        //     }

        //     .value {
        //         color: rgba(0, 0, 0, 0.65);
        //     }

        //     .label,
        //     .labelText {
        //         color: rgba(0, 0, 0, 0.45)
        //     }

        //     .policy {
        //         margin-right: 10px;
        //         padding: 1px 6px;
        //         //                 width: 56px;
        //         // height: 20px;
        //         background: #E4F2FF;
        //         border-radius: 2px 2px 2px 2px;
        //         //                 width: 48px;
        //         // height: 26px;
        //         color: #1483EB;

        //     }
        // }
    }
}

.cancel {
    position: absolute;
    bottom: 75px;
    width: 54px;
    height: 32px;
    left: 50%;
}

.serves {
    display: flex;
    flex-wrap: wrap;
    padding: 10px 30px;
    max-height: 120px;
    overflow: scroll;
    // width: 490px;
    .servesItem {
        width: 50%;
        display: flex;
        flex-wrap: nowrap;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 16px;
      cursor: pointer;
// -webkit-box-orient:vertical;
        .icons {
            width: 24px;
            height: 24px;
            background: center / contain no-repeat;
            margin-right: 10px;
        }

        .icons1 {
            background-image: url("@/assets/images/online/textIcon/icon1.png");
        }

        .icons2 {
            background-image: url("@/assets/images/online/textIcon/icon2.png");
        }

        .icons3 {
            background-image: url("@/assets/images/online/textIcon/icon3.png");
        }

        .icons4 {
            background-image: url("@/assets/images/online/textIcon/icon4.png");
        }

        .icons5 {
            background-image: url("@/assets/images/online/textIcon/icon5.png");
        }

        .icons6 {
            background-image: url("@/assets/images/online/textIcon/icon6.png");
        }

        .icons8 {
            background-image: url("@/assets/images/online/textIcon/icon8.png");
        }
        .icons9 {
            background-image: url("@/assets/images/online/textIcon/icon9.png");
        }
        .icons10 {
            background-image: url("@/assets/images/online/textIcon/icon10.png");
        }
        .icons11 {
            background-image: url("@/assets/images/online/textIcon/icon11.png");
        }
        .icons12 {
            background-image: url("@/assets/images/online/textIcon/icon12.png");
        }
        .icons13 {
            background-image: url("@/assets/images/online/textIcon/icon13.png");
        }
        .icons14 {
            background-image: url("@/assets/images/online/textIcon/icon14.png");
        }
        .icons15 {
            background-image: url("@/assets/images/online/textIcon/icon15.png");
        }
        .icons16 {
            background-image: url("@/assets/images/online/textIcon/icon16.png");
        }
        .icons17 {
            background-image: url("@/assets/images/online/textIcon/icon17.png");
        }
        .icons18 {
            background-image: url("@/assets/images/online/textIcon/icon18.png");
        }
        .icons19 {
            background-image: url("@/assets/images/online/textIcon/icon19.png");
        }
        .icons20 {
            background-image: url("@/assets/images/online/textIcon/icon20.png");
        }
        .icons21 {
            background-image: url("@/assets/images/online/textIcon/icon21.png");
        }
        .icons22 {
            background-image: url("@/assets/images/online/textIcon/icon22.png");
        }

        .text {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        -webkit-line-clamp: 1;/*第几行裁剪*/
        
            // width: calc(100%);
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
        }
    }
}

 

.onlineService .option {

    .topTitle {
        span.title {
            padding: 0;
        }

    }
}

.topTitle {
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    span.title {
        padding: 0;
    }
}

.detail {
    cursor: pointer;
    font-size: 12px;
    color: #437BFF;
}

.checkText {
    margin: 5px 0;
}
.unread{
    height: 16px;
    padding: 0 5px;
    line-height: 16px;
    text-align: center;
    font-weight: 400;
    font-size: 12px;
    color: white;
    border-radius: 12px;
    background: red;
    margin-top: -5px;
    transform: scale(0.8);
}

.detailBtn{
    display: flex;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;
}

.leftBtn,
.rightBtn {
    width: 15px;
    height: 15px;
    cursor: pointer;
    // opacity: 0.7;
}
.isOpacity{
    opacity: 0.2;
}
.leftBtn {
    background: center / contain no-repeat url('@/assets/images/online/zuojiantou.png');

}

.rightBtn {
    background: center / contain no-repeat url('@/assets/images/online/youjiantou.png');
}
.stateTips{
    margin-top: -5px;
    width: 80px;
text-align: center;
    padding: 3px 0px ;
    background: var(--system-primary-color);
    color: #ffffff;
    font-size: 14px;

}
::-webkit-scrollbar {
    /* 对应纵向滚动条的宽度 */
    width: 3px;
    /* 对应横向滚动条的宽度 */
    height: 3px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
    background-color:transparent;
    border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
    display: none;
}