<template>
  <!-- <Header /> -->
  <div class="onlineService">
    <div class="headerTitle">用大数据和AI打造智能园区</div>
    <div class="des">您好，欢迎来到江苏省数字交通产业园综合服务平台</div>

    <div style="display: flex">
      <div class="record1" style="display: flex; flex-direction: column">
        <div class="cards">
          <div class="head">在线办事</div>
          <div class="cardsMain">
            <div
              class="cardsItem"
              v-for="(list, index) in listData"
              :key="list.id"
              @click="handleOpen(list)"
            >
              <div :class="'icons' + index"></div>
              <div class="text">{{ list.name }}</div>
            </div>
          </div>
        </div>
        <div class="option">
          <div class="left">
            <div class="topTitle">
              <span class="title">快捷服务</span>
              <span class="detailBtn">
                <div
                  class="leftBtn"
                  @click="changeLeft"
                  :class="{ isOpacity: currentText === 1 }"
                ></div>
                {{ currentText }}/4
                <div
                  class="rightBtn"
                  :class="{ isOpacity: currentText === 4 }"
                  @click="changeRight"
                ></div>
              </span>
            </div>
            <div class="serves">
              <div
                class="servesItem"
                v-for="(list, index) in currentTextList"
                :key="list.id"
              >
                <!--    :style="{ backgroundImage: `url(@/assets/images/online/textIcon/icon${list?.id}.png)` }"-->
                <div class="icons" :class="'icons' + list.id"></div>
                <div class="text">
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    :content="list.name"
                    placement="bottom"
                  >
                    {{ list.name }}
                  </el-tooltip>
                </div>
              </div>
            </div>
          </div>
          <div class="right message">
            <div class="topTitle">
              <span class="title">租约账单</span>
              <span class="detail" @click="goDetail('third')">查看全部></span>
            </div>
            <div class="content">
              <div v-if="checkData">
                <div
                  class="stateTips"
                  :type="['warning', 'primary', 'danger'][checkData.status]"
                >
                  {{ ["待生效", "生效中", "已失效"][checkData.status] }}
                </div>
                <div class="checkText">
                  <span>入住房源：</span>
                  <span>{{ checkData?.moveInAddress || "-" }}</span>
                </div>
                <div class="checkText">
                  <span>起租日期：</span>
                  <span>{{ checkData?.moveInDate || "-" }}</span>
                </div>
                <div class="checkText">
                  <span>结束日期：</span>
                  <span>{{ checkData?.endDate || "-" }}</span>
                </div>
                <div>
                  <span class="detail" @click="handleOpen('leaseStatement')"
                    >详情></span
                  >
                </div>
              </div>
              <div v-else>
                <NoData />
              </div>
            </div>
          </div>
        </div>
        <div class="option">
          <div class="left message">
            <div class="topTitle">
              <span class="title"
                >我的消息
                <span class="unread" v-if="unread > 0">{{ unread }}</span></span
              >

              <span class="detail" @click="goDetail('first')">查看全部></span>
            </div>
            <MyMessage showHomePage="true" />
          </div>
          <div class="right message">
            <div class="topTitle">
              <span class="title">我的收藏</span>
              <span class="detail" @click="goDetail('second')">查看全部></span>
            </div>
            <MyCollect showHomePage="true" />
          </div>
        </div>
      </div>

      <div class="record message">
        <div class="topTitle">
          <span class="title">办事记录</span>
          <span class="detail" @click="goDetail('fourth')">查看全部></span>
        </div>
        <MyRecord showHomePage="true" />
      </div>
    </div>
  </div>
  <el-dialog
    v-model="dialogVisible"
    :title="currentAction?.name"
    custom-class=""
    :top="
      currentAction?.key !== 'leaseStatement' &&
      currentAction?.key !== 'businessDataReporting'
        ? '12vh'
        : '50px'
    "
    :width="
      currentAction?.key !== 'leaseStatement' &&
      currentAction?.key !== 'businessDataReporting'
        ? '55%'
        : '55%'
    "
    :before-close="handleClose"
  >
    <div>
      <div v-if="currentAction?.key === 'leaseStatement'">
        <LeaseStatement origin="onlineService" :id="checkData.id" />
      </div>
      <div v-else>
        <OnlineDialog
          :currentAction="currentAction"
          @handleClose="handleClose"
        />
        <!-- <el-button type="" @click="handleClose" class="cancel">取消 </el-button> -->
      </div>
    </div>
    <!-- <template #footer="">
      
    </template> -->
  </el-dialog>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref, computed, onBeforeMount } from "vue";
import { useStore } from "vuex";
import { useRoute, useRouter } from "vue-router";
import { ElMessageBox } from "element-plus";
import OnlineDialog from "./onlineDialog/index.vue";
import Header from "@/components/header/index.vue";
import LeaseStatement from "@/views/main/lease/com/addlease.vue";
import MyRecord from "@/views/main/personalCenter/components/myRecord/index.vue";
import MyMessage from "@/views/main/personalCenter/components/myMessage/index.vue";
import MyCollect from "@/views/main/personalCenter/components/myCollect/index.vue";
import NoData from "@/components/noData/index.vue";
// 租约账单
import { getMyCheck, countUnRead } from "@/api/personalCenter";
let checkData = ref();
const dialogVisible = ref(false);
const userInfo = computed(() =>
  JSON.parse(localStorage.getItem("userInfo") || "{}")
);
const store = useStore();
const router = useRouter();
const unread = ref();
const currentText = ref(1);
const listData = [
  {
    name: "物业报修",
    id: 1,
    keyName: "",
    key: "propertyRepair",
  },
  {
    name: "经营诉求",
    id: 2,
    keyName: "",
    key: "businessAppeal",
  },
  {
    name: "经营数据上报",
    id: 4,
    keyName: "",
    key: "businessDataReporting",
  },
  {
    name: "我要续租",
    id: 5,
    keyName: "",
    key: "renewLease",
  },
  {
    name: "我要退租",
    id: 6,
    keyName: "",
    key: "cancelRent",
  },
  //
  // {
  //     name:'物业报修',
  //     id:1
  // },
];
const serverList = [
  [
    {
      name: "涉企税费服务",
      id: 1,
      key: "1",
      url: "",
    },
    {
      name: "政策和办事咨询",
      id: 2,
      key: "2",
      url: "",
    },
    {
      name: "政策和办事信息查询",
      id: 3,
      key: "",
      url: "",
    },
    {
      name: "惠企政策兑现",
      id: 4,
      key: "",
      url: "",
    },
    {
      name: "企业一件事",
      id: 5,
      key: "",
      url: "",
    },
    {
      name: "公共资源交易查询",
      id: 6,
      key: "",
      url: "",
    },
  ],
  [
    {
      name: "产业园区规划及政策咨询服务",
      id: 8,
      key: "cancelRent",
      url: "",
    },
    {
      name: "政府服务平台运营服务",
      id: 9,
      key: "",
      url: "",
    },
    {
      name: "园区运营服务",
      id: 10,
      key: "",
      url: "",
    },
    {
      name: "品牌宣传服务",
      id: 11,
      key: "",
      url: "",
    },
    {
      name: "数字化服务",
      id: 12,
      key: "",
      url: "",
    },
    {
      name: "上市服务",
      id: 13,
      key: "cancelRent",
      url: "",
    },
  ],
  [
    {
      name: "知识产权服务",
      id: 14,
      key: "",
      url: "",
    },
    {
      name: "创投服务",
      id: 15,
      key: "",
      url: "",
    },
    {
      name: "双创服务",
      id: 16,
      key: "",
      url: "",
    },
    {
      name: "科技金融服务",
      id: 17,
      key: "",
      url: "",
    },
    {
      name: "成果转化服务",
      id: 18,
      key: "",
      url: "",
    },
    {
      name: "科创会展服务",
      id: 19,
      key: "cancelRent",
      url: "",
    },
  ],
  [
    {
      name: "项目规划服务",
      id: 20,
      key: "",
      url: "",
    },
    {
      name: "培训服务",
      id: 21,
      key: "",
      url: "",
    },
    {
      name: "科技人才服务",
      id: 22,
      key: "",
      url: "",
    },
  ],
];
const currentTextList = ref(serverList[0]);

const currentAction = ref({
  name: "物业报修",
  id: 1,
  key: "propertyRepair",
});

onBeforeMount(() => {
  if (localStorage.getItem("userState") === "user") {
    countUnRead().then((res) => {
      unread.value = res.data.unread;
    });
  }
});
onMounted(() => {
  // setFullScreen();
  // getCheck();
});

// onBeforeUnmount(() => {
//   store.commit("app/contentFullScreenChange", false);
// });
// const setFullScreen = () => {
//   store.commit("app/contentFullScreenChange", true);
// };
const getCheck = () => {
  getMyCheck()
    .then((result) => {
      // checkData.status
      // console.log(result.data.records.filter((item)=>item.status==='1'))
      checkData.value = result.data?.records?.[0];
      // ?.filter((item)=>item.status==='1')[0]
    })
    .catch((err) => {});
};
const handleOpen = (item) => {
  // console.log(item, "item");
  dialogVisible.value = true;
  if (item === "leaseStatement") {
    currentAction.value = {
      name: "租约账单",
      id: 7,
      key: "leaseStatement",
    };
  } else {
    currentAction.value = item;
  }
};
const handleClose = () => {
  dialogVisible.value = false;
};
const goDetail = (nameState) => {
  router.push(`/personalCenter?nameState=${nameState}`);
};
const changeLeft = () => {
  if (currentText.value > 1) {
    currentText.value = currentText.value - 1;
    currentTextList.value = serverList[currentText.value - 1];
  }
};
const changeRight = () => {
  if (
    currentText.value < 4 &&
    (currentText.value > 1 || currentText.value === 1)
  ) {
    currentTextList.value = serverList[currentText.value];
    currentText.value = currentText.value + 1;
  }
};
</script>
<style scoped lang="scss">
@import "./index.scss";
</style>
