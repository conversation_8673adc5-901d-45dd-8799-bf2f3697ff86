import { perFix, publicConfig, baseEvn, token } from "@/utils/utils";
import { ElMessage } from "element-plus";
import { transformData } from '@/utils/formData'
import { ref, onMounted, defineProps, watch, defineEmits } from "vue";
// import { render, reset } from 'amis';

const adaptor = (payload, response, api, context, fun) => {
  if (response.status === '0') {
    ElMessage({
      type: 'success',
      message: '请求成功'
    })
    setTimeout(() => {
      fun()
    }, 1500)
    return {
      data: response.data,
      msg: response.msg,
      status: 0
    };
  } else {
    return {
      data: response.data,
      msg: response.msg,
      status: response.status,
    };
  }
}
const getRealName = (fileArrays) => {
  if (fileArrays) {
    let fileArray = fileArrays?.split(',')
    let file = fileArray?.map((e) => {
      let list = e.split('/')
      let name = list[list.length - 1].split('-')
      let namess = name[name.length - 1]
      let realName = namess.substring(0, namess.lastIndexOf("."))
      return {
        name: realName,
        path: e
      }
    })
    return file
  }

}

export const dialogs = (types, fun) => {
  let buttons = [
    {
      type: "button",
      size: "md",
      label: "提交",
      actionType: "submit",
      level: "primary",
      className: 'submitBtn',
      id: "u:033aeb11ea19",
      "reload": "page",
      onClick: function (ctx, e) {
        // console.log(ctx,e)
      }
    },
    {
      "type": "button",
      "size": "md",
      label: "取消",
       level: "default",
      className: 'cancelBtn',
      onClick: function (ctx, e) {
        fun()
      }

    },
  ]
  let data = {
    // 物业报修
    propertyRepair: {
      type: "page",
      className: 'formMain',
      body: [
        {

          id: "u:775fee587691",
          type: "form",
          title: "",
          mode: "horizontal",
          dsType: "api",
          feat: "Insert",
          body: [
            {
              "type": "nested-select",
              "searchable": true,//可搜索。
              name: "houseId",
              "label": "地址",
              "onlyLeaf": true,
              size: "lg",
              source: {
                method: "get",
                url: `lease/getLeaseBuildingTreeByCurrentUser`,
                headers: {
                  token: `${token()}`,
                },
                /* "responseData": {
                  "options": "${items|pick:label~houseName,value~houseId}"
                } */
                required: true,
                // "labelField": "houseName",
                // "valueField": "houseId",
                adaptor: function (payload, response, api, context) {
                  let list = payload?.map((item) => {
                    return {
                      value: item.buildingId,
                      label: item.buildingName,
                      children: transformData(item.houseList)
                    };
                  });

                  return {
                    data: list,
                    msg: "请求成功",
                    status: 0,
                  };
                },
              },
            },
            {
              type: "input-datetime-range",
              label: "预约上门时间",
              name: "appointmentStartDateStamp",
              "extraName": "appointmentEndDateStamp",
              id: "u:7ac2041db387",
              valueFormat: "x",
              "timeFormat": "HH:mm",
              "shortcuts": [],
              required: true,
              //   "inputFormat": "YYYY年MM月DD日 HH时mm分ss秒"
              // format: "YYYY-MM-DD HH:mm:ss",
              //  "timeConstraints": {
              //           // "hours": {
              //           //   "min": 12,
              //           //   "max": 18
              //           // },
              //           "minutes": {
              //             "step": 15
              //           }
              //         }
            },
            {
              type: "textarea",
              label: "报修内容",
              name: "remark",
              id: "u:5e97b18cfe9b",
              required: true,
            },
            {
              type: "input-image",
              label: "图片上传",
              name: "image",
              autoUpload: true,
              proxy: true,
              uploadType: "fileReceptor",
              imageClassName: "r w-full",
              id: "u:b7aceb645670",
              multiple: true,
              maxLength: 9,
              delimiter: ",",
              useChunk: false,
              // "accept": ".jpg,.png,.PNG,.JPEG,",
              maxSize: 10495000,
              receiver: {
                method: "POST",
                url: `upload`,
                headers: {
                  token: `${token()}`,
                },
              },
            },
            ...buttons
          ],
          api: {
            url: `propertyRepair/add`,
            method: "post",
            headers: {
              token: `${token()}`,
            },
            data: {
              houseId: "${houseId}",
              remark: "${remark}",
              image: "${image}",
              appointmentEndDateStamp: "${appointmentEndDateStamp}",
              appointmentStartDateStamp: "${appointmentStartDateStamp}",

            },
            requestAdaptor: (rest) => {
              let { image, ...other } = rest.data

              let file = getRealName(image)
              let datas = {
                ...other,
                uploadFileList: file
              }
              return {
                ...rest,
                data: datas,
              }
            },
            adaptor: (payload, response, api, context) => {
              adaptor(payload, response, api, context, fun)
            }
          },

          resetAfterSubmit: true,
        },
      ],
      title: "",
      id: "u:c070dd0c3b4e",
    },
    // 经营诉求
    businessAppeal: {
      type: "page",
      className: 'formMain',
      body: [
        {

          id: "u:775fee587691",
          type: "form",
          title: "",
          mode: "horizontal",
          dsType: "api",
          feat: "Insert",
          body: [
            {
              "label": "描述",
              "type": "textarea",
              "name": "demandsDesc",
              "id": "u:ea2bfdba9bea",
              "required": true,
              maxLength: 200,
              showCounter: true,
              maxRows: 10,
              clearable: true,
              className: 'formLeft'
            },
            {
              "type": "input-file",
              "name": "demandsFile",
              "label": "附件",
              useChunk: false,
              maxSize: 20990000,
              "accept": ".jpg,.png,.gif,.psd,.tif,.bmp,.txt,.world,.pdf,.Excel,.doc,.docx,.wps,.xlsx,.xls,.et,.ett,.xlt,.dps,.dpt,.ppt,.pptx,.zip,.rar,.7z,.tar,.gz,.bz2,.xz",
              maxLength: 10,
              multiple: true,
              downloadUrl: false,
              className: 'formLeft',
              "receiver": {
                "method": "POST",
                "url": `upload`,
              },
            },
            ...buttons,
          ],
          api: {
            url: `businessDemands/save`,
            method: "post",
            headers: {
              token: `${token()}`,
            },
            data: {
              demandsDesc: "${demandsDesc}",
              demandsFile: "${demandsFile}",
              demandsImg: null
            },
            adaptor: (payload, response, api, context) => {
              adaptor(payload, response, api, context, fun)
            }
          },

          resetAfterSubmit: true,
        },
      ],
      title: "",
      id: "u:c070dd0c3b4e",
    },


    // 政策申报
    policyDeclaration: {
      type: "page",
      className: 'formMain',
      body: [
        {

          id: "u:775fee587691",
          type: "form",
          title: "",
          mode: "horizontal",
          dsType: "api",
          feat: "Insert",
          "initApi": {
            "method": "get",
            "url": `information/listPolicy`,

            "data": {
              // type:'2',
              isEnterprise: 'true'
            },
          },
          body: [
            {
              "type": "select",
              "searchable": true,
              "name": "policyId",
              size: 'md',
              "label": "申报政策",
              "required": true,
              // extractValue:"true",
              "labelField": "title",
              "valueField": "id",
              "source": "${items}"
              // "source": {
              //   "method": "get",
              //   "url": `information/listPolicy`,
              //   "headers": {
              //     "token": `${token()}`
              //   },
              //   // adaptor: function (payload, response, api, context) {
              //   //   console.log(payload);
              //   //   let list = payload.data.map(item => {
              //   //     return {
              //   //       value: item.id,
              //   //       label: item.title,
              //   //     }
              //   //   })
              //   //   return {
              //   //     data: list,
              //   //     msg: "请求成功",
              //   //     status: 0
              //   //   };
              //   // }
              // }
            },
            {
              "label": "申报描述",
              "type": "textarea",
              "name": "remark",
              "id": "u:ea2bfdba9bea",
              "required": true,
              maxLength: 200,
              showCounter: true,
              maxRows: 10,
              clearable: true,
            },
            {
              "type": "input-file",
              "name": "uploadFileList",
              "label": "附件",
              "id": "u:d41a4dff65af",
              useChunk: false,
              downloadUrl: false,
              maxSize: 20990000,
              "accept": ".jpg,.png,.gif,.psd,.tif,.bmp,.txt,.world,.pdf,.Excel,.doc,.docx,.wps,.xlsx,.xls,.et,.ett,.xlt,.dps,.dpt,.ppt,.pptx,.zip,.rar,.7z,.tar,.gz,.bz2,.xz",
              maxLength: 10,
              multiple: true,
              "receiver": {
                "method": "POST",
                "url": `upload`,

              },
            },
            ...buttons,
          ],
          api: {
            url: `policy/declaration/add`,
            method: "post",
            headers: {
              token: `${token()}`,
            },
            data: {
              policyId: "${policyId}",
              policyTitle: "${policyTitle}",
              remark: "${remark}",
              "source": "${items}",
              uploadFileList: "${uploadFileList}",
            },
            requestAdaptor: (rest) => {
              let { uploadFileList, policyId, source, ...other } = rest.data
              let policyName = source.find((item) => item.id === policyId)
              let file = getRealName(uploadFileList)
              let datas = {
                ...other,
                policyTitle: policyName.title,
                policyId,
                uploadFileList: file
              }
              return {
                ...rest,

                data: datas,
              }
            },
            adaptor: (payload, response, api, context) => {
              adaptor(payload, response, api, context, fun)
            }
          },

          resetAfterSubmit: true,
        },
      ],
      title: "",
      id: "u:c070dd0c3b4e",
    },

    // 经营数据上报
    businessDataReporting: {
      type: "page",
      className: 'formMain',
      body: [
        {

          id: "u:775fee587691",
          type: "form",
          title: "",
          mode: "horizontal",
          dsType: "api",
          feat: "Insert",
          body: [
            {
              "type": "grid",
              "columns": [
                {
                  "body": [
                    {
                      "type": "input-text",
                      "label": "企业名称",
                      "name": "name",
                      "id": "u:4fbe06f9ba6e",
                      "disabled": true,
                      "required": true,
                    },
                    {
                      "type": "input-text",
                      "label": "统一社会信用代码",
                      "name": "uniCode",
                      "id": "u:2e21df42228e",
                      "required": true,
                      "disabled": true
                    },
                    {
                      "type": "select",
                      "name": "fillingRange",
                      "label": "经营周期",
                      "required": true,
                      "labelClassName": "text-muted",
                      "inline": true,
                      "source": {
                        "url": `enterprise/business/getFillingRanges?type=3`
                      },
                    },
                    {
                      "type": "input-number",
                      "label": "产值(万元)",
                      "placeholder": "输入企业产值",
                      "name": "outputValue",
                      "id": "u:90235be82f16",
                      "required": true,
                    },
                    {
                      "type": "input-number",
                      "label": "税收(万元)",
                      "placeholder": "输入企业税收",
                      "name": "tax",
                      "id": "u:68c278f0728c",
                      "required": true,
                    },
                    {
                      "type": "input-number",
                      "label": "净利润(万元)",
                      "name": "profit",
                      "id": "u:6657aa7e303e",
                      //"required": true,
                    }

                  ],
                  "id": "u:0ae4306f18d4"
                },
                {
                  "body": [
                    {
                      "type": "radios",
                      "label": "是否高新",
                      "name": "is_high_tech",
                      "options": [
                        {
                          "label": "是",
                          "value": "1"
                        },
                        {
                          "label": "否",
                          "value": "0"
                        }
                      ],
                      "id": "u:6129f795e34b",
                      "value": "",
                      // "required": true,
                    },
                    {
                      "type": "input-number",
                      "label": "在职人数",
                      "name": "employee",
                      "placeholder": "输入企业本季度在职人数",
                      "id": "u:7f78eb4b60d2",
                      // "required": true,
                    },
                    {
                      "type": "input-number",
                      // 
                      "label": "人才数量",
                      "placeholder": "输入人才数量",
                      "name": "talents_num",
                      "id": "u:d1c1b7042444",
                      // "required": true,
                    },
                    {
                      "type": "input-number",
                      "label": "研发投入",
                      "placeholder": "输入研发投入金额",
                      "name": "rd_investment",
                      "id": "u:9f8542a0741c",
                      //"required": true,
                    },
                    {
                      "type": "input-text",
                      "label": "主营业务",
                      "placeholder": "请输入企业主营业务",
                      "name": "main_business",
                      "id": "u:0da6a952d062",
                      // "required": true,
                    },
                    {
                      "type": "input-text",
                      "label": "未来发展方向",
                      "placeholder": "未来发展方向",
                      "name": "main_development",
                      "id": "u:bf310d72ef9a",
                      // "required": true,
                    }
                  ],
                  "id": "u:b476231f301e"
                }
              ],
              "id": "u:75ca2cc6031a",
              className: 'gridClass'
            },
            {
              "type": "textarea",
              "label": "职称情况",
              "name": "textarea",
              "id": "u:fef68dd4f4e0",
              "minRows": 3,
              "maxRows": 20,
              "minRows": 3,
              "maxRows": 20,
              "maxLength": 200,
              "placeholder": "请输入职称情况(中高级及以上),最多输入200字",
              // "required": true,
            },
            {
              "type": "textarea",
              "label": "技术成果和专利情况",
              "placeholder": "输入技术成果或专利情况,最多输入200字",
              "name": "patents_achieve",
              "id": "u:4583b95d042e",
              "minRows": 3,
              "maxRows": 20,
              "maxLength": 200,
              // "required": true,
            },
            {
              "type": "input-file",
              "name": "attachment",
              "label": "附件",
              useChunk: false,
              downloadUrl: false,
              maxSize: 20990000,
              "accept": ".jpg,.png,.gif,.psd,.tif,.bmp,.txt,.world,.pdf,.Excel,.doc,.docx,.wps,.xlsx,.xls,.et,.ett,.xlt,.dps,.dpt,.ppt,.pptx,.zip,.rar,.7z,.tar,.gz,.bz2,.xz",
              maxLength: 10,
              multiple: true,
              "receiver": {
                "method": "POST",
                "url": `upload`,

              },
            },
          ],
          initApi: {
            url: `enterprise/getOwn`,
            method: "get",
            headers: {
              token: `${token()}`,
            },
            // adaptor: (payload)=> {
            //   setTimeout(()=>{
            //     fun()
            //   },1500)

            //     return {
            //       data: payload.data,
            //       msg:  payload.msg,
            //       status: 0
            //     };
            //   }
          },
          api: {
            url: `enterprise/business/saveMyBusiness`,
            method: "post",
            headers: {
              token: `${token()}`,
            },
            adaptor: (payload, response, api, context) => {
              adaptor(payload, response, api, context, fun)
            }
          },

          resetAfterSubmit: true,
        },
      ],
      title: "",
      id: "u:c070dd0c3b4e",
    },
    // 我要续租
    renewLease: {
      type: "page",
      className: 'formMain',
      body: [
        {

          id: "u:775fee587691",
          type: "form",
          title: "",
          mode: "horizontal",
          dsType: "api",
          feat: "Insert",
          body: [
            {
              "type": "select",
              "searchable": true,
              "name": "leaseId",
              size: 'md',
              "label": "我的租约",
              required: true,
              "labelField": "moveInAddress",
              "valueField": "id",
              "source": {
                "method": "get",
                "url": `lease/myList`,

              }
            },
            {
              "type": "tpl",
              "tpl": "提示：确定向园区管理方提出诉求吗？\n\n",
              "inline": true,
              "wrapperComponent": "",
              "id": "u:482becca7feb",
              className: 'toolTip'
            },
            ...buttons,
          ],
          api: {
            url: `pendingLease/save`,
            method: "post",
            headers: {
              token: `${token()}`,
            },
            data: {
              leaseId: "${leaseId}",
              operateType: "1",

            },
            adaptor: (payload, response, api, context) => {
              adaptor(payload, response, api, context, fun)
            }
          },

          resetAfterSubmit: true,
        },
      ],
      title: "",
      id: "u:c070dd0c3b4e",
    },
    // 我要退租
    cancelRent: {
      type: "page",
      className: 'formMain',
      body: [
        {

          id: "u:775fee587691",
          type: "form",
          title: "",
          mode: "horizontal",
          dsType: "api",
          feat: "Insert",
          body: [
            {
              "type": "select",
              "searchable": true,
              "name": "housesIds",
              size: 'md',
              "label": "我的租约",
              required: true,
              "labelField": "moveInAddress",
              "valueField": "id",
              "source": {
                "method": "get",
                "url": `lease/myList`,


              }
            },
            {
              "type": "tpl",
              "tpl": "提示：确定向园区管理方提出诉求吗？\n\n",
              "inline": true,
              "wrapperComponent": "",
              "id": "u:482becca7feb",
              className: 'toolTip'
            },
            ...buttons,
          ],

          api: {
            url: `pendingLease/save`,
            method: "post",
            headers: {
              token: `${token()}`,
            },
            data: {

              leaseId: "${housesIds}",
              operateType: "2",

            },
            adaptor: (payload, response, api, context) => {
              adaptor(payload, response, api, context, fun)
            }
          },

          resetAfterSubmit: true,
        },
      ],
      title: "",
      id: "u:c070dd0c3b4e",
    },
  };
  return data[types]
}



