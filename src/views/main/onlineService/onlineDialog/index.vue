
<template>
  <div v-if="amisjson" class="onlineDialog">
    <AmisComponent :amisjson="amisjson" />
  </div>
</template>
<script setup>
import { ref, onMounted, defineProps, watch,defineEmits } from "vue";
import AmisComponent from "@/components/amis/index.vue";
import { perFix, baseEvn, token } from "@/utils/utils";
import { dialogs } from "./dialog";

const props = defineProps(['currentAction']);
const emit = defineEmits(['handleClose'])

// 企业列表
const amisjson = ref();
watch((() => props.currentAction?.key),((newVal) => {
    amisjson.value = dialogs(newVal,changeOnView);
}), { deep: true })
watch((() => amisjson.value),((newVal) => {
  if( !newVal){
    amisjson.value = dialogs(props.currentAction?.key,changeOnView);
  }
 
}), { deep: true })
onMounted(() => {
  amisjson.value = dialogs(props.currentAction.key,changeOnView)
   
})
const changeOnView=()=>{
  emit('handleClose')
  amisjson.value = ''
}

</script>
<style lang="scss">
.formMain {
  .antd-Panel {
    border: 0;
    box-shadow: 0 0 0 0;
  }
  .toolTip {
    display: block;
    width: 100%; 
    height: 40px;
    text-align: center;
  }
}
.onlineDialog .submitBtn {
  margin-left: 45% !important;
}
.onlineDialog .cancelBtn {
  margin-left: 15px!important;
}
.gridClass{
  margin-bottom: 20px;
}
.antd-Panel-btnToolbar.antd-Panel-footer{
border: 0;
}

</style>
<style  lang="scss">
.formLeft{
  label{
    width: 30px !important;
  }
 
}
</style>
