<!--列表-->
<template>
  <div class="ms-doc">
    <div style="height: 20px"></div>
    <!--  查询条件 -->
    <div class="vue_search ms-title" @click="$emit('query')">
      <!--    :style="{height: isShowParams?'auto':'0px'}" -->
      <slot />
    </div>
  </div>
</template>

<script>
/* import Breadcrumb from '@/components/Breadcrumb'
import TablePagination from './table-pagination' */
/**
 * @property pageNo 当前tab索引，默认为 1
 * @property pageSize 当前tab名称数组
 * @property totalCount 是否显示查询右边向下箭头，默认 true 显示
 */
export default {
  name: 'TableLayout',
  components: {
    /*     TablePagination,
    Breadcrumb */
  },
  props: {
    pageNo: {
      type: Number,
      default: 0,
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    totalCount: {
      type: Number,
      default: 0,
    },

    selTabIndex: {
      type: Number,
      default: 1,
    },
    tabNameList: {
      type: Array,
      default: () => [],
    },
    //是否显示查询向下箭头
    showArrow: {
      type: Boolean,
      default: true,
    },
    //页面帮助显示
    isShowPageHelp: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isShowParams: true,
      border: {},
    };
  },

  methods: {
    seltabway(index) {
      this.$emit('seltabway', index);
    },
    getHeight() {
      this.$emit('showParamStu');
      localStorage.setItem(
        'selectHeight',
        document.getElementById('app').clientHeight -
          this.$refs.msSelect.offsetHeight -
          600
      );
    },
    // 分页大小
    handleSizeChange(val) {
      this.$emit('size-change', val);
    },
    // 分页行数
    handleCurrentChange(val) {
      this.$emit('current-change', val);
    },
    //页面帮助说明事件
    onClickHelp(index) {
      this.$emit('onPageHelpClick', index);
    },
  },
};
</script>
<style lang="scss" scoped>
// .ms-doc{padding-bottom: 100px;}
.vue_search {
  padding-top: 0px !important;
}
.vue_search .demo-form-inline {
   padding: 16px 0px 0px 0px !important;

  overflow: hidden;
}
</style>
