<template>
  <div>
    <!-- 政策申报 -->
    <div v-if="!!amisjson" class="publicTableStyle">
      <AmisComponent :amisjson="amisjson" />
    </div>
  </div>
</template>
<script setup>
import { onBeforeMount, onMounted, ref } from 'vue';
import AmisComponent from '@/components/amis/index.vue';
import { perFix, publicConfig, baseEvn, token } from '@/utils/utils';

import { useStore } from 'vuex';
import { useRoute, useRouter } from 'vue-router';
const route = useRoute();
const router = useRouter();

const passForm = (state) => {
  return {
    title: '通过',
    actions: [
      {
        type: 'button',
        size: 'md',
        actionType: 'cancel',
        label: '取消',
        level: 'default',
        level: 'default',
        primary: true,
      },
      {
        label: '确认',
        actionType: 'confirm',
        primary: true,
        reload: 'tab-s',
        close: true,
        type: 'button',
        size: 'md',
        api: {
          method: 'post',
          url: `policy/declaration/follow`,
          data: {
            id: '${id|default:undefined}',
            dealStatus: state,
            remark: '${remarkNew|default:undefined}',
            uploadFileList: '${uploadFileList|default:undefined}',
          },
          requestAdaptor: (rest) => {
            let { uploadFileList, ...other } = rest.data;
            let fileArray = uploadFileList.split(',');
            let file = fileArray?.map((e) => {
              let list = e.split('/');
              let name = list[list.length - 1].split('-')[1];
              let realName = name.substring(0, name.lastIndexOf('.'));
              return {
                name: realName,
                path: e,
              };
            });
            // console.log('rest', file)

            let datas = {
              ...other,
              uploadFileList: file,
            };
            return {
              ...rest,
              data: datas,
              // pageNum: page,
              // pageSize: perPage
            };
          },
        },
      },
    ],

    body: [
      {
        type: 'form',
        columnCount: 1,
        rules: {},

        body: [
          {
            label: '回复',
            type: 'textarea',
            name: 'remarkNew',
            id: 'u:ea2bfdba9bea',
            required: true,
          },
          {
            type: 'input-file',
            name: 'uploadFileList',
            label: '附件',
            useChunk: false,
            downloadUrl: false,
            maxSize: 20990000,
            id: 'u:d41a4dff65af',
            required: true,
            multiple: true,
            receiver: {
              method: 'POST',
              url: `upload`,
            },
          },
        ],
      },
    ],
  };
};
let formData = {
  type: 'page',
  title: '详情',
  body: [
    {
      type: 'form',
      title: '',
      submitText: '',
      initApi: {
        url: `policy/declaration/detail`,

        method: 'get',
        data: {
          id: '${id|default:undefined}',
        },
        responseData: {
          '&': '$$',
        },
      },
      body: [
        {
          type: 'card',
          header: {
            title: '',
            //  "${enterprise_name}<span style='color:red;font-size:12px'>${gmtCreate}</span>",
          },
          titleClassName: 'headerTitle',
          data: '${first}',
          body: [
            {
              name: 'policyTitle',
              label: '申报政策',
              type: 'static',
              labelClassName: 'leftTitle',
            },
            // policyTitle
            //                       gmtCreate
            {
              name: 'gmtCreate',
              label: '申报时间',
              type: 'date',
              // "src": "${img}",
              disabledOn: 'true',
              labelClassName: 'leftTitle',
            },
            {
              name: 'enterpriseName',
              label: '企业名称',
              type: 'static',
              // "src": "${img}",
              disabledOn: 'true',
              labelClassName: 'leftTitle',
            },
            {
              name: 'submitMobile',
              label: '联系方式',
              type: 'static',
              labelClassName: 'leftTitle',
            },

            // {
            //   "type": "tpl",
            //   "tpl": 'wwww',
            //   // "<a>sdjfnksjdksd</a>",
            // },
            {
              name: 'remark',
              label: '申报描述',
              type: 'static',
              labelClassName: 'leftTitle',
            },
            {
              type: 'list',
              label: '附件',
              source: '$relationFiles',
              listItem: {
                body: [
                  {
                    type: 'tpl',
                    tpl: "<a href='${relationFiles[index].path}'>${relationFiles[index].name}</a>",
                  },
                ],
              },
            },

            // {
            //   "name": "relationFiles",
            //   "label": "文件",
            //   "type": "static",
            //   // "src": "${img}",
            //   // "disabledOn": "true",
            //   // "labelClassName": "leftTitle",
            //   // documentLink:true,
            //   // downloadUrl:"relationFiles[0].path",
            // },
          ],
          mode: 'horizontal',
          id: 'qqq',
        },
        {
          type: 'card',
          disabledOn: "${dealStatus !== '0'}",
          header: {
            title:
              "${enterprise_name1}<span style='color:red;font-size:12px'>${gmtCreate1}</span>",
          },
          titleClassName: 'headerTitle',
          body: [
            {
              name: 'dealStatus',
              name: "${dealStatus==='1'?'通过':'退回'}",

              label: '状态',
              type: 'static',
              labelClassName: 'leftTitle',
            },
            {
              name: 'record.remark',
              label: '原因',
              type: 'static',
              labelClassName: 'leftTitle',
            },
            // {
            //   "name": "record.uploadFileList",
            //   "label": "文件",
            //   "type": "input-file",
            //   // "src": "${img}",
            //   "disabledOn": "true",
            //   "labelClassName": "leftTitle"
            // },
            {
              label: '文件',
              type: 'list',
              source: '$record.uploadFileList',
              listItem: {
                body: [
                  {
                    type: 'tpl',
                    tpl: "<a href='${record.uploadFileList[index].path}'>${record.uploadFileList[index].name}</a>",
                  },
                ],
              },
            },
          ],

          id: 'u:15008a1115bf',
        },
      ],
    },
  ],
  actions: [],
};
const columns = [
  {
    name: 'name',
    label: '企业名称',
    type: 'text',
    // "searchable": true
  },
  {
    name: 'contact',
    label: '联系人',
    type: 'text',
  },

  {
    name: 'phone',
    label: '手机号',
    type: 'text',
    // "searchable": true
  },

  {
    name: 'gmt_create',
    label: '账号申请日期',
    type: 'date',
    valueFormat: 'x',
  },

  {
    type: 'tpl',
    name: 'status',
    label: '审核状态',
    tpl: "<span class='${status == 0 ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200\" : (status == 1 ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200\" : \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200\")}'>${status == 0 ? '待审核' : (status == 1 ? '审核通过' : '审核不通过')}</span>",
  },

  {
    type: 'operation',
    label: '操作',
    fixed: 'right', // 固定在右侧
    width: 200,
    buttons: [
      // {
      //   // "icon": "fa fa-eye",
      //   "label": "预览",
      //   "type": "button",
      // "size": "md",
      //   "actionType": "drawer",
      //   "drawer": formData
      // },
      // {
      //   "disabledOn": "this.dealStatus === '1'||this.dealStatus === '2'",
      //   "label": "退回",
      //   "level": "link",
      //   "className": "text-danger",
      //   // "type": "button",
      // "size": "md",
      //   "actionType": "dialog",
      //   "dialog": passForm('2')
      // },
      {
        disabledOn: "this.status !== '0' ",
        level: 'primary',
        label: '通过',
        type: 'button',
        actionType: 'ajax',
        confirmText: '是否审核通过',
        confirmTitle: '通过',
        api: {
          method: 'post',
          url: `enterpriseRegister/audit`,

          data: {
            id: '${id}',
            status: '1',
          },
        },
      },
      {
        disabledOn: "this.status !== '0' ",
        level: 'danger',
        label: '拒绝',
        type: 'button',
        actionType: 'ajax',
        confirmText: '是否拒绝',
        confirmTitle: '拒绝',
        api: {
          method: 'post',
          url: `enterpriseRegister/audit`,

          data: {
            id: '${id}',
            status: '2',
          },
        },
      },
    ],
  },
];
const api = {
  method: 'post',
  url: `billund/getListPage`,

  // requestAdaptor: (rest) => {

  //   let { conditions, ...other } = rest.data
  //   let datas = {
  //     ...other,
  //     conditions:
  //   }
  //   return {
  //     ...rest,
  //     data: datas,

  //   }
  // },
  data: {
    businessCode: 'enterprise_register',
    conditions: [
      {
        compareType: 'eq',
        key: 'status',
        value: '${dealStatus|default:undefined}',
      },
    ],

    pageNum: '${page|default:1}',
    pageSize: '${perPage|default:10}',
  },
  responseData: {
    '&': '$$',
    items: 'records',
  },
};
const filter = {
  title: '',
  submitText: '',
  controls: [
    {
      type: 'select',
      name: 'dealStatus',
      label: '状态',
      size: 'md',
      // 0待审核 1审核通过 2审核不通过
      placeholder: '选择状态',
      options: [
        {
          label: '待审核',
          value: '0',
        },
        {
          label: '审核通过',
          value: '1',
        },
        {
          label: '审核不通过',
          value: '2',
        },
      ],
    },
    {
      type: 'reset',
      name: 'gmtCreate',
      label: '重置',
    },
    {
      type: 'submit',
      label: '搜索',
      primary: true,
    },
    // {
    //   "type": "button",
    // "size": "md",
    //   "label": "批量导出",
    //   "primary": true
    // },
  ],
  onSubmit: 'reload',
  // "actions": [
  //   {
  //     "type": "reset",
  //     "label": "重置"
  //   },
  //   {
  //     "type": "submit",
  //     "label": "搜索",
  //     "primary": true
  //   },

  // ]
};
let amisjson = ref(null);
onMounted(() => {
  let data = publicConfig({ columns, api, filter });
  amisjson.value = data;
});
</script>
<style>
.amis-scope .headerTitle {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  text-align: right;
}

.amis-scope .leftTitle {
  line-height: 33px;
  width: 100px;
}
</style>
