<template>
  <div>
    <el-drawer
      width="50%"
      title="新增用户"
      :visible="dialogFormVisible"
      :close-on-click-modal="false"
      :wrapper-closable="false"
      @close="cancel"
    >
      <div class="tabs">
        <el-tabs v-model="activeName" stretch @tab-click="handleClick">
          <el-tab-pane label="新增账号" name="first">
            <el-form
              ref="form"
              label-position="left"
              :model="form"
              class="form"
              :rules="rules"
              label-width="100px"
            >
              <el-form-item prop="orgCode" label="机构名称">
                <el-select
                  v-model="form.orgCode"
                  placeholder="请选择"
                  @change="departmentList"
                >
                  <el-option
                    v-for="item in autoList"
                    :key="item.id"
                    :label="item.orgName"
                    :value="item.orgCode"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="部门名称" prop="deptId">
                <!--                 <el-select
                  v-model="form.deptId"
                  placeholder="请选择部门"
                  style="width: 120px;"
                >
                  <el-option
                    v-for="item in deptListFn"
                    :key="item.id"
                    :label="item.userDeptName"
                    :value="item.id"
                  />
                </el-select> -->
                <el-cascader
                  v-model="form.deptId"
                  placeholder="请选择部门"
                  style="width: 120px"
                  :options="deptListFn"
                  :show-all-levels="false"
                  :props="optionProps"
                />
                <el-button
                  size="default"
                  style="margin-left: 2px"
                  :disabled="form.orgCode ? false : true"
                  @click="department"
                >
                  新增部门
                </el-button>
                <el-button
                  size="default"
                  style="margin-left: 2px"
                  @click="department"
                >
                  新增部门
                </el-button>
              </el-form-item>
              <el-form-item prop="realName" label="姓名">
                <el-input v-model="form.realName" placeholder="请输入姓名" />
              </el-form-item>
              <el-form-item prop="mobile" label="手机(账号)">
                <el-input
                  v-model="form.mobile"
                  placeholder="请输入联系人手机号码"
                  class="miniInput"
                />
              </el-form-item>
              <el-form-item label="职位">
                <el-input v-model="form.position" placeholder="请输入职位" />
              </el-form-item>
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="form.email" placeholder="请输入邮箱" />
              </el-form-item>
              <el-form-item label="角色" prop="roleIds">
                <el-select v-model="form.roleIds" multiple placeholder="请选择">
                  <el-option
                    v-for="item in rolesList"
                    :key="item.id"
                    :label="item.roleName"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-form>
            <div class="dialog-footer">
              <el-button size="default" @click="cancel"> 取 消 </el-button>
              <el-button size="default" class="btn" @click="preserve">
                确 认
              </el-button>
            </div>
          </el-tab-pane>
          <!--           <el-tab-pane
            label="批量新增"
            name="second"
          /> -->
        </el-tabs>
      </div>
    </el-drawer>
    <!-- <addindustryList
      v-if="addindustry"
      :addindustry.sync="addindustry"
      :org-code="form.orgCode"
    /> -->
  </div>
</template>
  
  <script>
import { getAccountInfoAPI } from "@/api/userset";
import addindustryList from "./addindustryList.vue";
import {
  listDeptByOrgCodeAPI,
  listRolesByOrgCodeAPI,
  getAllAPI,
} from "./apiUrl";
export default {
  name: "AddUser",
  components: {
    addindustryList,
  },
  props: {
    dialogFormVisible: {
      type: Boolean,
      default: false,
    },
    compileEcho: {
      type: Object,
      default: () => {},
    },
    /*       rolesList: {
        type: Array,
        default: null
      }, */
    deptList: {
      type: Array,
      default: null,
    },
  },
  data() {
    return {
      form: {
        realName: "",
        deptId: "",
        orgCode: "",
        mobile: "",
        position: "",
        email: "",
        roleIds: [],
      },
      activeName: "first",
      addindustry: "",
      optionProps: {
        value: "id",
        label: "userDeptName",
        children: "childDeptList",
        checkStrictly: true,
      },
      rules: {
        orgCode: [{ required: true, message: "机构名称必选", trigger: "blur" }],
        deptId: [{ required: true, message: "部门名称必选", trigger: "blur" }],
        realName: [
          { required: true, message: "姓名不能为空", trigger: "blur" },
        ],
        roleIds: [{ required: true, message: "角色必选", trigger: "blur" }],
        email: [
          {
            pattern:
              /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
            message: "邮箱格式不符合规则",
            trigger: "blur",
          },
        ],
        mobile: [
          { required: true, message: "手机号码不能为空", trigger: "blur" },
          {
            pattern: /^(?:(?:\+|00)86)?1[3-9]\d{9}$/,
            message: "手机号不符合规则",
            trigger: "blur",
          },
        ],
      },
      deptListFn: [],
      autoList: [],
      rolesList: [],
    };
  },
  watch: {
    async addindustry(value) {
      if (!value) {
        const res = await listDeptByOrgCodeAPI({
          orgCode: this.form.orgCode,
        });
        this.deptListFn = this.getTreeData(res);
        this.form.deptId = "";
      }
    },
  },
  created() {
    this.departmentList();
    this.autoSearchAPI();
    // }
  },
  methods: {
    getTreeData(data) {
      for (var i = 0; i < data.length; i++) {
        if (data[i].childDeptList.length < 1) {
          data[i].childDeptList = undefined;
        } else {
          this.getTreeData(data[i].childDeptList);
        }
      }
      return data;
    },
    handleClick() {},
    async departmentList() {
      this.form.deptId = "";
      this.form.roleIds = [];
      const res = await listDeptByOrgCodeAPI({
        orgCode: this.form.orgCode,
      });
      this.deptListFn = this.getTreeData(res);
      const arr = await listRolesByOrgCodeAPI({
        orgCode: this.form.orgCode,
      });
      this.rolesList = arr;
    },
    cancel() {
      this.$emit("update:dialogFormVisible", false);
    },
    async preserve() {
      await this.$refs.form.validate();
      try {
        this.form.username = this.form.mobile;
        if (Array.isArray(this.form.deptId)) {
          this.form.deptId = this.form.deptId.pop();
        }
        await getAccountInfoAPI(this.form);
        this.cancel();
        this.$message.success("新增用户成功");
        this.$emit("industryList");
      } catch (error) {
        // console.log(error);
      }
    },
    async department() {
      //this.$emit('departmentFn',this.form.orgCode)
      this.addindustry = true;
    },
    // 获取机构列表
    autoSearchAPI() {
      /*  autoSearchAPI({orgName:''}).then(res=>{
          this.autoList = res.result?.searchHits;
        }) */
      getAllAPI().then((res) => {
        this.autoList = res;
      });
    },
  },
};
</script>
  
  <style scoped lang="scss">
.btn {
  color: rgba(255, 255, 255, 0.85);
  background: #1c91ff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}
.btn:hover {
  background: #1765ad;
  color: rgba(255, 255, 255, 0.85);
}
.btn:active {
  background: #52abff;
  color: rgba(255, 255, 255, 0.85);
}
.btn:focus {
  background: #1c91ff;
  color: rgba(255, 255, 255, 0.85);
}
::v-deep {
  .el-drawer__header {
    font-size: 20px;
    color: #000;
  }
}
.tabs {
  width: 95%;
  margin-left: 2.5%;
  .form {
    width: 80%;
    margin-left: 10%;
  }
}
.dialog-footer {
  width: 100%;
  margin-left: 55%;
}
</style>