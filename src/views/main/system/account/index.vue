<template>
  <div ref="msMain" class="vue-ms-main vue-ms-main-sel">
    <div v-if="isList == 1">
      <table-layout :tab-name-list="['用户管理']">
        <!-- 查询内容 -->
        <el-form
          slot="elForm"
          ref="params"
          label-width="82px"
          class="demo-form-inline"
          :model="pageSize"
        >
          <el-form-item label="人员姓名">
            <el-input
              v-model="pageSize.realName"
              placeholder="请输入人员姓名"
            />
          </el-form-item>
          <el-form-item label="机构名称">
            <el-autocomplete
              v-model="pageSize.orgName"
              class="inline-input"
              :fetch-suggestions="querySearch"
              :trigger-on-focus="false"
              placeholder="请输入内容"
            />
          </el-form-item>
          <el-form-item label="部门名称">
            <el-select v-model="pageSize.deptId" clearable placeholder="请选择">
              <el-option
                v-for="item in deptList"
                :key="item.id"
                :label="item.userDeptName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="手机号码">
            <el-input v-model="pageSize.mobile" placeholder="请输入手机号码" />
          </el-form-item>
          <el-form-item label="邮箱">
            <el-input v-model="pageSize.email" placeholder="请输入邮箱" />
          </el-form-item>
          <el-form-item label="角色">
            <el-select v-model="pageSize.roleId" placeholder="请选择" clearable>
              <el-option
                v-for="item in rolesList"
                :key="item.id"
                :label="item.roleName"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item style="width: 500px">
            <el-button
              size="default"
              v-loading="listLoading"
              class="btn"
              @click="search"
            >
              查询
            </el-button>
            <el-button size="default" class="btn" @click="reset">
              重置
            </el-button>
            <el-button size="default" class="btn" @click="sectorsetup">
              部门设置
            </el-button>
            <el-button size="default" class="btn" @click="addUser">
              新增用户
            </el-button>
          </el-form-item>
        </el-form>
        <div slot="selTable">
          <el-table
            v-loading="loading"
            border
            :data="tableData"
            style="width: 100%"
          >
            <el-table-column
              prop="realName"
              label="姓名"
              align="center"
              width="180"
            />
            <el-table-column
              prop="orgName"
              align="center"
              width="120"
              label="机构名称"
            />
            <el-table-column
              prop="deptName"
              align="center"
              width="120"
              label="部门名称"
            />
            <el-table-column
              prop="username"
              align="center"
              label="账号"
              width="120"
            />
            <el-table-column prop="mobile" align="center" label="手机号码" />
            <el-table-column prop="position" label="职位" align="center" />
            <el-table-column
              prop="email"
              align="center"
              label="邮箱"
              width="160"
            />
            <el-table-column
              prop="roleName"
              align="center"
              label="角色"
              width="160"
            />
            <el-table-column align="center" label="操作">
              <template v-slot="{ row }">
                <div v-if="$store.getters.user.userId != row.id">
                  <el-button
                    size="default"
                    type="text"
                    @click="allocation(row)"
                  >
                    编辑
                  </el-button>
                  <el-divider direction="vertical" />
                  <el-button
                    size="default"
                    type="text"
                    @click="userdeleted(row.id)"
                  >
                    删除
                  </el-button>
                </div>
                <div v-else>
                  <el-button
                    size="default"
                    type="text"
                    style="color: ##b7b6bb; opacity: 0.4"
                    @click="itself"
                  >
                    编辑
                  </el-button>
                  <el-divider direction="vertical" />
                  <el-button
                    size="default"
                    type="text"
                    style="color: ##b7b6bb; opacity: 0.4"
                    @click="itself"
                  >
                    删除
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </table-layout>
      <div class="ye">
        <el-pagination
          :current-page.sync="pageSize.pageNum"
          :page-sizes="[5, 10, 20, 50]"
          :page-size.sync="pageSize.pageSize"
          :total="+total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="industryList"
          @current-change="industryList"
        />
      </div>
    </div>
    <div v-if="isList == 2">
      <!-- <table-layout
        :tab-name-list="['用户管理']"
      /> -->
      <sectorSetupCom :dept-list="deptList" @close="close" />
    </div>

    <AddUser
      v-if="dialogFormVisible"
      :roles-list="rolesList"
      :dept-list="deptList"
      :dialog-form-visible.sync="dialogFormVisible"
      @industryList="industryList"
    />
    <EditUser
      v-if="editUser"
      :edit-info="editInfo"
      :roles-list="rolesList"
      :dept-list="deptList"
      :row-id="rowId"
      :edit-user.sync="editUser"
      @industryList="industryList"
    />
    <!-- <addindustryList
      v-if="addindustry"
      :addindustry.sync="addindustry"
      :org-code="orgCode"
    /> -->
  </div>
</template>

<script>
import TableLayout from "../components/table-layout.vue";
import AddUser from "./adduser.vue";
import EditUser from "./edituser.vue";
// import addindustryList from './addindustryList.vue'
import sectorSetupCom from "./sectorSetupCom.vue";
import { userPageAPI, userdeletedAPI } from "@/api/userset";
import { rolesAPI, autoSearchAPI } from "@/api/role";
import {
  deptUserList,
  // userDetailAPI
} from "./apiUrl";
import { filterData } from "@/utils/dateFormat";
export default {
  name: "AccountManagement",
  components: {
    TableLayout,
    AddUser,
    EditUser,
    sectorSetupCom,
  },
  data() {
    return {
      isList: 1,
      listLoading: false,
      loading: false,
      // 添加用户
      dialogFormVisible: false,
      // 编辑用户
      editUser: false,
      // 添加部门
      addindustry: false,
      // 联系人名称
      linkman: "",
      params: {},
      //列表
      tableData: [],
      options: [
        {
          value: "true",
          label: "启用",
        },
        {
          value: "false",
          label: "禁用",
        },
      ],
      //默认页码
      pageSize: {
        pageSize: 10,
        pageNum: 1,
        realName: "", // 人员姓名
        orgName: "", //
        mobile: "",
        email: "",
        deptId: "",
        roleId: "",
      },
      //总数
      total: "",
      rowId: "",
      deptList: [], // 部门列表
      rolesList: [], // 角色列表
      editInfo: {}, // 编辑用户的信息
      orgCode: "",
    };
  },
  computed: {},
  watch: {},
  created() {
    this.industryList();
    // 获取部门列表
    // this.listAllChildDeptUsersAPI();
    // 获取角色列表
    this.rolesAPI();
  },
  methods: {
    async querySearch(queryString, cb) {
      if (this.pageSize.orgName !== "") {
        const res = await autoSearchAPI({
          orgName: this.pageSize.orgName,
        });
        var results = res.result.searchHits;
        let dataList = [];
        for (let i = 0; i <= results.length - 1; i++) {
          dataList[i] = {
            value: results[i].content.orgName,
          };
        }
        cb(dataList);
      }
    },
    itself() {
      this.$message.error("不能对自己进行操作");
    },
    // 部门列表
    listAllChildDeptUsersAPI() {
      deptUserList().then((res) => {
        this.deptList = res;
      });
    },
    // 角色列表
    rolesAPI() {
      rolesAPI().then((res) => {
        this.rolesList = res.result;
      });
    },
    // 新增部门
    /*     departmentFn(value){
      this.orgCode=value
      this.dialogFormVisible=false
      //this.addindustry=true
    }, */
    // 部门设置
    sectorsetup() {
      this.isList = 2;
    },
    // 编辑部门
    async allocation(e) {
      this.rowId = e.id;
      this.editUser = true;
    },
    //获取列表
    async industryList() {
      try {
        this.loading = true;
        filterData(this.pageSize);
        const res = await userPageAPI(this.pageSize);
        this.tableData = res.result.records;
        this.total = res.result.total;
      } catch (error) {
        // console.log(error);
      } finally {
        this.loading = false;
      }
    },
    // 新增
    async addUser() {
      this.dialogFormVisible = true;
    },
    close(value) {
      this.isList = value;
    },
    // 删除
    async userdeleted(id) {
      // console.log(id,id);
      this.$confirm("确定删除该用户？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        await userdeletedAPI({
          id: id,
        });
        // 刷新产业链
        this.industryList();
        this.$message({
          type: "success",
          message: "删除用户成功!",
        });
      });
    },
    // 重置
    reset() {
      this.pageSize = {
        pageSize: 10,
        pageNum: 1,
        realName: "", // 人员姓名
        orgName: "", //
        mobile: "",
        email: "",
        deptId: "",
        roleId: "",
      };
      this.industryList();
    },
    // 搜索
    async search() {
      try {
        this.pageSize.pageNum = 1;
        this.listLoading = true;
        await this.industryList();
      } catch (error) {
        // console.log(error);
      } finally {
        this.listLoading = false;
      }
    },
  },
};
</script>

<style lang="scss">
.el-tooltip__popper {
  max-width: 900px;
}
</style>
<style scoped lang="scss">
.ye {
  margin-bottom: 50px;
}
.btn {
  color: rgba(255, 255, 255, 0.85);
  background: #1c91ff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}
.btn:hover {
  background: #1765ad;
  color: rgba(255, 255, 255, 0.85);
}
.btn:active {
  background: #52abff;
  color: rgba(255, 255, 255, 0.85);
}
.btn:focus {
  background: #1c91ff;
  color: rgba(255, 255, 255, 0.85);
}
</style>