<template>
  <div>
    <el-drawer
      width="50%"
      title="新增部门"
      :visible="addindustry"
      :close-on-click-modal="false"
      :wrapper-closable="false"
      @close="cancel"
    >
      <DeptTree :org-code="orgCode" />
    </el-drawer>
  </div>
</template>
    
<script>
import DeptTree from './component/deptTree.vue';
export default {
  name: "AddindustryList",
  components: {
    DeptTree,
  },
  props: {
    addindustry: {
      type: Boolean,
      default: false,
    },
    orgCode:{
      type: String,
      default:null
    }
  },
  data() {
    return {
    };
  },
  created () {
  },
  methods: {
    cancel() {
      this.$emit("update:addindustry", false);
    },

  }
}
</script>
    
<style scoped lang="scss">

.dialog-footer {
  margin-top: 8%;
  margin-left: 58%;
}
.btn {
  color: rgba(255, 255, 255, 0.85);
  background: #1c91ff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}
.btn:hover {
  background: #1765ad;
  color: rgba(255, 255, 255, 0.85);
}
.btn:active {
  background: #52abff;
  color: rgba(255, 255, 255, 0.85);
}
.btn:focus {
  background: #1c91ff;
  color: rgba(255, 255, 255, 0.85);
}
::v-deep {
  .el-drawer__header {
    font-size: 20px;
    color: #000;
  }
  .el-tree {
    max-height: 100px;
  }
}
</style>