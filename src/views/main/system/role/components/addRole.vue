<template>
  <div>
    <el-drawer
      :title="compileindustryId ? ' 编辑角色' : '新增角色'"
      :visible="isDrawer"
      :before-close="handleClose"
      :wrapper-closable="false"
    >
      <!-- <el-divider /> -->
      <div style="width: 90%; margin-left: 5%">
        <el-form
          ref="ruleForm"
          :model="ruleForm"
          :rules="rules"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="角色名称" prop="name">
            <el-input v-model="ruleForm.name" />
          </el-form-item>
          <el-form-item label="角色类型" prop="type">
            <el-radio-group v-model="ruleForm.type">
              <el-radio :disabled="compileindustryId ? true : false" label="1">
                运营端
              </el-radio>
              <el-radio :disabled="compileindustryId ? true : false" label="2">
                用户端
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="功能权限">
            <div
              style="
                width: 90%;
                border: 1px solid #ededed;
                height: 420px;
                display: block;
                overflow-y: scroll;
              "
            >
              <div
                style="
                  display: flex;
                  justify-content: space-between;
                  background-color: #eeeeee;
                "
              >
                <el-checkbox
                  v-model="isAllChecked"
                  style="margin-left: 3px"
                  @change="handleAllCheckedChange"
                >
                  全选
                </el-checkbox>
                <span style="margin-right: 3px"
                  >权限{{ selected }}/{{ sum }}</span
                >
              </div>
              <el-tree
                ref="refTree"
                v-loading="treeLoading"
                :data="data"
                :props="defaultProps"
                default-expand-all
                show-checkbox
                :check-strictly="isCheck"
                node-key="id"
                @check-change="checked"
              />
            </div>
          </el-form-item>
          <el-form-item label="数据权限">
            <el-select v-model="value" disabled placeholder="请选择数据权限">
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item class="submit-button">
            <el-button size="default" @click="handleClose"> 取消 </el-button>
            <el-button
              size="default"
              v-loading="btnLoading"
              class="btn"
              @click="preserve"
            >
              保存
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
  </div>
</template>
  
  <script>
import { getTreeLength } from "@/utils/dateFormat";
import {
  rolesAddAPI,
  rolesEditAPI,
  listAclByUserIdAPI,
  getAclTreeAPI,
  roleAclTreeAPI,
} from "@/api/role";
export default {
  name: "DetailedLIstRight",
  props: {
    isDrawer: {
      type: Boolean,
      default: false,
    },
    compileindustryId: {
      type: String,
      default: null,
    },
    echoObj: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      // 父子节点有无关联关系
      isCheck: false,
      btnLoading: false,
      treeLoading: false,
      data: [],
      defaultProps: {
        children: "aclDTOList",
        label: "aclName",
      },
      ruleForm: {
        name: "",
        type: "1",
      },
      rules: {
        name: [{ required: true, message: "请输入角色名称", trigger: "blur" }],
        type: [
          {
            required: true,
            message: "请至少选择一个角色类型",
            trigger: "change",
          },
        ],
      },
      // 数据权限
      options: [],
      value: "",
      // 全选
      isAllChecked: false,
      selected: 0,
    };
  },
  computed: {
    // 计算总数
    sum() {
      if (this.data) {
        return getTreeLength(this.data);
      } else {
        return 0;
      }
    },
  },
  watch: {
    // "ruleForm.type"() {
    //   this.$refs.refTree?.setCheckedKeys([]);
    //   this.treeList();
    //   if (this.isAllChecked == true) {
    //     this.isAllChecked = !this.isAllChecked;
    //   }
    // },
  },
  async created() {
    //   await this.treeList();
    this.ruleForm.type = "2";
    await this.treeList();
    // }
    if (this.compileindustryId) {
      this.echo();
    }
  },
  methods: {
    // 获取树状图
    async treeList() {
      try {
        this.treeLoading = true;
        if (this.compileindustryId) {
          const res = await getAclTreeAPI({
            roleType: this.echoObj.type,
          });
          this.data = res.result;
        } else {
          const res = await getAclTreeAPI({
            roleType: this.ruleForm.type,
          });
          this.data = res.result;
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.treeLoading = false;
      }
    },
    // 获取数据回显
    async echo() {
      try {
        this.treeLoading = true;
        this.ruleForm.name = this.echoObj.name;
        this.ruleForm.type = this.echoObj.type;
        const res = await roleAclTreeAPI({
          roleId: this.compileindustryId,
        });
        this.$nextTick(() => {
          const nodes = [];
          res.result.forEach((item) => {
            const node = this.$refs.refTree.getNode(item);
            if (node?.isLeaf) {
              nodes.push(item);
            }
          });
          this.$refs.refTree?.setCheckedKeys(nodes, true);
        });
      } catch (error) {
        console.log(error);
      } finally {
        this.treeLoading = false;
      }

      //this.isCheck= true
      //this.$nextTick(()=>{
      //this.$refs.refTree.setCheckedKeys(res.result)
      //this.isCheck= false
      //})
    },
    // 计算已勾选
    checked() {
      const res = this.$refs.refTree.getCheckedKeys();
      this.selected = res.length;
      if (this.sum == this.selected) {
        this.isAllChecked = true;
      }
    },
    // 关闭
    handleClose() {
      this.$emit("update:isDrawer", false);
    },
    // 全选
    handleAllCheckedChange() {
      if (this.isAllChecked) {
        const keys = [];
        const nodes = this.$refs.refTree.store.nodesMap;
        for (let key in nodes) {
          keys.push(key);
        }
        this.$refs.refTree?.setCheckedKeys(keys);
        // this.$refs.refTree.setCheckedNodes(this.data);
      } else {
        this.$refs.refTree?.setCheckedKeys([]);
      }
    },
    // 保存
    async preserve() {
      try {
        this.btnLoading = true;
        const arr1 = this.$refs.refTree.getCheckedKeys();
        const arr2 = this.$refs.refTree.getHalfCheckedKeys(); //获取半选
        const arr = [...arr1, ...arr2];
        if (arr.length == 0) {
          return this.$message.error("请至少勾选一项权限");
        }
        //const arr = this.$refs.refTree.getCheckedKeys();
        await this.$refs.ruleForm.validate();
        let data = {
          roleName: this.ruleForm.name, //角色名称
          roleType: this.ruleForm.type, //角色类型
          aclIds: arr,
        };
        let datas = {
          id: this.compileindustryId,
          roleName: this.ruleForm.name, //角色名称
          aclIds: arr,
        };
        this.compileindustryId
          ? await rolesEditAPI(datas)
          : await rolesAddAPI(data);
        this.compileindustryId
          ? this.$message.success("编辑成功")
          : this.$message.success("添加成功");
        this.handleClose();
        this.$emit("industryList");
      } catch (error) {
        console.log(error);
      } finally {
        this.btnLoading = false;
      }
    },
  },
};
</script>
  
  <style scoped lang="scss">
.btn {
  color: rgba(255, 255, 255, 0.85);
  background: #1c91ff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}
.btn:hover {
  background: #1765ad;
  color: rgba(255, 255, 255, 0.85);
}
.btn:active {
  background: #52abff;
  color: rgba(255, 255, 255, 0.85);
}
.btn:focus {
  background: #1c91ff;
  color: rgba(255, 255, 255, 0.85);
}
.submit-button {
  margin-left: 90px;
  margin-top: 20px;
}
::v-deep {
  .el-drawer__header {
    font-size: 20px;
    color: #000;
  }
  .el-input__inner {
    width: 240px;
  }
}
</style>
  <style lang="scss">
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #1684fc !important;
  border-color: #1684fc !important;
}
.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #1684fc !important;
  border-color: #1684fc !important;
}
.el-radio__input.is-checked .el-radio__inner::after {
  background-color: #1684fc;
  width: 7px;
  height: 7px;
}
.el-radio__input.is-checked .el-radio__inner {
  border-color: #1684fc !important;
  background: #fff !important;
}
.el-radio__input.is-disabled.is-checked .el-radio__inner::after {
  background-color: #1684fc !important;
  opacity: 0.6;
}
</style>