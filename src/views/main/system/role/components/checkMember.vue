<template>
  <div>
    <el-drawer
      title="查看成员"
      :visible="ischeckMember"
      :before-close="handleClose"
      :wrapper-closable="false"
      size="50%"
    >
      <!-- <el-divider /> -->
      <div style="width: 90%; margin-left: 5%">
        <div>
          <el-input v-model="Membername" placeholder="请输入人员名称">
            <i
              slot="suffix"
              class="el-input__icon el-icon-search"
              style="cursor: pointer"
              @click="industryList"
            />
          </el-input>
          <el-button size="default" class="btn" @click="add">
            添加成员
          </el-button>
        </div>

        <el-table
          v-loading="listLoading"
          :data="tableData"
          style="width: 100%; margin-top: 20px"
        >
          <el-table-column
            prop="realName"
            label="姓名"
            width="110"
            align="center"
          />
          <el-table-column
            prop="username"
            label="账号"
            width="110"
            align="center"
          />
          <el-table-column
            prop="orgName"
            label="机构"
            width="160"
            align="center"
          />
          <el-table-column
            prop="deptName"
            label="部门"
            width="160"
            align="center"
          />
          <el-table-column label="操作" align="center">
            <template v-slot="{ row }">
              <el-button
                size="default"
                type="text"
                @click="ischeckMemberFn(row.id)"
              >
                移除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="ye">
          <el-pagination
            :current-page="form.pageNum"
            :page-sizes="[5, 10, 20, 50]"
            :page-size="form.pageSize"
            :total="+total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="industryList"
            @current-change="industryList"
          />
        </div>
      </div>
    </el-drawer>
  </div>
</template>
      
<script>
import { listByRoleIdAPI, deleteRoleUserAPI } from "@/api/role";
export default {
  name: "DetailedLIstRight",
  props: {
    ischeckMember: {
      type: Boolean,
      default: false,
    },
    compileindustryId: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      form: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      listLoading: false,
      Membername: "",
      tableData: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.industryList();
  },
  methods: {
    add() {
      this.$emit("addition", this.compileindustryId);
    },
    // 获取列表
    async industryList() {
      try {
        this.listLoading = true;
        let data = {
          pageNum: this.form.pageNum,
          pageSize: this.form.pageSize,
          param: this.Membername,
          roleId: this.compileindustryId,
        };
        const res = await listByRoleIdAPI(data);
        this.tableData = res.result.records;
        this.total = res.result.total;
      } catch (error) {
        console.log(error);
      } finally {
        this.listLoading = false;
      }
    },
    ischeckMemberFn(id) {
      // 确定将xx移除xx？
      this.$confirm("确定删除该成员吗", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        await deleteRoleUserAPI({
          id,
        });
        this.industryList();
        this.$message({
          type: "success",
          message: "移除成功!",
        });
      });
    },
    handleClose() {
      this.$emit("update:ischeckMember", false);
    },
  },
};
</script>
      
<style scoped lang="scss">
.ye {
  position: relative;
}
::v-deep {
  .el-button--text {
    color: #1c91ff;
  }
  .el-input {
    width: 70%;
  }
}
.btn {
  color: rgba(255, 255, 255, 0.85);
  background: #1c91ff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
  margin-left: 40px;
}
.btn:hover {
  background: #1765ad;
  color: rgba(255, 255, 255, 0.85);
}
.btn:active {
  background: #52abff;
  color: rgba(255, 255, 255, 0.85);
}
.btn:focus {
  background: #1c91ff;
  color: rgba(255, 255, 255, 0.85);
}
.submit-button {
  margin-left: 90px;
  margin-top: 20px;
}
::v-deep {
  .el-drawer__header {
    font-size: 20px;
    color: #000;
  }
}
</style>
  