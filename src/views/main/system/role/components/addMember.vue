<template>
  <div>
    <el-drawer
      title="添加成员"
      :visible="isMember"
      :before-close="handleClose"
      :wrapper-closable="false"
    >
      <!-- <el-divider /> -->
      <div style="width: 80%; margin-left: 10%">
        <el-input v-model="filterText" placeholder="请输入人员名称">
          <i
            slot="suffix"
            class="el-input__icon el-icon-search"
            style="cursor: pointer"
            @click="search"
          />
        </el-input>
        <div
          style="
            width: 100%;
            border: 1px solid #ededed;
            height: 500px;
            display: block;
            overflow-y: scroll;
            margin-top: 20px;
          "
        >
          <div
            style="
              height: 40px;
              display: flex;
              justify-content: space-between;
              background-color: #eeeeee;
            "
          >
            <div />
            <!--             <el-checkbox
              v-model="isAllChecked"
              style="margin-left: 3px; margin-top: 9px"
              @change="handleAllCheckedChange"
            >
              全选
            </el-checkbox> -->
            <span style="margin-right: 3px; margin-top: 12px"
              >已选{{ selected }}/{{ sum }}</span
            >
          </div>
          <el-tree
            ref="refTree"
            v-loading="treeLoading"
            :data="data"
            :props="defaultProps"
            default-expand-all
            show-checkbox
            :check-strictly="isCheck"
            node-key="id"
            :filter-node-method="filterNode"
            @check-change="checked"
          >
            <span slot-scope="{ node, data }" class="custom-tree-node">
              <span>{{ node.label }} </span>
            </span>
          </el-tree>
        </div>
        <div class="button">
          <el-button size="default" @click="handleClose"> 取消 </el-button>
          <el-button
            size="default"
            v-loading="buttonLoading"
            class="btn"
            @click="preserve"
          >
            保存
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>
    
    <script>
import { getTreeLength2, xzdisableTree } from "@/utils/dateFormat";
import { rolesaveAPI, listRoleUserAPI, deptUserTreeAPI } from "@/api/role";
export default {
  name: "DetailedLIstRight",
  props: {
    isMember: {
      type: Boolean,
      default: false,
    },
    compileindustryId: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      // 父子节点有无关联关系
      isCheck: false,
      buttonLoading: false,
      treeLoading: false,
      data: [],
      defaultProps: {
        children: "childDeptList",
        label: "userDeptName",
      },
      //模糊搜索人员
      Membername: "",
      // 数据权限
      options: [],
      value: "",
      // 全选
      isAllChecked: false,
      // 已勾选数量
      selected: 0,
      filterText: "",
      alreadyHave: [],
    };
  },
  computed: {
    // 计算总数
    sum() {
      return getTreeLength2(this.data);
    },
  },
  watch: {
    filterText(val) {
      this.$refs.refTree.filter(val);
    },
  },
  created() {
    this.deptUserTree();
  },
  methods: {
    // 获取用户部门树
    async deptUserTree() {
      try {
        this.treeLoading = true;
        let res = await deptUserTreeAPI({
          roleId: this.compileindustryId,
        });
        const fun = (arr) => {
          arr.map((e) => {
            if (e.userDTOS) {
              e.childDeptList = e.childDeptList
                ? [...e.userDTOS, ...e.childDeptList]
                : e.userDTOS;
              e.childDeptList.map((el) => {
                el.userDeptName = el.userDeptName
                  ? el.userDeptName
                  : `${el.realName}(${el.username})`;
                if (el.userDTOS) {
                  e.childDeptList = fun(e.childDeptList);
                }
                // return el
              });
            }
            return e;
          });
          return arr;
        };
        res.result = fun(res.result);
        const res2 = await listRoleUserAPI({
          roleId: this.compileindustryId,
        });
        this.data = res.result;
        // 子节点名称拼接
        /*         this.data.forEach((item) => {
          if (item.userDTOS !== null) {
            item.userDTOS.forEach((user) => {
              user.userDeptName = `${user.realName}(${user.username})`;
            });
          }
        }); */
        this.alreadyHave = res2.result;
        this.data = xzdisableTree(this.data, res2.result);
        // 回显
        this.$nextTick(() => {
          const nodes = [];
          res2.result.forEach((item) => {
            const node = this.$refs.refTree.getNode(item);
            if (node?.isLeaf) {
              nodes.push(item);
            }
          });
          this.$refs.refTree.setCheckedKeys(nodes, true);
        });
      } catch (error) {
        // console.log(error);
      } finally {
        this.treeLoading = false;
      }
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.userDeptName.indexOf(value) !== -1;
    },

    search() {},
    // 计算已勾选
    checked() {
      const res = this.$refs.refTree.getCheckedKeys();
      this.selected = res.length;
      if (this.sum == this.selected) {
        this.isAllChecked = true;
      }
    },
    // 关闭
    handleClose() {
      this.$emit("update:isMember", false);
    },
    // 全选
    handleAllCheckedChange() {
      if (this.isAllChecked) {
        const keys = [];
        const nodes = this.$refs.refTree.store.nodesMap;
        for (let key in nodes) {
          // console.log(key);
          keys.push(key);
        }
        this.$refs.refTree.setCheckedKeys(keys);
      } else {
        this.$refs.refTree.setCheckedKeys([]);
      }
    },
    // 保存
    async preserve() {
      try {
        this.buttonLoading = true;
        //const arr = this.$refs.refTree.getCheckedKeys();
        //const arr = this.$refs.refTree.getCurrentNode();
        //console.log(arr);
        const tree = this.$refs.refTree;
        const checkedNodes = tree.getCheckedNodes();
        // console.log(checkedNodes);
        let arr = checkedNodes
          .map((item) => {
            if (item.username) {
              return item.id;
            }
          })
          .filter((item) => item !== undefined);
        // console.log(arr);
        //console.log(this.alreadyHave,'已有');
        //const set = new Set(this.alreadyHave);
        //const filteredArr = arr.filter(item => !set.has(item));
        //console.log(filteredArr,'筛选');
        if (arr.length == 0) {
          return this.$message.error("您还未勾选用户");
        }
        await rolesaveAPI({
          roleId: this.compileindustryId, //角色id
          userIds: arr, //用户id集合
        });
        this.handleClose();
        this.$message.success("用户添加成功");
        this.$emit("industryList");
      } catch (error) {
        // console.log(error);
      } finally {
        this.buttonLoading = false;
      }
    },
  },
};
</script>
    
    <style scoped lang="scss">
::v-deep {
  .el-input--suffix .el-input__inner {
    width: 100%;
  }
}
.button {
  float: right;
  margin-top: 30px;
}
.btn {
  color: rgba(255, 255, 255, 0.85);
  background: #1c91ff;
  border-radius: 4px 4px 4px 4px;
  opacity: 1;
}
.btn:hover {
  background: #1765ad;
  color: rgba(255, 255, 255, 0.85);
}
.btn:active {
  background: #52abff;
  color: rgba(255, 255, 255, 0.85);
}
.btn:focus {
  background: #1c91ff;
  color: rgba(255, 255, 255, 0.85);
}
.submit-button {
  margin-left: 90px;
  margin-top: 20px;
}
::v-deep {
  .el-drawer__header {
    font-size: 20px;
    color: #000;
  }
  .el-input__inner {
    width: 240px;
  }
}
</style>
