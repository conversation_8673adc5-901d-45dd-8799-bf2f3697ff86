<script setup>
// import HelloWorld from './components/HelloWorld.vue'
// import TheWelcome from './components/TheWelcome.vue'
import AmisComponent from '@/components/amis/index.vue';
import { ref } from 'vue';
import { perFix, baseEvn, token } from '@/utils/utils';
import { useRoute, useRouter } from 'vue-router';

const router = useRouter();
function handleButtonClick(e) {
  // console.log(e, "111");
  // "link": "#/workbench/lease/addlease",
  router.push(e);
}
const viewDraw = {
  size: 'lg',
  title: '查看详情',
  actions: [
    {
      type: 'button',
      size: 'md',
      actionType: 'cancel',
      label: '确认',
      primary: true,
    },
  ],
  body: [
    {
      type: 'form',
      columnCount: 2,
      initApi: {
        url: `investment/management/detail`,

        method: 'get',
        data: {
          id: '${id}',
        },
      },
      rules: {},
      body: [
        {
          type: 'input-text',
          name: 'enterpriseName',
          label: '企业名称',
          required: true,
          disabledOn: 'true',
        },
        // {
        //   type: 'input-text',
        //   name: 'legalPerson',
        //   label: '法人',
        //   disabledOn: 'true',
        // },
        // {
        //   type: 'input-text',
        //   name: 'legalPhone',
        //   label: '法人联系电话',
        //   disabledOn: 'true',
        // },
        {
          type: 'input-text',
          name: 'contactPerson',
          label: '联系人',
          required: true,
          disabledOn: 'true',
        },
        {
          type: 'input-text',
          name: 'contactPhone',
          label: '联系人电话',
          required: true,
          disabledOn: 'true',
        },
        {
          type: 'input-date',
          name: 'obtainClientDate',
          label: '获客时间',
          valueFormat: 'x',
          required: true,
          disabledOn: 'true',
        },
        {
          type: 'input-text',
          name: 'intentionArea',
          label: '意向面积',
          disabledOn: 'true',
        },
        {
          type: 'input-text',
          name: 'clientSource',
          label: '客户来源',
          required: true,
          disabledOn: 'true',
        },
        {
          type: 'select',
          name: 'clientLevel',
          disabledOn: 'true',
          label: '客户意向等级',
          options: [
            { label: 'A', value: 'A' },
            { label: 'B', value: 'B' },
            { label: 'C', value: 'C' },
          ],
        },
        {
          type: 'input-text',
          name: 'enterpriseArea',
          label: '企业所在区域',
          disabledOn: 'true',
        },
        {
          type: 'input-text',
          name: 'registeredCapital',
          label: '注册资本',
          disabledOn: 'true',
        },
        {
          type: 'input-text',
          name: 'annualOutputValue',
          label: '年产值(万元)',
          disabledOn: 'true',
        },
        {
          type: 'input-text',
          name: 'annualTax',
          label: '年纳税(万元)',
          disabledOn: 'true',
        },
        {
          type: 'input-text',
          name: 'industry',
          label: '所属行业',
          disabledOn: 'true',
        },
        {
          type: 'input-text',
          name: 'mainProduct',
          label: '主要产品',
          disabledOn: 'true',
        },
        {
          type: 'input-text',
          name: 'companyPersonNum',
          label: '企业人数',
          disabledOn: 'true',
        },
        {
          name: 'nowEnterpriseHouseType',
          type: 'radios',
          disabledOn: 'true',
          label: '现有企业厂房控制',
          options: [
            {
              label: '租赁',
              value: '1',
            },
            {
              label: '买断',
              value: '2',
            },
          ],
        },
        {
          type: 'input-text',
          name: 'intentionHouse',
          label: '意向房源',
          disabledOn: 'true',
        },
        {
          type: 'input-date',
          name: 'firstVisitDate',
          label: '首次来访时间',
          valueFormat: 'x',
          disabledOn: 'true',
        },
      ],
    },
  ],
};
// 招商管理
const amisjson2 = {
  type: 'page',
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: '0px 0px 0px 0px transparent',
  },
  body: [
    {
      type: 'tpl',
      inline: true,
      wrapperComponent: '',
      syncLocation: false,
      id: 'u:3345e187f2df',
      style: {
        boxShadow: '0px 0px 0px 0px transparent',
        fontWeight: '500',
        lineHeight: '1.7',
        fontSize: '20px',
      },
    },
    {
      type: 'crud',
      syncLocation: false,
      name: 'tab-s',
      api: {
        method: 'post',
        url: `investment/management/pageList`,

        requestAdaptor: (rest) => {
          let { gmtCreate, ...other } = rest.data;
          let datas = {
            ...other,
            obtainClientStartDateStamp:
              rest.data.gmtCreate?.split(',')[0] * 1000,
            obtainClientEndDateStamp: rest.data.gmtCreate?.split(',')[1] * 1000,
          };
          return {
            ...rest,
            data: datas,
          };
        },
        data: {
          enterpriseName: '${enterpriseName}',
          contactPerson: '${contactPerson}',
          contactPhone: '${contactPhone}',
          followProgress: '${followProgress}',
          clientLevel: '${clientLevel}',
          clientType: '${clientType}',
          pageNum: '${page}',
          pageSize: '${perPage}',
          gmtCreate: '${gmtCreate}',
        },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: 'table',
      scroll: {
        x: 2000, // 设置表格可滚动宽度
      },
      //列表
      columns: [
        {
          type: 'operation',
          label: '企业名称',
          fixed: 'left', // 固定在左侧
          width: 220,
          buttons: [
            {
              type: 'button',
              level: 'link',
               size: 'md',
              label: '${enterpriseName}',
              // "disabledOn": "operateType === '3'",
              actionType: 'dialog',

              dialog: viewDraw,
            },
          ],
        },
        // {
        //   "name": "enterpriseName",
        //   "label": "企业名称",
        //   "type": "text",

        // },
        {
          name: 'contactPerson',
          label: '联系人',
          type: 'text',
          width: 120,
        },
        {
          name: 'contactPhone',
          label: '联系人电话',
          type: 'text',
          width: 140,
        },
        // {
        //   name: 'legalPerson',
        //   label: '法人',
        //   type: 'text',
        //   width: 120,
        // },
        // {
        //   name: 'legalPhone',
        //   label: '法人联系电话',
        //   type: 'text',
        //   width: 140,
        // },
        {
          name: 'obtainClientDate',
          label: '获客时间',
          type: 'date',
          valueFormat: 'x',
          width: 120,
        },
        {
          name: 'clientSource',
          label: '客户来源',
          type: 'text',
          width: 120,
        },
        {
          name: 'intentionArea',
          label: '意向面积',
          type: 'text',
          width: 120,
        },
        {
          name: 'clientLevel',
          label: '客户意向等级',
          type: 'text',
        },
        {
          name: 'annualOutputValue',
          label: '年产值',
          type: 'text',
        },
        {
          name: 'annualTax',
          label: '年税收',
          type: 'text',
        },
        {
          name: 'industry',
          label: '所属行业',
          type: 'text',
        },
        {
          name: "${clientType==1 ? '意向客户' : (clientType==2 ? '在招客户' : (clientType==3 ? '成交客户' : ''))}",
          label: '客户类型',
          width: 120,
          type: 'tpl',
          tpl: '<span class="inline-block px-2 py-1 text-xs font-medium rounded-full ${clientType==1 ? "bg-blue-100 text-blue-800" : clientType==2 ? "bg-yellow-100 text-yellow-800" : clientType==3 ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"}">${clientType==1 ? "意向客户" : (clientType==2 ? "在招客户" : (clientType==3 ? "成交客户" : ""))}</span>',
        },
        {
          name: 'followDate',
          label: '跟进时间',
          width: 200,
          type: 'text',
        },
        {
          name: "${['','初步联系','考察','洽谈','签约','关闭'][followProgress]||'-'}",
          label: '跟进进展',
          width: 120,
          type: 'tpl',
          tpl: '<span class="inline-block px-2 py-1 text-xs font-medium rounded-full ${followProgress==1 ? "bg-blue-100 text-blue-800" : followProgress==2 ? "bg-purple-100 text-purple-800" : followProgress==3 ? "bg-yellow-100 text-yellow-800" : followProgress==4 ? "bg-green-100 text-green-800" : followProgress==5 ? "bg-red-100 text-red-800" : "bg-gray-100 text-gray-800"}">${["","初步联系","考察","洽谈","签约","关闭"][followProgress]||"-"}</span>',
        },

        {
          type: 'operation',
          label: '操作',
          width: 300,
          fixed: 'right', // 固定在右侧
          buttons: [
            {
              type: 'button',
              level: 'primary',
              label: '查看',
              actionType: 'dialog',
              dialog: viewDraw,
            },
            {
              type: 'button',

              label: '编辑',
              level: 'primary',
              actionType: 'dialog',
              dialog: {
                size: 'lg',
                title: '编辑客户',
                actions: [
                  {
                    type: 'button',
                    size: 'md',
                    actionType: 'cancel',
                    label: '取消',
                    level: 'default',
                    primary: true,
                  },
                  {
                    label: '确认',
                    actionType: 'confirm',
                    primary: true,
                    reload: 'tab-s',
                    close: true,
                    type: 'button',
                    size: 'md',
                    api: {
                      method: 'post',
                      url: `investment/management/update`,

                      data: {
                        '&': '$$',
                        id: '${id}',
                        firstVisitDate: '${firstVisitDateStamp}',
                        obtainClientDate: '${obtainClientDateStamp}',
                      },
                    },
                  },
                ],
                body: [
                  {
                    type: 'form',
                    columnCount: 2,
                    initApi: {
                      url: `investment/management/detail`,

                      method: 'get',
                      data: {
                        id: '${id}',
                      },
                    },
                    rules: {},
                    body: [
                      {
                        type: 'input-text',
                        name: 'enterpriseName',
                        label: '企业名称',
                        required: true,
                      },
                      // {
                      //   type: 'input-text',
                      //   name: 'legalPerson',
                      //   label: '法人',
                      // },
                      // {
                      //   type: 'input-text',
                      //   name: 'legalPhone',
                      //   label: '法人联系电话',
                      // },
                      {
                        type: 'input-text',
                        name: 'contactPerson',
                        label: '联系人',
                        required: true,
                      },
                      {
                        type: 'input-text',
                        name: 'contactPhone',
                        label: '联系人电话',
                        required: true,
                      },
                      {
                        type: 'input-date',
                        name: 'obtainClientDateStamp',
                        label: '获客时间',
                        valueFormat: 'x',
                        required: true,
                      },
                      {
                        type: 'input-text',
                        name: 'intentionArea',
                        label: '意向面积',
                      },
                      {
                        type: 'input-text',
                        name: 'clientSource',
                        label: '客户来源',
                        required: true,
                      },
                      {
                        type: 'select',
                        name: 'clientLevel',
                        label: '客户意向等级',
                        options: [
                          { label: 'A', value: 'A' },
                          { label: 'B', value: 'B' },
                          { label: 'C', value: 'C' },
                        ],
                      },
                      {
                        type: 'input-text',
                        name: 'enterpriseArea',
                        label: '企业所在区域',
                      },
                      {
                        type: 'input-text',
                        name: 'registeredCapital',
                        label: '注册资本',
                      },
                      {
                        type: 'input-text',
                        name: 'annualOutputValue',
                        label: '年产值(万元)',
                      },
                      {
                        type: 'input-text',
                        name: 'annualTax',
                        label: '年纳税(万元)',
                      },
                      {
                        type: 'input-text',
                        name: 'industry',
                        label: '所属行业',
                      },
                      {
                        type: 'input-text',
                        name: 'mainProduct',
                        label: '主要产品',
                      },
                      {
                        type: 'input-text',
                        name: 'companyPersonNum',
                        label: '企业人数',
                      },
                      {
                        name: 'nowEnterpriseHouseType',
                        type: 'radios',
                        label: '现有企业厂房控制',
                        options: [
                          {
                            label: '租赁',
                            value: '1',
                          },
                          {
                            label: '买断',
                            value: '2',
                          },
                        ],
                      },
                      {
                        type: 'input-text',
                        name: 'intentionHouse',
                        label: '意向房源',
                      },
                      {
                        type: 'input-date',
                        name: 'firstVisitDateStamp',
                        label: '首次来访时间',
                        valueFormat: 'x',
                      },
                    ],
                  },
                ],
              },
            },
            {
              label: '查看跟进记录',
              type: 'button',
              level: 'primary',
              // "actionType": "link",
              // "link": "#/workbench/investment/foll?id=$id",
              onClick: (e, item) => {
                handleButtonClick(
                  `/workbench/investment/foll?id=${item?.data?.id}`
                );
              },
            },
            {
              type: 'button',
              level: 'primary',
              label: '跟进',
              actionType: 'dialog',
              dialog: {
                title: '跟进',
                actions: [
                  {
                    type: 'button',
                    size: 'md',
                    actionType: 'cancel',
                    label: '取消',
                    level: 'default',
                    primary: true,
                  },
                  {
                    label: '确认',
                    actionType: 'confirm',
                    primary: true,
                    reload: 'tab-s',
                    close: true,
                    type: 'button',
                    size: 'md',
                    api: {
                      method: 'post',
                      url: `investment/management/follow`,

                      requestAdaptor: (rest) => {
                        let { gmtCreate, ...other } = rest.data;
                        let timestamp = Math.floor(new Date().getTime() / 1000);
                        let datas = {
                          ...other,
                          dealDate: timestamp,
                        };
                        return {
                          ...rest,
                          data: datas,
                        };
                      },
                      data: {
                        investmentId: '$id',
                        clientLevel: '$clientLevel',
                        followProgress: '$followProgress',
                        isDeal: '$isDeal',
                        remark: '$remark',
                      },
                    },
                  },
                ],
                body: [
                  {
                    type: 'form',
                    body: [
                      {
                        type: 'select',
                        name: 'clientLevel',
                        label: '客户意向等级',
                        placeholder: '客户意向等级',
                        options: [
                          { label: 'A', value: 'A' },
                          { label: 'B', value: 'B' },
                          { label: 'C', value: 'C' },
                        ],
                      },
                      {
                        name: 'isDeal',
                        type: 'radios',
                        required: true,
                        label: '是否成交',
                        value: 'false',
                        options: [
                          {
                            label: '否',
                            value: 'false',
                          },
                          {
                            label: '是',
                            value: 'true',
                          },
                        ],
                      },
                      {
                        type: 'select',
                        name: 'followProgress',
                        label: '跟进状态',
                        placeholder: '跟进进展',
                        required: true,
                        options: [
                          { label: '初步联系', value: '1' },
                          { label: '考察', value: '2' },
                          { label: '洽谈', value: '3' },
                          { label: '签约', value: '4' },
                          { label: '关闭', value: '5' },
                        ],
                      },
                      {
                        name: 'remark',
                        type: 'textarea',
                        label: '跟进描述',
                        required: true,
                        showCounter: true,
                        maxLength: 300,
                        placeholder: '添加跟进记录,不超过300字',
                      },
                    ],
                  },
                ],
              },
            },
          ],
        },
      ],
      alwaysShowPagination: true,
      // "autoFillHeight": false,
      autoFillHeight: {
        height: 450,
      },
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      //筛选
      filter: {
        title: '',
        submitText: '搜索',
        controls: [
          {
            type: 'text',
            name: 'enterpriseName',
            label: '企业名称',
            size: 'md',
            placeholder: '请输入企业名称',
          },
          {
            type: 'text',
            name: 'contactPerson',
            label: '联系人',
            size: 'md',
            placeholder: '请输入联系人姓名',
          },
          {
            type: 'text',
            name: 'contactPhone',
            label: '联系人电话',
            size: 'md',
            placeholder: '请输入联系人电话',
          },
          {
            type: 'input-date-range',
            name: 'gmtCreate',
            size: 'md',
            label: '获客时间',
            shortcuts: [
              {
                label: '1天前',
                startDate: "${DATEMODIFY(NOW(), -1, 'day')}",
                endDate: '${NOW()}',
              },
              {
                label: '1个月前',
                startDate: "${DATEMODIFY(NOW(), -1, 'months')}",
                endDate: '${NOW()}',
              },
              {
                label: '本季度',
                startDate: "${STARTOF(NOW(), 'quarter')}",
                endDate: "${ENDOF(NOW(), 'quarter')}",
              },
            ],
          },

          {
            type: 'select',
            name: 'clientLevel',
            size: 'md',
            label: '客户意向等级',
            placeholder: '客户意向等级',
            value: '',
            options: [
              { label: '全部', value: '' },
              { label: 'A', value: 'A' },
              { label: 'B', value: 'B' },
              { label: 'C', value: 'C' },
            ],
          },
          {
            type: 'select',
            name: 'clientType',
            label: '客户类型',
            size: 'md',
            placeholder: '客户类型',
            value: '',
            options: [
              { label: '全部', value: '' },
              { label: '意向客户', value: '1' },
              { label: '在招客户', value: '2' },
              { label: '成交客户', value: '3' },
            ],
          },
          {
            type: 'reset',
            label: '重置',
          },
          {
            type: 'submit',
            label: '搜索',
            primary: true,
          },
          {
            type: 'button',
            size: 'md',
            label: '新增',
            actionType: 'dialog',
            dialog: {
              size: 'lg',
              title: '新增客户',
              data: {},
              actions: [
                {
                  type: 'button',
                  size: 'md',
                  actionType: 'cancel',
                  label: '取消',
                  level: 'default',
                  primary: true,
                },
                {
                  label: '确认',
                  actionType: 'confirm',
                  primary: true,
                  reload: 'tab-s',
                  close: true,
                  type: 'button',
                  size: 'md',
                  api: {
                    method: 'post',
                    url: `investment/management/add`,

                    data: [
                      {
                        '&': '$$',
                      },
                    ],
                  },
                },
              ],
              body: [
                {
                  type: 'form',
                  columnCount: 2,
                  rules: {},
                  body: [
                    {
                      type: 'input-text',
                      name: 'enterpriseName',
                      label: '企业名称',
                      required: true,
                    },
                    // {
                    //   type: 'input-text',
                    //   name: 'legalPerson',
                    //   label: '法人',
                    // },
                    // {
                    //   type: 'input-text',
                    //   name: 'legalPhone',
                    //   label: '法人联系电话',
                    // },
                    {
                      type: 'input-text',
                      name: 'contactPerson',
                      label: '联系人',
                      required: true,
                    },
                    {
                      type: 'input-text',
                      name: 'contactPhone',
                      label: '联系人电话',
                      required: true,
                    },
                    {
                      type: 'input-date',
                      name: 'obtainClientDate',
                      label: '获客时间',
                      valueFormat: 'x',
                      required: true,
                    },
                    {
                      type: 'input-text',
                      name: 'intentionArea',
                      label: '意向面积',
                    },
                    {
                      type: 'input-text',
                      name: 'clientSource',
                      label: '客户来源',
                      required: true,
                    },
                    {
                      type: 'select',
                      name: 'clientLevel',
                      label: '客户意向等级',
                      options: [
                        { label: 'A', value: 'A' },
                        { label: 'B', value: 'B' },
                        { label: 'C', value: 'C' },
                      ],
                    },
                    {
                      type: 'input-text',
                      name: 'enterpriseArea',
                      label: '企业所在区域',
                    },
                    {
                      type: 'input-text',
                      name: 'registeredCapital',
                      label: '注册资本',
                    },
                    {
                      type: 'input-text',
                      name: 'annualOutputValue',
                      label: '年产值(万元)',
                    },
                    {
                      type: 'input-text',
                      name: 'annualTax',
                      label: '年纳税(万元)',
                    },
                    {
                      type: 'input-text',
                      name: 'industry',
                      label: '所属行业',
                    },
                    {
                      type: 'input-text',
                      name: 'mainProduct',
                      label: '主要产品',
                    },
                    {
                      type: 'input-text',
                      name: 'companyPersonNum',
                      label: '企业人数',
                    },
                    {
                      name: 'nowEnterpriseHouseType',
                      type: 'radios',
                      label: '现有企业厂房控制',
                      options: [
                        {
                          label: '租赁',
                          value: '1',
                        },
                        {
                          label: '买断',
                          value: '2',
                        },
                      ],
                    },
                    {
                      type: 'input-text',
                      name: 'intentionHouse',
                      label: '意向房源',
                    },
                    {
                      type: 'input-date',
                      name: 'firstVisitDate',
                      label: '首次来访时间',
                      valueFormat: 'x',
                    },
                  ],
                },
              ],
            },
          },
        ],
        onSubmit: 'reload',
        // "actions": [

        // ]
      },
    },
  ],
};
</script>

<template>
  <div class="publicTableStyle">
    <AmisComponent :amisjson="amisjson2" />
  </div>
</template>
<style scoped lang="scss">
.tab {
  padding-top: 20px;
  padding-left: 10px;
}

.on {
  background-color: #169bd5;
  color: #fff;
}
</style>
