<template>
  <div class="det">
    <div class="title">
      <h2>跟进记录</h2>
      <span @click="returnFn" class="btn">X</span>
    </div>
    <div class="boxes">
      <div class="boxss" v-for="(item, index) in deletes" :key="index">
        <div>跟进人：{{ item.createBy }}</div>
        <div>跟进时间：{{ item.gmtCreate }}</div>
        <div>
          跟进进展：{{['','初步联系','考察','洽谈','签约','关闭'][item.followProgress]
          }}
        </div>
        <div>客户意向等级：{{ item.clientLevel }}</div>
        <div>是否成交：{{ item.isDeal ? "是" : "否" }}</div>
        <div>跟进描述：{{ item.remark }}</div>
      </div>
      <div class="btn">
        <el-button size="default" @click="dialogVisible = true" type="primary"
          >添加跟进记录</el-button
        >
      </div>
      <el-dialog
        v-model="dialogVisible"
        title="添加跟进记录"
        width="30%"
        :before-close="handleClose"
      >
        <el-form
          ref="ruleFormRef"
          :model="ruleForm"
          :rules="rules"
          label-width="120px"
          class="demo-ruleForm"
          status-icon
        >
          <el-form-item label="客户意向等级" size="default">
            <el-select
              v-model="ruleForm.clientLevel"
              placeholder="请选择意向等级"
            >
              <el-option label="A" value="A" />
              <el-option label="B" value="B" />
              <el-option label="C" value="C" />
            </el-select>
          </el-form-item>
          <el-form-item label="是否成交" size="default" prop="isDeal">
            <el-radio-group v-model="ruleForm.isDeal">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="跟进进展" size="default" prop="followProgress">
            <el-select
              v-model="ruleForm.followProgress"
              placeholder="请选择跟进进展"
            >
              <el-option label="初步联系" value="1" />
              <el-option label="考察" value="2" />
              <el-option label="洽谈" value="3" />
              <el-option label="签约" value="4" />
              <el-option label="关闭" value="5" />
            </el-select>
          </el-form-item>
          <el-form-item label="跟进描述" prop="remark" size="default">
            <el-input
              style="height: 200px"
              size="default"
              v-model="ruleForm.remark"
              maxlength="300"
              placeholder="请输入跟进记录，最多输入300字"
              show-word-limit
              type="textarea"
            />
          </el-form-item>
        </el-form>

        <template #footer>
          <span class="dialog-footer">
            <el-button size="default" @click="dialogVisible = false"
              >取消</el-button
            >
            <el-button size="default" type="primary" @click="present">
              确认
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { useRoute, useRouter } from "vue-router";
import {
  followListAPI,
  addFollowRecord,
  followAPI,
} from "@/api/system/propertyRepair";
import { formatDate } from "@/utils/formData";
export default {
  data() {
    return {
      route: useRoute(),
      router: useRouter(),
      deletes: [],
      dialogVisible: false,
      ruleForm: {
        isDeal: false,
      },
      rules: {
        remark: [{ required: true, message: "跟进描述必填", trigger: "blur" }],
        isDeal: [{ required: true, message: "是否成交必填", trigger: "blur" }],
        followProgress: [
          { required: true, message: "跟进进展必填", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.getdetails();
  },
  watch: {
    dialogVisible() {
      this.ruleForm = {
        isDeal: false,
      };
    },
  },
  methods: {
    async present() {
      await this.$refs.ruleFormRef.validate();
      let timestamp = Math.floor(new Date().getTime() / 1000);
      let data = {
        investmentId: this.$route.query.id,
        clientLevel: this.ruleForm.clientLevel,
        isDeal: this.ruleForm.isDeal,
        remark: this.ruleForm.remark,
        followProgress: this.ruleForm.followProgress,
        dealDate: timestamp,
      };
      const res = await followAPI(data);
      this.dialogVisible = false;
      this.getdetails();
    },
    async getdetails() {
      const res = await followListAPI({
        id: this.$route.query.id,
      });
      this.deletes = res.data;
      this.deletes.forEach((it) => {
        it.timeData = formatDate(
          "yyyy-MM-dd HH:mm:ss",
          new Date(+it.follow_date)
        );
      });
    },
    returnFn() {
      this.router.push("/workbench/investment");
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep {
  .el-textarea__inner {
    height: 200px;
  }
}

.det {
  padding: 0px 40px;
  width: 100%;
  height: 100%;

  .title {
    display: flex;
    justify-content: space-between;

    .btn {
      cursor: pointer;
    }
  }

  .boxes {
    width: 100%;
    height: auto;
    display: flex;
    flex-wrap: wrap;

    .boxss {
      width: 30%;
      margin-left: 2%;
      height: 200px;
      margin-top: 30px;
      padding: 20px;
      border: 1px solid #f2f2f2;
      border-radius: 20px;
    }

    .btn {
      width: 30%;
      margin-left: 2%;
      height: 200px;
      margin-top: 30px;
      padding: 20px;
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>