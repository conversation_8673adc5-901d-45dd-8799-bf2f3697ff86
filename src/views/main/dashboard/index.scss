@font-face {
  font-family: "YouSheBiaoTiHei";
  src: url('@/assets/text/YouSheBiaoTiHei-2.ttf');
}
/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
   background-color: #3241563c;
   border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
   display: none;
}
.viewScreen{
  min-width: 1200PX;
  overflow: scroll;
  // position: relative;
}
.view{
  min-width: 1200PX;
  overflow-x: scroll;
}
.viewScreen{
  // min-width: 1500px;
  // overflow: scroll;
  position: relative;
}
.topHeader {
  // position: absolute;
  width: 508px;
  height: 71px;
  //background: center / contain no-repeat url('../../../assets/images/view/title.png');
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 50px;
  //font-weight: bold;
  color: #fff;
  white-space: nowrap;
  padding-top: 10px;
  font-family: YouSheBiaoTiHei;

  // margin: 0 auto;
  // color: #00F8F4;
  // // text-shadow: 0px 10px 10px rgba(1, 24, 33, 0.7);
  // background: linear-gradient(0deg, #d5e3f7 80%, #004242 100%);
  // -webkit-background-clip: text;
  // -webkit-text-fill-color: transparent;
  // &::before{
  //   /*覆盖的文字*/
  //  content: attr(text);
  //  position: absolute;
  //  z-index: 10;
  //  /*覆盖文字的颜色*/
  //  color: rgba(119,186,255,0.45);

  // // -webkit-mask: linear-gradient(to bottom, rgba(252, 252, 252, 0.45), transparent);

  // }
}

.left,
.right {

  width: 400px;
  height: calc(100% - 185px);
  position: absolute;
  // background-color: rebeccapurple;
  top: 90px;
  z-index: 99;
}
.left{
z-index: 999;
}
.left {
.leftInner{
  height:100%;
}

  left: 55px;
}

.right {
z-index: 1099;

  right: 55px;

 .chat{
  padding: 2px 20px;
  border: 1px solid #2B75D9;
   box-shadow: 0 0 5px 1px  #2B75D9 inset;
  background: rgba(64,131,239,0.1);
 }
 .login{
  border: 1px solid #2B75D9;
  color: white;
  padding: 2px 15px;
  border: 1px solid #2B75D9;
  background: rgba(64,131,239,0.1);
 }
 .text.login{
  padding: 2px 25px;
 }
  .jump {

    cursor: pointer;
    color: white;
    line-height: 30px;
    height: 35px;
    margin-left: 10px;
    span{
      line-height: 30px;
    }
  }
}

.view {
  // padding-top: 44px;fsearch
  overflow: hidden;
  height: 100%;
// background-color: rebeccapurple;
  .header {
    padding: 0 140px;
    display: flex;
    justify-content: space-evenly;
    align-items: flex-end;
    color: white;
    width: 890px;
    height: 66px;
    margin: 24px auto 60px auto;
    // background-color: rebeccapurple;
    background: top 5px center / contain no-repeat url('../../../assets/images/view/bar.png');

    .headerItem {
      cursor: pointer;
      width: 126px;
      height: 84px;
      text-align: center;
      padding: 13px 6px 6px 6px;
      line-height: 84px;
      font-family: Alibaba PuHuiTi;
      font-weight: 600;
      font-style: italic;
      color: #B2C3DF;
      opacity: 0.9;
      font-size: 15px;
    }

    .active {


      color: #FFFFFF;
      background: center / contain no-repeat url('../../../assets/images/view/header-select.png');

    }
  }

  .center {
    // height: 45.44rem;
    // overflow: hidden;

  }

  .center1 {
    width: 925px;
    margin: -130px auto 0 auto;
    height:  calc(100% - 280px );

    // height: 71px;
    // height: 66px;
    // background: aliceblue;
    margin: -2rem auto 0 auto;
  }
}

.box {
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-image: url('../../../assets/images/view/bg.png');
  background-size: 100% 100%;
}

.box.bg {
  background-image: url('../../../assets/images/view/bg2.png');

}

.search {
  width: 400px;
  display: flex;
  // position: fixed;
  top: -50px;
  // left: 60px;
  color: #B8E4F6;
  justify-content: flex-start;
  align-items: center;
  position: absolute;

  font-size: 14px;
  .title{
    width: 120px;

  }
}
.rightHeader{
  position: absolute;
    top: -50px;
    width: 400px;
    display: flex;
    justify-content: flex-end;
    width: 400px;
    right: 10px;
    align-items: center;
}
