<template>
    <!-- 企业 -->
    <div class="industry">
        <div class="content first">
            <div @click="enterprisePage(1)" class="box">
                <div class="top"></div>
                <div class="middle">
                    <span>{{ viewData?.highTechNum }}</span>
                    <span class="last">{{ viewData?.highTechNum }}</span>
                </div>
                <div class="bottom">高新企业</div>
            </div>

        </div>
        <div class="content last">
            <div @click="enterprisePage(2)" class="box">
                <div class="top"></div>
                <div class="middle"><span>{{ viewData?.innovativeNum }}</span>
                    <span class="last">{{ viewData?.innovativeNum }}</span>
                </div>
                <div class="bottom">规上企业</div>
            </div>

        </div>
        <div>

        </div>
    </div>
    <el-dialog class="screenTable padding0Dialog" custom-class="screenTable padding0Dialog" v-model="innerVisible"
        width="30%" append-to-body>
        <div class="box">
            <div class="headTab">
                <div :class="picatTab == index + 1 ? 'pacton' : 'pact'" v-for="(item, index) in tabList" :key="item">
                    <img v-if="picatTab == index + 1" class="onimg" src="../../../../../assets/images/view/ons.png" alt="">
                    <span @click="tabcut(index + 1)" style="cursor: pointer;">{{ item }}</span>
                </div>
            </div>
            <div class="tableHead">
                <div class="name">企业名称</div>
                <div class="code">企业信用代码</div>
 <!--                <div class="site">企业地址</div> -->
            </div>
            <div v-loading="tabloading" class="tabbox">
                <div v-for="(item, index) in dataList" :key="index" class="tabList">
                    <div class="name">{{ item.enterpriseName }}</div>
                    <div class="code">{{ item.uniCode }}</div>
<!--                     <div class="site">
                        <span class="ovo">
                            <el-tooltip class="box-item" effect="dark" popper-class="customPopper" :show-arrow="false"
                                placement="top">
                                <template #content> <span v-for="(it, ind) in item.addressDel " :key="ind">{{ it }}<span
                                            v-if="ind !== item.addressDel.length - 1">,</span><br
                                            v-if="ind % 3 == 0 && ind !== 0"></span></template>
                                {{ item.address ? item.address.slice(0, 40) : '' }}
                            </el-tooltip>
                        </span>
                    </div> -->
                </div>
            </div>
            <div class="pages">
                <el-pagination background v-model:current-page="pageNum" :page-size='pageSize'
                    layout="total, prev, pager, next" :total="+total" @current-change="handleCurrentChange" />
            </div>

        </div>
    </el-dialog>
</template>
<script setup>
import { defineProps, onMounted, ref } from 'vue'
import { getenterprisePageAPI } from '@/api/industry'
const props = defineProps(['viewData']);
const tabList = ['高新企业', '规上企业']
const innerVisible = ref(false)
const tabloading = ref(false)
const pageNum = ref(1)
const pageSize = ref(10)
const total = ref(0)
const dataList = ref([])
const picatTab = ref(1)
const enterprisePage = async (i) => {
    picatTab.value = i
    pageNum.value = 1
    innerVisible.value = true
    getList()
}
const getList = async () => {
    try {
        tabloading.value = true
        const res = await getenterprisePageAPI({
            type: picatTab.value,
            pageNum: pageNum.value,
            pageSize: pageSize.value
        })
        dataList.value = res.data.records
        dataList.value.forEach((item) => {
            if (item?.address) {
                item.addressDel = item?.address.split(",")
            }
        })
        total.value = res.data.total
    } finally {
        tabloading.value = false
    }

}
const tabcut = async (i) => {
    picatTab.value = i
    pageNum.value = 1
    getList()
}
const handleCurrentChange = async (i) => {
    pageNum.value = i
    getList()
}
</script>
<style lang="scss">
.customPopper {
    background-color: #00102d !important;
    opacity: 0.9 !important;
    color: #ffffff !important;
    border: 1px solid #003485 !important;
}
</style>
<style scoped lang="scss">
::v-deep {

    .el-pagination.is-background .btn-next.is-disabled,
    .el-pagination.is-background .btn-next:disabled,
    .el-pagination.is-background .btn-prev.is-disabled,
    .el-pagination.is-background .btn-prev:disabled,
    .el-pagination.is-background .el-pager li.is-disabled,
    .el-pagination.is-background .el-pager li:disabled {
        background-color: transparent !important;
        color: #fff;
    }

    .el-pagination__total {
        color: #fff;
    }

    .el-pagination.is-background .btn-next.is-active,
    .el-pagination.is-background .btn-prev.is-active,
    .el-pagination.is-background .el-pager li.is-active {
        width: 20px !important;
        //height: 20px !important;
        background: rgba(42, 102, 176, 0.7);
        border: 1px solid #2F89CD;
        border-radius: 4px;
        color: rgba(255, 255, 255, 1);
    }

    .el-pagination.is-background .btn-next,
    .el-pagination.is-background .btn-prev,
    .el-pagination.is-background .el-pager li {
        background: rgba(42, 102, 176, 0);
        border: 1px solid #2F89CD;
        opacity: 0.6;
        border-radius: 4px;
        color: rgba(255, 255, 255, 0.4);
    }

    .el-loading-mask {
        background-color: transparent !important;
    }
}

.pages {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 30px;
}

.box {
    padding: 12px 0px;

    .headTab {
        display: flex;

        .pact {
            width: 50%;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-family: Alibaba PuHuiTi;
            font-weight: 400;
            font-style: italic;
            color: #C5D4ED;
        }

        .pacton {
            width: 50%;
            height: 50px;
            font-size: 20px;
            font-family: Alibaba PuHuiTi;
            font-weight: bold;
            font-style: italic;
            color: #C5D4ED;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            line-height: 32px;
            background: linear-gradient(0deg, #ACDDFF 0%, #FFFFFF 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .onimg {
            position: absolute;
            width: 72px;
            height: 29px;
            margin-right: 55px;
            margin-top: 4px;
        }
    }

    .tabbox {
        height: 440px;
    }

    .tabList {
        width: 100%;
        display: flex;
        font-size: 14px;
        font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #B2C3DF;
        border-bottom: 1px solid rgba(71, 158, 222, 0.3);

        .name {
            padding-left: 17px;
            display: flex;
            align-items: center;
            width: 50%;
            height: 44px;
        }

        .code {
            padding-left: 17px;
            display: flex;
            align-items: center;
            width: 50%;
            height: 44px;
        }

        .site {
            padding-left: 17px;
            padding-right: 10px;
            display: flex;
            align-items: center;
            width: 30%;
            height: 44px;

            .ovo {
                white-space: nowrap;
                /* 设置文本不换行 */
                overflow: hidden;
                /* 隐藏溢出内容 */
                text-overflow: ellipsis;
                /* 以省略号显示溢出内容 */
            }
        }
    }

    .tableHead {
        width: 100%;
        display: flex;
        font-size: 14px;
        margin-top: 12px;
        //font-family: Alibaba PuHuiTi;
        font-weight: 400;
        color: #FFFFFF;

        .name {
            padding-left: 17px;
            display: flex;
            align-items: center;
            width: 50%;
            height: 47px;
            background: rgba(40, 130, 232, 0.1);
        }

        .code {
            padding-left: 17px;
            display: flex;
            align-items: center;
            margin-left: 2px;
            width: 50%;
            height: 47px;
            background: rgba(40, 130, 232, 0.1);
        }

        .site {
            padding-left: 17px;
            display: flex;
            align-items: center;
            margin-left: 2px;
            width: 30%;
            height: 47px;
            background: rgba(40, 130, 232, 0.1);
        }
    }
}

.industry {
    width: 100%;
    display: flex;

    .content.last {
        .top {
            background: left 8px center / contain no-repeat url('../../../../../assets/images/view/industry-bg2.png');
        }
    }

    .content.first {
        .top {
            background: left 8px center / contain no-repeat url('../../../../../assets/images/view/industry-bg1.png');
        }
    }

    .content {
        width: 50%;

        .box {
            cursor: pointer;
            width: 110px;
            margin: 0 auto;
            padding: 30px 0;
        }

        .top {
            width: 100px;
            height: 102px;
        }

        .middle {
            margin: 3px 0 5px;
            position: relative;

            span {
                // padding-right: 15px;
                width: 100%;
                display: inline-block;
                text-align: center;
                font-size: 24px;
                font-family: YouSheBiaoTiHei;
                font-weight: 400;
                color: #49C5FF;
                line-height: 34px;
                // text-shadow: 0px 6px 0px rgba(43,159,214,0.2);

                background: linear-gradient(0deg, #0064F8 0.1953125%, #70FFFA 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;

            }

            .last {
                opacity: 0.3;
                top: 5px;
                position: absolute;
                left: 0;
            }
        }

        .bottom {
            font-size: 16px;
            color: #FFFFFF;
            line-height: 12px;
            text-align: center;

        }

    }
}
</style>
<!-- <template>
    <div class="setting">
      <span text="阴影文字">阴影文字</span>
    </div>
  </template>
   
  <style   scoped>
  .setting span {
    font-size: 50px;
    font-weight: bold;
    text-shadow: 0px 5px 0px rgba(0, 0, 0, 0.35);
    /*文字的颜色*/
    color: #fff;
  }
  .setting span:before {
    /*覆盖的文字*/
    content: attr(text);
    position: absolute;
    z-index: 10;
    /*覆盖文字的颜色*/
    color: rgb(243, 72, 72);
    -webkit-mask: linear-gradient(to left, rgb(243, 72, 72), transparent);
  }
  </style> -->