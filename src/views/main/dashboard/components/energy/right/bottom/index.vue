<template>
  <Content :style="{ height: getRem(230) }" title="企业能耗TOP5" type="inside" tab="1" current="right">
    <template #header>
      <Selector @change="changeData" ref="currentData" />
    </template>
    <div class="bottomMain">
      <div v-if="currentState==='electric'" >
        <div v-if="(electric?.length===0)" >
        <div class="empty" > 暂无数据</div>

        </div>
        <div v-else>
          <div class="progress" v-for="(data,index) in electric" :key="data.name">
        <div class="progressLeft">
           {{ index+1 }}
        </div>
        <div class="progressRight">
          <div class="rightTop">
            <div class="topText">
              {{ data.name }} 
            </div>
            <div class="topNum">
            {{ data.value }}
            </div>
          </div>
          <el-progress :percentage="(data.value/electric[0].value)*100" :stroke-width="4" :stroke-linecap="square" />
        </div>
      </div>
        </div>
      
      </div>
      <div v-else-if="(currentState==='water'&&water.length===0)" >
        <div class="empty" > 暂无数据</div>

        </div>
   
      <div v-else >
        <div class="progress" v-for="(data,index) in water" :key="index">
        <div class="progressLeft">
           {{ index+1 }}
        </div>
        <div class="progressRight">
          <div class="rightTop">
            <div class="topText">
              {{ data.name }}
            </div>
            <div class="topNum">
            {{ data.value }}
            </div>
          </div>
          <el-progress :percentage="(data.value/water[0].value)*100" :stroke-width="4" :stroke-linecap="square" />

        </div>
      </div>
      </div>
    
  
    </div>
  </Content>
</template>
<script setup>
import Content from '../../../content/index.vue';
import { getRem, getVh } from '@/utils/rem.js';
import Selector from '@/views/main/dashboard/components/energy/components/selector/index.vue'
import { defineComponent, computed, ref, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
 
const customColor = ref('#437BFF')
const store = useStore()
const currentState= ref('electric')
const currentData=ref()

const water = computed(() => store.state.dashboard.thirdViewData?.enterpriseTop?.water)
const electric = computed(() => store.state.dashboard.thirdViewData?.enterpriseTop?.electric)
watch(()=>store.state.dashboard.thirdViewData?.enterpriseTop, (newVal) => {
      currentData.value.handleClick({
        type:'electric',name:'用电'
      })
    }, { deep: true })
const changeData = (type) => {
  currentState.value=type
    }
// watch(()=>store.state.dashboard.secondViewData.enterpriseTypeStatistics,(newVal)=>{
//     onChange(newVal[0])
// },{deep:true})
// const activeKey=ref('其他')
// onMounted(()=>{
//     onChange(elementSize[0])
//     // activeKey.value=elementSize[0]?.typeName
// })
</script>
<style scoped lang="scss">
.bottomMain {
  display: flex;
  flex-wrap: wrap;
  // justify-content: space-between;
  padding:15px 0px 22px 0px ;
  width: 100%;

  .progress {
    width: 400px;
    padding: 0px 12px;
    display: flex;
  }

  .progressLeft {
    width: 30px;
    height: 32px;
    font-size: 20px;
    font-family: YouSheBiaoTiHei;
    font-weight: 400;
    color: #49E7FF;
    border-right: 1px solid rgba(255, 255, 255, 0.4);
    // padding: 0px 0 0 15px;
  }

  .progressRight {
    margin-top: -5px;
    width: calc(100% - 30px);
    margin-left: 12px;

    .rightTop {
      display: flex;
  justify-content: space-between;
  margin-bottom: 3px;
  // padding-right: 25px;

      .topText {
        font-size: 12px;
        color: #FFFFFF;
      }

      .topNum {

        font-size: 16px;
        color: #00FFE4;
        font-weight: 500;
      }
    }

  }

}
</style>
<style  lang="scss"> .progress {

   .el-progress--line {

     margin-bottom: 15px;
     width: 310px;

     .el-progress-bar__inner {

       background: linear-gradient(90deg, #2A3544, #00FFD8);
       border-radius: 0;

       &::after {
         width: 2px;
         height: 10px;
         background: #FFFFFF;
         right: 0px;
         top: -3px;
         position: absolute;
       }
     }

     .el-progress-bar__outer {
       border-radius: 0;
       overflow: inherit;
       width: 310px;
       height: 3px;
       background: #2A3544;

     }

     .el-progress__text {
       display: none;
     }
   }
 }</style>
