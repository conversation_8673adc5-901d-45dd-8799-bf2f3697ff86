<template>
    <Content :style="{ height: getRem(215) }" title="能耗异常企业预警" type="inside" tab="1" current="right">
        <div class="bottomMain">
            <div class="energy">
                <div class="bg index0">{{elementSize?.normal||0}}</div>
                <div class="text">正常</div>
            </div>
            <div class="energy">
                <div class="bg index1">{{elementSize?.abnormal||0}}</div>
                <div class="text">异常</div>

            </div>
            <div class="energy">
                <div class="bg index2">{{elementSize?.warning||0}}</div>
                <div class="text"> 警告</div>
            </div>
        </div>
    </Content>
</template>
<script setup>
import Content from '../../../content/index.vue';
import { getRem, getVh } from '@/utils/rem.js';

import { defineComponent, computed, ref, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
const store = useStore()
const elementSize = computed(() => store.state.dashboard.thirdViewData?.abnormal)
 
</script>
<style scoped lang="scss">
.bottomMain {
    display: flex;
    justify-content: space-between;
    padding: 35px 16px 33px;

    .energy {
        width: 30%;
        height: 100%;

        .bg {
            width: 90px;
            height: 90px;
            margin: 0 auto;
padding-left: 10px;
            font-size: 28px;
            font-family: YouSheBiaoTiHei;
            color: #FFFFFF;
            text-align: center;
            line-height: 90px;

        }

        .text {
            font-size: 16px;
            text-align: center;
            width: 100%;
            margin: 25px 0 13px;
            color: #FFFFFF;
            padding-left: 10px;
        }


    }

    .index0 {
        background: center / contain no-repeat url('@/assets/images/view/energy/icon6.png');
    }

    .index1 {
        background: center / contain no-repeat url('@/assets/images/view/energy/icon5.png');

    }

    .index2 {
        background: center / contain no-repeat url('@/assets/images/view/energy/icon4.png');

    }
}</style>
