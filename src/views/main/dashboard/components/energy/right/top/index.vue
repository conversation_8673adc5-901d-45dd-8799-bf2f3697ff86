<template>
    <!-- 企业 -->
    <Content :style="{ height: getRem(220) }" title="企业用能监测" type="inside" tab="1" current="right">
        <div class="industry">
            <div class="content ">
                <div class="box">
                    <div class="middle">
                        <span class="text"> {{ elementSize?.used||0 }}</span>
                        <span class="last">家 </span>
                    </div>
                    <div class="top"></div>

                    <div class="bottom">用能企业</div>
                </div>

            </div>
            <div class="content ">
                <div class="box">
                    <div class="middle">
                        <span class="text">{{elementSize?.radio||0}}</span>
                        <span class="last">%</span>
                    </div>
                    <div class="top"></div>

                    <div class="bottom">开工率</div>
                </div>

            </div>
            <div>

            </div>
        </div>
    </Content>
</template>
<script setup>
import Content from '../../../content/index.vue';
import { defineProps, onMounted,computed } from 'vue'
import { getRem, getVh } from '@/utils/rem.js'
const props = defineProps(['viewData']);
import { useStore } from 'vuex'
const store = useStore()
const elementSize = computed(() => store.state.dashboard.thirdViewData?.enterprise)
// 
</script>

<style scoped lang="scss">
.industry {
    width: 100%;
    display: flex;

    .content {
        width: 50%;

        .box {
            width: 110px;
            margin: 0 auto;
            padding: 30px 0;
            text-align: center;
        }

        .top {
            width: 113px;
            height: 60px;
            background: left/contain no-repeat url('@/assets/images/view/energy/bg3.png');
            margin-bottom: 15px;
            margin-top: -10px;
        }

        .middle {
            // margin: 3px 0 5px;
            position: relative;

            .text {
                font-size: 30px;
                font-family: YouSheBiaoTiHei;
                font-weight: 400;
                color: #FFFFFF;
                background: linear-gradient(0deg, #0097FF 0%, #FFFFFF 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;

            }

            .last {
                width: 11px;
                height: 10px;
                font-size: 12px;
                font-family: Source Han Sans CN;
                font-weight: 400;
                color: #FFFFFF;
            }
        }

        .bottom {
            font-size: 16px;
            color: #FFFFFF;
            line-height: 12px;
            text-align: center;

        }

    }
}
</style>
 