
 
import * as echarts from 'echarts/core'
const options =(data:any)=>{
  let colors = [       
    {
      colorEnd:'#75C756',
      colorStart:'#A2D591'
  },
  {
    colorEnd:'#3E6AC9',
      colorStart:'#6989CE'
  },  {
    colorEnd:'#FFBA00',
      colorStart:'#FDD17A'                     
  },
  {
    colorEnd:'#FF4044',
      colorStart:'#F57C7E'
  },
  {
    colorEnd:'#47BDE2',
    colorStart:'#84CCE3'
}, {
  colorEnd:'#009B63',
  colorStart:'#4CB28B'
},
{
  colorEnd:'#FF6500',
  colorStart:'#FF976F'
},{
  colorEnd:'#9852B2',
  colorStart:'#AD7ABF'
},{
  colorEnd:'#F764C9',
  colorStart:'#F093D3'
},
{
  colorEnd:'#75C756',
  colorStart:'#A2D591'
},
{
colorEnd:'#3E6AC9',
  colorStart:'#6989CE'
},  {
colorEnd:'#FFBA00',
  colorStart:'#FDD17A'                     
},
{
colorEnd:'#FF4044',
  colorStart:'#F57C7E'
},
{
colorEnd:'#47BDE2',
colorStart:'#84CCE3'
}, {
colorEnd:'#009B63',
colorStart:'#4CB28B'
},
{
colorEnd:'#FF6500',
colorStart:'#FF976F'
},{
colorEnd:'#9852B2',
colorStart:'#AD7ABF'
},{
colorEnd:'#F764C9',
colorStart:'#F093D3'
},
  ]
  let filterValue=data&&data?.filter((item:any)=>!!item.value)
  if(filterValue&&filterValue.length>0){
  return{
    title: {
      text: '',
    },
tooltip: {
  trigger: 'item',
  // formatter: '{a} <br/>{b} : {c} ({d}%)',
  backgroundColor: 'rgba(0, 0, 0, 0.8)', // 背景色
  textStyle: {
    color: 'white', // 文本颜色
    fontSize: 14, // 文本字号
  },
  formatter:( params:any)=>{
    let data=`<div
    style="padding: '0px';margin:0px;background:red'"
    > <div>${params.data.name}:${params.percent}%  </div></div>`
    return data
  }
  // extraCssText: 'background: linear-gradient(to right,#286CE9 0%, #01D1FF 100%); color: white; border: 0px ',
},
series: [
  {
    name: '',
    type: 'pie',
    radius: '50%',
    center: ['50%', '40%'],
    // roseType: 'radius',
    emphasis: {
      label: {
        show: true
      }
    },
    label: {
      show: true,
      // position: 'center'
      color:'white'
    },
    labelLine: {
      show: true,
      lineStyle:{
        // color:'white'
      }
    },
    itemStyle: {
      normal: {
        show: true, // 显示示例牵引线
          borderWidth: 0,
          color: (list:any) => {
            // 设置描边宽度
              var colorList =  colors
              return new echarts.graphic.LinearGradient(1, 1, 0, 0, [{ //左、下、右、上
                  offset: 0,
                  color: colorList[list.dataIndex].colorStart ||'red'
              },   {
                  offset: 1,
                  color: colorList[list.dataIndex].colorEnd ||'red'
              }])                                    
            },
      }
  },
    data 
  },
 
]
} 
}else{
  return{
    title: {
  text: '暂无数据',
  x:'center',
  y:'center',
  textStyle:{
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize:12,
    fontWeight:'400'
  }
},
tooltip:'',
series:{
  name: '',
  type: 'pie',
  radius: '0%',
  center: ['0%', '0%'],
  // roseType: 'radius',
  emphasis: {
    label: {
      show: false
    }
  },
  label: {
    show: false,
    // position: 'center'
    // color:'white'
  },
  data:null
}
  }
}
}
export default  options
