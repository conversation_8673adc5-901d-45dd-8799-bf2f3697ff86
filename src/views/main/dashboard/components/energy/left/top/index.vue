<template>
    <Content :style="{ height: getRem(480) }" title="分类能耗(季度用能)" type="inside" tab="1" current="left">
        <div class="mainLabel">

       
        <div class="labels">
            <div class="icon index0"></div>
            <div class="datas">
                <div class="title">用电 kw·h</div>
                <div class="number">
                    <div class="numberLeft">
                        <div class="text">总数</div>
                        <div class="num">{{elementSize?.electric||0}}</div>
                    </div>
                    <div class="numberRight">
                        <div class="text">环比增长</div>
                        <div class="num">{{elementSize?.electricRadio||0}}%</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="labels">
            <div class="icon index1"></div>
            <div class="datas">
                <div class="title">用水 m³</div>
                <div class="number">
                    <div class="numberLeft">
                        <div class="text">总数</div>
                        <div class="num">{{elementSize?.water||0}}</div>
                    </div>
                    <div class="numberRight">
                        <div class="text">环比增长</div>
                        <div class="num">{{elementSize?.waterRadio||0}}%</div>
                    </div>
                </div>
            </div>
        </div>   <div class="labels">
            <div class="icon index2"></div>
            <div class="datas">
                <div class="title">综合能耗 tce</div>
                <div class="number">
                    <div class="numberLeft">
                        <div class="text">总数</div>
                        <div class="num">{{elementSize?.all||0}}</div>
                    </div>
                    <div class="numberRight">
                        <div class="text">环比增长</div>
                        <div class="num">{{elementSize?.allRadio||0}}%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </Content>
</template>
<script setup>
import Content from '../../../content/index.vue';
import { getRem, getVh } from '@/utils/rem.js'
import { defineComponent, computed, unref, onMounted } from 'vue'
import { useStore } from 'vuex'
const store = useStore()
const elementSize = computed(() => store.state.dashboard.thirdViewData?.classified)
onMounted(() => {
    // console.log(elementSize)
})
</script>
<style scoped lang="scss">
.mainLabel{
    // padding-top: ;
}
.labels {
    display: flex;
    flex-wrap: nowrap;
    padding: 50px 0 0px 20px;

    .icon {
        width: 90px;
        height: 90px;
    }
    .index0{
        background: center/contain no-repeat url('@/assets/images/view/energy/icon1.png');

    }
.index1{
    background: center/contain no-repeat url('@/assets/images/view/energy/icon2.png');

}
.index2{
    background: center/contain no-repeat url('@/assets/images/view/energy/icon3.png');

}
    .datas {
        width: 270px;
        height: 90px;
        padding-left: 20px;

        .title {
            width: 290px;
            height: 24px;
            background: left/contain no-repeat url('@/assets/images/view/energy/bg4.png');
            background-size: 109% 100%;

        padding-left: 20px;
font-size: 16px;
font-weight: 500;
color: #C0E7FF;
        }

        .number {
            width: 270px;
            height: 66px;
            display: flex;
            flex-wrap: nowrap;
justify-content: center;
align-items: center;
            .numberLeft {
                .num {

                    color: #49E7FF;
                }
            }

            .numberRight {
                .num {

                    color: #BFE4F8;
                }
            }

            .numberLeft,
            .numberRight {
                width: 50%;
                height: 66px;
                padding: 15px 20px 10px 30px;
                .num {

                    font-size: 18px;
                    font-family: YouSheBiaoTiHei;
                    font-weight: 400;
                }

                .text {
                    font-size: 14px;
                    color: #B8E4F6;
                }
            }
        }
    }
}</style>