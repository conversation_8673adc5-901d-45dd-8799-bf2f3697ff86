<template>
    <div class="center">
        <div class="centerTop">
            <div class="left">

                <div class="title">

                    <div class="titleTop">
                        <div class="titleBottom">
                            <span class="text">累计用电总量</span><span class="point">·万千瓦时</span>
                        </div>
                        <div class="number">{{ elementSize?.totalElectric || 0 }}</div>
                    </div>

                    <div class="titleTop">
                        <div class="titleBottom">
                            <span class="text">同比增长</span><span class="point"></span>
                        </div>
                        <div class="number">{{ elementSize?.electricRadio || 0 }}%</div>
                    </div>
                </div>
            </div>
            <div class="middle">

            </div>

            <div class="right">

                <div class="titleTop">
                    <div class="titleBottom">
                        <span class="text">累计用水总量</span><span class="point">·万吨</span>
                    </div>
                    <div class="number">{{ elementSize?.totalWater || 0 }}</div>
                </div>
                <div class="titleTop">
                    <div class="titleBottom">
                        <span class="text">同比增长</span><span class="point"></span>
                    </div>
                    <div class="number">{{ elementSize?.waterRadio || 0 }}%</div>
                </div>
            </div>
        </div>

        <Bottom />


    </div>
</template>
<script setup>
import Bottom from './bottom/index.vue';

import { defineComponent, computed, ref, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
const store = useStore()
const elementSize = computed(() => store.state.dashboard.thirdViewData)
// watch(()=>store.state.dashboard.secondViewData.statisticsValues,(newVal)=>{
//     onChange(newVal[0])
// },{deep:true})
// const activeKey=ref('其他')
// onMounted(()=>{
//     onChange(elementSize[0])
//     // activeKey.value=elementSize[0]?.typeName
// })
</script>
<style scoped lang="scss">
.center {
    width: 100%;
    height: 700px !important;

    .centerTop {
        width: 100%;
        height: 425px;
        position: relative;

        // margin-bottom: 60px;
        // display: flex;
        // padding: 0 90px;
        // flex-wrap: nowrap;
        .left {
            float: left;

            .titleTop {
                &:first-child {
                    margin-left: -60px;

                }
            }
        }

        .right {
            float: right;
            right: 0;
            top: 0;
            position: absolute;

            .titleTop {
                &:last-child {
                    margin-left: -60px;
                }
            }
        }

        .left,
        .right {
            padding: 30px 0 70px 0;
            // width: 90px;
            display: flex;
            justify-content: center;
            align-content: space-between;
            flex-wrap: wrap;
            height: 400px;
            width: 269px;

            .title {
                width: 100%;
                height: 60px;
                display: flex;
                flex-wrap: wrap;
                justify-content: center;


            }
        }

        .middle {
            width: 570px;
            height: 440px;
            background-repeat: no-repeat;
            background-position: center;
            background-image: url('../../../../../../assets/images/view/energy/bg1.png');
            background-size: 120%;
            margin: 0 auto;
        }
    }

    .centerBottom {
        width: 100%;
        height: 270px;

    }

    .titleTop {

        width: 269px;
        height: 152px;
        font-size: 15px;
        color: white;
        display: flex;
        flex-wrap: wrap;
        // line-height: 152px;
        // position: relative;
        justify-content: center;
        align-content: flex-start;
        // padding-right: 14px;
        // padding-top: 5px;
        background-repeat: no-repeat;
        background-position: center;
        background-image: url('../../../../../../assets/images/view/energy/bg2.png');
        background-size: 100%;
        margin-bottom: 30px;

    }

    .number {
        width: 269px;
        font-size: 35px;
        font-weight: 500;
        font-family: "YouSheBiaoTiHei";
        text-align: center;
        padding-left: 25px;
    }

    .titleBottom {
        width: 269px;
        height: 55px;
        font-size: 17px;
        display: flex;
        justify-content: flex-start;
        align-items: center;

        padding-left: 60px;

        .text {
            font-family: YouSheBiaoTiHei;
            color: #FFFFFF;

            background: linear-gradient(0deg, #0097FF 0%, #00FFF0 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .point {
            font-size: 12px;
            color: #FFFFFF;
        }
    }
}</style>