import * as echarts from 'echarts/core'
import { el } from 'element-plus/es/locale'

const options =(params:any,type:string)=>{
  // console.log('data111',params)
  let dataAxis=params&&params?.map((item:object)=>item?.name)
  let dataValue=params&&params?.map((item:object)=>item?.value  )
  let dataRadio=params&&params?.map((item:object)=>item?.radio  )

  return {
  title:{
    text:'',
    x:'center',
    y:'center',
    textStyle:{
      fontSize:14,
  
    }
  },
  legend: [   { 
    left: 'center',
  width:200,
  top: '0',
  // data:dataAxis,
  itemWidth:15,
  itemHeight:8,
  textStyle: {
    color: 'white',
    fontSize: 10,
    lineHeight:14
    // fontStyle: 'italic',
  },

},],
    tooltip: {
      trigger: 'axis',
      extraCssText: 'background: linear-gradient(to right,#286CE9 0%, #01D1FF 100%); color: white; border: 0px ',
      // borderColor: 'red', // 边框颜色
      borderWidth: 0, // 边框宽度
      textStyle: {
        color: 'white', // 文本颜色
        fontSize: 14, // 文本字号
      },
      axisPointer: {
        type: 'shadow'
      },
      // formatter:( params:any)=>{
      //   // console.log('params',params)
      //   let data=`<div> <div>${params[0].name}:${params[0].value}</div></div>`
      //   return data
      // }
    },
  
    grid: {
      left: '7%',
      right: '6%',
      bottom: '2%',
      top:'25%',
      containLabel: true
    },
    xAxis: [{
      type: 'category',
      data:dataAxis,
      textStyle: {
        color: '#fff',
        fontSize: 10,
      },
      axisTick: {
        alignWithLabel: true
      },
      splitLine: {
        show: false, // 隐藏x轴分隔线
      },
      axisLabel: {
        color: "#b5c1d180",
        interval:0,
        rotate:-20,	
        fontSize: 10 // 设置 x 轴文字的大小为 12 像素
      },
      nameTextStyle: {
        color: "#b5c1d166",
        // fontSize: 8,
      },
    },],
    yAxis:[ {
      splitLine: {
        show: false, // 隐藏x轴分隔线
      },
      type: 'value',
      axisLine: {
        show: false
      },
      name:type==='electric'? "单位：万千瓦时":"单位：万吨",
      axisLabel: {
        color: "#b5c1d180",
        fontSize: 10 // 设置 x 轴文字的大小为 12 像素
       
      },
            nameTextStyle: {
          color: "#b5c1d166",
          fontSize: 8,
        },
    },
    {
      
         splitLine: {
        show: true, // 隐藏x轴分隔线
        lineStyle: {
          type: "solid",
          color: "#b5c1d11a"
        }
      },
      type: 'value',
      name: '%',
      // min: 0,
      // max: 25,
      // interval: 5,
      axisLabel: {
        formatter: '{value} %'
      }
    }],
    series: [
      {
        name: type==='electric'?'耗电量':'用水量',
        type: 'bar',
        // showBackground: true,
        barWidth: 10, // 设置柱子宽度，单位为像素
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0, 0, 0, 1,
            [
              {offset: 0, color: '#00A7F5'},
              {offset: 1, color: '#00F1F5'},
              // {offset: 0.5, color: '#188df0'},
            ]
          )
        },
        // emphasis: {
        //   itemStyle: {
        //     color: new echarts.graphic.LinearGradient(
        //       0, 0, 0, 1,
        //       [
        //         {offset: 0, color: '#2378f7'},
        //         {offset: 0.7, color: '#2378f7'},
        //         {offset: 1, color: '#83bff6'}
        //       ]
        //     )
        //   }
        // },
        data:  dataValue
      },
      {
        name:  '增比',
        type: 'line',
        yAxisIndex: 1,
        itemStyle: {
          color: '#c4b152'
        },
        tooltip: {
          valueFormatter: function (value) {
            return value + '%';
          }
        },
        data: dataRadio
      }
    ]
  } 
}
export default  options
