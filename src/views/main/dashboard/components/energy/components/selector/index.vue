<template>
      <el-dropdown  >
                <span class="el-dropdown-link">
                    {{ currentData }}
                    <el-icon class="el-icon--right">
                        <arrow-down />
                    </el-icon>
                </span>
                <template #dropdown>
                    <el-dropdown-menu>
                        <el-dropdown-item v-for="i in options" :key="i" @click="handleClick(i)">{{ i.name }}</el-dropdown-item>
                    </el-dropdown-menu>
                </template>
            </el-dropdown>
</template>

<script setup>
import { ArrowDown } from '@element-plus/icons-vue'
import { defineComponent, onMounted, ref, computed, watch ,defineExpose} from 'vue'
 const props=defineProps(['currentData', ])
 const options=[{type:'electric',name:'用电'},{type:'water',name:'用水'}]
 const currentData=ref('用电')
 const emit = defineEmits(['change'])
 const handleClick=(e)=>{
    currentData.value=e.name
    emit('change',e.type)
 }
 defineExpose({
    handleClick
 })
</script>


