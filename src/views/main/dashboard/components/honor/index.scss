@import '@/assets/style/variables.scss';
@import '@/assets/style/mixins.scss';

@mixin swiper-wrapper($height: 190px) {
  width: 100%;
  height: $height;
}

@mixin swiper-slide() {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  font-size: $font-size-huge * 1.6;
  background-color: $banner-bg;
  color: $text-secondary;
}

@mixin toolbar() {
  width: 100%;
  display: flex;
  justify-content: space-around;
  border-bottom: 1px solid $border-color;
}

@mixin toolbar-button() {
  flex: 1;
  padding: 0;
  margin: 0;
  border: none;
  border-right: 1px solid $border-color;
  line-height: 3em;
  background-color: $banner-bg;
  color: $text-color;
  cursor: pointer;
  &:last-child {
    border: none;
  }
  &:hover {
    background-color: $header-bg;
  }
}