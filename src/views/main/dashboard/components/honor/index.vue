 <template>
    <div class="honer" :style="{
        display: display,
        // transform: `${translate}`,marginLeft:margin+'px'
    }">
        <div v-for="(item, index) in showList" :key="index" style="width: 50%;" class="honerItem">
            <div class="content">
                <div class="top">
                </div>
                <div class="bottom">
                    {{ item }}
                </div>
            </div>
        </div>
    </div>
    <div v-if="data.length>2" class="swiper-button-next" @click="clickNext"></div>
    <div v-if="data.length>2" class="swiper-button-prev" @click="clickPrev"></div>
</template>
  
<script lang="ts" setup>
import { defineProps, onMounted, ref, watch } from 'vue'
const props = defineProps(['viewData']);
let data = ref([])
let showList = ref([])
let display = ref('')
let i = ref(0)
onMounted(() => {
    data.value = JSON.parse(JSON.stringify(props?.viewData?.honors || []))
    if (data.value.length> 2) {
          showList.value =data.value.slice(0, 2);
        } else {
            showList.value =data.value
        }
})
watch(() => props.viewData, (newVal) => {
    data.value = JSON.parse(JSON.stringify(newVal?.honors  || []))
    if (data.value.length> 2) {
          showList.value =data.value.slice(0, 2);
        } else {
            showList.value =data.value
        }
}, { deep: true }
)

const clickNext = () => {

    //let viewDatas = data.value
    //let test = viewDatas.splice(1, 2)
    //test.push(viewDatas[0])
    //data.value = test
    i.value = (i.value + 2) % data.value.length;
    showList.value = data.value.slice(i.value , i.value  + 2).concat(data.value.slice(0, Math.max(0, 2 - (data.value.length - i.value))));
    // console.log(test)
    // display.value='none'
    // // transform.value=-(transform.value+3)
    // // translate.value=`translate(${transform.value}px ,0px)`
    // setTimeout(()=>{
    //     display.value='flex'
    //     // margin.value=-(transform.value)
    // },1)

}
const clickPrev = () => {
    //let viewDatas = data.value
//
    //let test = viewDatas.splice(1, 3)
    //test = [...test, ...viewDatas.splice(0, 1)]
    //data.value = test
    i.value = (i.value - 2 + data.value.length)  % data.value.length;
    showList.value = data.value.slice(i.value , i.value  + 2).concat(data.value.slice(0, Math.max(0, 2 - (data.value.length - i.value))));
    // console.log(test)
    // display.value='none'
    // // transform.value=-(transform.value+3)
    // // translate.value=`translate(${transform.value}px ,0px)`
    // setTimeout(()=>{
    //     display.value='flex'
    //     // margin.value=-(transform.value)
    // },1)
}

</script>
<style>
.swiper-slide-active {
    /* transform: scale(1.2) !important; */
}
</style>
<style lang="scss" scoped>
@import '@/assets/style/variables.scss';
@import '@/assets/style/mixins.scss';
@import './index.scss';

.active {
    transition: all 0.3s;
    transform: scale(1.2) !important;
}

.swiper-button-next {
    top: 55%;
    right: 0;
    z-index: 999;
    position: absolute;
    width: 15px;
    height: 15px;
    background: center / contain no-repeat url("@/assets/images/view/honer-right.png");
}

.swiper-button-prev {
    width: 15px;
    height: 15px;
    background: center / contain no-repeat url("@/assets/images/view/honer-left.png");
    top: 55%;
    z-index: 999;
    position: absolute
}

.honer {
    transition: all 0.3s;
    padding: 10px;
    width: 92%;
    display: flex;
    margin-left: 4%;
    justify-content: space-around
}

.swiper {
    @include swiper-wrapper();
}

.slide {
    @include swiper-slide();
}

.content {
    margin-top: 20px;

    //     margin: auto;
    // width: 100%;
    // margin: 0 auto;
    .top {
        margin: auto;
        width: 70px;
        height: 70px;
        background: left -32px top -35px / 190% 190% no-repeat url("@/assets/images/view/honer.png");
    }

    .bottom {
        margin-top: 16px;
        width: 100%;
        text-align: center;
        font-size: 14px;
        font-family: YouSheBiaoTiHei;
        padding: 0 10px;
        font-weight: 500;
        color: #89b1f5;
        //line-height: 33px;
        // text-shadow: 0px 10px 10px rgba(1,24,33,0.7);

        //background: linear-gradient(0deg, #0064F8 0.1953125%, #FFFFFF 100%);
        //background-image:-webkit-linear-gradient(bottom,#0064F8,#FFFFFF); 
        //-webkit-background-clip: text;
        //-webkit-text-fill-color: transparent;
    }
}
</style>  
<!-- 
<template>
    <el-carousel :interval="4000" type="card" height="160px">
      <el-carousel-item v-for="item in 3" :key="item">
        <h3 text="2xl" justify="center">{{ item }}</h3>
      </el-carousel-item>
    </el-carousel>
  </template>
  
  <style scoped>
  .el-carousel__item h3 {
    color: #475669;
    opacity: 0.75;
    line-height: 200px;
    margin: 0;
    text-align: center;
  }
  
  .el-carousel__item:nth-child(2n) {
    background-color: #99a9bf;
  }
  
  .el-carousel__item:nth-child(2n + 1) {
    background-color: #d3dce6;
  }
  </style>
   -->