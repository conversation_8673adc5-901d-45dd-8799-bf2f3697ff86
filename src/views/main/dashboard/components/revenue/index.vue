<template>
    <!-- 经营状况 -->
    <div class=" revenue">
        <div  v-if="(loginState==='admin'||loginState==='zhengwu')">
        <div class=" revenueLeft">
            <div class=" top">

            </div>
            <div class=" middle">
                <div class="text">{{viewData?.annualOutput}}亿</div>
            </div>
            <div class=" bottom">
                园区上年度总产值
            </div>
        </div>
        <div class=" revenueRight">
            <div class=" top">

            </div>
            <div class=" middle">
                <div class="text">{{viewData?.totalTaxRevenue}}亿</div>
            </div>
            <div class=" bottom">
                园区上年度总税收
            </div>
        </div>
    </div>
        <div v-else class="noneData">暂无数据</div>
    </div>
    
</template>
<script setup>
import { getRem } from '@/utils/rem.js'
import {defineProps,ref} from 'vue'
const props = defineProps(['viewData']);
let loginState=ref(localStorage.getItem('userState'))
</script>
<style scoped lang="scss">
.top,
.middle,
.bottom,
.revenueLeft,
.revenueRight {
    background-repeat: no-repeat;
    background-size: 100% 100%;

}

.revenueLeft,
.revenueRight {
    position: absolute;
    width: 198px;
    height: 30vh;
    background-image: url('../../../../../assets/images/view/bg3.png');

    .top {
        width: 175px;
        height: 144px;
        margin: 0 auto;
        
    }

    .middle {
        position: relative;
        width: 198px;
        height: 25px;
        background-image: url('../../../../../assets/images/view/re-bg1.png');
        margin: 40px auto 15px auto;

        .text {
            position: absolute;
            text-align: center;
            width: 100%;
            top: -10px;
            font-size: 24px;
            font-family: YouSheBiaoTiHei;
            font-weight: 400;
            color: #00F8F4;
            text-shadow: 0px 10px 10px rgba(1, 24, 33, 0.7);
            background: linear-gradient(0deg, #0064F8 0.1953125%, #FFFFFF 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
    }

    .bottom {
        width: 183px;
        height: 40px;
        background-image: url('../../../../../assets/images/view/re-bg.png');
        margin: 0 auto;
        font-size: 1rem;
    font-weight: 400;
    color: #FFFFFF;
 line-height: 40px;  
    text-align: center;
    }
}

.revenueLeft {
    left: 0;

 .top {

        background-image: url('../../../../../assets/images/view/re-chanzhi.png');
    }

    .bottom {
        // font-size: 16px;
        // font-weight: 300;
        // color: #FFFFFF;
    }
}

.revenueRight {
    right: 0;

    .top {
        background-image: url('../../../../../assets/images/view/re-shui.png');
    }
}

.revenue {
    width: 400px;
    height: 293px;
    /* background: red; */
    position: absolute;
    top: 100px;
}
.noneData{
    margin-top: -35px !important;
}
</style>
