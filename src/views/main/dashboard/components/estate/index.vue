<template>
  <div>
    <!-- <div class="tips">
      <div class="tipsItem" v-for="(item, index) in data" :key="item.name">
        <div class="tipsIcon"></div>
        <div class="tipsName"> {{ item.name }}
         <span class="value" :class='"tipsName" + index'> 
          {{ item.value }}</span>
        </div>
      </div>
    </div> -->
    <div class="layout-container">
      <div ref="dom" class="chart">
      </div>


    </div>
  </div>
</template>

<script lang="ts">
import type { Ref } from 'vue'
import { defineComponent, onMounted, ref, computed, watch } from 'vue'
import { useEventListener } from '@vueuse/core' //引入监听函数，监听在vue实例中可自动销毁，无须手动销毁
import * as echarts from 'echarts/core'
import {
  CanvasRenderer
} from 'echarts/renderers'
import {
  PieChart
} from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  ToolboxComponent
} from 'echarts/components'

// 引入options配置
import options from './pie'
import { isNullOrUndefined } from 'util'
// import {defineProps,onMounted} from 'vue'
// const props = defineProps(['viewData']);
export default defineComponent({
  props: {
    viewData: {
      type: Object,

    }
  },
  //   data(){
  // return{
  //   isShow:false
  // }
  //   },
  setup(props) {
    const dom: Ref<HTMLDivElement> = ref(null) as any
    let myEchart: echarts.ECharts | null = null

    const data = computed(() => props.viewData)

    watch(data, (newVal) => {
      if (newVal) {
        setecharts(newVal, 1)
      }

    })
    onMounted(() => {
      //   if(data){
      //     console.log('index',data.value)
      setecharts(data, 2)
      //   }


    })
    const setecharts = (newVal: any, index: number) => {
      let datas = props.viewData
      if (datas) {
        echarts.use([TitleComponent, TooltipComponent, GridComponent, PieChart, LegendComponent, ToolboxComponent, CanvasRenderer])
        myEchart = echarts.init(dom.value)
        let option = options(datas)
        myEchart.setOption(option)
        useEventListener("resize", () => myEchart!.resize())
      } else {
        dom.value.innerHTML = '暂无数据'


        //     myEchart.showLoading({
        //     text: '暂无数据',
        //     effect: 'bubble',
        //     effectOption: {
        //         effect: {
        //             n: 0
        //         }
        //     }
        // });
        // myEchart.setOption(null)
        // dom.value=null
        // myEchart = echarts.init(null)
      }
      //  [
      //   { value: newVal.digitalPortNum, name: '数字化港口' },
      //   { value: newVal.digitalRailTransitNum, name: '数字化轨交' },
      //   { value: newVal.digitalRoadBridgeNum, name: '数字化道桥' },
      //   { value: newVal.smartParkingNum, name: '智慧停车' },
      // ]

    }
    return {
      dom,
      data,
      // data: [
      //   { value: 'digitalPortNum', name: '数字化港口' },
      //   { value: 'digitalRailTransitNum', name: '数字化轨交' },
      //   { value: 'digitalRoadBridgeNum', name: '数字化道桥' },
      //   { value: 'smartParkingNum', name: '智慧停车' },
      // ]
    }
  }
})
</script>

<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 100%;
  // background: #fff;
  // padding: 10px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #ffffffb3;
}

.layout-container {
  width: 100%;
  height: 260px;
  background: transparent;
}

.tips {
  padding: 10px 10px 0 10px;
  display: flex;
  width: 100%;
  flex-wrap: wrap;

  .tipsItem {
    width: 30%;
    display: flex;
    flex-wrap: nowrap;
    color: #FFFEFE;
    vertical-align: middle;
    align-items: center;
  }

  .tipsIcon {
    width: 23px;
    height: 20px;
    margin-top: 5px;
    margin-right: 3px;
    background: center / contain no-repeat url('../../../../../assets/images/view/squear.png');

  }

  .tipsName {
    width: calc(100% - 20px);

    font-size: 16px;

    font-weight: 400;
    color: #FFFEFE;

    .value {
      padding-left: 3px;
      font-size: 20px;
      font-family: YouSheBiaoTiHei;
    }
  }

  .tipsName0 {
    color: #42A3FE;
  }

  .tipsName1 {
    color: #12B1BE;
  }

  .tipsName2 {
    color: #CDB40C;

  }

  .tipsName3 {
    color: #0043FF;

  }
}
</style>
 
