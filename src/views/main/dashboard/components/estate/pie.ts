
 
import * as echarts from 'echarts/core'
const options =(data:any)=>{
  // console.log('data111',data)
  let leng=data&&data?.map((item:any)=>item.name)
  let colors = [       
    {
      colorEnd:'#75C756',
      colorStart:'#A2D591'
  },
  {
    colorEnd:'#3E6AC9',
      colorStart:'#6989CE'
  },  {
    colorEnd:'#FFBA00',
      colorStart:'#FDD17A'                     
  },
  {
    colorEnd:'#FF4044',
      colorStart:'#F57C7E'
  },
  {
    colorEnd:'#47BDE2',
    colorStart:'#84CCE3'
}, {
  colorEnd:'#009B63',
  colorStart:'#4CB28B'
},
{
  colorEnd:'#FF6500',
  colorStart:'#FF976F'
},{
  colorEnd:'#9852B2',
  colorStart:'#AD7ABF'
},{
  colorEnd:'#F764C9',
  colorStart:'#F093D3'
},
  ]
  return{

 
// title: {
//   text: '',
//   subtext: '',
//   left: ''
// },
tooltip: {
  trigger: 'item',

  // formatter: '{a} <br/>{b} : {c} ({d}%)',
  backgroundColor: 'rgba(0, 0, 0, 0.8)', // 背景色
  textStyle: {
    color: 'white', // 文本颜色
    fontSize: 14, // 文本字号
  },
  formatter:( params:any)=>{
    // console.log(params)
    let data=`<div
    style="padding: '0px';margin:0px;background:red'"
    > <div>${params.data.name}:${params.value}家  </div>
    <div> 占比:${params.percent}%  </div>
    </div>`
    return data
  }
  // extraCssText: 'background: linear-gradient(to right,#286CE9 0%, #01D1FF 100%); color: white; border: 0px ',
},
legend:   {
   left: '10',
type:'scroll',
bottom: '0',
data:leng,
itemWidth:9,
itemHeight:3,
textStyle: {
  color: 'white',
  fontSize: 10,
  lineHeight:14
  // fontStyle: 'italic',
},},
// toolbox: {
//   show: true,
//   // feature: {
//   //   mark: { show: true },
//   //   dataView: { show: true, readOnly: false },
//   //   restore: { show: true },
//   //   saveAsImage: { show: true }
//   // }
// },
// color:colors,

series: [
  {
    name: '',
    type: 'pie',
    radius: '50%',
    center: ['50%', '40%'],
    // roseType: 'radius',
    // itemStyle: {
    //   borderRadius: 5
    // },
    // label: {
    //   show: false
    // },
    emphasis: {
      label: {
        show: true
      }
    },
    label: {
      show: true,
      // position: 'center'
      color:'white'
    },
    labelLine: {
      show: true,
      lineStyle:{
        // color:'white'
      }
      
    },
    itemStyle: {
      normal: {
        show: true, // 显示示例牵引线
          color: (list:any) => {
          // 设置描边宽度
            var colorList =  colors
            return new echarts.graphic.LinearGradient(1, 1, 0, 0, [{ //左、下、右、上
                offset: 0,
                color: colorList[list.dataIndex].colorStart 
            },   {
                offset: 1,
                color: colorList[list.dataIndex].colorEnd 
            }])                                    
          }, 
          // borderColor: 'white',
          borderWidth: 0,

      }
  },
    data 
  },
 
]
} 
}
export default  options
