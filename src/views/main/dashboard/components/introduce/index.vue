<template>
  <!-- 概况 -->
  <div class="introduce">
    <div
      class="introduceContent"
      v-for="(dataItem, index) in dataList"
      :key="index"
    >
      <div class="left" :class="`left${index}`"></div>
      <div class="right">
        <div class="name">{{ dataItem.name }}</div>
        <div class="data">
          {{ viewData?.[dataItem.key] }}{{ dataItem.unit }}
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { defineProps, onMounted } from "vue";

const props = defineProps(["viewData"]);
onMounted(() => {
  // console.log(viewData)
});
const dataList = [
  {
    img: "",
    name: "园区占地",
    key: "landOccupation",
    unit: "亩",
  },
  {
    img: "",
    name: "总面积",
    key: "totalArea",
    unit: "万㎡",
  },
  {
    img: "",
    name: "园区楼栋",
    key: "buildingNum",
    unit: "栋",
  },
  {
    img: "",
    name: "招商总面积",
    key: "investmentArea",
    unit: "万㎡",
  },
  {
    img: "",
    name: "入驻企业",
    key: "enterpriseNum",
    unit: "家",
  },
  {
    img: "",
    name: "园区人才",
    key: "personnel",
    unit: "人",
  },
];
</script>

<style scoped lang="scss">
.introduce {
  padding: 30px 5px;
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}

.introduceContent {
  width: 49%;
  height: 75px;
  /* background: red; */
  margin-bottom: 30px;
  display: flex;
}

.left {
  background: center / contain no-repeat;
  width: 80px;
  height: 75px;
}
.left0 {
  background-image: url("@/assets//images/view/introduce-bg1.png");
}
.left1 {
  background-image: url("@/assets//images/view/introduce-bg2.png");
}
.left2 {
  background-image: url("@/assets//images/view/introduce-bg3.png");
}
.left3 {
  background-image: url("@/assets//images/view/introduce-bg4.png");
}
.left4 {
  background-image: url("@/assets//images/view/introduce-bg5.png");
}
.left5 {
  background-image: url("@/assets//images/view/introduce-bg6.png");
}

.right {
  padding: 7px;
  .name {
    font-size: 16px;
    color: #ffffff;
  }

  .data {
    font-size: 16px;
    font-family: YouSheBiaoTiHei;
    font-weight: 500;
    color: #00f8f4;
    text-shadow: 0px 10px 10px rgba(1, 24, 33, 0.7);
    background: linear-gradient(0deg, #0064f8 0.1953125%, #70fffa 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}
</style>