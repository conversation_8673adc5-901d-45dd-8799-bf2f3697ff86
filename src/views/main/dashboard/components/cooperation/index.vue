
<template>
  <div class="boxss">
    <div class="title">园区合作咨询</div>
    <div class="content">
      <div class="left">
        <span
          >请完善以下信息，我们的招商顾问会在3个工作日与您联系，请保持电话畅通！</span
        >
        <el-form
          ref="ruleFormRef"
          :model="ruleForm"
          :inline="true"
          :rules="rules"
          label-width="120px"
          size="default"
          label-position="top"
        >
          <el-form-item label="您的姓名" prop="clientName">
            <el-input
              v-model="ruleForm.clientName"
              placeholder="请输入您的姓名"
            />
          </el-form-item>
          <el-form-item label="您的手机号码" prop="phone">
            <el-input v-model="ruleForm.phone" placeholder="请输入您的手机号" />
          </el-form-item>
          <el-form-item label="您的单位名称" prop="enterpriseName">
            <el-input
              v-model="ruleForm.enterpriseName"
              placeholder="请输入您的单位名称"
            />
          </el-form-item>
          <el-form-item label="您的职位" prop="jobTitle">
            <el-input
              v-model="ruleForm.jobTitle"
              placeholder="请输入您的职位"
            />
          </el-form-item>
          <el-form-item
            style="width: 100%"
            label="您的合作诉求"
            prop="cooperationAppeal"
          >
            <el-input
              v-model="ruleForm.cooperationAppeal"
              placeholder="请输入您的合作诉求，不超过500字"
              maxlength="500"
              type="textarea"
            />
          </el-form-item>
        </el-form>
        <div class="btn">
          <button @click="submit" class="primary">提交</button>
          <button @click="quit" class="info">取消</button>
        </div>
      </div>
      <div class="right">
        <div class="top">
          <div @click="left" class="l"></div>
          <div
            :class="'carouselimg' + imgindex"
          />
          <div @click="right" class="r"></div>
        </div>
        <div class="phe"><span>招商热线：0513-81188088</span></div>
      </div>
    </div>
  </div>
</template>

<script>
import { conaddAPI } from "@/api/system/propertyRepair";
export default {
  data() {
    return {
      ruleForm: {},
      rules: {
        clientName: [
          { required: true, message: "请输入姓名", trigger: "blur" },
          { max: 15, message: "姓名15个字符以内", trigger: "blur" },
        ],
        phone: [
          { required: true, message: "请输入手机号", trigger: "blur" },
          { max: 11, min:11,message: "手机号必须为11位", trigger: "blur" },
      ],
        cooperationAppeal: [
          { required: true, message: "请输入合作诉求", trigger: "blur" },
        ],
        enterpriseName:[
        { max: 15, message: "单位名称15个字符以内", trigger: "blur" },
        ],
        jobTitle:[
        { max: 15, message: "职务名称15个字符以内", trigger: "blur" },
        ]
      },
      showImg: "/src/assets/cooperation/1.png",
      imgindex:1,
      imgList: [
        {
          url: "/src/assets/cooperation/1.png",
        },
        {
          url: "/src/assets/cooperation/2.png",
        },
        {
          url: "/src/assets/cooperation/3.png",
        },
        {
          url: "/src/assets/cooperation/4.png",
        },
        {
          url: "/src/assets/cooperation/5.png",
        },
        {
          url: "/src/assets/cooperation/6.png",
        },
        {
          url: "/src/assets/cooperation/7.png",
        },
        {
          url: "/src/assets/cooperation/8.png",
        },
      ],
    };
  },
  methods: {
    async submit() {
      await this.$refs.ruleFormRef.validate();
      await conaddAPI(this.ruleForm);
      this.$message.success("提交成功！");
      this.quit();
    },
    left(){
      if(this.imgindex==1){
        this.imgindex=8
        this.showImg=this.imgList[this.imgindex].url
      }else{
        this.imgindex--
        this.showImg=this.imgList[this.imgindex].url
      }
    },
    right(){
      if(this.imgindex==8){
        this.imgindex=1
        this.showImg=this.imgList[this.imgindex].url
      }else{
        this.imgindex++
        this.showImg=this.imgList[this.imgindex].url
      }
    },
    quit() {
      this.$emit("cooperation2");
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep {
  .el-form-item__label {
    font-size: 14px;
    color: #b2c3df;
  }

  .el-form--inline.el-form--label-top .el-form-item {
    width: 45%;
  }

  .el-input__inner {
    color: #fff;
  }

  .el-textarea__inner {
    background: #0d1e3d;
    box-shadow: 0 0 0 1px rgba(64, 129, 203, 0.8) inset;
    border: 1px solid #0d1e3d;
    height: 250px;
    color: #fff;
  }

  .el-button .el-button--primary .el-button--default {
    color: red;
  }
}

.boxss {
  margin-top: 5.2rem;

  .title {
    padding-top: 20px;
    font-size: 20px;
    font-family: Alibaba PuHuiTi;
    font-weight: bold;
    color: #c5d4ed;
    margin-bottom: 1rem;
  }

  .content {
    display: flex;
  }

  .left {
    width: 60%;
    height: 560px;
    border-right: 1px solid rgba(19, 56, 103);

    span {
      font-size: 16px;
      font-family: Alibaba PuHuiTi;
      font-weight: 400;
      color: #b2c3df;
      line-height: 32px;
      opacity: 0.5;
      margin-bottom: 20px;
      display: flex;
    }

    .btn {
      margin-top: 10px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;

      .primary {
        width: 80px;
        height: 32px;
        cursor: pointer;
        background: rgba(42, 102, 176, 0.7);
        border: 1px solid #2f89cd;
        border-radius: 4px;
        color: #ffffff;
        margin-right: 16px;
      }

      .info {
        width: 80px;
        cursor: pointer;
        height: 32px;
        background: rgba(42, 102, 176, 0);
        border: 1px solid #2f89cd;
        opacity: 0.6;
        color: #ffffff;
        border-radius: 4px;
      }
    }
  }

  .right {
    width: 40%;
    height: 560px;

    .top {
      width: 100%;
      display: flex;
      justify-content: center;
    }

    .carouselimg {
      width: 338px;
      height: 517px;
      background-size: contain;
    }
    .carouselimg1{
      width: 338px;
      height: 517px;
      background-size: contain;
      background: center/contain no-repeat
      url(/src/assets/cooperation/1.png);
    }
    .carouselimg2{
      width: 338px;
      height: 517px;
      background-size: contain;
      background: center/contain no-repeat
      url(/src/assets/cooperation/2.png);
    }
    .carouselimg3{
      width: 338px;
      height: 517px;
      background-size: contain;
      background: center/contain no-repeat
      url(/src/assets/cooperation/3.png);
    }
    .carouselimg4{
      width: 338px;
      height: 517px;
      background-size: contain;
      background: center/contain no-repeat
      url(/src/assets/cooperation/4.png);
    }
    .carouselimg5{
      width: 338px;
      height: 517px;
      background-size: contain;
      background: center/contain no-repeat
      url(/src/assets/cooperation/5.png);
    }
    .carouselimg6{
      width: 338px;
      height: 517px;
      background-size: contain;
      background: center/contain no-repeat
      url(/src/assets/cooperation/6.png);
    }
    .carouselimg7{
      width: 338px;
      height: 517px;
      background-size: contain;
      background: center/contain no-repeat
      url(/src/assets/cooperation/7.png);
    }
    .carouselimg8{
      width: 338px;
      height: 517px;
      background-size: contain;
      background: center/contain no-repeat
      url(/src/assets/cooperation/8.png);
    }

    .l {
      width: 0.94rem;
      height: 0.94rem;
      background: center/contain no-repeat
        url(/src/assets/images/view/honer-left.png);
      z-index: 999;
      display: flex;
      margin-top: 50%;
      margin-right: 20px;
      cursor: pointer;
    }

    .r {
      display: flex;
      cursor: pointer;
      margin-top: 50%;
      margin-left: 20px;
      z-index: 999;
      width: 0.94rem;
      height: 0.94rem;
      background: center/contain no-repeat
        url(/src/assets/images/view/honer-right.png);
    }
    .phe {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 12px;
      span {
        font-size: 18px;
        font-family: Alibaba PuHuiTi;
        font-weight: bold;
        font-style: italic;
        color: #c5d4ed;
        line-height: 32px;
      }
    }
  }
}
</style>