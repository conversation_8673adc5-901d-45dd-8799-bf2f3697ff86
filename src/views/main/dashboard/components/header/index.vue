<template>
    <div class="header" :class="isCenter">
        <div class="text">{{ title }}</div>
        <div class="right" v-if="showRight === 'showMore'" @click="showMore">
            <div>更多 ></div>
        </div>
        <div v-else class="right" >
            <slot></slot>
        </div>
        
    </div>
</template>
<script setup>
import { defineProps,defineEmits } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'
const props = defineProps(['title', 'showRight', 'isCenter']);
const emit = defineEmits(['showMore'])

const showMore=()=>{

    emit('showMore')
}
</script>

<style  lang="scss">
   .right {
       
        .el-dropdown {
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            color: #FFFFFF;
            display: flex;
            align-items: center;
        }
        .el-dropdown{
            i{
                svg{
                    padding-top: 2px;
                }
            }
        }

    }
</style>
<style scoped lang="scss">
.center.header {
    background-image: url('../../../../../assets/images/view/tab2/title.png');
    background-size: 104% 100%;
    background-position-x: -37px;

    .text {
        margin-top: -13px;
        // padding-left: 27px;
    }
}

.header {
    width: 100%;
    height: 44px;
    background-image: url('../../../../../assets/images/view/header.png');
    /* background-position-x: -30px; */
    background-repeat: no-repeat;
    background-position: center;
    background-size: 105%;
    background-position-x: -21px;
    /* margin: 0 auto 40px auto; */
    /* background: center / contain no-repeat */
    display: flex;
    justify-content: space-between;

    .right {
        height: 44px;
        padding-top: 14px;
        // width: 27px;
        // height: 14px;
        font-size: 12px;
        // font-family: Alibaba PuHuiTi;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.8);
        padding-right: 10px;
        cursor: pointer;

     
       

    }
}

.text {
    width: 65%;
    padding-left: 40px;
    color: #FBEFFF;
    font-weight: bolder;
    // background-image:
    //    rgba(111, 200, 241, 0.36) 10%
    // -webkit-linear-gradient(top, rgba(111, 200, 241, 0.36) 0%, #ffffff 60%, );
    //    -webkit-linear-gradient(top,#0EC5EC 0%, #ffffff 60%, );
    // -webkit-background-clip: text;
    // -webkit-text-fill-color: transparent;
    text-align: left;
    font-size: 20px;
    font-family: YouSheBiaoTiHei;
    font-weight: 500;
    font-style: italic;


}</style>