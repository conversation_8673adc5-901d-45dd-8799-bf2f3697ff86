<template>
    <div class="main">
        <Header :title="title" :showRight="showRight"  :isCenter="current" @showMore="showMore" >
            <slot name="header" message="hello"></slot>
        </Header>
        <div class="introduceMain" :style="style" :class='["background"+`${tab?tab:1}`, current]'  >
        <div v-if="type==='inside'" class="typeInside"> 
            <slot></slot>
        </div>
        </div>
<!-- <div class="introduceFooter"> -->
   <div  v-if="type!=='inside'">
    <slot></slot>
   </div>
<!-- </div> -->
    </div>
</template>
<script setup>
import {defineProps,defineEmits} from 'vue'
import Header from '../header/index.vue'
const props = defineProps(['style','title' , "type",'showRight','tab','current']);
// console.log(props)
const emit = defineEmits(['showMoreDialog'])

const showMore=()=>{
    emit('showMoreDialog')
}
</script>
<style scoped>
.main{
    position: relative;
}
.introduceMain {
    width: 100%;
    height: 400px;
    background-repeat: no-repeat;
    background-size: 100% 100%;

}
.background0{
    background-image: url('../../../../../assets/images/view/bg1.png');

}
.background1.left{
    /* background-image: url('../../../../../assets/images/view/tab2/bg2.png'); */
}
.background1.right{
    background-image: url('../../../../../assets/images/view/tab2/bg3.png');
}
.background1.center{
    background-image: url('../../../../../assets/images/view/tab2/bg4.png');
}
.introduceFooter{

}
.typeInside{
    width: 100%;
}
</style>
