<template>
    <Content :style="{ height: getRem(210) }" :title="title" type="inside" :tab="recentTab" current="left"
        showRight="showMore" @showMoreDialog="showMoreDialog">
        <ScreenTable :tableData="tableList" :title="title+'详情'" />
    </Content>
    <el-dialog v-model="centerDialogVisible" title="" align-center class="screenTable padding0Dialog"
        custom-class="screenTable padding0Dialog" center>
        <Dialog :centerDialogVisible="centerDialogVisible" @changeInner="changeInner" :dialogData="dialogData"
            @getData="getDialogData" :title="title" />
        <!-- 详情 -->
        <el-dialog class="screenTable padding0Dialog" custom-class="screenTable padding0Dialog" v-model="innerVisible"
            width="30%" append-to-body>
            <DialogDetail :innerVisible="innerVisible" :detailData="detailData" :title="innerVisibleTitle" />
            <!-- @changeInner="changeInner"  -->
            <!--  -->
        </el-dialog>
    </el-dialog>
</template>
<script setup>
// import ScrollTable from '@/components/scrollTable/index.vue'
import ScreenTable from '@/components/screenTable/index.vue'
import Content from '@/views/main/dashboard/components/content/index.vue';
import { getRem, getVh } from '@/utils/rem.js'
import { defineComponent, computed, unref, onMounted,watch, ref,defineProps } from 'vue'
import { getHomeEven } from '@/api/dashboard'
import { useStore } from 'vuex'
import Dialog from '@/components/screenTable/dialog/index.vue'
import DialogDetail from '@/components/screenTable/dialog/detail.vue'
const props = defineProps(['title', 'id']);

const centerDialogVisible = ref(false)
const innerVisible = ref(false)
const store = useStore()
const elementSize = computed(() => store.state.dashboard.secondViewData?.outputTop10)
const tableList = ref()
const dialogData = ref()
const innerVisibleTitle = ref()
const detailData = ref()
const isInitialized = ref(false) // 添加初始化标识

onMounted(() => {
    if (!isInitialized.value) {
        getList()
        isInitialized.value = true
    }
})

// 监听id变化，避免重复渲染
watch(() => props.id, (newId, oldId) => {
    if (newId && newId !== oldId && isInitialized.value) {
        getList()
    }
})

const getList = () => {
    if (!props.id) return // 如果没有id，直接返回
    
    let data = {
        "pageSize": 2,
        "pageNum": 1,
        id: props.id,
        conditions: []
    }
    getHomeEven(data).then((res) => {
        tableList.value = res.data.elements

    })
}
const changeInner = (data) => {
    innerVisible.value = true
    innerVisibleTitle.value = data.title
    detailData.value = data
}
const showMoreDialog = () => {

    getDialogData({
        "pageSize": 12,
        "pageNum": 1,
        id: props.id,
        conditions: []
    }, true)
}
const getDialogData = (pageData, showTrue) => {
    let data = pageData ? pageData : {
        "pageSize": 12,
        "pageNum": 1,
        id: props.id, // 确保dialog也使用正确的id
        conditions: []
    }
    getHomeEven(data).then((res) => {
        if (showTrue) {
            centerDialogVisible.value = true
        }
        dialogData.value = res.data
    })
}
</script>
<style scoped></style>