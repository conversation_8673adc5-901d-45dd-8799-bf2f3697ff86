<template>
  <div class="center">
    <div class="centerTop">

      <div class="topTitle">

        <Content :style="{ height: 0 }" title="园区风采" />
      </div>
      <div class="tabText">
        <span :class="{ active: viewState === '1' }" @click="changViewState('1')"> 风采图片</span>
        <span :class="{ active: viewState === '2' }" @click="changViewState('2')"> 风采视频</span>
      </div>
      <div class="centerImg">
        <div class="centerImgs" v-if="viewState === '1'">
          <el-carousel v-if="imgValue.length > 0" trigger="click" :height='getRem(350)' arrow="never" :interval='3000'>
            <el-carousel-item v-for="item in imgValue" :key="item">
              <div class="titleImg" :style="{ backgroundImage: `url(${item.url})` }"></div>
            </el-carousel-item>
          </el-carousel>
          <div v-else class="nodata"></div>
        </div>
        <div class="centerVideo" v-else>
          <div class="centerVideoIn" v-if="videoUrl">

            <Video :videoUrl="videoUrl" />


          </div>
          <div v-else class="centerVideoIn">
            <div class="nodata"></div>
          </div>
        </div>
      </div>
      <div class="introduction">
        <div class="introductionIn" :style="{ transform: `translateX(-${showText ? 0 : getRem(215)})` }">

          <div class="introductionText">
            {{ introDesc && introDesc.length > 140 ? `${introDesc.slice(0, 140)}...` : introDesc }}<span class="show"
              @click="showMore">更多>></span>
          </div>

          <!-- <div class="introductionButton">
            <div :class="showText ? 'introductionButtonIn' : 'introductionButtonOut'" @click="hideText">

            </div>
          </div> -->
        </div>
      </div>
    </div>

    <Bottom />

    <el-dialog custom-class="screenTable" class="screenTable" v-model="innerVisible" width="30%" append-to-body>
      <DialogDetail :innerVisible="innerVisible" />
      <!-- @changeInner="changeInner" :detailData="detailData" :title="title"  -->
    </el-dialog>
  </div>
</template>
<script setup>
import Bottom from './bottom/index.vue';
import { getRem, getVh } from '@/utils/rem.js'
import Content from '../../content/index.vue';
import Video from './Video.vue'
import { defineComponent, computed, ref, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
import DialogDetail from './dialogDetail/index.vue'
import { getParkStyle } from '@/api/dashboard.ts'
const viewState = ref('1')
const store = useStore()
const elementSize = computed(() => store.state.dashboard.secondViewData.statisticsValues)
let showText = ref(true)
let imgValue = ref([])
let introDesc = ref()
const videoUrl = ref('')
const innerVisible = ref(false)
onMounted(() => {
  getPark()
})
const changViewState = (type) => {
  viewState.value = type
}
const getPark = () => {
  getParkStyle().then((result) => {
    imgValue.value = result.data.styleList
    introDesc.value = result.data.introDesc
    videoUrl.value = result.data.videoUrl
  }).catch((err) => {
  });
  // getParkStyle({
  //   "businessCode": "park_intro",
  //   "conditions": []
  //   }).then((result) => {
  //     parkIntro.value = result.data.elements
  // }).catch((err) => {
  // });
}
const hideText = () => {
  showText.value = !showText.value
}
const showMore = () => {
  innerVisible.value = true
}
</script>
<style scoped lang="scss">
.nodata {
  width: 100%;
  height: 350px;
  background-image: url(../../../../../../assets/cooperation/nodata.jpg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}

// .el-carousel{
//     height: 350px;
// }
.demonstration {
  color: var(--el-text-color-secondary);
}

.el-carousel__item h3 {
  color: #475669;
  opacity: 0.75;
  line-height: 150px;
  margin: 0;
  text-align: center;
}

.el-carousel__item:nth-child(2n) {
  background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
  background-color: #d3dce6;
}

.center {
  width: 100%;
  height: 700px !important;

  .centerTop {
    .topTitle {
      position: absolute;
      z-index: 22;
      width: 400px;
      top: 19px;
      left: 30px;
    }

    width: 100%;
    height: 425px;
    display: flex;
    position: relative;
    flex-wrap: nowrap;
    background-position: center;
    background-image: url('../../../../../../assets/images/view/home/<USER>');
    background-size: 100% 100%;

    .centerImg {
      width: 100%;
      height: 425px;
      padding: 40px;

      .centerImgs {
        width: 75%;
        float: right;
      }

      .titleImg {
        width: 100%;
        height: 100%;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        background-position: center;
        // background-size: cover;
        // url('@/assets/images/view/tab2/bg1.png');

      }

      .centerVideoIn {
        width: 75%;
        // margin: 0 auto;
        float: right;

        .amis-scope .antd-Page-body {
          padding: 0 !important;
        }
      }

      .centerVideo {
        width: 100%;
        background: #13244c;

      }
    }

    .introduction {
      width: 25%;
      height: 100%;

      position: absolute;
      left: 19px;
      padding: 18px 0px 18px 0;
      overflow: hidden;

      .introductionIn {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
        width: 100%;
        height: 390px;
        // transform: translateX(-230px);
        // left: -230px;
        // background-position: center;
        // background-image: url('../../../../../../assets/images/view/home/<USER>');
        // background-size: 100% 100%;
        position: absolute;
        display: flex;

        // overflow: scroll;
        .introductionText {
          // background-position: ;
          background: center /contain no-repeat url('../../../../../../assets/images/view/home/<USER>');
          background-size: 100% 1000%;
          width: 233px;
          height: 100%;
          padding: 50px 20px 20px 20px;
          font-size: 14px;
          color: #FFFFFF;
          line-height: 26px;
          text-align: left;
        }

        .introductionButton {
          width: 17px;
          height: 100%;
          display: flex;
          align-items: center;

          .introductionButtonIn {
            width: 100%;
            height: 50px;
            background: center/60% no-repeat url('@/assets/images/view/honer-left.png');
          }

          .introductionButtonOut {
            width: 100%;
            height: 50px;
            background: center/60% no-repeat url('@/assets/images/view/honer-right.png');
          }
        }
      }

    }

    .middle {
      width: 570px;
      height: 440px;
      background: center / contain no-repeat url('@/assets/images/view/view.png');
    }
  }

  .centerBottom {
    width: 100%;
    height: 270px;

  }
}

.show {
  cursor: pointer;
  color: #38f1e0;
}

.screenTable {
  width: 900px;
  height: 580px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: top;
  //  center / contain no-repeat 
  border-radius: 5px;
  background-image: url("@/assets/images/view/home/<USER>");
  background-size: 100% 100%;

  .el-dialog__headerbtn {
    right: 0px;
    padding: 5px;
  }

  .el-dialog__header {
    padding: 0;
  }
}
</style>
<style lang="scss">
.tabText {
  display: flex;
  flex-wrap: nowrap;
  // width: 180px;
  position: absolute;
  right: 40px;
  top: 40px;
  z-index: 111;

  span {
    cursor: pointer;
    // width: 90px;
    display: inline-block;
    padding: 2px 10px;
    background: #0C2753;

    opacity: 0.7;
    font-size: 14px;
    font-weight: 400;
    color: #FFFFFF;
    box-shadow: 0 0 0 0px;

    // border: 0;
    &:first-child {
      &.active {
        // border: 1px solid #3695FF;
        box-shadow: 0 0 3px 1px #3695FF inset;
      }

      border-radius: 4px 0px 0px 4px;

      border-right: 0;
    }

    &:last-child {
      &.active {
        // border: 1px solid #3695FF;
        box-shadow: 0 0 3px 1px #3695FF inset;
      }

      border-radius: 0px 4px 4px 0px;

    }
  }
}

.centerVideoIn {
  .amis-scope .antd-Page-body {
    padding: 0 !important;
  }
}

.centerImgs .el-carousel__button {
  width: 10px;
}
</style>