<template>
    <Content :style="{ height: getRem(410) }" title="园区公告" type="inside" :tab="recentTab" current="left"
        showRight="showMore" @showMoreDialog="showMoreDialog">
        <ScreenTable :tableData="tableList" title="园区公告详情" />
    </Content>
    <el-dialog v-model="centerDialogVisible" title="" align-center class="screenTable padding0Dialog"
        custom-class="screenTable padding0Dialog" center>
        <Dialog :centerDialogVisible="centerDialogVisible" @changeInner="changeInner" :dialogData="dialogData"
            @getData="getDialogData" title="园区公告" />
        <el-dialog class="screenTable padding0Dialog" custom-class="screenTable padding0Dialog" v-model="innerVisible"
            width="30%" append-to-body>
            <DialogDetail :innerVisible="innerVisible" :detailData="detailData" :title="innerVisibleTitle" />
        </el-dialog>
    </el-dialog>
</template>
<script setup>
// import ScrollTable from '@/components/scrollTable/index.vue'
import ScreenTable from '@/components/screenTable/index.vue'
import Content from '../../../content/index.vue';
import { getRem, getVh } from '@/utils/rem.js'
import { defineComponent, computed, unref, onMounted, ref } from 'vue'
import { useStore } from 'vuex'
import { getHomeAnnouncement } from '@/api/dashboard'
import Dialog from '@/components/screenTable/dialog/index.vue'
import DialogDetail from '@/components/screenTable/dialog/detail.vue'

const store = useStore()
const elementSize = computed(() => store.state.dashboard.secondViewData?.outputTop10)
const tableList = ref()
// const showDetail=ref(false)
const centerDialogVisible = ref(false)
const innerVisible = ref(false)
const detailData = ref()
const dialogData = ref()
const innerVisibleTitle = ref()
onMounted(() => {
    getList()
})
const getList = (pageData) => {
    let data = pageData ? pageData : {
        "pageSize": 4,
        "pageNum": 1,
        conditions: []
    }
    getHomeAnnouncement(data).then((res) => {
        tableList.value = res.data.elements
    })
}
const showMoreDialog = () => {
    getDialogData({
        "pageSize": 12,
        "pageNum": 1,
        conditions: []
    })
}
const getDialogData = (pageData) => {
    let data = pageData ? pageData : {
        "pageSize": 12,
        "pageNum": 1,
        conditions: []
    }
    getHomeAnnouncement(data).then((res) => {
        centerDialogVisible.value = true
        dialogData.value = res.data
    })
}
const changeInner = (data) => {

    if (data?.input_type == 2) {
        window.open(`${data?.link_url}`, '_blank');
    } else {
        innerVisible.value = true
        innerVisibleTitle.value = data.title
        detailData.value = data
    }
}
</script>
<style scoped></style>