

<template>
  <div v-if="amisjson">
  <AmisComponent :amisjson="amisjson" />
  </div>
</template>
<script setup>
import {defineProps,ref, onMounted,watch} from 'vue'
import AmisComponent from "@/components/amis/index.vue";
 const props=defineProps(['videoUrl','imgValue'])
 watch(()=> props.videoUrl,(newVal)=>{
  amisjson.value= {
  type: "page",
  "body": {
    "type": "video",
    "src": props.videoUrl,
//  "poster":  '',
    "jumpFrame": true,
    "jumpBufferDuration": 0,
    "aspectRatio": "16:9",
    "autoPlay": true,
    loop:true,
  },
  title: "",
  id: "u:c070dd0c3b4e",
}
 },{deep: true})
 const  amisjson =ref()
 onMounted(()=>{
  // console.log(111,props.videoUrl)
  amisjson.value= {
  type: "page",
  "body": {
    "type": "video",
    "src": props.videoUrl,
//  "poster":  '',
    "jumpFrame": true,
    "jumpBufferDuration": 0,
    "aspectRatio": "16:9",
    "autoPlay": true,
    loop:true,
  },
  title: "",
  id: "u:c070dd0c3b4e",
}
 })
  
</script>
<style scoped lang="scss">
.tab {
  padding-top: 20px;
  padding-left: 10px;
}

.on {
  background-color: #169bd5;
  color: #fff;
}

</style>
<style  lang='scss'>
.buttonSubmit{
  margin-left: 50% !important;
}
.videoView{
  .antd-Video{
    width: 500px !important;
    // height: 300px !important;
  }
  video {
   
  }
 
}
.bigButton{
  margin-top: 15px !important;
  float:right;
  
}
.uploadImg{
  padding-top: 60px !important;
  
}
</style>