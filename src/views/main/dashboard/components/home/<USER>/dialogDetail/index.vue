<tem>

</tem>
<template>
    <div class="tableClass">
        <div class="detail">
            <div class="content"  ref="introInfo">
            </div>
        </div>
    </div>
</template>
<script setup>
import { defineProps, onMounted, watch, ref } from 'vue'
import { getDetail } from '@/api/dashboard'
import dayjs from 'dayjs'

const props = defineProps(['detailData', 'innerVisible', 'title']);
const showData = ref();
const introInfo=ref()
watch(() => props.innerVisible, (newVal) => {
    if (newVal === false) {
        showData.value = ''
    } else {
        getDetailData()
    }
}, { deep: true })

onMounted(() => {
    getDetailData()
})
const detailData = ref()
const getDetailData = () => {
    getDetail().then((result) => {
        showData.value = result.data.elements[0] || ''
        introInfo.value.innerHTML=result.data.elements[0]?.intro_info || ''
    }).catch((err) => {

    })
}
const open = (url) => {
    window.open(url)
}
</script>

<style scoped lang="scss">
// @import './index.scss';
.img{
    width: 100%;
 }
.content {
    font-size: 14px;
    color: #B2C3DF;
    line-height: 32px;
padding: 0 10px;
width: 100%;

}
 
.detail {
    width: 100%;
    height: 500px;
    overflow-y: scroll;
    font-size: 14PX;
}
.tableSearch {
    display: flex;
    justify-content: space-between;
}

.tableInput {
    width: 284px;
    height: 32px;
    font-size: 14PX;
    // border: 1px solid #4081CB;

    .el-input__wrapper {
        background: #0d346a;
        box-shadow: 0 0 0 1px #1153ee inset;
    }
}
 
</style>

<style lang="scss">
 .content{
    img{
    width: 100%  !important;
 }
 }

 </style>