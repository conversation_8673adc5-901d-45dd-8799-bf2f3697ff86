import * as echarts from "echarts/core";
// var dataAxis =   ['1月', '2月', '3月', '4月', '5月', '6月', '7月','8月','9月','10月','11月','12月'];
// var data = [220, 182, 191, 234, 290, 330, 310, 123, 442, 321, 90, 149, 210, 122, 133, 334, 198, 123, 125, 220];
// var yMax = 500;
// var dataShadow = [];

// for (var i = 0; i < data.length; i++) {
//     dataShadow.push(yMax);
// }

const options = (params) => {
  // console.log('data',data)
  let dataAxis = params&&params?.map((item: object) => item?.name);
  let dataValue = params&&params?.map((item: object) => item?.value);
  return {
    tooltip: {
      trigger: "axis",
      extraCssText:
        "background: linear-gradient(to right,#286CE9 0%, #01D1FF 100%); color: white; border: 0px ",
      // borderColor: 'red', // 边框颜色
      borderWidth: 0, // 边框宽度
      textStyle: {
        color: "white", // 文本颜色
        fontSize: 14, // 文本字号
      },
      axisPointer: {
        type: "shadow",
      },
      formatter: (params: any) => {
        // console.log('params',params)
        if(params){
          let data = `<div> <div>${params[0].name}:${params[0].value}</div></div>`;
          return data;
        }
      },
    },

    grid: {
      left: "2%",
      right: "6%",
      bottom: "2%",
      top: "2%",
      containLabel: true,
    },
    xAxis: {
      // type: 'category',
      type: "value",
      // data: dataAxis,
      // axisLabel: {
      //   inside: false,
      // },
      textStyle: {
        color: "#fff",
        fontSize: 10,
      },
      axisTick: {
        alignWithLabel: true,
      },
      splitLine: {
        show: false, // 隐藏x轴分隔线
      },
      axisLabel: {
        color: "#b5c1d180",
        interval: 0,
        // rotate:-45,
      },
      nameTextStyle: {
        color: "#b5c1d166",
      },
      // nameGap:15,					//---坐标轴名称与轴线之间的距离
      // nameRotate:30,			//---坐标轴名字旋转

      axisLine: {
        show: true
      },
      // z: 10
    },
    yAxis: {
      splitLine: {
        show: false, // 隐藏x轴分隔线
      },
      type: "category",
      // type: 'value',
      data: dataAxis,
      axisLine: {
        show: true,
      },
      // name: "人",
      axisLabel: {
        color: "#b5c1d180",
      },
      nameTextStyle: {
        color: "#b5c1d166",
      },
    },
    // yAxis: [

    //   {
    //     type: "value",
    //     name: "万元",
    //     nameTextStyle: {
    //       color: "#b5c1d166"
    //     },
    //     splitLine: {
    //       show: true,
    //       lineStyle: {
    //         type: "solid",
    //         color: "#b5c1d11a"
    //       }
    //     },
    //     axisLabel: {
    //       color: "#b5c1d180"
    //     }
    //   }
    // ],
    dataZoom: [
      {
        type: "inside",
      },
    ],

    series: [
      {
        type: "bar",
        // showBackground: true,
        barWidth: 10, // 设置柱子宽度，单位为像素
        itemStyle: {
          barBorderRadius: [0, 10, 10, 0],
          color: (list: any) => {
            // 设置描边宽度
            var colorList = [
              {
                colorStart: "#30C6DC",
                colorEnd: "#2FC6DC",
              },
              {
                colorStart: "#30C6DC",
                colorEnd: "#2FC6DC",
              },
              {
                colorStart: "#F4CF3D",
                colorEnd: "#F47E3D",
              },
              {
                colorStart: "#30C6DC",
                colorEnd: "#2FC6DC",
              },
              {
                colorStart: "#30C6DC",
                colorEnd: "#2FC6DC",
              },
              {
                colorStart: "#30C6DC",
                colorEnd: "#2FC6DC",
              },
              {
                colorStart: "#30C6DC",
                colorEnd: "#2FC6DC",
              },
            ];
            return new echarts.graphic.LinearGradient(1, 1, 0, 0, [
              {
                //左、下、右、上
                offset: 0,
                color: colorList[list.dataIndex]["colorStart"] || "#30C6DC",
              },
              {
                offset: 1,
                color: colorList[list.dataIndex]["colorEnd"] || "#2FC6DC",
              },
            ]);
          },
        },
        // emphasis: {
        //   itemStyle: {
        //     color: new echarts.graphic.LinearGradient(
        //       0, 0, 0, 1,
        //       [
        //         {offset: 0, color: '#2378f7'},
        //         {offset: 0.7, color: '#2378f7'},
        //         {offset: 1, color: '#83bff6'}
        //       ]
        //     )
        //   }
        // },
        data: dataValue,
      },
    ],
  };
};
export default options;
