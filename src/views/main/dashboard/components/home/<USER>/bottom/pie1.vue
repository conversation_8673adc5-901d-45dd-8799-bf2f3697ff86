<template>
  <div v-if="showCharts" :id="chartId" class="chart-container" />
  <div v-else class="noDataSelf">
    NoData
    <!-- <NoData /> -->
  </div>
</template>

<script setup>
import * as echarts from 'echarts';
import { ref, watch, onMounted, onBeforeUnmount, nextTick } from 'vue';

// Props定义
const props = defineProps({
  showData: {
    type: Object,
    default: () => ({}),
  },
});

// 响应式数据
const animate = ref(true);
const list = ref([]);
const showCharts = ref(false);
const chart = ref(null);
// 生成唯一的图表ID
const chartId = ref(`middlePie_${Math.random().toString(36).substr(2, 9)}`);

// 处理窗口调整大小
const resizeHandler = () => {
  if (chart.value) {
    chart.value.resize();
  }
};

// 主要图表渲染方法
const industrySector = (newValue) => {
  if (!newValue || !newValue.nodeCount) {
    showCharts.value = false;
    return;
  }

  let colors = [
    ['#1637FF', '#0482FF'],
    ['#FDBA00', '#F4ED62'],
    ['#3FC444', '#29CC79'],
    ['#FC4907', '#F19C0B'],
    ['#771DFF', '#A946FF'],
    ['#60BFCA', '#1DFFF7'],
    ['#FFAC46', '#FFCE6C'],
    ['#1637AF', '#0482AF'],
    ['#FCC4A3', '#F7E0A9'],
    ['#3FC4A4', '#29CCC9'],
    ['#FC49F7', '#F19CAB'],
    ['#771DAF', '#A946AF'],
    ['#60BFAA', '#1DFFA7'],
    ['#FFACC6', '#FFCEBC'],
  ];

  const result = newValue?.value;

  // 先设置 showCharts 状态，确保 DOM 会被渲染
  showCharts.value = result.length > 0;

  // 如果没有数据，直接返回，不需要初始化图表
  if (!showCharts.value) {
    return;
  }

  // 使用 nextTick 确保 DOM 已更新
  nextTick(() => {
    try {
      // 如果已有实例，先销毁
      if (chart.value) {
        chart.value.dispose();
        chart.value = null;
      }

      const dom = document.getElementById(chartId.value);
      if (!dom) {
        console.error(`Failed to find DOM element with id "${chartId.value}"`);
        return;
      }

      // 确保DOM元素有有效的尺寸
      if (dom.offsetWidth === 0 || dom.offsetHeight === 0) {
        console.error('DOM element has invalid dimensions');
        return;
      }

      chart.value = echarts.init(dom);

      let totalQuantity = newValue.nodeCount || 0;

      let newArr = result.map((item, index) => {
        return {
          name: item.name,
          value: item.value,
          proportion: '',
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: colors[index % colors.length][0] },
              { offset: 1, color: colors[index % colors.length][1] },
            ]),
          },
        };
      });

      // 计算占比
      newArr.forEach((item) => {
        if (totalQuantity > 0) {
          item.proportion =
            ((item.value / totalQuantity) * 100).toFixed(2) + '%';
        } else {
          item.proportion = '0%';
        }
      });

      let grid = {
        left: '5%',
        top: '0%',
        right: '5%',
        bottom: '10%',
      };

      let tooltip = {
        trigger: 'item',
        backgroundColor: '#00112dbf',
        borderColor: '#0066FF',
        borderWidth: 1,
        textStyle: {
          color: '#fff',
          fontStyle: 'normal',
          fontWeight: 'normal',
          fontFamily: 'sans-serif',
          fontSize: 12,
        },
        formatter: function (params) {
          let res =
            params.data.name +
            '： ' +
            '<span style="color: #00FFF0;font-size: 12px;">' +
            params.data.value +
            '</span> 人' +
            '<br/>' +
            '占比：' +
            '<span style="color: #00FFF0;font-size: 12px;">' +
            params.data.proportion +
            '</span>';
          return res;
        },
      };

      let title = [
        {
          text: `${totalQuantity}`,
          top: '40%',
          textAlign: 'center',
          left: '24%',
          textStyle: {
            color: '#FFFFFF',
            fontSize: 20,
            fontWeight: '400',
            fontFamily: 'YouSheBiaoTiHei',
          },
        },
        {
          text: `人`,
          top: '52%',
          textAlign: 'center',
          left: '24%',
          textStyle: {
            color: '#AEB9C0',
            fontSize: 16,
            fontWeight: '400',
            fontFamily: 'YouSheBiaoTiHei',
          },
        },
      ];

      let series = [
        {
          type: 'pie',
          zlevel: 2,
          radius: ['62%', '80%'],
          animationDuration: 1500,
          animationDurationUpdate: 1500,
          itemStyle: {},
          left: '-50%',
          top: '0%',
          emphasis: {},
          label: {
            show: false,
          },
          data: newArr,
        },
        {
          type: 'gauge',
          zlevel: 1,
          center: ['25%', '50%'],
          radius: '60%',
          startAngle: 90,
          endAngle: -270,
          axisLine: {
            show: false,
          },
          axisTick: {
            distance: -6,
            length: 6,
            lineStyle: {
              color: '#585e67',
              width: 2,
            },
          },
          axisLabel: {
            show: false,
          },
          splitNumber: 6,
          splitLine: {
            show: true,
            distance: -6,
            length: 6,
            lineStyle: {
              color: 'rgba(255, 255, 255, 0.18)',
              width: 2,
            },
          },
          data: [],
          detail: {},
          pointer: {
            show: false,
          },
        },
      ];

      let lineHeight = 14;
      let fontSize = 12;
      let top = '20%';
      let left = '50%';

      let option = {
        grid,
        tooltip,
        title,
        legend: {
          type: 'scroll',
          textStyle: {
            color: '#fff',
            fontSize,
            opacity: 0.8,
            lineHeight,
          },
          top,
          left,
          pageIconColor: '#fff',
          pageIconInactiveColor: '#fff',
          pageIconSize: '12',
          pageTextStyle: {
            color: '#FFF',
          },
          orient: 'vertical',
          itemWidth: 10,
          itemHeight: 10,
          formatter: function (name) {
            // 查找对应的数据项
            const item = newArr.find(item => item.name === name);
            return `${name} ${item ? item.value : ''}人`;
          },
        },
        series,
      };

      // 设置配置并绑定 resize 事件
      chart.value.setOption(option);
      window.addEventListener('resize', resizeHandler);
    } catch (error) {
      console.error('Error initializing chart:', error);
      showCharts.value = false;
    }
  });
};

// Watch监听showData变化
watch(
  () => props.showData,
  (newValue) => {
    if (newValue && Object.keys(newValue).length > 0) {
      industrySector(newValue);
    }
  },
  { immediate: true, deep: true }
);

// 生命周期钩子
onMounted(() => {
  // 组件挂载后的逻辑
});

onBeforeUnmount(() => {
  // 在组件销毁前销毁图表实例，避免内存泄漏
  if (chart.value) {
    chart.value.dispose();
    chart.value = null;
  }
  window.removeEventListener('resize', resizeHandler);
});
</script>

<style lang="scss" scoped>
.chart-container {
  width: 100%;
  height: 200px;
  min-height: 200px;
}

.noDataSelf {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  color: #fff;
}
</style>
