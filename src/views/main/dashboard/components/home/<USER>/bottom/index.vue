<template>
  <Content
    :style="{ height: getRem(280) }"
    title="园区人才情况"
    tab="1"
    current="right"
    type="inside"
  >
    <div class="layout-container" :style="{ height: getRem(250) }">
      <!-- 切换标签 -->
      <div class="tab-container">
        <div
          class="tab-item"
          :class="{ active: currentTab === 'education' }"
          @click="switchTab('education')"
        >
          学历情况
        </div>
        <div
          class="tab-item"
          :class="{ active: currentTab === 'title' }"
          @click="switchTab('title')"
        >
          职称情况
        </div>
      </div>

      <!-- 图表和数据展示 -->
      <div class="content-container">
        <PieChartShow
          v-if="currentTab === 'education'"
          :showData="educationData"
        />
        <PieChartShow v-if="currentTab === 'title'" :showData="titleData" />
      </div>
    </div>
  </Content>
</template>

<script lang="ts" setup>
import type { Ref } from 'vue';
import { defineComponent, onMounted, ref, computed, watch } from 'vue';
import * as echarts from 'echarts/core';
import Content from '../../../content/index.vue';
import { getRem } from '@/utils/rem.js';
import PieChartShow from './pie1.vue';
import { getTalentStat } from '@/api/dashboard';
import { number } from 'echarts';

// 学历情况数据

const educationData = ref({
  nodeCount: 60,
  value: [
    { name: '博士研究生', value: 122, percentage: 20, color: '#4CAF50' },
    { name: '硕士研究生', value: 122, percentage: 20, color: '#FF7043' },
    { name: '大学本科', value: 122, percentage: 20, color: '#2196F3' },
  ],
});
// 职称情况数据

const titleData = ref({
  nodeCount: 610,
  value: [
    { name: '高级', value: 183, percentage: 30, color: '#4CAF50' },
    { name: '中级', value: 427, percentage: 70, color: '#FF7043' },
  ],
});
const currentTab = ref('education'); // 当前选中的标签
const dom: Ref<HTMLDivElement> = ref(null) as any;

onMounted(() => {
  initChart();
});

// 切换标签
const switchTab = (tab: string) => {
  currentTab.value = tab;
};

// 初始化图表
const initChart = () => {
  getTalentStat().then((res) => {
    // console.log(res);
    if (res.status === '0') {
      let educationTotal = 0;
      res.data.education.forEach((item: any) => {
        educationTotal += Number(item.value);
      });

      let titleTotal = 0;
      res.data.title.forEach((item: any) => {
        titleTotal += Number(item.value);
      });
      educationData.value.nodeCount = educationTotal;
      titleData.value.nodeCount = titleTotal;
      educationData.value.value = res.data.education;
      titleData.value.value = res.data.title;
    }
  });
};

// const getTalentStatData = async () => {
//   try {
//     const res = await getTalentStat();
//     console.log(res);
//   } catch (error) {
//     console.log(error);
//   }
// };

// getTalentStatData();
</script>

<style lang="scss" scoped>
.layout-container {
  width: 100%;
  background: transparent;
  display: flex;
  flex-direction: column;
}

.tab-container {
  display: flex;
  margin-bottom: 20px;
  border: 1px solid #1c91ff;
  border-radius: 20px;
  overflow: hidden;
  width: fit-content;
  margin-left: auto; // 添加右对齐
  margin-top: 10px;
}

.tab-item {
  padding: 3px 24px;
  cursor: pointer;
  color: #1c91ff;
  font-size: 14px;
  transition: all 0.3s ease;
  background: transparent;

  &.active {
    background: #1c91ff;
    color: #fff;
  }

  &:hover:not(.active) {
    background: rgba(255, 255, 255, 0.1);
  }
}

.content-container {
  display: flex;
  align-items: center;
  height: calc(100% - 60px);
  gap: 20px;
}

.chart-container {
  position: relative;
  flex: 1;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart {
  width: 100%;
  height: 180px;
}

.total-number {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 24px;
  font-weight: bold;
  color: #fff;
  text-align: center;
  pointer-events: none;
}

.data-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding-left: 20px;
}

.data-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #fff;
  font-size: 14px;
}

.color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  flex-shrink: 0;
}

.category-name {
  flex: 1;
  min-width: 80px;
}

.percentage {
  color: #4caf50;
  font-weight: 500;
  min-width: 40px;
}

.count {
  color: #fff;
  font-weight: 500;
  min-width: 40px;
  text-align: right;
}
</style>
