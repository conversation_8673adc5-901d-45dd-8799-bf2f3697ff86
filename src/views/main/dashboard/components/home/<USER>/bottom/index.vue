<template>
    <Content :style="{ height: getRem(350) }" title="园区活动" type="inside" :tab="recentTab" current="left"
        showRight="showMore" @showMoreDialog="showMoreDialog">
        <ScreenTable :tableData="tableList" title="园区活动详情" />
    </Content>
    <el-dialog v-model="centerDialogVisible" title="" align-center class="screenTable padding0Dialog"
        custom-class="screenTable padding0Dialog" center>
        <Dialog :centerDialogVisible="centerDialogVisible" @changeInner="changeInner" :dialogData="dialogData"
            @getData="getDialogData" title="园区活动" />
        <!-- 详情 -->
        <el-dialog class="screenTable padding0Dialog" custom-class="screenTable padding0Dialog" v-model="innerVisible"
            width="30%" append-to-body>
            <DialogDetail :innerVisible="innerVisible" :detailData="detailData" :title="innerVisibleTitle" />
            <!-- @changeInner="changeInner"  -->
            <!--  -->
        </el-dialog>
    </el-dialog>
</template>
<script setup>
// import ScrollTable from '@/components/scrollTable/index.vue'
import ScreenTable from '@/components/screenTable/index.vue'
import Content from '../../../content/index.vue';
import { getRem, getVh } from '@/utils/rem.js'
import {  computed, onMounted, ref } from 'vue'
import { getHomeEven } from '@/api/dashboard'
import { useStore } from 'vuex'
import Dialog from '@/components/screenTable/dialog/index.vue'
import DialogDetail from '@/components/screenTable/dialog/detail.vue'

const centerDialogVisible = ref(false)
const innerVisible = ref(false)
const store = useStore()
const elementSize = computed(() => store.state.dashboard.secondViewData?.outputTop10)
const tableList = ref()
const dialogData = ref()
const innerVisibleTitle = ref()
const detailData = ref()
onMounted(() => {
    getList()
})
const getList = () => {
    let data = {
        "pageSize": 3,
        "pageNum": 1,
        conditions: [],
        id:'5',
    }
    getHomeEven(data).then((res) => {
        tableList.value = res.data.elements

    })
}
const changeInner = (data) => {
    innerVisible.value = true
    innerVisibleTitle.value = data.title
    detailData.value = data
}
const showMoreDialog = () => {

    getDialogData({
        "pageSize": 12,
        "pageNum": 1,
        conditions: [],
         id:'5',
    }, true)
}
const getDialogData = (pageData, showTrue) => {
    let data = pageData ? pageData : {
        "pageSize": 12,
        "pageNum": 1,
         id:'5',
        conditions: []
    }
    getHomeEven(data).then((res) => {
        if (showTrue) {
            centerDialogVisible.value = true
        }
        dialogData.value = res.data
    })
}
</script>
<style scoped></style>