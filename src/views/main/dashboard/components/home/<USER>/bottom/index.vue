<template>
  <Content :style="{ height: getRem(235) }" title="园区载体配套" type="inside" :tab="1" current="center">
    <div class="container">
      <div class="left">
        <div class="iconBox">
          <div v-for="(item, index) in houseSupports" :key="index" class="itemBox">
            <div :class="item.name == '餐饮服务' ? 'icon1' : (item.name == '产教融合基地' ? 'icon2' : 'icon3')"></div>
            <div class="numBox">
              <div class="num">{{ item.num }}</div><span class="unit">家</span>
            </div>
            <div class="name">{{ item.name }}</div>
          </div>
        </div>
        <div class="details">
          <div v-for="(data) in houseSupportsDetail" :key="data.name">
            <div class="imgs" :style="{ backgroundImage: `url(${data?.images?.[0]})` }"></div>
            <div class="text"> {{ data.name }}</div>
          </div>
        </div>
      </div>
      <div class="middle"><el-divider direction="vertical" /></div>


      <div class="right">
        <div class="buttons">

          <div class="xuanfu" v-for="(item, index) in groupSupports" :key="index"
            :class="{ active: index === groupActive }" @click="changeGroup(item, index)">{{ item.name }}({{ item.num }})
          </div>

        </div>
        <div class="details">

          <div class="tips" v-for="(data) in groupSupportsDetail" :key="data">{{ data }}</div>


        </div>
      </div>
    </div>
  </Content>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, ref, computed, watch } from 'vue'
import Content from '../../../content/index.vue';
import { getRem, getVh } from '@/utils/rem.js';
import { useStore } from 'vuex'
import { getSupporting } from '@/api/dashboard'

const store = useStore()
const investNum = computed(() => store.state.dashboard.secondViewData.investNum)
const investEnterprises = computed(() => store.state.dashboard.secondViewData.investEnterprises)
// watch(()=>store.state.dashboard.secondViewData.enterpriseTypeStatistics,(newVal)=>{
//     onChange(newVal[0])
// },{deep:true})
// const activeKey = ref('其他')
const groupSupports = ref()
const houseSupports = ref()
const groupSupportsDetail = ref()
const houseSupportsDetail = ref()
const houseActive = ref(0)
const groupActive = ref(0)
onMounted(() => {
  getDetail()
})
const getDetail = () => {
  getSupporting().then((result) => {
    groupSupports.value = result.data.groupSupports
    houseSupports.value = result.data.houseSupports
    changeGroup(groupSupports.value[0], 0)
    changeHouse(houseSupports.value[0], 0)
  }).catch((err) => {
  });
  // getParkStyle({
  //   "businessCode": "park_intro",
  //   "conditions": []
  //   }).then((result) => {
  //     parkIntro.value = result.data.elements
  // }).catch((err) => {
  // });
}
const changeGroup = (data, index) => {
  groupSupportsDetail.value = data.groups
  groupActive.value = index

}
const changeHouse = (data, index) => {

  houseSupportsDetail.value = data.houses
  houseActive.value = index
}

const marginLeft = ref(0)
const listRef = ref()
const buttonRef = ref()
// 引入options配置
// import {defineProps,onMounted} from 'vue'
// const props = defineProps(['viewData']);
// const changeRight = () => {
//   // marginLeft的值不能大于0
//   let num = marginLeft.value === 0 || marginLeft.value > 0
//   if (!!num) return
//   marginLeft.value = marginLeft.value + 300
//   // console.log((marginLeft.value), 222)

// }
// const changeLeft = () => {
//   // 数据的总长-margin的值<父级的长度就停止
//   if (listRef.value.clientWidth + marginLeft.value < buttonRef.value.clientWidth) return
//   marginLeft.value = marginLeft.value - 300
//   // console.log((listRef.value.clientWidth + marginLeft.value < buttonRef.value.clientWidth), 111)

// }
// getSupporting
</script>

<style lang="scss" scoped>
.iconBox {
  width: 100%;
  height: 100%;
  display: flex;
  opacity: 1;
  padding-top: 120px;
  box-sizing: border-box;

  .itemBox {
    width: 33%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .name {
    color: #FFFFFF;
    text-shadow: 0px 0px 2.69px rgba(30, 198, 255, 0.8);
    font-size: 14px;
    font-weight: 500;
  }

  .numBox {
    display: flex;
    align-items: center;
    margin-top: 12px;
    margin-bottom: 12px;

    .unit {
      color: #C5C5C5;
      font-size: 12px;
      font-weight: 500;
      margin-left: 6px;
    }

    .num {
      font-size: 20px;
      font-weight: normal;
      background: linear-gradient(180deg, #FFFFFF 25%, #9BE5FF 57%, #0DCAF5 73%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      font-family: YouSheBiaoTiHei;
      text-fill-color: transparent;

    }
  }


  .icon1 {
    width: 95px;
    height: 63px;
    background-image: url('@/assets/images/mien/icon1.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .icon2 {
    width: 95px;
    height: 63px;
    background-image: url('@/assets/images/mien/icon2.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .icon3 {
    width: 95px;
    height: 63px;
    background-image: url('@/assets/images/mien/icon3.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
}

::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 5px;
  /* 对应横向滚动条的宽度 */
  height: 0px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
  background-color: #324156;
  border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  display: none;
}

.chart {
  width: 100%;
  box-sizing: border-box;
}


.container {
  width: 100%;
  height: 235px;
  display: flex;
  flex-wrap: nowrap;
  align-items: center;

  .left {
    // border-right: 1px solid rgba(44, 112, 191, );
  }

  .middle {
    height: 80%;

    .el-divider--vertical {
      height: 100%;
      background: #FFFFFF;
      opacity: 0.1;
    }

  }

  .left,
  .right {
    padding: 15px 30px;
    width: 49%;
    // display: flex;
    // flex-wrap: wrap;
    // justify-content: center;
    // align-content: space-around;


    .buttons {
      flex-wrap: wrap;
      cursor: pointer;
      justify-content: center;
      align-content: space-around;
      justify-content: flex-start;
      width: 100%;
      display: flex;
      margin-bottom: 25px;

      div {
        width: 100px;
        height: 34px;
        // background: rgba(12,39,83,0.5);
        // border: 1px solid rgba(54,149,255,.5);
        // border-radius: 4px;
        font-size: 14px;
        font-weight: 400;
        line-height: 34px;
        color: rgba(255, 255, 255, .5);

        &:first-child {
          margin-right: 10px;
        }

        &:hover {
          background: rgba(12, 39, 83);
          // box-shadow: 0px 0px 5PX -2px rgb(253 251 251) inset;

        }

        &.active {

          // box-shadow: 0px 0px 5PX -2px rgb(253 251 251) inset;
          color: white;

        }
      }

    }
  }

  .details {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding-right: 30px;
    height: 150px;
    flex-wrap: wrap;
    overflow-y: scroll;

    // padding-bottom: 15px;
    // justify-content:flex-start;
    div:first-child {
      // margin-right: 45px;
    }

    .imgs {
      width: 152px;
      height: 107px;
      background: center / contain no-repeat;
    }

    .text {
      margin-top: 12px;
      margin-bottom: 25px;
      width: 153px;
      height: 29px;
      line-height: 29px;
      color: white;
      // background: rgba(64,131,239,0.1);
      // border: 1px solid #70AEFF;
      background-position: center;
      background-image: url('@/assets/images/view/home/<USER>');

      background-size: 100% 100%;

    }

  }

  .right {
    padding: 15px 30px 15px 0;
    width: 50%;

    .buttons {
      padding-left: 30px;
      cursor: pointer;
      margin-bottom: 0;
    }

    .details {
      margin-top: 15px;
    }
  }

  .tips {
    width: 415px;
    height: 120px;
    margin-left: -30px;
    margin-top: -30px;
    // background: url(@/assets/images/view/);
    background: 15px 0px/31% 109% no-repeat url("/src/assets/images/view/home/<USER>"), 100px 40px/contain no-repeat url("/src/assets/images/view/home/<USER>");
    display: flex;
    align-items: center;
    /* justify-content: center; */
    margin-top: -1.88rem;
    padding-left: 125px;
    padding-top: 6px;
    color: #fff;
    font-size: 14px;
    font-family: YouSheBiaoTiHei;
  }


  // .tables {
  //   display: flex;
  //   justify-content: space-between;
  //   align-items: center;
  //   height: 100%;
  //   padding: 5px 5px 10px 5px;

  //   .leftBtn,
  //   .rightBtn {
  //     width: 9px;
  //     height: 10px;
  //     cursor: pointer;
  //     background-size: 40% 40%;
  //     background-repeat: no-repeat;
  //     background-position: center;
  //     padding: 20px;
  //     // opacity: 0.7;
  //   }

  //   .leftBtn {
  //     background-image: url('@/assets/images/view/honer-left.png');

  //   }

  //   .rightBtn {
  //     background-image: url('@/assets/images/view/honer-right.png');
  //   }

  //   .center {
  //     // margin: 5px 0 0 0;
  //     display: flex;
  //     flex-wrap: nowrap;
  //     justify-content: flex-start;
  //     width: 90%;
  //     overflow: hidden;
  //     height: 200px;

  //     .centerIn {
  //       height: 200px;
  //       display: flex;
  //       flex-wrap: nowrap;
  //       justify-content: flex-start;

  //       .centerInList {
  //         width: 300px;
  //         height: 200px;
  //         background: center / contain no-repeat url('@/assets/images/view/tab2/bg5.png');
  //         padding: 15px 23px;
  //         // font-size: 14px;
  //         // margin-right: 5px;
  //         // line-height: 34px;
  //         // color: #FFFFFF;
  //         // background: rgba(12, 34, 63, 0.7);
  //         // border: 1px solid rgba(40, 92, 151, 0.7);

  //         // &.active {
  //         //     background: rgba(12, 39, 83, 0.7);
  //         //     border: 1px solid rgba(54, 149, 255, 0.7);
  //         //     box-shadow: 0px 0px 5PX -2px rgb(253 251 251) inset;
  //         // }
  //         .title {
  //           font-size: 16px;
  //           font-weight: 500;
  //           color: #FFFFFF;
  //           text-align: left;

  //         }

  //         .titleDes {
  //           font-size: 12px;
  //           color: #B2C3DF;
  //           text-align: left;
  //         }

  //         .titleImg {
  //           width: 197px;
  //           height: 100px;
  //           background: center / contain no-repeat;
  //           // url('@/assets/images/view/tab2/bg1.png');

  //         }
  //       }
  //     }
  //   }
  // }
}
</style>
