::-webkit-scrollbar {
    /* 对应纵向滚动条的宽度 */
    width: 0px !important; 
    /* 对应横向滚动条的宽度 */
    height: 0px !important;
}
.Dialog {
    color: white;
    padding: 0 50px;
    width: 830px;
    margin: 0 auto;

    .top {
        width: 100%;
        height: 46px;
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
        display: flex;
        background: left / contain no-repeat url('../../../../../../assets/images/view/dialog/icon1.png');

        .topLeft {
            width: 100%;
            display: flex;
        }

        .topRight {
            display: flex;
            font-size: 16px;
            margin-top: 12px;
            color: #F6F9FE;
            text-shadow: 2px 3px 0px rgba(22, 22, 19, 0.27);
            width: 100%;
            justify-content: space-around;
            align-items: center;

            span {
                color: #588ae4
            }
        }

        .topTitleLeft {
            padding-left: 40px;
            font-size: 26px;
            // font-family: Alibaba PuHuiTi;
            font-weight: bold;
            font-style: italic;
            color: #F6F9FE;
            // text-shadow: 2px 3px 0px rgba(22,22,19,0.27);

            // background: linear-gradient(0deg, rgba(208, 210, 170, 0.48) 0%, rgba(255, 255, 255, 0.48) 100%);
            // -webkit-background-clip: text;
            // -webkit-text-fill-color: transparent;
        }

        .topTitleFloor {

            padding-left: 17px;
            font-size: 26px;
            font-family: Alibaba PuHuiTi;
            font-weight: bold;
            font-style: italic;
            color: #F6F9FE;

            // text-shadow: 2px 3px 0px rgba(22, 22, 19, 0.27);

            // background: linear-gradient(0deg, rgba(208, 210, 170, 0.48) 0%, rgba(255, 255, 255, 0.48) 100%);
            // -webkit-background-clip: text;
            // -webkit-text-fill-color: transparent;
        }
    }

    .middle {
        margin: 46px 0 20px 0;
        width: 100%;
        height: 103px;
        display: flex;
       // flex-wrap: nowrap;
       justify-content:space-evenly;
        //background: linear-gradient(90deg, rgb(0, 154, 242, .1) 0%, rgba(201, 165, 255, 0.12) 88%);
        background: linear-gradient(90deg, rgba(10, 135, 211,0.1) 0%, rgba(201,165,255,0.01) 88%);
        .middleItem {
            width: 22%;
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            padding: 24px 15px;
            align-content: space-between;

            .middleText {
                width: 100%;
                height: 0.5rem;
                font-size: 0.88rem;
                font-weight: 600;
                display: flex;
                color: #FFFFFF;
                padding: 0px 8px;
                padding-bottom: 3px;
                align-items: center;
                background: left / contain no-repeat url('../../../../../../assets/images/view/dialog/title2.png');

            }

            .middleNum {
                width: 100%;
                font-size: 26px;
                font-weight: bold;
                color: #3DFFC6;
                text-align: left;
                padding-left: 10px;
            }

        }
    }

    .bottom {
        .bottomTitle {
            width: 100%;
            height: 29px;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            color: #FFFFFF;
            padding: 0px 24px;
            // padding-bottom: 3px;
            align-items: center;
            background: left / contain no-repeat url('../../../../../../assets/images/view/dialog/title1.png');

            .text {

                font-weight: 500;
                font-style: italic;
                color: #D1D6DF;
                background: linear-gradient(0deg, #ACDDFF 0%, #FFFFFF 100%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparen
            }
        }

        .top {
            width: 100%;

            height: 30px;
        }

        .floorDetail {
            display: flex;

        }

        .bottomLeft {
            width: 386px;
            height: 400px;
            display: flex;
            flex-wrap: wrap;
            margin-top: 10px;

            .floorMiddle {
                margin: 18px 0;
                width: 386px;
                display: flex;
                flex-wrap: wrap;
                height: 432px;
                overflow: hidden;
                // background-color: rebeccapurple;
                z-index: 111;

                .layer {
                    width: 100%;
                    display: flex;
                    justify-content: space-between;
                }

                .noData.layer {
                    .layerIn {
                        background: #0C223F  !important;
                        box-shadow: 0 0 0px 0px  !important;
                        border: 1px solid #285C97  !important;
                    }
                }

                .empty.layer,
                .industry.layer {
                    height: 30px;
                    position: relative;
                    margin-bottom: 18px;

                    // padding: 2px 5px;
                    &.active {

                        .layerIn {
                            border: 0 !important;
                            background: #005168 !important;
                            box-shadow: 0 0 3px 1px #0BE8F6 inset !important;  
                            color: #02F2FF;

                            span {
                                color: #02F2FF;
                            }
                        }
                    }

                    .layerIn {
                        width: 346px;
                        height: 30px;
                        background: #0C223F;
                        border: 1px solid rgba(54, 149, 255, .7);
                        border-radius: 4PX;
                        font-size: 16px;
                        color: #FFFFFF;
                        text-align: center;

                        line-height: 30.5px;

                        cursor: pointer;

                        span {
                            display: inline-block;
                            width: 100px;
                            height: 22px;
                            overflow-x: scroll;
                            //background: rgba(111, 179, 255, .1);
                            //border-radius: 50%;
                            white-space: nowrap;
                            font-size: 16px;
                            color: #FFFFFF;
                            text-align: center;
                            line-height: 22px;
                            position: absolute;
                            left: 10px;
                            top: 3px;
                        }

                        box-shadow: 0 0 3px 1px rgba(54, 149, 255, .7) inset;


                    }

                }


            }

            .floorUp,
            .floorUpDown {
                width: 10px;
                height: 9px;
                margin: 0 auto;
                cursor: pointer;
            }

            .floorUp.showBg {
                background: left / contain no-repeat url('../../../../../../assets/images/view/dialog/up.png');
            }

            .floorUpDown.showBg {
                background: left / contain no-repeat url('../../../../../../assets/images/view/dialog/down.png');
            }
        }

        .bottomRight {
            // margin-left: -1px;
            position: relative;
            margin: 25px 0px 25px -10px;
            // width: 338px;
            height: 400px;

            .content {
                float: right;
                width: 318px;
                height: 432px;
                overflow-y: scroll;
                border: 1px solid rgba(40, 92, 151, 0.7);
                background: rgba(12, 34, 63, 0.7);
                display: flex;
                justify-content: space-between;
                color: white;
                flex-wrap: wrap;
                font-size: 14px;
                color: #FFFFFF;
                align-items: center;
                align-content: flex-start;
                padding: 20px 30px 20px 30px;
                border-radius: 4px;


                .full {
                    // height: 28px;
                    width: 100%;
                    line-height: 28px;
                    flex-wrap: nowrap;
                }

                .full {
                    span {
                        font-size: 14px;
                        color: #C4DCFF;
                        line-height: 26px;
                        opacity: 0.8;
                        padding-left: 5px;
                    }

                }

                .images {
                    width: 100%;
                    height: 200px;
                    background-size: 100%;
                    background-repeat: no-repeat;
                    background-position: center;
                }
            }
            .contents {
                float: right;
                width: 318px;
                height: 432px;
                overflow-y: scroll;
                border: 1px solid rgba(40, 92, 151, 0.7);
                background: rgba(12, 34, 63, 0.7);
                display: flex;
                justify-content: space-between;
                color: white;
                flex-wrap: wrap;
                font-size: 14px;
                color: #FFFFFF;
                //align-items: center;
                align-content: flex-start;
                padding: 20px 30px 20px 30px;
                border-radius: 4px;


                .full {
                    // height: 28px;
                    width: 50%;
                    line-height: 28px;
                    flex-wrap: nowrap;
                    display: flex;
                    flex-direction: column;
                }
                .full2 {
                    // height: 28px;
                    width: 100%;
                    line-height: 28px;
                    flex-wrap: nowrap;
                    display: flex;
                    flex-direction: column;
                }

                .full2 {
                    span {
                        font-size: 14px;
                        color: #C4DCFF;
                        line-height: 26px;
                        opacity: 0.8;
                        padding-left: 5px;
                    }

                }
                .full {
                    span {
                        font-size: 14px;
                        color: #C4DCFF;
                        line-height: 26px;
                        opacity: 0.8;
                        padding-left: 5px;
                    }

                }

                .images {
                    width: 100%;
                    height: 200px;
                    background-size: 100%;
                    background-repeat: no-repeat;
                    background-position: center;
                }
            }


        }
    }

    .layerInText {
        width: 230px;
        overflow-x: scroll;
        margin-left: 110px;
        white-space: nowrap;
        padding: 0 10px 0 0;
    }

    .floorList {
        width: 100%;
        height: 100%;
        //padding-bottom: 30px;
        overflow: auto;
    }

    .left {
        width: 20px;
        height: 20px;
        background: #0c2142;
        border-left: 1px solid rgba(40, 92, 151, 0.7);
        border-bottom: 1px solid rgba(40, 92, 151, 0.7);
        transform: rotate(45deg);
        margin-top: 7px;
    }
}
::-webkit-scrollbar {
    /* 对应纵向滚动条的宽度 */
    width: 5px;
    /* 对应横向滚动条的宽度 */
    height: 0px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
   background-color: #3241563c;
    border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
    display: none;
}