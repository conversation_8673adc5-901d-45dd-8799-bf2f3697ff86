<template>
  <div class="Dialog">
    <!-- 上 -->
    <div class="top">
      <div class="topLeft">
        <div class="topTitleLeft">楼栋信息</div>
        <div class="topTitleFloor">{{ buildings?.name }}</div>
      </div>
      <div class="topRight">
        <div>
          ·层数 <span>{{ buildings?.floor }}</span>
        </div>
        <div>
          ·楼栋面积 <span>{{ buildings?.total_area }}</span>
        </div>
        <div>
          ·类型 <span>{{ buildings?.type }}</span>
        </div>
        <div>
          ·出租状态
          <span>{{ buildings?.isFull === "0" ? "未租满" : "已租满" }}</span>
        </div>
      </div>
    </div>
    <!-- 中 -->
    <div class="middle">
      <div class="middleItem">
        <div class="middleText">入驻企业</div>
        <div class="middleNum">{{ floorData?.settledNum || 0 }}</div>
      </div>
      <div class="middleItem">
        <div class="middleText">空置房源</div>
        <div class="middleNum">{{ floorData?.vacancy || 0 }}</div>
      </div>
      <div class="middleItem">
        <div class="middleText">空置总面积</div>
        <div class="middleNum">{{ floorData?.totalVacantArea || 0 }}</div>
      </div>

      <!-- <div class="middleItem" v-if="store.state.user.token&&(loginState==='admin'||loginState==='zhengwu')">
        <div class="middleText">本月用水</div>
        <div class="middleNum">{{ floorData?.waterMonthly || 0 }}</div>
      </div>
      <div class="middleItem" v-if="tokenValue&&(loginState==='admin'||loginState==='zhengwu')"> 
        <div class="middleText">本月用电 </div>
        <div class="middleNum">{{ floorData?.electronicMonthly || 0 }}</div>
      </div> -->
    </div>
    <!-- 下 -->

    <div class="bottom">
      <!-- 标题 -->
      <div class="bottomTitle">
        <div class="text">楼层信息</div>
      </div>
      <!-- 内容 -->
      <div class="floorDetail">
        <!-- left -->
        <div class="bottomLeft">
<!--           <div
            :class="{ showBg: floorsList.value?.length > 9 }"
            class="floorUp"
            @click="down"
          ></div> -->
          <div class="floorMiddle" ref="floorMiddleRef">
            <div
              class="floorList"
              :style="{ marginTop: `${marginTop}px` }"
              ref="floorListRef"
            >
              <div
                v-for="(floor, index) in floorsList.value"
                :key="index"
                class="layer"
                :class="{
                  industry: !!floor.enterprise,
                  empty: !floor.enterprise,
                  active: activeKey === floor.id,
                  noData:floor.enterprise==='*****************',
                }"
                @click="changeActive(floor, index)"
                ref="floorItemRef"
              >
                <div class="layerIn">
                  <span>F{{ floor.floorNum }}-{{  floor.houseName }}</span>
                  <div class="layerInText">
                    {{ showText(floor) }}
                  </div>
                </div>
                <div v-if=" activeKey === floor.id" class="left"
              ></div>
              </div>
              <!--  :style="{ top: `${rowHeight}px` }" -->
           
            </div>
          </div>
<!--           <div
            :class="{ showBg: floorsList.value?.length > 9 }"
            class="floorUpDown"
            @click="up"
          ></div> -->
        </div>
        <!-- right -->
        <div class="bottomRight">
          <!-- 楼层信息'2' 已租'3'业信息''1' -->
          <div class="contents" v-if="showIndustry === '2'">
            <div class="full">
              ·楼层：<span>{{ floorDetail.floors }}</span>
            </div>
            <div class="full">
              ·房源租金: <span>{{ floorDetail.rent || 0 }}/m2</span>
            </div>
            <div class="full">
              ·房源面积:<span>{{ floorDetail.areas }}m2</span>
            </div>
          <!--   <div class="full">
              ·可租日期:
              <span>
                {{  floorDetail.rentable_date&& dayjs(Number(floorDetail.rentable_date)).format("YYYY-MM-DD") }}
              </span>
            </div> -->
            <div class="full">
              ·起租年限:<span>{{ floorDetail.min_lease }}年</span>
            </div>
            <div class="full">
              ·水费单价: <span>{{ floorDetail.water_fee }}元/t</span>
            </div>
            <div class="full">
              ·电费单价:<span>{{ floorDetail.electric_fee }}元/h</span>
            </div>
            <div class="full">
              ·产权年限: <span>{{ floorDetail.ownership_period }}年</span>
            </div>
            <div class="full">
              ·建筑日期:<span>
              {{  floorDetail.construct_date&& dayjs(Number(floorDetail.construct_date)).format("YYYY-MM-DD") }}
              </span>
            </div>
            <div class="full">
              ·层高:<span>{{ floorDetail.floor_height }}m</span>
            </div>
            <div class="full">
              ·承重:<span>{{ floorDetail.bearing }}kn/m2</span>
            </div>
            <div class="full2">
              ·意向招租企业类型：<span>{{ floorDetail.enterprise_type }}</span>
            </div>
            <div class="full">·房源描述：</div>
            <div class="full2">
              <span>{{ floorDetail.desc }}</span>
            </div>
            <div
              class="images"
              v-for="(img, index) in images"
              :key="index"
              :style="
                 {
                  backgroundImage: `url(${img}) `
                }
              "
            >
    
          </div>
          </div>
          <div class="content" v-if="showIndustry === '3'">
            已租
            <!-- 成立日期：2019-10-21<br />
            注册资本：5158 万人民币<br />
            法定代表人：赵云<br />
            企业资质：科技型企业、国家级科技型中小企业<br />
            产业类型：监测检测<br />
            主营业务：水处理工程 -->
          </div>
         
          <div class="content" v-if="showIndustry === '1'  ">
            <div v-if=" recentClick.enterprise==='*****************'">已租
            </div>
<div v-else>


            <div class="full">
              ·企业名称：<span> {{ industryData?.name }}</span>
            </div>
            <div class="full">
              ·企业信用代码： <span>{{ industryData?.uni_code }} </span>
            </div>
            <div class="full">
              <!-- ['','新兴企业','规上企业','其他'] -->
              ·企业类型：<span
                v-for="item in industryData?.enterprise_type"
                :key="item"
                >{{ item }}</span
              >
            </div>
            <div class="full">
              ·行业分类：
              <span>
                {{
                  [
                    "其他",
                    "设计",
                    "咨询",
                    "监测检测",
                    "新能源",
                    "新材料",
                    "车路协同",
                    "智能网联",
                    "软件开发应用",
                  ][industryData?.industry_type]
                }}
              </span>
            </div>
            <div class="full">
              ·成立日期： <span>{{ industryData?.establish_date&&dayjs(Number(industryData?.establish_date) ).format("YYYY-MM-DD")  }} </span>
            </div>
            <div class="full">
              ·入驻日期： <span> {{ industryData?.settle_date&&dayjs( Number(industryData?.settle_date)).format("YYYY-MM-DD") }}  </span>
            </div>
            <div class="full">
              ·联系人： <span>{{ industryData?.contacts }} </span>
            </div>
            <div class="full">
              ·联系方式： <span> {{ industryData?.contacts_way }} </span>
            </div>
            <div class="full">
              ·注册资金(万元) ：
              <span> {{ industryData?.registered_capital }} </span>
            </div>
            <div class="full">
              ·资质荣誉： <span> {{ industryData?.honors }} </span>
            </div>
            <div class="full">
              ·企业简介： <span> {{ industryData?.desc }} </span>
            </div>
          </div>
        </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import {
  onMounted,
  defineProps,
  watch,
  reactive,
  onBeforeUnmount,
  ref,
  computed,
  onUnmounted,
  onDeactivated,
} from "vue";
import {
  getSecondVieData,
  getFloorData,
  getBuilding,
  getEnterpriseData,
} from "@/api/dashboard";
import { useStore } from "vuex";
import _, { filter, findIndex } from "lodash";
import dayjs from "dayjs";
import { token} from '@/utils/utils'

let tokenValue=ref('')
let loginState=ref(localStorage.getItem('userState'))
const props = defineProps(["activeData", "centerDialogVisible"]);
const floorsList = reactive([]);
const floorData = ref({});
let marginTop = ref(0);
let floorListRef = ref(null);
let floorMiddleRef = ref(null);
let activeKey = ref(0);
let floorItemRef = ref(null);
let rowHeight = ref(10);
let floorDetail = ref({});
const recentClick=ref()
let images = ref([]);
let buildings = ref({});
//      楼层信息'2' 已租'3'业信息''1'
const showIndustry = ref("0");
const industryData = ref();
const store = useStore();
const showText = (floor) => {
  // <!-- {{}} -->
let userState=localStorage.getItem('userState')
  if (token()) {

    // let data =''
    // if( userState==='admin'){
    //   if(floor.status === "0"){
    //   data =   floor.rentStatus === "0"? "空置":floor.enterprise ?floor.enterprise :'已租'
    // }else if(floor.status === "0"){
    //   data = '不可租'
    // }
    if( userState==='admin'|| userState==='zhengwu'){
      let data  =''
      if(floor.enterprise && token() &&  floor.rentStatus==1){
        data =floor.enterprise
      }else if(floor.rentStatus==1 && !floor.enterprise){
        data ='已租'
      }else{
        data ='空置'
      }
      return data;
    }else{
      let data = floor.rentStatus==1 &&  token() ? "已租" :"空置"
      return data; 
    }
  } else {
    let data = floor.rentStatus==1  ? "已租" : "空置"
    return data;
  }
};

onMounted(() => {
  tokenValue.value= token()
  getData();
  getBuildings();
});

watch(
  () => props.centerDialogVisible,
  (newVal) => {
    if (newVal) {
      // console.log("props", props.activeData);
      marginTop.value = 0;
      activeKey.value = 0;
      getData();
      getBuildings();
    } else {
      floorDetail.value = {};
    }
  },
  { deep: true }
);
const getBuildings = () => {
  let data = props.activeData.id;
  getBuilding({
    businessCode: "building",
    conditions: [
      {
        key: "id",
        value: data,
      },
    ],
  }).then((res) => {
    // console.log(res.data)
    buildings.value = res.data;
  });
};
const getData = () => {
  let data = props.activeData.id;
  getSecondVieData({ id: data }).then((res) => {
    let { floors, ...prop } = res.data;
    floorsList.value = floors.reverse();
    let empty = filter(floorsList.value, (item) => !item.enterprise);
    let index = findIndex(
      floorsList.value,
      (e) => e.floorNum === empty?.[0]?.floorNum
    );
    floorData.value = prop;
    // console.log(floorData.value,'floorData')
    if (empty.length > 0) {
      changeActive(empty[0], index);
    } else {
      changeActive(floorsList.value[0], 0);
    }
  });
};
const down = () => {
  let num = marginTop.value === 0 || marginTop.value > 0;
  if (!!num) return;
  marginTop.value = marginTop.value + 48;
};
const up = () => {
  if (
    -marginTop.value + 40 >=
    floorListRef.value.clientHeight - floorMiddleRef.value.clientHeight
  )
    return;
  marginTop.value = marginTop.value - 48;
};
const changeActive = (floor, index) => {
    recentClick.value=floor
  if (token()) {
    // 已登录
    if (floor.enterprise && floor.enterprise !== "") {
      activeKey.value = floor?.id;
      showIndustry.value = "1";
      if(floor.enterprise!=='*****************'){
           // 已租。有企业名称
 
      // 查企业信息
        getEnterprise(floor);
      
      } 
   
    
     
  
    } else {
      // 空置
      // 查 楼层信息
      activeKey.value = floor?.id;
      showIndustry.value = "2";

      getFoolData(floor, index);
    }
  } else {
    // 未登录
    if (floor.enterprise && floor.enterprise !== "") {
      // 已租。有企业名称
      activeKey.value = floor?.id;
      //  设置为 已租
      showIndustry.value = "3";
    } else {
      // 空置
      // 查 楼层信息
      activeKey.value = floor?.id;
      getFoolData(floor, index);
      showIndustry.value = "2";
    
  } }
};
const getFoolData = (floor, index) => {
  // offsetHeight
  let height = 48 * index - marginTop.value;
  rowHeight.value = height === 0 ? 10 : height;
  // console.log('offsetHeight', height)
  getFloorData({
    businessCode: "houses",
    conditions: [
      {
        key: "id",
        value: floor?.id,
      },
    ],
  }).then((res) => {
    floorDetail.value = res.data;
    images.value = res.data?.images?.split(",");
    // console.log(images.value)
  });
};
const getEnterprise = (floor) => {
  let data = {
    businessCode: "enterprise",
    conditions: [
      {
        key: "id",
        value: floor.enterpriseId,
      },
    ],
  };
  getEnterpriseData(data).then((res) => {
    // console.log(res);
    industryData.value = res.data;
  });
};
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
