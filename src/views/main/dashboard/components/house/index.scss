
.center {
    padding-top: 3%;
    margin: 0 9%;
    position: relative;
    width: 1450px;
    height: 775px !important;
    // background: center / contain no-repeat url('../../../assets/images/view/country.png');
    background: center / contain no-repeat url('../../../../../assets/images/view/country.png');
    .house {
      margin-left: 18%;
      position: relative;
      /* padding-top: 20px; */
      width: 75%;
      height: 80%;
      //background #8439ce45;
    }
  }

  .house {
    .brickhouse22 {
      position: absolute;
      display: block;
      //background #da0a0a;  
      width: 50px;
      height: 50px;
  
      .wall-one {
        position: absolute;
        display: block;
        //background rgba(208, 59, 59, 0.3294117647);
        width: 135px;
        height: 147px;
        transform: skew(61deg, 11deg);
        top: -62px;
        border-top-left-radius: 86px;
        border-bottom-right-radius: 49px;
        left: -91px;
      }
  
      .wall-two {
        position: absolute;
        display: block;
        //background red;
        width: 228px;
        height: 157px;
        transform: skew(-4deg, 28deg);
        top: -131px;
        left: -76px;
      }
  
      .roof {
        position: absolute;
        display: block;
        //background rgb(117 41 41 / 67%);
        width: 82px;
        height: 138px;
        top: -176px;
        left: -150px;
        border-bottom-left-radius: 13px;
        border-top-left-radius: 21px;
        border-top-right-radius: 10px;
        transform: skew(-9deg, -16deg);
      }
    }
  
    .brickhouse1,
    .brickhouse2,
    .brickhouse3,
    .brickhouse4,
    .brickhouse5,
    .brickhouse6,
    .brickhouse7,
    .brickhouse24,
    .brickhouse23 {
  
      position: absolute;
      display: block;
      //background #160404;
      // width: 54px;
      // height: 78px;
  
      .wall-one {
        //background #ff000073;
        position: absolute;
        display: block;
        width: 64px;
        height: 39px;
        transform: skew(58deg, -8deg);
        top: 36px;
        border-top-left-radius: 21px;
        left: -39px;
      }
  
      .wall-two {
        //background #ffff006b;
        position: absolute;
        display: block;
        width: 65px;
        height: 83px;
        transform: skew(-5deg, 32deg);
        border-top-left-radius: 20px;
        top: -31px;
        left: -4px;
      }
  
      .roof {
        //background rgb(0 0 0 / 40%);
        position: absolute;
        display: block;
        width: 50px;
        height: 71px;
        top: -32px;
        left: -50px;
        border-bottom-left-radius: 10px;
        border-top-left-radius: 18px;
        transform: skew(-4deg, -19deg);
      }
    }
  
    .brickhouse8,
    .brickhouse9,
    .brickhouse10,
    .brickhouse11,
    .brickhouse12,
    .brickhouse13,
    .brickhouse14,
    .brickhouse15,
    .brickhouse16,
    .brickhouse17,
    .brickhouse19 {
  
      position: absolute;
      display: block;
      //background #160404;
      // width: 54px;
      // height: 78px;
  
      .wall-one {
        //background rgba(255, 0, 0, 0.4509803922);
        position: absolute;
        display: block;
        width: 57px;
        height: 32px;
        transform: skew(60deg, -27deg);
        top: 59px;
        border-top-left-radius: 10px;
        left: -32px;
      }
  
      .wall-two {
        //background rgba(255, 255, 0, 0.4196078431);
        position: absolute;
        display: block;
        width: 55px;
        height: 107px;
        transform: skew(-5deg, 30deg);
        border-top-left-radius: 5px;
        top: -45px;
        left: 3px;
      }
  
      .roof {
        //background rgba(0, 0, 0, 0.4);
        position: absolute;
        display: block;
        width: 50px;
        height: 107px;
        top: -47px;
        left: -44px;
        transform: skew(-6deg, -28deg);
      }
    }
  
    .brickhouse18 {
  
      position: absolute;
      display: block;
      //background #160404;
      // width: 54px;
      // height: 78px;
  
      .wall-one {
        //background rgba(255, 0, 0, 0.4509803922);
        position: absolute;
        display: block;
        width: 95px;
        height: 25px;
        transform: skew(61deg, -32deg);
        top: 76px;
        border-top-left-radius: 9px;
        left: -74px;
      }
  
      .wall-two {
        //background rgba(255, 255, 0, 0.4196078431);
        position: absolute;
        display: block;
        width: 40px;
        height: 81px;
        transform: skew(-5deg, 30deg);
        border-top-left-radius: 5px;
        top: -22px;
        left: 0px;
      }
  
      .roof {
        //background rgba(0, 0, 0, 0.4);
        position: absolute;
        display: block;
        width: 83px;
        height: 77px;
        top: -1px;
        left: -80px;
        transform: skew(-9deg, -35deg);
      }
    }
  
    .brickhouse20,
    .brickhouse21 {
  
      position: absolute;
      display: block;
      //background #160404;
      // width: 54px;
      // height: 78px;
  
      .wall-one {
        //background rgba(255, 0, 0, 0.4509803922);
        position: absolute;
        display: block;
        width: 100px;
        height: 36px;
        transform: skew(60deg, -34deg);
        top: 27px;
        border-top-left-radius: 10px;
        left: -76px;
      }
  
      .wall-two {
        //background rgba(255, 255, 0, 0.4196078431);
        position: absolute;
        display: block;
        width: 55px;
        height: 60px;
        transform: skew(-5deg, 30deg);
        border-top-left-radius: 5px;
        top: -45px;
        left: 3px;
      }
  
      .roof {
        //background rgba(0, 0, 0, 0.4);
        position: absolute;
        display: block;
        width: 99px;
        height: 50px;
        top: -20px;
        left: -95px;
        transform: skew(-3deg, -34deg);
      }
    }
  
    .brickhouse1 {
      top: 67%;
      left: 31%;
      transform: rotateZ(59deg);
      z-index: 100;
    }
  
    .brickhouse2 {
      transform: rotateZ(60deg) scale(0.8, 0.8);
      top: 58%;
      left: 26%;
      z-index: 95;
    }
  
    .brickhouse3 {
      transform: rotateZ(58deg) scale(0.8, 0.9);
      top: 44%;
      left: 16%;
      z-index: 94;
    }
  
    .brickhouse4 {
      top: 59.5%;
      left: 40.5%;
      transform: rotateZ(59deg);
      z-index: 93;
    }
  
    .brickhouse5 {
      transform: rotate(58deg) scale(0.9, 0.9);
      top: 52%;
      left: 34%;
      z-index: 92;
    }
  
    .brickhouse6 {
      transform: rotateZ(61deg) scale(0.8, 0.9);
      top: 45%;
      left: 29%;
      z-index: 91;
    }
  
    .brickhouse7 {
      transform: rotateZ(58deg) scale(0.8, 0.9);
      top: 39%;
      left: 25%;
      z-index: 90;
    }
  
  
    .brickhouse8 {
      top: 48%;
      left: 54.5%;
      transform: rotateZ(60deg) scale(1, 1);
      z-index: 89;
    }
  
    .brickhouse9 {
      top: 39%;
      left: 46.7%;
      transform: rotateZ(62deg) scale(0.9, 0.9);
      z-index: 88;
    }
  
    .brickhouse10 {
      top: 30%;
      left: 39%;
      transform: rotateZ(62deg) scale(0.85);
      z-index: 87;
    }
  
    .brickhouse11 {
      top: 39%;
      left: 65%;
      transform: rotateZ(61deg) scale(1, 1);
      z-index: 86;
    }
  
    .brickhouse12 {
      top: 32%;
      left: 57%;
      transform: rotateZ(61deg) scale(0.85, 0.86);
      z-index: 85;
    }
  
    .brickhouse13 {
      top: 23%;
      left: 49%;
      transform: rotateZ(62deg) scale(0.75, 0.83);
      z-index: 84;
    }
  
    .brickhouse14 {
      top: 32%;
      left: 74.5%;
      transform: rotateZ(61deg) scale(0.9, 0.8);
      z-index: 83;
    }
  
    .brickhouse15 {
      top: 26%;
      left: 66.5%;
      transform: rotateZ(61deg) scale(0.9, 0.8);
      z-index: 82;
    }
  
    .brickhouse16 {
      top: 18%;
      left: 58%;
      transform: rotateZ(61deg) scale(0.8, 0.8);
      z-index: 81;
    }
  
    .brickhouse17 {
      top: 23%;
      left: 86%;
      transform: rotateZ(60deg) scale(0.8, 0.8) skew(0deg, -1deg);
      z-index: 80;
    }
  
    .brickhouse18 {
      top: 17%;
      left: 81%;
      transform: rotateZ(60deg) scale(1, 1);
      z-index: 79;
    }
  
    .brickhouse19 {
      top: 9%;
      left: 69.5%;
      transform: rotateZ(60deg) scale(0.7, 0.8);
      z-index: 78;
    }
  
    .brickhouse20 {
      top: 19%;
      left: 92%;
      transform: rotateZ(60deg) scale(0.8, 0.8) skew(0deg, -1deg);
      z-index: 77;
    }
  
    .brickhouse21 {
      top: 8%;
      left: 78%;
      transform: rotateZ(60deg) scale(0.7, 0.7);
      z-index: 76;
    }
  
    .brickhouse22 {
      transform: rotateZ(61deg);
      z-index: 130;
      top: 76%;
      left: 72px;
  
    }
  
    .brickhouse23 {
      top: 68%;
      left: 9%;
      transform: rotateZ(75deg) skew(0deg, -7deg) scale(1.3, 0.7);
      z-index: 120;
    }
  
    .brickhouse24 {
      top: 53%;
      left: 8%;
      transform: rotateZ(75deg) skew(15deg, 1deg) scale(1.4, 0.8);
      z-index: 110;
    }
  
  
    .number1 {
      top: 58%;
      left: 26%;
      z-index: 100;
    }
  
    .number2 {
      top: 49%;
      left: 22%;
      z-index: 95;
    }
  
    .number3 {
      top: 35%;
      left: 11%;
      z-index: 94;
    }
  
    .number4 {
      top: 51%;
      left: 36%;
      z-index: 93;
    }
  
    .number5 {
      top: 44%;
      left: 30%;
      z-index: 92;
    }
  
    .number6 {
      top: 37%;
      left: 25%;
      z-index: 91;
    }
  
    .number7 {
      top: 31%;
      left: 21%;
      z-index: 90;
    }
  
  
    .number8 {
      top: 40%;
      left: 50%;
      z-index: 89;
    }
  
    .number9 {
      top: 31%;
      left: 42%;
      z-index: 88;
    }
  
    .number10 {
      top: 22%;
      left: 35%;
      z-index: 87;
    }
  
    .number11 {
      top: 31%;
      left: 61%;
      z-index: 86;
    }
  
    .number12 {
      top: 24%;
      left: 53%;
      z-index: 85;
    }
  
    .number13 {
      top: 15%;
      left: 45%;
      z-index: 84;
    }
  
    .number14 {
      top: 24%;
      left: 70%;
      z-index: 83;
    }
  
    .number15 {
      top: 16%;
      left: 62%;
      z-index: 82;
    }
  
    .number16 {
      top: 10%;
      left: 54%;
      z-index: 81;
    }
  
    .number17 {
      top: 15%;
      left: 82%;
      z-index: 80;
    }
  
    .number18 {
      top: 8%;
      left: 73%;
      z-index: 79;
    }
  
    .number19 {
      top: 1%;
      left: 65%;
      z-index: 78;
    }
  
    .number20 {
      top: 7%;
      left: 88%;
      z-index: 77;
    }
  
    .number21 {
      top: -3%;
      left: 74%;
      z-index: 76;
    }
  
    .number23 {
      top: 55%;
      left: 2%;
      z-index: 120;
    }
  
    .number24 {
      top: 39%;
      left: 3%;
      z-index: 110;
    }
  
    .number22 {
      top: 43%;
      left: 106px;
      z-index: 130;
    }
  }
  
  .number {
    width: 60px;
    height: 30px;
    position: absolute;
    padding: 2px 15px 1px 31px;
    display: block;
    color: white;
    font-weight: 500;
    font-size: 14px;
    // font-family: YouSheBiaoTiHei !important;
    // transform: skew(-9deg, -16deg);
    background: center / contain no-repeat url('../../../../../assets/images/view/toastBlue.png');
  
  
  }
  
  
  .house1,
  .house2,
  .house3,
  .house4,
  .house5,
  .house6,
  .house7,
  .house8,
  .house9,
  .house10,
  .house11,
  .house12,
  .house13,
  .house14,
  .house15,
  .house16,
  .house17,
  .house18,
  .house19,
  .house20,
  .house21,
  .house22,
  .house23,
  .house24 {
  .empty1{
      background: center / contain no-repeat 
      url('../../../../../assets/images/view/toastRed.png') !important;
    }
    &:hover {
      .number {
        transform: scale(1.2);
        transition: all .5s;
        background: center / contain no-repeat 
        url('../../../../../assets/images/view/toastYellow.png');
      }
    }
  }