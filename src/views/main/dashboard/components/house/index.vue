<template>
  <div class="center">
    <div class="house">

      <div v-for="box in viewData" :key="box" :class='"house" + box.index' @click="pickMe(box)">
        <div class="number" :class='`number${box.index}  empty${box.status}`'>
          {{ box.name }}
        </div>
        <div :class='"brickhouse" + box.index'>
          <div class='wall-one'> </div>
          <div class='wall-two'>
          </div>
          <div class='roof'>

          </div>
        </div>
      </div>
    </div>
    <el-dialog v-model="centerDialogVisible" title="" align-center custom-class="dashboardHouse" class="dashboardHouse"
      center>
      <Dialog :activeData="activeData" :centerDialogVisible="centerDialogVisible" />
      <!-- <template #footer>
      <span class="dialog-footer">
        <el-button @click="centerDialogVisible = false">Cancel</el-button>
        <el-button type="primary" @click="centerDialogVisible = false">
          Confirm
        </el-button>
      </span>
    </template> -->
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import Dialog from './dialog/index.vue'
import { onMounted, defineProps, watch, reactive, onBeforeUnmount, ref, computed, onUnmounted, onDeactivated } from 'vue'


const centerDialogVisible = ref(false)
const props = defineProps(['viewData']);
const activeData = ref({})
const pickMe = (data: Object) => {
  centerDialogVisible.value = true
  activeData.value = data

}
</script>
<style  lang="scss">
.dashboardHouse {
  width: 880px;
  height: 817px;
  background: center / contain no-repeat url("@/assets/images/view/dialog/bg.png");

  .el-dialog__headerbtn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: rgb(111, 179, 255, .3);
    line-height: 38px;
    font-size: 20px;

  }

  .el-dialog__footer {
    display: none;

  }
}
</style>
 
<style lang="scss" scoped>
@import './index.scss';

.dashboardHouse {}
</style>