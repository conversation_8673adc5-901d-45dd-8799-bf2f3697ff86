<template>
    <Content :style="{ height: getRem(215) }" title="本年重点产业经营概况" type="inside" tab="1" current="right">
        <div class="tables">
            <div class="left" @click="changeLeft"  :class={isOpacity:!marginLeft}></div>
            <div class="center" ref="buttonRef">
                <div class="centerIn" :style="{marginLeft:marginLeft+'px'}"  ref="listRef">
                    <div class="elButton" v-for="title in elementSize" :key="title.typeName"
                    :class="{active:activeKey===title.typeName}"
                    @click="onChange(title)"
                    >
                        {{title.typeName}}</div>
                </div>
            </div>
            <div class="right"  :class={isOpacity}  @click="changeRight"></div>

        </div>
        <div  v-for="list in elementSize" :key="list.typeName"> 
            <!-- {{ activeKey }} -->
            <ScrollTable  v-if="list.enterprises.length>0&&activeKey===list.typeName" :style="{ height: getRem(170) }" :propData="list.enterprises" />
            <div class="empty"  v-if="list.enterprises.length===0&&activeKey===list.typeName" >暂无数据 </div>
            <!-- <ScrollTable :style="{ height: getRem(200) }" :propData="elementSize"/> -->
        
    </div>
    </Content>
</template>
<script setup>
import ScrollTable from '@/components/scrollTable/index.vue'
import Content from '../../../content/index.vue';
import { getRem, getVh } from '@/utils/rem.js'
import { useStore } from 'vuex'
import { defineComponent, computed, ref, onMounted ,watch} from 'vue'

const store = useStore()
const elementSize = computed(() => store.state.dashboard.secondViewData?.enterpriseTypeStatistics || [])
watch(()=>store.state.dashboard.secondViewData.enterpriseTypeStatistics,(newVal)=>{
    onChange(newVal?.[0])
},{deep:true})
const activeKey=ref('其他')
onMounted(()=>{
    onChange(elementSize[0])
    // activeKey.value=elementSize[0]?.typeName
})
const marginLeft=ref(0)
 const listRef =ref()
const isOpacity=ref()
 const buttonRef=ref()
const   changeLeft= () => {
    // marginLeft的值不能大于0
    let num = marginLeft.value === 0 || marginLeft.value > 0
    if (!!num) return
    marginLeft.value = marginLeft.value + 55 
    // console.log((marginLeft.value),222)

}
const changeRight  = () => {
    // 数据的总长-margin的值<父级的长度就停止
   if(listRef.value.clientWidth+marginLeft.value< buttonRef.value.clientWidth ) {
    isOpacity.value=true 
    return}
    marginLeft.value = marginLeft.value - 55
    // console.log((listRef.value.clientWidth+marginLeft.value< buttonRef.value.clientWidth),111)

}
const onChange=(data)=>{
    activeKey.value=data?.typeName
    // console.log('eee', activeKey.value)
}
</script>
<style scoped lang="scss">
.tables {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 5px 5px 10px 5px;

    .left,
    .right {
        width: 9px;
        height: 10px;
        cursor: pointer;
        // opacity: 0.7;
    }

    .left {
        background: center / contain no-repeat url('@/assets/images/view/honer-left.png');

    }

    .right {
        background: center / contain no-repeat url('@/assets/images/view/honer-right.png');
    }

    .center {
        // margin: 5px 0 0 0;

        display: flex;
        flex-wrap: nowrap;
        justify-content: flex-start;
        width: 90%;
        overflow: hidden;
        height: 35px;

        .centerIn {
            height: 35px;
            display: flex;
            flex-wrap: nowrap;
            justify-content: flex-start;

            .elButton {
                cursor: pointer;
                width: 110px;
                height: 34px;
                border-radius: 4PX;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                padding: 0 4px;

                font-size: 14px;
                margin-right: 5px;
                line-height: 34px;
                color: #FFFFFF;
                background: rgba(12, 34, 63, 0.7);
                border: 1px solid rgba(40, 92, 151, 0.7);

                &.active {
                    background: rgba(12, 39, 83, 0.7);
                    border: 1px solid rgba(54, 149, 255, 0.7);
                    box-shadow: 0px 0px 5PX -2px rgb(253 251 251) inset;
                }
            }
        }
    }
}
.empty{
    color: #ffffffb3;
    font-size: 14px;
    text-align: center;
    width: 100%;
    height: 170px;
    line-height: 170px;
}
.isOpacity{
  opacity: 0.4;
}
</style>
