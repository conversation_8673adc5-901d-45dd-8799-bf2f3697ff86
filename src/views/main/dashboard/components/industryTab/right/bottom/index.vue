<template>
    <Content :style="{ height: getRem(220) }" title="园区成果" type="inside" tab="1" current="right">
        <div class="bottomMain">
            <div class="left">
                <div class="bg"></div>
                <div class="text">{{elementSize?.[0]?.name}}</div>
                <div class="number">{{elementSize?.[0]?.value}}<span>个</span> </div>
            </div>
            <div class="middle">
                <div class="bg"></div>
                <div class="text">{{elementSize?.[1]?.name}}</div>
                <div class="number">{{elementSize?.[1]?.value}}<span>个</span> </div>

            </div>
            <div class="right">
                <div class="bg"></div>
                <div class="text">{{elementSize?.[2]?.name}}</div>
                <div class="number">{{elementSize?.[2]?.value}}<span>个</span> </div>
            </div>
        </div>
    </Content>
</template>
<script setup>
import Content from '../../../content/index.vue';
import { getRem, getVh } from '@/utils/rem.js';

import { defineComponent, computed, ref, onMounted ,watch} from 'vue'
import { useStore } from 'vuex'
const store = useStore()
const elementSize = computed(() => store.state.dashboard.secondViewData?.parkAchieves || [])
// watch(()=>store.state.dashboard.secondViewData.enterpriseTypeStatistics,(newVal)=>{
//     onChange(newVal[0])
// },{deep:true})
// const activeKey=ref('其他')
// onMounted(()=>{
//     onChange(elementSize[0])
//     // activeKey.value=elementSize[0]?.typeName
// })
</script>
<style scoped lang="scss">
.bottomMain {
    display: flex;
    justify-content: space-between;
    padding: 22px 16px;

    .left,
    .middle,
    .right {
        width: 30%;
        height: 100%;

        .bg {
            width: 91px;
            height: 98px;
            margin: 0 auto;
        }

        .text {
            font-size: 16px;
            color: #B5C1D1;
            text-align: center;
            width: 100%;
            margin: 15px 0 13px;
        }

        .number {
font-size: 24px;
font-family: "YouSheBiaoTiHei";
font-weight: 500;
color: #FFFFFF;
line-height: 15px;
text-align: center;
padding-left: 15px;
span{
    font-size: 14px;
color: #8292A8;
line-height: 15px;
opacity: 0.54; 
padding-left: 8px;
}
        }
    }

    .left {
        .bg {
            background: center / contain no-repeat url('../../../../../../../assets/images/view/tab2/zhuanli.png');

        }
    }

    .middle {
        .bg {
            background: center / contain no-repeat url('../../../../../../../assets/images/view/tab2/chengguo.png');

        }
    }

    .right {
        .bg {
            background: center / contain no-repeat url('../../../../../../../assets/images/view/tab2/rongyu.png');

        }
    }
}
</style>
