<template>
   <Content :style="{ height: getRem(220) }" title="企业规模分布" type="inside"
        :tab="1"
        current="right">
    <div class="layout-container" :style="{ height: getRem(220) }">
      <div ref="dom" class="chart"></div>
    </div>
</Content>
</template>

<script lang="ts">
import type { Ref } from 'vue';
import { defineComponent, onMounted, ref, computed, watch } from 'vue';
import { useEventListener } from '@vueuse/core'; //引入监听函数，监听在vue实例中可自动销毁，无须手动销毁
import * as echarts from 'echarts/core';
import Content from '../../../content/index.vue';
import { getRem ,getVh} from '@/utils/rem.js';
import {
  CanvasRenderer
} from 'echarts/renderers'
import {
  PieChart
} from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  ToolboxComponent
} from 'echarts/components'
import { useStore } from 'vuex'
// 引入options配置
import options from './pie'
export default defineComponent({
  components: {
    Content
  },
  props: {
    viewData: {
      type: Object,
    }
  },
  setup(props) {
    const store = useStore()
    const dom: Ref<HTMLDivElement> = ref(null) as any
    let myEchart: echarts.ECharts | null = null
    // const outputDistribution = computed(() => store.state.dashboard.secondViewData.outputDistribution)//规模分布-产值分布
    // const capitalDistribution = computed(() => store.state.dashboard.secondViewData.capitalDistribution)//规模分布-资本分布
    const data = computed(() => props.viewData)

    watch(data, (newVal) => {
      setecharts(newVal)
    })
    onMounted(() => {
      setecharts(data)

    })
    const setecharts = (newVal: any) => {
      let data = {
        outputDistribution:newVal.outputDistribution,
        capitalDistribution:newVal.capitalDistribution
      } 
      echarts.use([TitleComponent, TooltipComponent, GridComponent, PieChart, LegendComponent, ToolboxComponent, CanvasRenderer])
      myEchart = echarts.init(dom.value)

      let option = options(data)
      myEchart.setOption(option)
      useEventListener("resize", () => myEchart!.resize())
    }
    return {
      dom,
      getRem,
      getVh,
  
    }
  }
})
</script>

<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 100%;
  // background: #fff;
  // padding: 10px;
  box-sizing: border-box;
}

.layout-container {
  width: 100%;
  // height: 190px;
  background: transparent;
}

.tips {
  padding: 10px 10px 0 10px;
  display: flex;
  width: 100%;
  flex-wrap: wrap;

  .tipsItem {
    width: 50%;
    display: flex;
    flex-wrap: nowrap;
    color: #FFFEFE;
    vertical-align: middle;
    align-items: center;
  }

  .tipsIcon {
    width: 23px;
    height: 20px;
    margin-top: 5px;
    margin-right: 3px;
    background: center / contain no-repeat url('../../../../../assets/images/view/squear.png');

  }

  .tipsName {
    width: calc(100% - 20px);

    font-size: 16px;

    font-weight: 400;
    color: #FFFEFE;

    .value {
      padding-left: 3px;
      font-size: 20px;
      font-family: YouSheBiaoTiHei;
    }
  }

  .tipsName0 {
    color: #42A3FE;
  }

  .tipsName1 {
    color: #12B1BE;
  }

  .tipsName2 {
    color: #CDB40C;

  }

  .tipsName3 {
    color: #0043FF;

  }
}
</style>
 
