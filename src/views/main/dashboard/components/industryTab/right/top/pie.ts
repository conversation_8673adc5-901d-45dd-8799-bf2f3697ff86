import { style } from "@/theme";
import * as echarts from "echarts/core";
const options = (data: any) => {
  const { outputDistribution, capitalDistribution } = data;

  let outputLegend = outputDistribution?.map((item) => item.name.split("万")[0]);
  let data1 = outputDistribution?.map((item) => {
    return {
      name: item.name.split("万")[0],

      value: item.value,
    };
  });
 

  let capitalLegend = capitalDistribution?.map((item) => item.name.split("万")[0]);
  let data2 = capitalDistribution?.map((item) => {
    return {
      name: item.name.split("万")[0],

      value: item.value,
    };
  });

  let colors = [
    {
      colorEnd: "#75C756",
      colorStart: "#A2D591",
    },
    {
      colorEnd: "#3E6AC9",
      colorStart: "#6989CE",
    },
    {
      colorEnd: "#FFBA00",
      colorStart: "#FDD17A",
    },
    {
      colorEnd: "#FF4044",
      colorStart: "#F57C7E",
    },
    {
      colorEnd: "#47BDE2",
      colorStart: "#84CCE3",
    },
    {
      colorEnd: "#009B63",
      colorStart: "#4CB28B",
    },
    {
      colorEnd: "#FF6500",
      colorStart: "#FF976F",
    },
    {
      colorEnd: "#9852B2",
      colorStart: "#AD7ABF",
    },
    {
      colorEnd: "#F764C9",
      colorStart: "#F093D3",
    },
  ];
  // console.log(outputDistribution,capitalDistribution)
  return {
    title: [
      {
        subtext:[
          '{a|产值分布}',
          '{b|（单位：万)}'
        ].join(''),
        left: "15%",
        top: "0",
        textAlign: "center",
      
        subtextStyle: {
          rich: {
            a: {
              color: 'white',
              fontSize: 10, 
            },
            b: {
              fontSize: 8,
              color: 'rgba(255,255,255,0.6)',
            }
          },
        },
      },
      {
        subtext:[
          '{a|资本分布}',
          '{b|（单位：万)}'
        ].join(''),
        left: "65%",
        top: "0",
        textAlign: "center",
        // textStyle: {
        //   color: 'white', // 标题文字颜色
        //   fontSize: 12, // 标题文字大小
        // },
        subtextStyle: {
          rich: {
            a: {
              color: 'white',
              fontSize: 10, 
            },
            b: {
              fontSize: 8,
              color: 'rgba(255,255,255,0.6)',
            }
          },
        },
      },
    ],
    tooltip: {
      trigger: "item",
      // backgroundColor: 'rgba(0, 0, 0, 0.7)', // 背景色
      extraCssText:
        "background: linear-gradient(to right,#286CE9 0%, #01D1FF 100%); color: white; border: 0px ",
      textStyle: {
        color: "white", // 文本颜色
        fontSize: 12, // 文本字号
      },
      formatter: (params: any) => {
        // console.log('params',params)
        let data = `<div
    style="padding: '0px';margin:0px;background:red'"
    > <div>${params.data.name}:${params.data.value}</div></div>`;
        return data;
      },

      // '{a} <br/>{b} : {c} ({d}%)'
    },
    legend: [
      {
        left: "10",
        width: "45%",
        bottom: "1%",
        data: outputLegend,
        itemWidth: 2,
        itemHeight: 2,
        textStyle: {
          color: "white",
          fontSize: 8,
          lineHeight:8,
        },
      },
      {
        left: "55%",
        bottom: "1%",
        height: "30%",

        // top: 'bottom',
        width: "45%",
        // type: "scroll",
        data: capitalLegend,
        itemWidth: 2,
        itemHeight: 2,
        textStyle: {
 
          color: "white",
          fontSize: 8,
          lineHeight:3,
        },
      },
    ],
    // toolbox: {
    //   show: true,
    //   // feature: {
    //   //   mark: { show: true },
    //   //   dataView: { show: true, readOnly: false },
    //   //   restore: { show: true },
    //   //   saveAsImage: { show: true }
    //   // }
    // },
    // color:colors,
    series: [
      {
        name: "",
        type: "pie",
        radius: "45%",
        center: ["25%", "50%"],
        // roseType: 'radius',
        emphasis: {
          label: {
            show: false,
          },
        },
        label: {
          show: false,
          color: "white",
          fontSize: 10,
        },
        labelLine: {
          // normal: {
          show: false,
          length: 5,
          length2: 9,
          // }
        },
        itemStyle: {
          normal: {
            show: false, // 显示示例牵引线
            color: (list: any) => {
              // 设置描边宽度
              var colorList = colors;
              return new echarts.graphic.LinearGradient(1, 1, 0, 0, [
                {
                  //左、下、右、上
                  offset: 0,
                  color: colorList[list.dataIndex].colorStart,
                },
                {
                  offset: 1,
                  color: colorList[list.dataIndex].colorEnd,
                },
              ]);
            },
          },
        },
        data: data1,
      },

      {
        name: "",
        type: "pie",
        radius: "45%",
        center: ["75%", "50%"],
        // roseType: 'radius',
        emphasis: {
          label: {
            show: false,
          },
        },
        label: {
          show: false,
          color: "white",
          fontSize: 10,
        },
        labelLine: {
          // normal: {
          show: false,
          length: 5,
          length2: 9,
          // }
        },
        itemStyle: {
          normal: {
            show: false, // 显示示例牵引线
            color: (list: any) => {
              // 设置描边宽度
              var colorList = colors;
              return new echarts.graphic.LinearGradient(1, 1, 0, 0, [
                {
                  //左、下、右、上
                  offset: 0,
                  color: colorList[list.dataIndex].colorStart,
                },
                {
                  offset: 1,
                  color: colorList[list.dataIndex].colorEnd,
                },
              ]);
            },
          },
        },
        data: data2,
      },
    ],
  };
};
export default options;
