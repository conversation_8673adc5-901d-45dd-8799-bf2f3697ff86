
 
import * as echarts from 'echarts/core'
const options =(data:any)=>{
  // console.log('data',data)
  let option =  {
    title:  [ {
      subtext: '· 招商任务总数',
      left: '60',
      top: '0',
      textAlign: 'center',
      textStyle: {
        color: 'white', // 标题文字颜色
        fontSize: 16, // 标题文字大小
      },
      subtextStyle: {
        color: 'white', // 副标题文字颜色
        fontSize: 16,  // 副标题文字大小
        fontWeight:600,
    
        // fontStyle: 'italic', // 斜体样式
      },
    },
 ],
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)'
    },
    legend:  [
      { left: '180',
      width:100,
      top: '65',
      data: ['数字化港口', '数字化道桥', '数字化轨交', '智慧停车'],
      itemWidth:9,
      itemHeight:3,
      textStyle: {
        color: 'white',
        fontSize: 10,
        lineHeight:14
        // fontStyle: 'italic',
      },
   
    },
    
    ],
    // toolbox: {
    //   show: true,
    //   // feature: {
    //   //   mark: { show: true },
    //   //   dataView: { show: true, readOnly: false },
    //   //   restore: { show: true },
    //   //   saveAsImage: { show: true }
    //   // }
    // },
 
    series: [
      
     {
        name: '',
        type: 'pie',
        // radius: '60%',
        center: ['30%', '60%'],
        // roseType: 'radius',
        radius: ['30%', '45%'],
        avoidLabelOverlap: false,
        emphasis: {
          label: {
            show: true,
            fontSize: 40,
            fontWeight: 'bold'
          }
        },
        label: {
          show: false,
          position: 'center'
        },
        labelLine: {
          show: false,
        },
        itemStyle: {
          normal: {
            show: true, // 显示示例牵引线
            // color:  ['#10E8AA','#2447F7','#24DCF7','#FFDB15'],
            borderColor: '#0c1837',
            // ['#3BAC7C','#0271FB','#014AF6','#808080'],
            borderWidth: 2,
              color: (list:any) => {
              // 设置描边宽度
                var colorList =  ['#10E8AA','#2447F7','#24DCF7','#FFDB15']
                return new echarts.graphic.LinearGradient(1, 1, 0, 0, [{ //左、下、右、上
                    offset: 0,
                    color: colorList[list.dataIndex] 
                },   {
                    offset: 1,
                    color: colorList[list.dataIndex] 
                }])                                    
              }
          }
      },
        data : [{ value: 40, name: '数字化港口' },
        { value: 33, name: '数字化道桥' },
        { value: 28, name: '数字化轨交' },
        { value: 22, name: '智慧停车' },]
      },
    
    ]
    } ;
  // let option = {
  //   grid: {
  //     top: 30,
  //     right: 0,
  //     bottom: 20
  //   },
  //   xAxis: [
  //     {
  //       data: ["2月1日", "2月2日", "2月3日", "2月4日", "2月5日", "2月6日", "2月7日"],
  //       axisTick: {
  //         show: false
  //       },
  //       axisLine: {
  //         show: false
  //       },
  //       axisLabel: {
  //         show: true,
  //         fontSize: 10,
  //         color: "#fff"
  //       }
  //     }
  //   ],
  //   yAxis: [
  //     {
  //       type: "value",
  //       name: "(个)",
  //       nameTextStyle: {
  //         color: "#8CB5E2"
  //       },
  //       splitLine: {
  //         show: true,
  //         lineStyle: {
  //           type: "dashed",
  //           color: "#182450"
  //         }
  //       },
  //       axisLabel: {
  //         color: "#8CB5E2"
  //       }
  //     }
  //   ],
  //   series: [
    
  //   ]
  // }

  return  option
}
export default  options
