<template>
  <Content :style="{ height: getRem(235) }" title="年度招商概况" type="inside" :tab="1" current="center">
    <div class="container">
      <div class="left">
        <div class="img">
          <div class="num">
            {{ investNum }}
          </div>
        </div>
        <div class="text">招商企业数量</div>
      </div>
      <div class="right">
        <div class="tables">
          <div class="leftBtn" :class={isOpacity:!marginLeft} @click="changeRight"></div>
          <div class="tablesCenter " ref="buttonRef">
            <div class="centerIn" :style="{ marginLeft: marginLeft + 'px' }" ref="listRef">
              <div class="centerInList" v-for="(item, index) in investEnterprises" :key="index">
              <div class="centerItem" >
                <div class="title">
                  {{ item.name }}
                </div>
                <div class="titleDes">
                  <el-tooltip class="box-item" trigger="click" effect="dark" :content="item.desc" placement="bottom">

                    {{ item.desc }}

                  </el-tooltip>

                </div>
                <div class="titleImg" :style="{ backgroundImage: `url(${item.image})` }">
                </div>
              </div>
              </div>

            </div>
          </div>
          <div class="rightBtn"  :class='{isOpacity:isOpacity}' @click="changeLeft"></div>

        </div>
      </div>
      <!-- <Left/>
       <Right/> -->
    </div>
  </Content>
</template>

<script lang="ts" setup>
import { defineComponent, onMounted, ref, computed, watch } from 'vue'
import Content from '../../../content/index.vue';
import { getRem, getVh } from '@/utils/rem.js';
import { useStore } from 'vuex'
const store = useStore()
const investNum = computed(() => store.state.dashboard.secondViewData.investNum)
const investEnterprises = computed(() => store.state.dashboard.secondViewData.investEnterprises)
// watch(()=>store.state.dashboard.secondViewData.enterpriseTypeStatistics,(newVal)=>{
//     onChange(newVal[0])
// },{deep:true})
// const activeKey = ref('其他')
onMounted(() => {
  // onChange(elementSize[0])
  // activeKey.value=elementSize[0]?.typeName
})
const marginLeft = ref(0)
const listRef = ref()
const buttonRef = ref()
const isOpacity=ref(false)
// 引入options配置
// import {defineProps,onMounted} from 'vue'
// const props = defineProps(['viewData']);
const changeRight = () => {
  // marginLeft的值不能大于0

  let num = marginLeft.value === 0 || marginLeft.value > 0
  if (!!num){
    isOpacity.value=false
    return
  }
  marginLeft.value = marginLeft.value + 300
  // console.log((marginLeft.value), 222)

}
const changeLeft = () => {
  // 数据的总长-margin的值<父级的长度就停止
  if (listRef.value.clientWidth + marginLeft.value < buttonRef.value.clientWidth){
    isOpacity.value=true 
    return
  }
  marginLeft.value = marginLeft.value - 300
  // console.log((listRef.value.clientWidth + marginLeft.value < buttonRef.value.clientWidth), 111)

}
</script>

<style lang="scss" scoped>
::-webkit-scrollbar {
    /* 对应纵向滚动条的宽度 */
    width: 5px;
    /* 对应横向滚动条的宽度 */
    height: 0px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
    background-color: #324156;
    border-radius: 32px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
    display: none;
}
.chart {
  width: 100%;
  box-sizing: border-box;
}


.container {
  width: 925px;
  // width: 100%;
  height: 235px;
  display: flex;
  flex-wrap: nowrap;

  .left {
    padding: 40px 10px 20px;
    width: 200px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-content: space-around;

    .img {
      margin: 0 auto;
      width: 140px;
      height: 102px;
      background: center / contain no-repeat url('@/assets/images/view/tab2/bg1.png');

      .num {
        font-size: 40px;
        font-family: YouSheBiaoTiHei;
        font-weight: 500;
        color: #FFFFFF;

        background: linear-gradient(0deg, #9AD8FF 0%, #FFFFFF 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
    }

    .text {
      margin: 0 auto;
      width: 174px;
      height: 42px;
      font-size: 16px;
      line-height: 42px;
      color: #FFFFFF;
      background: center / contain no-repeat url('@/assets/images/view/tab2/bg6.png');

    }
  }

  .right {
    width: calc(100% - 200px);
  }

  .tables {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 5px 5px 10px 5px;

    .leftBtn,
    .rightBtn {
      width: 9px;
      height: 10px;
      cursor: pointer;
      background-size: 40% 40%;
      background-repeat: no-repeat;
      background-position: center;
      padding: 20px;
      // opacity: 0.7;
    }

    .leftBtn {
      background-image: url('@/assets/images/view/honer-left.png');

    }

    .rightBtn {
      background-image: url('@/assets/images/view/honer-right.png');
    }

    .tablesCenter {
      // margin: 5px 0 0 0;
      display: flex;
      flex-wrap: nowrap;
      justify-content: flex-start;
      width: 90%;
      overflow: hidden;
      height: 200px;
      align-items: center;
    text-align: center;
      .centerIn {
        height: 200px;
        display: flex;
        flex-wrap: nowrap;
        justify-content: flex-start;

        .centerInList {
          width: 300px;
          height: 200px;
          background: center / contain no-repeat url('@/assets/images/view/tab2/bg5.png');
          // padding: 10px 20px 0px 20px;
         
          
          .centerItem{
            height: 180px;
            overflow: auto;
            margin-top: 10px;
            padding: 0px 20px 0px 20px;
          }
          // font-size: 14px;
          // margin-right: 5px;
          // line-height: 34px;
          // color: #FFFFFF;
          // background: rgba(12, 34, 63, 0.7);
          // border: 1px solid rgba(40, 92, 151, 0.7);

          // &.active {
          //     background: rgba(12, 39, 83, 0.7);
          //     border: 1px solid rgba(54, 149, 255, 0.7);
          //     box-shadow: 0px 0px 5PX -2px rgb(253 251 251) inset;
          // }
          .title {
            // margin-top: 20px;
            font-size: 16px;
            font-weight: 500;
            color: #FFFFFF;
            text-align: left;

          }

          .titleDes {
            font-size: 12px;
            color: #B2C3DF;
            text-align: left;
            margin-bottom: 10px;
          }

          .titleImg {
            width: 197px;
            height: 100px;
            background: center / contain no-repeat;
            margin-bottom: 20px;
            // url('@/assets/images/view/tab2/bg1.png');

          }
        }
      }
    }
  }
}
.isOpacity{
  opacity: 0.4;
}
</style>

