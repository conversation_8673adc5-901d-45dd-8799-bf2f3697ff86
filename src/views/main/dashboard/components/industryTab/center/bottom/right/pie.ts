
 
import * as echarts from 'echarts/core'
const options =(data:any)=>{
  // console.log('data',data)
  let option =  {

 
    title:  [ 
    {
      subtext: '· 招商企业概况',
      left: '11%',
      top: '0',
      textAlign: 'center', 
      // textStyle: {
      //   color: 'white', // 标题文字颜色
      //   fontSize: 16, // 标题文字大小
      // },
      subtextStyle: {
        color: 'white', // 副标题文字颜色
        fontSize: 16,  // 副标题文字大小
        fontWeight:600,
    
        // fontStyle: 'italic', // 斜体样式
      },
    },],
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b} : {c} ({d}%)'
    },
    legend:  [
     
      { left: '60%',
      top: 'bottom',
      data: ['数字化港口', '数字化道桥', '数字化轨交', '智慧停车'],
      itemWidth:5,
      itemHeight:3,
      textStyle: {
        color: 'white',
        fontSize: 8,
        // fontStyle: 'italic',
      }}
    ],
   
    grid: {
      top: '30%', // 设置图表上边距为 20%
      // 其他 grid 相关配置
    },
         xAxis: [
     
      {
        data:  ['1月', '2月', '3月', '4月', '5月', '6月', '7月','8月','9月','10月','11月','12月'],
        axisTick: {
          show: false
        },
        axisLine: {
          show: false
        },
        axisLabel: {
          show: true,
          fontSize: 10,
          color: "#b5c1d180"
        }
      }
    ],
    yAxis: [
  
      {
        type: "value",
        name: "万元",
        nameTextStyle: {
          color: "#b5c1d166"
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: "solid",
            color: "#b5c1d11a"
          }
        },
        axisLabel: {
          color: "#b5c1d180"
        }
      }
    ],
    series: [
   // 柱子上圆
      {
        
        // 真实数据
        data: [70, 100, 200, 230, 200, 400, 270,300,500,134,400,388],

        width:120,
        // barWidth: 9, // 设置柱子宽度，单位为像素
        name: "顶部圆片 （柱形图顶部）",
        // center: ['55%', '65%'],
        //symbol标记类型包括 'circle', 'rect', 'roundRect', 'triangle', 'diamond','pin','arrow', 'none' (默认为圆形)
        type: 'pictorialBar', //指定类型
        symbolSize: [7, 2.2], //指定大小，[宽,高]
        symbolOffset: [0, -1.1], //位置偏移 [右，下] 负数反方向
        z: 12, // 层级（优先级展示）
        itemStyle: {
          color: "#C8FFFA"
        },
        symbolPosition: "end"
      },
    
      // 柱子下圆

      // {
      //   // barWidth: 10, // 设置柱子宽度，单位为像素
      //   center: ['55%', '50%'],
      //   name: '真实数据柱形图',
      //   type: "pictorialBar",
      //   symbolSize: [7, 2.2],
      //   symbolOffset: [0, 2],
      //   z: 12,
      //   itemStyle: {
      //     color: "rgba(48,228,255,1)"
      //   },
      //   // 真实数据
      //   data: [70, 100, 200, 230, 200, 400, 270]
      // },
      // 柱子本身+背景
      // {
      //   barWidth: 7, // 设置柱子宽度，单位为像素
      //   name: '底部圆片（柱形图底部）',
      //   type: "bar",
      //   itemStyle: {
      //     color: new echarts.graphic.LinearGradient(
      //       0, 0, 1, 0,
      //       [
      //         // {offset: 0, color:'rgba(146,255,246,1)'},
      //         {offset: 0, color:  '#14203e'},
      //         {offset: 0.1, color:  '#14203e94'},
      //         {offset: 1, color:  '#14203e00'},
      //         // {offset: 0.5, color: '#188df0'},
      //       ]
      //     )
      //   },
      //   z: 16,
      //   silent: true,
      //   barGap: "-100%",
      //   showBackground: true,
      //   backgroundStyle: {
      //     color: "rgba(20,82,130, 0.4)"
      //   },
      //   // 真实数据
      //   data: [70, 100, 200, 230, 200, 400, 270],
      // },
      {
        // center: ['30%', '65%'],
        barWidth: 7, // 设置柱子宽度，单位为像素
        name: '底部圆片（柱形图底部）',
        type: "bar",
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0, 0, 0, 1,
            [
              // {offset: 0, color:'rgba(146,255,246,1)'},
              {offset: 0, color:'#008E7C'},
                  // 'rgba(0,94,82,0.9)'},
              {offset: 1, color:  'rgba(0,94,82,0)'},
              // {offset: 0.5, color: '#188df0'},
            ]
          )
        },
        z: 16,
        silent: true,
        barGap: "-100%",
        showBackground: true,
        backgroundStyle: {
          color: "rgba(20,82,130, 0.4)"
        },
        // 真实数据
        data: [70, 100, 200, 230, 200, 400, 270,300,500,134,400,388],
      },
     
      // 顶部圆柱
      {
        // barWidth: 10, // 设置柱子宽度，单位为像素
        // center: ['55%', '65%'],
        name: "最顶部圆片(背部阴影最顶部圆片)",
        type: "pictorialBar",
        symbolSize: [7, 2.2],
        symbolOffset: [0, 0],
        z: 3,
        symbolPosition: "end",
        itemStyle: {
          color: "#c8fffa82",
          opacity: 0.7
        },
        // 设置最大数据，可以不设置data，默认为真实数据的最大值
        data: [500, 500, 500, 500, 500, 500, 500]
      }
    ]
    } ;
 
  return  option
}
export default  options
