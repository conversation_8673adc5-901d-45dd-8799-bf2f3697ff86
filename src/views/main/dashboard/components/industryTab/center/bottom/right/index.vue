<template>
   
     <div class="layout-container" :style="{ height: getVh(235) }">
       <div ref="dom" class="chart"></div>
     </div>
 
  </template>
  
  <script lang="ts">
  import type { Ref } from 'vue'
  import { defineComponent, onMounted, ref, computed, watch } from 'vue'
  import { useEventListener } from '@vueuse/core' //引入监听函数，监听在vue实例中可自动销毁，无须手动销毁
  import * as echarts from 'echarts/core';
  import { getRem ,getVh} from '@/utils/rem.js'
  import {
   CanvasRenderer
  } from 'echarts/renderers'
  import {
   PieChart,PictorialBarChart
  } from 'echarts/charts'
  import {
   TitleComponent,
   TooltipComponent,
   GridComponent,
   LegendComponent,
   ToolboxComponent
  } from 'echarts/components'
  
  // 引入options配置
  import options from './pie'
  // import {defineProps,onMounted} from 'vue'
  // const props = defineProps(['viewData']);
  export default defineComponent({
 
   props: {
     viewData: {
       type: Object,
       // default: () => {
       //   return {
       //     "digitalPortNum": 0, //数字化港口
       //       "digitalRailTransitNum": 0, //数字化轨交
       //       "digitalRoadBridgeNum": 0, //数字化道桥
       //       "smartParkingNum": 0 //智慧停车
       //   }
       // }
     }
   },
   setup(props) {
     const dom: Ref<HTMLDivElement> = ref(null) as any
     let myEchart: echarts.ECharts | null = null
  
     const data = computed(() => props.viewData)
  
     watch(data, (newVal) => {
       setecharts(newVal)
     })
     onMounted(() => {
       setecharts(data)
  
     })
     const setecharts = (newVal: any) => {
       //console.log(newVal)
       let data = [
         { value: newVal.digitalPortNum, name: '数字化港口' },
         { value: newVal.digitalRailTransitNum, name: '数字化轨交' },
         { value: newVal.digitalRoadBridgeNum, name: '数字化道桥' },
         { value: newVal.smartParkingNum, name: '智慧停车' },
       ]
       echarts.use([TitleComponent, TooltipComponent, GridComponent, PieChart,PictorialBarChart, LegendComponent, ToolboxComponent, CanvasRenderer])
       myEchart = echarts.init(dom.value)
  
       let option = options(data)
       myEchart.setOption(option)
       useEventListener("resize", () => myEchart!.resize())
     }
     return {
       dom,
       getRem,
       getVh,
       data: [
         { value: 'digitalPortNum', name: '数字化港口' },
         { value: 'digitalRailTransitNum', name: '数字化轨交' },
         { value: 'digitalRoadBridgeNum', name: '数字化道桥' },
         { value: 'smartParkingNum', name: '智慧停车' },
       ]
     }
   }
  })
  </script>
  
  <style lang="scss" scoped>
  .chart {
   width: 100%;
   height: 100%;
   // background: #fff;
   // padding: 10px;
   box-sizing: border-box;
  }
  
  .layout-container {
   width: 65%;
   // height: 190px;
   background: transparent;
  }
  
 
  </style>
  
  