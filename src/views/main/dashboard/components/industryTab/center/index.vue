<template>
    <div class="center">
        <div class="centerTop">
            <div class="left">
                <div class="title">
                    <div class="titleTop">
                        <span class="number">{{ elementSize?.[0]?.value||0 }}</span>
                        <span class="unit">{{ elementSize?.[0]?.unit }}</span>
                    </div>
                    <div class="titleBottom">
                        <div class="point"></div>
                        {{ elementSize?.[0]?.name }}
                    </div>
                </div>
                <div class="title">
                    <div class="titleTop">
                        <span class="number">{{ elementSize?.[1]?.value ||0}}</span>
                        <span class="unit">{{ elementSize?.[1]?.unit }}</span>
                    </div>
                    <div class="titleBottom">
                        <div class="point"></div>
                        {{ elementSize?.[1]?.name }}
                    </div>
                </div>
            </div>
            <div class="middle">

            </div>

            <div class="right">
                <div class="title">
                    <div class="titleTop">
                        <span class="number">{{ elementSize?.[2]?.value ||0}}</span>
                        <span class="unit">{{ elementSize?.[2]?.unit }}</span>
                    </div>
                    <div class="titleBottom">
                        <div class="point"></div>
                        {{ elementSize?.[2]?.name }}
                    </div>
                </div>
                <div class="title">
                    <div class="titleTop">
                        <span class="number">{{ elementSize?.[3]?.value ||0}}</span>
                        <span class="unit">{{ elementSize?.[3]?.unit }}</span>
                    </div>
                    <div class="titleBottom">
                        <div class="point"></div>
                        {{ elementSize?.[3]?.name }}
                    </div>
                </div>
            </div>
        </div>

        <Bottom />


    </div>
</template>
<script setup>
import Bottom from './bottom/index.vue';

import { defineComponent, computed, ref, onMounted, watch } from 'vue'
import { useStore } from 'vuex'
const store = useStore()
const elementSize = computed(() => store.state.dashboard.secondViewData?.statisticsValues || [])
// watch(()=>store.state.dashboard.secondViewData.statisticsValues,(newVal)=>{
//     onChange(newVal[0])
// },{deep:true})
// const activeKey=ref('其他')
// onMounted(()=>{
//     onChange(elementSize[0])
//     // activeKey.value=elementSize[0]?.typeName
// })
</script>
<style scoped lang="scss">
.center {
    width: 100%;
    height: 700px !important;

    .centerTop {
        width: 100%;
        height: 425px;
        // margin-bottom: 60px;
        display: flex;
        // padding: 0 90px;
        flex-wrap: nowrap;

        .left,
        .right {
            padding: 30px 0 70px 0;
            width: 170px;
            display: flex;
            justify-content: center;
            align-content: space-between;
            flex-wrap: wrap;

            .title {
                width: 100%;
                height: 60px;
                display: flex;
                flex-wrap: wrap;
                justify-content: center;

                .titleTop {
                    width: 100%;
                    font-size: 15px;
                    color: white;
                    display: flex;
                    line-height: 60px;
                    height: 60px;
                    position: relative;
                    justify-content: flex-start;
                    padding-right: 14px;
                    padding-top: 5px;

                    .number {
                        font-size: 40px;
                        font-weight: 500;
                        font-family: "YouSheBiaoTiHei";
                        // display: inline-block;
                        // position: absolute;
                        // top: 0;
                        // left: 10px;
                    }

                    .unit {
                        padding-left: 6px;
                        line-height: 70px;
                    }

                }

                .titleBottom {
                    width: 100%;
                    font-size: 17px;
                    color: #008AFF;
                    display: flex;
                    justify-content: flex-start;
                    align-items: center;

                    .point {
                        width: 6px;
                        height: 6px;
                        background: linear-gradient(0deg, #5EAAFF 0%, #FFFFFF 100%);
                        border-radius: 50%;
                        margin-right: 15px;
                    }
                }
            }
        }

        .left {
            padding-left: 25px;
            // .titleTop{
            //     // justify-content: flex-end !important;

            // }
        }

        .middle {
            width: 570px;
            height: 440px;
            background: center / contain no-repeat url('@/assets/images/view/view.png');
        }
    }

    .centerBottom {
        width: 100%;
        height: 270px;

    }
}</style>