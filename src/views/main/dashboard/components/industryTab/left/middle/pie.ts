import * as echarts from 'echarts/core'
// var yMax = 500;
// var dataShadow = [];

// for (var i = 0; i < data.length; i++) {
//     dataShadow.push(yMax);
// }

const options =(params)=>{
  let dataAxis=params&&params?.map((item:object)=>item?.name)||[]
  let dataValue=params&&params?.map((item:object)=>item?.value)||[]
  let   option = {
//  grid: {
//   x: 25,
//   y: 45,
//   x2: 5,
//   y2: 25,
//   borderWidth: 1,
//  },
    tooltip: {
      trigger: 'axis',
      // backgroundColor: linear-gradient(90deg, #286CE9 0%, #01D1FF 100%), // 背景色
      // 使用 CSS3 渐变来定义背景色
    extraCssText: 'background: linear-gradient(to right,#286CE9 0%, #01D1FF 100%); color: white; border: 0px ',
      // borderColor: 'red', // 边框颜色
      borderWidth: 0, // 边框宽度
      textStyle: {
        color: 'white', // 文本颜色
        fontSize: 14, // 文本字号
      },
      axisPointer: {
        type: 'cross',
        // label: {
        //   backgroundColor: '#6a7985'
        // }
      },
      formatter:( params:any)=>{
        // console.log('params',params)
        let data=`<div> <div>${params[0].name}:${params[0].value}万元</div></div>`
        return data
      }

    },
    legend: {
      data:  ['1月', '2月', '3月', '4月', '5月', '6月', '7月','8月','9月','10月','11月','12月']
    },
    // toolbox: {
    //   feature: {
    //     saveAsImage: {}
    //   }
    // },
    grid: {
      left: '3%',
      right: '6%',
      bottom: '2%',
      top:'20%',
      containLabel: true
    },
    xAxis: [
      {
        splitLine: {
          show: false, // 隐藏x轴分隔线
        },
        axisLabel: {
          fontSize: 10 ,// 设置 x 轴文字的大小为 12 像素
          color: "#b5c1d180",
          interval:0,
          rotate:-75,	
        },
        type: 'category',
        boundaryGap: false,
        data:dataAxis,
        fontSize: 10,
        color: "#b5c1d180"
      }
    ],
    yAxis: [

      {
        splitLine: {
          show: false, // 隐藏x轴分隔线
        },
        type: 'value',
        name: "单位：万元",
        axisLabel: {
          color: "#b5c1d180",
          fontSize: 10 // 设置 x 轴文字的大小为 12 像素
          // formatter: function (value:any) {
          //   if (value >= 1000 && value < 10000) {
          //     value = value / 1000 ;
          // } else if (value >= 10000) {
          //     value = value / 10000  ;
          // }
          // return value;
          //         }
        }
      }
    ],
  
    series: [
      {
        // name: 'Search Engine',
        // radius:"60%",
        type: 'line',
        stack: 'Total',
        label: {
          show: false,
          position: 'top'
        },
        areaStyle: {},
        emphasis: {
          focus: 'series'
        },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0,0,0, 1,
            [
              {offset: 0, color: 'rgba(4,205,244,0.4)'},
              {offset: 0.7, color:'rgba(67,144,250,0.3)'}, 
              {offset: 1, color: 'rgba(67,144,250,0.1)'},
            ]
          )
        },
        data:dataValue
      }
    ]
  };
  return  option
}
export default  options
