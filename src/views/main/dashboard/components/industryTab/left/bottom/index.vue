<template>
        <Content :style="{ height: getRem(220) }" title="年度产值TOP10企业" type="inside" 
        tab="1"
        current="left"
        >
    <ScrollTable :style="{ height: getRem(220) }" :propData="tableData"/>
    </Content>
</template>
<script setup>
 import ScrollTable from '@/components/scrollTable/index.vue'
 import Content from '../../../content/index.vue';
import { getRem, getVh } from '@/utils/rem.js'
import { defineComponent, computed, onMounted,watch ,ref} from 'vue'
import { useStore } from 'vuex'
const store = useStore()
const elementSize = computed(() => store.state.dashboard.secondViewData?.outputTop10)
let tableData=ref()
watch(()=>store.state.dashboard.secondViewData.outputTop10,(newVal)=>{
    // console.log('elementSize==>11',newVal)
    tableData.value=newVal
    // onChange(newVal?.[0])
},{deep:true})
onMounted(()=>{
    tableData.value=elementSize.value

})
</script>
<style scoped>

</style>
