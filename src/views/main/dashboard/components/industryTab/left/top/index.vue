<template>
  <Content :style="{ height: getRem(220) }" title="年度产值情况" :tab="1" type="inside" current="left">

    <div class="layout-container" :style="{ height: getRem(220) }">
      <!-- <div v-if="isShowData"> -->
        <div ref="dom" class="chart"></div>
      <!-- </div>
      <div v-else> 暂无数据</div> -->

    </div>

  </Content>
</template>

<script lang="ts">
import type { Ref } from 'vue'
import { defineComponent, onMounted, ref, computed, watch } from 'vue'
import { useEventListener } from '@vueuse/core' //引入监听函数，监听在vue实例中可自动销毁，无须手动销毁
import * as echarts from 'echarts/core'
import Content from '../../../content/index.vue'
import { getRem, getVh } from '@/utils/rem.js'
import _, { filter, findIndex } from 'lodash'

import {
  CanvasRenderer
} from 'echarts/renderers'
import {
  BarChart
} from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  ToolboxComponent,
  DataZoomComponent
} from 'echarts/components'

// 引入options配置
import options from './pie'
// import {defineProps,onMounted} from 'vue'
// const props = defineProps(['viewData']);
import { useStore } from 'vuex'

export default defineComponent({
  components: {
    Content
  },
  props: {
    viewData: {
      type: Object,
      // default: () => {
      //   return {
      //     "digitalPortNum": 0, //数字化港口
      //       "digitalRailTransitNum": 0, //数字化轨交
      //       "digitalRoadBridgeNum": 0, //数字化道桥
      //       "smartParkingNum": 0 //智慧停车
      //   }
      // }
    }
  },
  // data() {
  //   return {
  //     isShowData:true
  //   }},
  setup(props) {
    const dom: Ref<HTMLDivElement> = ref(null) as any
    let myEchart: echarts.ECharts | null = null
    const store = useStore()
    const elementSize = computed(() => store.state.dashboard.secondViewData?.annualOutputs)
    // const data = computed(() => props.viewData)
    // const isShowData = ref(true)
    watch(elementSize, (newVal) => {
      // console.log('newVal==>', newVal)
      let filterData = filter(newVal, (e) => e.value !== '0')
      // console.log('data===>', filterData,  isShowData.value)

      // if (filterData.length === 0) {
      //   isShowData.value = false
      // }
      setecharts(newVal)
    }, { deep: true })
    onMounted(() => {
      setecharts(elementSize?.value)

    })
    const setecharts = (newValues: any) => {
      myEchart = null
      echarts.use([TitleComponent, TooltipComponent, GridComponent, BarChart, LegendComponent, ToolboxComponent, DataZoomComponent, CanvasRenderer])
      myEchart = echarts.init(dom.value)
      let option = options(newValues)
      myEchart.setOption(option)
      useEventListener("resize", () => myEchart!.resize())
    }
    return {
      dom,
      getRem,
      getVh,
      // isShowData
    }
  }
})
</script>

<style lang="scss" scoped>
.chart {
  width: 100%;
  height: 100%;
  // background: #fff;
  // padding: 10px;
  box-sizing: border-box;
}

.layout-container {
  width: 100%;
  // height: 190px;
  background: transparent;
}

.tips {
  padding: 10px 10px 0 10px;
  display: flex;
  width: 100%;
  flex-wrap: wrap;

  .tipsItem {
    width: 50%;
    display: flex;
    flex-wrap: nowrap;
    color: #FFFEFE;
    vertical-align: middle;
    align-items: center;
  }

  .tipsIcon {
    width: 23px;
    height: 20px;
    margin-top: 5px;
    margin-right: 3px;
    background: center / contain no-repeat url('../../../../../assets/images/view/squear.png');

  }

  .tipsName {
    width: calc(100% - 20px);

    font-size: 16px;

    font-weight: 400;
    color: #FFFEFE;

    .value {
      padding-left: 3px;
      font-size: 20px;
      font-family: YouSheBiaoTiHei;
    }
  }

  .tipsName0 {
    color: #42A3FE;
  }

  .tipsName1 {
    color: #12B1BE;
  }

  .tipsName2 {
    color: #CDB40C;

  }

  .tipsName3 {
    color: #0043FF;

  }
}
</style>
 
