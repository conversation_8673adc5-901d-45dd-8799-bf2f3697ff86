import * as echarts from 'echarts/core'
import { el } from 'element-plus/es/locale'

const options =(params:any)=>{
  let dataAxis=params&&params?.map((item:object)=>item?.name)||[]
  let dataValue=params&&params?.map((item:object)=>item?.value )||[]


  return {
  title:{
    text:'',
    x:'center',
    y:'center',
    textStyle:{
      fontSize:14,
  
    }
  },
    tooltip: {
      trigger: 'axis',
      extraCssText: 'background: linear-gradient(to right,#286CE9 0%, #01D1FF 100%); color: white; border: 0px ',
      // borderColor: 'red', // 边框颜色
      borderWidth: 0, // 边框宽度
      textStyle: {
        color: 'white', // 文本颜色
        fontSize: 14, // 文本字号
      },
      axisPointer: {
        type: 'shadow'
      },
      formatter:( params:any)=>{
        // console.log('params',params)
        let data=`<div> <div>${params[0].name}:${params[0].value}万元</div></div>`
        return data
      }
    },
  
    grid: {
      left: '3%',
      right: '6%',
      bottom: '2%',
      top:'20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dataAxis,
      // axisLabel: {
      //   inside: false,
      // },
      textStyle: {
        color: '#fff',
        fontSize: 8,
      },
      axisTick: {
        alignWithLabel: true
      },
      splitLine: {
        show: false, // 隐藏x轴分隔线
      },
      axisLabel: {
        color: "#b5c1d180",
        interval:0,
        rotate:-65,	
        fontSize: 10 // 设置 x 轴文字的大小为 12 像素
      },
      nameTextStyle: {
        color: "#b5c1d166",
        fontSize: 8,
      },
      // nameGap:15,					//---坐标轴名称与轴线之间的距离
      // nameRotate:30,			//---坐标轴名字旋转

      // axisLine: {
      //   show: false
      // },
      // z: 10
    },
    yAxis: {
      splitLine: {
        show: false, // 隐藏x轴分隔线
      },
      type: 'value',
      axisLine: {
        show: true
      },
      name: "单位：万元",
      axisLabel: {
        color: "#b5c1d180",
        fontSize: 10 // 设置 x 轴文字的大小为 12 像素
       
      },
            nameTextStyle: {
          color: "#b5c1d166"
        },
    },
    // yAxis: [
  
    //   {
    //     type: "value",
    //     name: "万元",
    //     nameTextStyle: {
    //       color: "#b5c1d166"
    //     },
    //     splitLine: {
    //       show: true,
    //       lineStyle: {
    //         type: "solid",
    //         color: "#b5c1d11a"
    //       }
    //     },
    //     axisLabel: {
    //       color: "#b5c1d180"
    //     }
    //   }
    // ],
    dataZoom: [
      {
        type: 'inside'
      }
    ],

    series: [
      {
        type: 'bar',
        // showBackground: true,
        barWidth: 10, // 设置柱子宽度，单位为像素
        itemStyle: {
          color: new echarts.graphic.LinearGradient(
            0, 0, 0, 1,
            [
              {offset: 0, color: '#00A7F5'},
              {offset: 1, color: '#00F1F5'},
              // {offset: 0.5, color: '#188df0'},
            ]
          )
        },
        // emphasis: {
        //   itemStyle: {
        //     color: new echarts.graphic.LinearGradient(
        //       0, 0, 0, 1,
        //       [
        //         {offset: 0, color: '#2378f7'},
        //         {offset: 0.7, color: '#2378f7'},
        //         {offset: 1, color: '#83bff6'}
        //       ]
        //     )
        //   }
        // },
        data: dataValue
      }
    ]
  } 
}
export default  options
