<template>
  <div class="box viewScreen" :class="{ bg: recentTab !== '0' }">
    <div class="left">
      <div class="search" v-if="recentTab === '1' || recentTab === '2'">
        <div class="title">数据截止日期</div>
        <el-select
          v-model="value"
          class="selectDate"
          placeholder="请选择数据截止日期"
          @change="changeSelect"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div v-if="recentTab === '0'" class="leftInner">
        <Content
          :style="{ height: getRem(350) }"
          title="园区概况"
          type="inside"
          tab="0"
          current="left"
        >
          <Introduce :viewData="viewData?.parkOverview" />
        </Content>

        <Content
          :style="{ height: getRem(240) }"
          title="园区经营状况"
          tab="0"
          current="left"
        >
          <Revenue :viewData="viewData?.parkBusinessStatus" />
        </Content>
      </div>
      <div v-if="recentTab === '1' && showSecondList" class="leftInner">
        <Tab2LeftTop />
        <Tab2LeftMiddle />
        <Tab2LeftBottom />
      </div>
      <div v-if="recentTab === '3'" class="leftInner">
        <HomeLeft />
      </div>
      <div v-if="recentTab === '2'" class="leftInner">
        <EnergyLeft />
      </div>
    </div>
    <div class="view">
      <div class="topHeader">
        <span
          v-if="userInfo?.orgName"
          :style="{
            fontSize:
              userInfo?.orgName.length > 10
                ? screenWidth > 1600
                  ? '36px'
                  : '28px'
                : '50px',
          }"
          >{{ userInfo?.orgName }}
        </span>
        <span v-else>江苏省数字交通产业园</span>
      </div>
      <div class="header">
        <div
          v-for="(item, index) in headerList"
          :key="index"
          class="headerItem"
          :class="{ active: recentTab === item.index }"
          @click="changeTab(item.index)"
        >
          {{ item.name }}
        </div>
      </div>

      <div class="center" v-if="recentTab === '0'">
        <House :viewData="boxList" />
      </div>
      <div v-if="recentTab === '1'" class="center1">
        <CenterTab />
      </div>
      <div v-if="recentTab === '3'" class="center1">
        <HomeCenter />
      </div>
      <div v-if="recentTab === '2'" class="center1">
        <EnergyCenter />
      </div>
    </div>
    <div class="right">
      <div class="rightHeader">
        <div @click="signUp" class="jump chat" v-if="!tokenValue">注册</div>
        <!--<div @click="cooperation" class="jump chat"  v-if="!tokenValue" >合作咨询</div> -->
        <div
          v-if="!userInfo && !tokenValue"
          @click="jump"
          class="jump login text"
        >
          登录
        </div>
        <div class="jump" v-else>
          <el-dropdown :hide-on-click="false" @command="handleCommand">
            <span class="el-dropdown-link">
              {{ userInfo?.realName || ""
              }}<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <!-- <el-dropdown-item
                  v-for="item in newList"
                  :key="item.id"
                  :command="item.key"
                  >{{ item.label }}</el-dropdown-item
                > -->

                <!-- <el-dropdown-item  v-if="loginState==='user'"  command="3">在线办事</el-dropdown-item>
        <el-dropdown-item v-if="loginState==='admin'" command="1"> 工作台  </el-dropdown-item>
        <el-dropdown-item  command="5" > 统一用户管理 </el-dropdown-item>
        <el-dropdown-item command="4"> 个人中心</el-dropdown-item>-->
                <el-dropdown-item command="1">控制台首页</el-dropdown-item>
                <el-dropdown-item command="2"> 退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>

      <div v-if="recentTab === '0'" class="rightInner">
        <Content
          :style="{ height: getRem(230) }"
          title="园区重点企业分布"
          type="inside"
          tab="0"
          current="right"
        >
          <Industry :viewData="viewData?.parkEnterprise" />
        </Content>
        <Content
          :style="{ height: getRem(260) }"
          title="园区重点行业分布"
          type="inside"
          tab="0"
          current="right"
        >
          <Estate :viewData="viewData?.parkIndustry" />
        </Content>
        <Content
          :style="{ height: getRem(160) }"
          title="园区荣誉"
          type="inside"
          tab="0"
          current="right"
        >
          <Honor :viewData="viewData?.parkHonor" />
        </Content>
      </div>
      <div v-if="recentTab === '1'" class="rightInner">
        <Tab2RightTop :viewData="secondList" />
        <Tab2RightMiddle />
        <Tab2RightBottom />
      </div>
      <div v-if="recentTab === '3'" class="rightInner">
        <HomeRight />
      </div>
      <div v-if="recentTab === '2'" class="rightInner">
        <EnergyRight />
      </div>
    </div>
    <el-dialog
      v-model="centerDialogVisible"
      title=""
      align-center
      custom-class="cooperation"
      center
    >
      <cooperations
        v-if="centerDialogVisible"
        @cooperation2="cooperation2"
      ></cooperations>
    </el-dialog>
    <el-dialog
      v-model="signDialogVisible"
      width="1200px"
      :before-close="handleClose"
      custom-class="screenTable"
      class="screenTable"
      style="height: 550px"
      :close-on-click-modal="false"
      center
    >
      <SignUp
        :signDialogVisible="'signDialogVisible'"
        @handleClose="handleClose"
        ref="signRef"
      />
    </el-dialog>
    <el-dialog
      v-model="dialogVisible"
      width="700"
      :before-close="handleClose"
      :close-on-click-modal="false"
      custom-class="loginDialog login"
      center
    >
      <Login :dialogVisible="dialogVisible" />
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import {
  defineComponent,
  onMounted,
  computed,
  onBeforeUnmount,
  ref,
  onBeforeMount,
  watch,
} from "vue";
import { useStore } from "vuex";
import { useRoute, useRouter } from "vue-router";
import cooperations from "./components/cooperation/index.vue";
import Content from "./components/content/index.vue";
import Revenue from "./components/revenue/index.vue";
import Introduce from "./components/introduce/index.vue";
import Honor from "./components/honor/index.vue";
import Estate from "./components/estate/index.vue";
import Industry from "./components/industry/index.vue";
import House from "./components/house/index.vue";
import CenterTab from "./components/industryTab/center/index.vue";
import Tab2LeftTop from "./components/industryTab/left/top/index.vue";
import Tab2LeftMiddle from "./components/industryTab/left/middle/index.vue";
import Tab2LeftBottom from "./components/industryTab/left/bottom/index.vue";
import Tab2RightTop from "./components/industryTab/right/top/index.vue";
import Tab2RightMiddle from "./components/industryTab/right/middle/index.vue";
import Tab2RightBottom from "./components/industryTab/right/bottom/index.vue";
import EnergyLeft from "./components/energy/left/index.vue";
import EnergyRight from "./components/energy/right/index.vue";
import EnergyCenter from "./components/energy/center/index.vue";

import HomeRight from "./components/home/<USER>/index.vue";
import HomeCenter from "./components/home/<USER>/index.vue";
import HomeLeft from "./components/home/<USER>/index.vue";
import dayjs from "dayjs";
import Login from "@/views/system/login.vue";
import { ElMessage } from "element-plus";
import { getRem, getVh } from "@/utils/rem.js";
import quarterOfYear from "dayjs/plugin/quarterOfYear";
import { ArrowDown } from "@element-plus/icons-vue";
import SignUp from "../../system/signUp.vue";
import { token } from "@/utils/utils";
import { setRem, setRootFontSize } from "@/utils/rems.js"; // 引入字体调整函数

// var quarterOfYear = require('dayjs/plugin/quarterOfYear')
dayjs.extend(quarterOfYear);
const tokenValue = ref("");
const store = useStore();
const route = useRoute();
const router = useRouter();
const userInfo = computed(() =>
  JSON.parse(localStorage.getItem("userInfo") || "{}")
);
const userState = computed(() => localStorage.getItem("userState") || "");
const newList = computed(() =>
  JSON.parse(localStorage.getItem("newList") || "{}")
);
//const mainUserInfo = storedUserInfo ? JSON.parse(storedUserInfo) : null;
// const energy=computed(()=> localStorage.getItem(energy)||'false')
let value = ref("");
let loginState = ref(localStorage.getItem("userState"));
// let loginState=computed(()=> store.state.user?.userState)
const dialogVisible = ref(localStorage.getItem("isLogin") === "true");
// const loginOuts = () => {
let options = ref([]);
const contentFullScreen = computed(() => store.state.app.contentFullScreen);
const headerList = ref([
  {
    name: "首页",
    index: "3",
  },
  {
    name: "园区总览",
    index: "0",
  },
]);
const showSecondList = ref(false);
const secondList = ref();
const screenWidth = ref(0);
const showThirdList = ref(false);
const thirdList = ref();
const recentTab = ref(!!route.query.tab ? route.query.tab : "3");
const signDialogVisible = ref();
const signRef = ref();
const signUp = () => {
  signDialogVisible.value = true;
  // router.push('/signUp')
};
const handleClose = () => {
  signDialogVisible.value = false;
  signRef.value.clear();
};
const centerDialogVisible = ref(false);
watch(
  () => route.query.tab,
  (newVal: any) => {
    // console.log('newVal',newVal)
    recentTab.value = !!newVal ? newVal : "3";
  },
  { deep: true }
);
watch(
  () => userInfo,
  (newVal: any) => {
    // debugger
    if (!newVal?.value || newVal?.value === "") {
      if (route.query.tab === "0") {
        router.push("/dashboard?tab=0");
      } else {
        router.push("/dashboard?tab=3");
      }
    }
  },
  { deep: true }
);
watch(
  () => loginState,
  (newVal: any) => {
    getTabs();
  },
  { deep: true }
);
watch(
  () => token(),
  (newVal: any) => {
    getTabs();
  },
  { deep: true }
);
onBeforeMount(() => {
  if (recentTab.value === "0") {
    getData();
  } else if (recentTab.value === "1") {
    getSecondLisData({
      year: null,
      season: null,
    });
  } else if (recentTab.value === "2") {
    getThirdLisData({
      year: null,
      season: null,
    });
  }
});
onMounted(() => {
  screenWidth.value = window.innerWidth;
  // store.dispatch('user/getjurisdiction')
  tokenValue.value = token();
  setFullScreen();
  getTimeData();
  // console.log('页面初始化');
  getTabs();
  // console.log(userInfo.value.orgName, "页面初始化===");
  if (!userInfo.value) {
    if (route.query.tab === "0") {
      router.push("/home?tab=0");
    } else {
      router.push("/home?tab=3");
    }
  }
  setShowLogin();
});
const getTabs = () => {
  headerList.value = [
    ...headerList.value,
    {
      name: "企业洞察",
      index: "1",
    },
    {
      name: "能耗统计",
      index: "2",
    },
  ];
};
const setFullScreen = () => {
  store.commit("app/contentFullScreenChange", true);
  recentTab.value = !!route.query.tab ? route.query.tab : "3";
};
const cooperation = () => {
  centerDialogVisible.value = true;
};
const cooperation2 = () => {
  centerDialogVisible.value = false;
};
const boxList = ref([]);
onBeforeUnmount(() => {
  store.commit("app/contentFullScreenChange", false);
});
const jump = () => {
  dialogVisible.value = true;
  sessionStorage.setItem("isLogin", "true");
};

const viewData = ref({});
// 园区总览 总数据
const getData = () => {
  store
    .dispatch("dashboard/getViewData")
    .then((res) => {
      viewData.value = res.data;
      boxList.value = res.data?.buildingStatus?.buildingStatus;
    })
    .finally(() => {});
};
// 企业洞察 总数据
const getSecondLisData = (params: any) => {
  store
    .dispatch("dashboard/getSecondViewData", params)
    .then((res) => {
      showSecondList.value = true;
      secondList.value = res.data;
      value.value = res.data?.reportTime;
    })
    .finally(() => {});
};
// 能耗统计 总数据
const getThirdLisData = (params: any) => {
  store
    .dispatch("dashboard/getThirdLisData", params)
    .then((res) => {
      showThirdList.value = true;
      thirdList.value = res.data;
      value.value = res.data?.reportTime;
    })
    .finally(() => {});
};
const changeTab = (index: string) => {
  // TODO

  /**
   * 校验登陆
   * 没登录不跳转
   * 登陆后其他操作
   *
   */

  // recentTab.value=index
  router.push(`/home?tab=${index}`);
  setRootFontSize();
};
const getTimeData = () => {
  let quarters = [];
  let currentQuarter = dayjs().quarter();
  let currentYear = dayjs().year();
  for (let i = 0; i < 11; i++) {
    quarters.push(`${currentYear} Q${currentQuarter}`);
    if (currentQuarter === 1) {
      currentYear--;
      currentQuarter = 4;
    } else {
      currentQuarter--;
    }
  }

  let data = quarters.slice(1, 10);
  options.value = data.map((item, index) => {
    let time = item.split(" ");
    return {
      label: `${time[0]}Q${time[1][1]}`,
      value: index,
      year: time[0],
      season: time[1][1],
    };
  });
};
const changeSelect = (e) => {
  if (recentTab.value === "1") {
    getSecondLisData({
      year: options.value[e].year,
      season: options.value[e].season,
    });
  } else if (recentTab.value === "2") {
    getThirdLisData({
      year: options.value[e].year,
      season: options.value[e].season,
    });
  }
};

const handleCommand = (command: string | number | object) => {
  if (command === "logOut" || command === "2") {
    store.dispatch("user/loginOut");
    router.push("/login");
  } else if (command === "1") {
    router.push("/");
  }
};
watch(
  () =>
    sessionStorage.getItem("loginOut") === "true" &&
    sessionStorage.getItem("status") === "401",
  () => {
    setShowLogin();
  },
  { deep: true }
);
const setShowLogin = () => {
  if (
    sessionStorage.getItem("loginOut") === "true" &&
    sessionStorage.getItem("status") === "401"
  ) {
    dialogVisible.value = true;
    sessionStorage.setItem("isLogin", "true");
    sessionStorage.setItem("loginOut", "false");
  }
};
</script>

<style lang="scss">
.viewScreen {
  .el-input__wrapper {
    background: #0d1e3d;
    box-shadow: 0 0 0 1px rgba(64, 129, 203, 0.8) inset;
    border: 1px solid #0d1e3d;
  }

  .el-select__popper {
    border: 1px solid #0d1e3d !important;
  }

  .el-select-dropdown {
    background: #0d1e3d;
    border-radius: 2px;
  }

  .el-popper.is-light .el-popper__arrow::before {
    background: #0d1e3d;
  }

  .el-select-dropdown__item.hover {
    background: #f5f7fa2e;
    color: #aaaeb6;
  }
}
</style>
<style lang="scss" scoped>
@import "./index.scss";
</style>
<style lang="scss">
.cooperation {
  width: 1200px;
  height: 930px;
  background: center / contain no-repeat
    url("@/assets/images/view/dialog/cooperation.png");
  .el-dialog__headerbtn {
    position: absolute;
    top: 8rem;
    right: 0;
    padding: 0;
    width: 3.38rem;
    height: 3.38rem;
    background: 0 0;
    border: none;
    outline: 0;
    cursor: pointer;
    font-size: var(--el-message-close-size, 16px);
  }
  .el-dialog--center .el-dialog__body {
    margin-top: 6rem !important;
    padding-top: 30px !important;
  }
}
</style>
<style  lang='scss'>
.empty {
  color: #ffffffb3;
  font-size: 14px;
  text-align: center;
  width: 400px;
  height: 170px;
  line-height: 170px;
}
.leftInner,
.rightInner {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  .main {
    width: 100%;
  }
}
.rightInner {
  .main {
    width: 100%;
  }
  align-content: space-between;
}
.center1 {
  .center {
    width: 100%;
    height: 100% !important;
    display: flex;
    flex-wrap: wrap;
    align-content: space-between;
    .centerTop,
    .main {
      width: 100%;
    }
  }
}
.selectDate {
  .el-select__wrapper {
    background: transparent !important;
    box-shadow: 0 0 0 1px #3370ff inset !important;
  }
}
</style>