<template>
  <div class="back" :class="type">
    <div class="account">
      <div class="accountHead">基本信息</div>
      <div class="accountBase">
        <div class="userPhoto">
          <img
            style="border-radius: 50%; width: 80px; height: 80px"
            :src="avatarURL"
          />
          <span class="edit" @click="editAvatar"> 修改头像 </span>
          <!-- <img src="@/assets/images/defaultHeard.png" alt=""/> -->
        </div>
        <div class="userBaseInfo">
          <div class="info">
            姓名: {{ dataList?.baseInfo?.realName || "-" }}
            <el-popover :visible="nameVisible" placement="bottom" :width="160">
              <el-input v-model="userInput" placeholder="请输入姓名" />
              <div style="text-align: right; margin-top: 10px">
                <el-button size="default" text @click="showPopover('name')"
                  >取消</el-button
                >
                <el-button size="default" type="primary" @click="submit('name')"
                  >确定</el-button
                >
              </div>
              <template #reference>
                <span class="edit" @click="showPopover('name')">修改</span>
              </template>
            </el-popover>
          </div>
          <div class="info">
            邮箱: {{ dataList?.baseInfo?.email || "-" }}
            <el-popover
              :visible="emailVisible"
              placement="bottom"
              :width="160"
              validate-event
            >
              <el-input v-model="emailInput" placeholder="请输入邮箱" />
              <div style="text-align: right; margin-top: 10px">
                <el-button size="default" text @click="showPopover('email')"
                  >取消</el-button
                >
                <el-button
                  size="default"
                  type="primary"
                  @click="submit('email')"
                  >确定</el-button
                >
              </div>
              <template #reference>
                <span class="edit" @click="showPopover('email')">修改</span>
              </template>
            </el-popover>
          </div>
          <div class="info">手机: {{ dataList?.baseInfo?.phone || "-" }}</div>
          <div class="info" v-if="sessionState === 'user'">
            账号申请日期：{{ dataList?.baseInfo?.applyDate || "-" }}
          </div>
          <div class="info" v-if="sessionState === 'user'">
            账号开通日期：{{ dataList?.baseInfo?.openingDate || "-" }}
          </div>
          <div class="info" v-if="sessionState === 'admin'">
            角色：{{ dataList?.baseInfo?.roleName || "-" }}
          </div>
          <div class="info" v-if="sessionState === 'admin'">
            行政区划：江苏省
          </div>
          <div class="info" v-if="sessionState !== 'user'">
            所属部门：{{ dataList?.baseInfo?.deptName || "-" }}
          </div>
          <div class="info" v-if="sessionState !== 'user'">
            职位：{{ dataList?.baseInfo?.position || "-" }}
          </div>
          <div class="info"></div>
        </div>
        <!-- </div> -->
      </div>
    </div>
    <div class="account" v-if="sessionState === 'user'">
      <div class="accountHead">
        企业信息
        <span class="edit" @click="enterpriseEdit('enterpriseEdit')">编辑</span>
      </div>
      <div class="accountBase">
        <div class="userBaseInfo">
          <div class="info">
            企业名称: {{ dataList?.enterpriseInfo?.enterpriseName || "-" }}
          </div>
          <div class="info">
            统一社会信用代码:{{ dataList?.enterpriseInfo?.uniCode || "-" }}
          </div>
          <div class="info">
            房源位置:{{ dataList?.enterpriseInfo?.enterpriseAddress || "-" }}
          </div>
          <div class="info">
            注册资本:{{ dataList?.enterpriseInfo?.registeredCapital || "-" }}
          </div>
          <div class="info">
            行业类别:{{ dataList?.enterpriseInfo?.industryTypeDesc || "-" }}
          </div>
          <div class="info">
            荣誉资质:{{ dataList?.enterpriseInfo?.honors || "-" }}
          </div>
          <div class="info">
            企业简介:{{ dataList?.enterpriseInfo?.desc || "-" }}
          </div>
        </div>
      </div>
    </div>
    <div class="account">
      <div class="accountHead">密码保护</div>
      <div class="accountBase">
        <div class="userBaseInfo">
          <div class="info">
            密码:********<span
              class="edit"
              @click="enterpriseEdit('passwordEdit')"
              >修改</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>
  <AvatarCropper
    :img="avatarURL"
    v-if="dialogVisible"
    :dialogVisible="dialogVisible"
    @closeAvatarDialog="closeAvatarDialog"
  ></AvatarCropper>
  <Enterprise
    ref="enterpriseRef"
    :dataList="dataList"
    :dialogVisible="enterpriseVisible"
    @hideEnterprise="hideEnterprise"
    :state="state"
  />
</template>
<script setup>
import { ref, reactive, defineProps, onMounted, computed } from "vue";
import { VueCropper } from "vue-cropper";
import AvatarCropper from "./components/avator.vue";
import Enterprise from "./components/enterpriseEdit.vue";
import Imgs from "@/assets/images/online/icon13.png";

import { getUser, setEmail } from "@/api/user";
const dataList = ref();
const userInput = ref();
const emailInput = ref();
let sessionState = computed(() => localStorage.getItem("userState"));
onMounted(() => {
  getUserList();
});

const getUserList = () => {
  getUser()
    .then((result) => {
      dataList.value = result.data;
      if (result.data.baseInfo.showHeadImg) {
        avatarURL.value = result.data.baseInfo.showHeadImg;
      }
    })
    .catch((err) => {});
};
let props = defineProps(["type"]);

let enterpriseRef = ref();
let state = ref("enterpriseEdit");
const input = ref("");
const nameVisible = ref(false);
const emailVisible = ref(false);
let enterpriseVisible = ref(false);

const formInline = reactive({
  user: "",
  region: "",
  date: "",
});
const avatarURL = ref(Imgs);
let dialogVisible = ref(false);
// 关闭弹框所触发的事件
const closeAvatarDialog = (data) => {
  dialogVisible.value = false;
  getUserList();
};

const editAvatar = () => {
  emailVisible.value = false;
  nameVisible.value = false;
  dialogVisible.value = true;
  //   option.value.img = avatarURL.value
};
const showPopover = (type) => {
  if (type === "name") {
    nameVisible.value = !nameVisible.value;
    emailVisible.value = false;
  } else {
    nameVisible.value = false;
    emailVisible.value = !emailVisible.value;
  }
  userInput.value = dataList.value.baseInfo?.realName;
  emailInput.value = dataList.value.baseInfo?.email;
};
const submit = (type) => {
  let data = {
    email: emailInput.value,
    realName: userInput.value,
  };

  setEmail(data).then((res) => {
    getUserList();
  });

  type === "name" ? (nameVisible.value = false) : (emailVisible.value = false);
};
const enterpriseEdit = (type) => {
  emailVisible.value = false;
  nameVisible.value = false;
  enterpriseVisible.value = true;
  state.value = type;
  // TODO
  // enterpriseData=
};
const hideEnterprise = () => {
  enterpriseVisible.value = false;
  getUserList();
};
</script>

<style scoped lang="scss">
.userCenter.back {
  background: #f8f9fc;
  padding: 0;
}
.back {
  width: 100%;
  height: 100%;
  background: #f0f2f5;
  padding: 15px;
}
.account {
  margin-bottom: 15px;
  border-radius: 5px;
  background: #fff;
}
.accountHead {
  height: 66px;
  border-bottom: 1px solid #dddddd;

  font-size: 14px;
  font-weight: 500;
  color: #000000;
  line-height: 66px;
  padding: 0 15px;
}
.accountBase {
  padding: 30px;
}
.previewBox {
  text-align: center;
  margin-left: 60px;
}

.preview {
  width: 150px;
  height: 150px;
  margin: 0px auto 20px auto;
  border-radius: 50%;
  border: 1px solid #ccc;
  background-color: #ccc;
  overflow: hidden;
}

.cropper {
  width: 260px;
  height: 260px;
}
.userPhoto {
  display: flex;
  align-items: center;
  /* line-height: 120px; */
}
.userBaseInfo {
  margin: 15px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  .info {
    width: 30%;
    margin-bottom: 15px;
  }
}
.edit {
  padding-left: 10px;
  font-weight: 400;
  cursor: pointer;
  color: #437bff;
}
</style>
