
<template>
      <el-dialog
      :close-on-click-modal="false"
      v-model="dialogTableVisible" :title="state==='enterpriseEdit'?'编辑':'密码设置'" width="500">
        <div v-if="state==='enterpriseEdit'">
    <el-form :model="form"   :label-width="formLabelWidth" >
      <el-form-item label="荣誉资质"  >
        <el-input
        v-model="form.name"
    :autosize="{ minRows: 4, maxRows: 6 }"
    type="textarea"
    placeholder="请输入荣誉资质"

  />
      </el-form-item>
      <el-form-item label="企业简介">
        <el-input
        v-model="form.nameText"
        :autosize="{ minRows: 4, maxRows: 6 }"
    type="textarea"
    placeholder="请输入企业简介"
  />
      </el-form-item>
    </el-form>
  </div>
  <div v-else>
  
<Password  ref="passwordRef" :cancel='cancel'/>
  </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click= "cancel" size="default">取消</el-button>
        <el-button type="primary"  size="default" @click="submit()">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref ,defineProps,defineEmits,watch} from 'vue'
import Password from './password.vue'
import {setIndustry} from '@/api/user'
import {
      ElMessage
  } from 'element-plus'
let props=defineProps(['dialogVisible','state','dataList'])
let emit=defineEmits(['hideEnterprise'])
const dialogTableVisible = ref(props.dialogVisible)
watch(() => props.dialogVisible, (newVal) => {
  dialogTableVisible.value=newVal
  form.name= props.dataList?.enterpriseInfo?.honors
  form.nameText= props.dataList?.enterpriseInfo?.desc
}, { deep: true })
// const dialogFormVisible = ref(false)
const formLabelWidth = '90px'
let passwordRef=ref()
const form = reactive({
  name: '',
  nameText: '',
  
})
const cancel=()=>{
  emit('hideEnterprise')
}
const submit = () => {
 props.state==='enterpriseEdit'?save():
 passwordRef.value.confirm()
};
const save=()=>{
// debugger
// console.log(props.dataList?.enterpriseInfo?.enterpriseId)
  // 企业信息
  let data={
    "businessCode": "enterprise",
    "action": "update",
    "element": {
        "honors": form.name,
        "desc": form.nameText
    },
    "conditions": [
        {
            "key": "id",
            "value": props.dataList?.enterpriseInfo?.enterpriseId
        }
    ]
}
  setIndustry(data).then(res=>{
    ElMessage.success( res.msg);
    cancel()
  })

}
 </script>
<style scoped>
.el-button--text {
  margin-right: 15px;
}
.el-select {
  width: 300px;
}
.el-input {
  width: 300px;
}
.dialog-footer button:first-child {
  margin-right: 10px;
}
</style>