<script setup>
// import HelloWorld from './components/HelloWorld.vue'
// import TheWelcome from './components/TheWelcome.vue'
import AmisComponent from '@/components/amis/index.vue';
import {
  downloadByCondition,
  downloadTemplate,
  enterprisebusiness,
} from '@/api/updata';
import ApprovalRecord from '@/components/dialog/ApprovalRecord.vue';
import { enterpriseData, transformObject } from '@/utils/enterpriseFormData';
import { ref, onMounted } from 'vue';

const dialogVisible = ref(false);
const approvalRecordData = ref([]);
const canleDig = () => {
  approvalRecordData.value = [];
  dialogVisible.value = false;
};
const openExamine = async (item) => {
  const data = {
    businessScope: 'enterprise', //类型 企业、租金、租约
    //"approveId": item.data.id,//消息id
    relationId: item.data.id, //原id
  };
  // const res = await approvalListAPI(data)
  approvalRecordData.value = data;
  dialogVisible.value = true;
};

function getTimes() {
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1; // 月份从0开始，所以加1
  const options = [];

  for (let year = 2022; year <= currentYear; year++) {
    options.push({ label: `${year}上半年`, value: `${year}上半年` });
    options.push({ label: `${year}下半年`, value: `${year}下半年` });
    options.push({ label: `${year}全年`, value: `${year}全年` });

    // 如果当前月份小于7月，则不展示当年的下半年选项和全年选项
    if (year === currentYear && currentMonth < 7) {
      options.pop(); // 移除全年选项
      options.pop(); // 移除下半年选项
    }
  }
  return options;
}
function extractValuesAndConvert(obj, keysToExtract) {
  // 提取指定键的值
  const extracted = keysToExtract.reduce((acc, key) => {
    if (obj?.hasOwnProperty(key)) {
      acc[key] = obj[key];
    }
    return acc;
  }, {});

  // 构建剩余数据对象
  const remaining = Object.keys(obj).reduce((acc, key) => {
    if (!keysToExtract.includes(key)) {
      acc[key] = obj[key];
    }
    return acc;
  }, {});

  // 转换为格式化的JSON字符串
  const remainingJSON = JSON.stringify(remaining, null, 2);

  return [extracted, remainingJSON];
}
let amisjson = ref(null);
// 企业列表
const amisjson3 = {
  type: 'page',
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: '0px 0px 0px 0px transparent',
  },
  body: [
    {
      type: 'tpl',
      inline: true,
      wrapperComponent: '',
      syncLocation: false,
      id: 'u:3345e187f2df',
      style: {
        boxShadow: '0px 0px 0px 0px transparent',
        fontWeight: '500',
        lineHeight: '1.7',
        fontSize: '20px',
      },
    },
    {
      type: 'crud',
      syncLocation: false,
      name: 'tab-s',
      api: {
        method: 'post',
        url: `enterprise/getListPage`,
        adaptor: function (payload, response, api, context) {
          const newElements = response.data.elements.map((element) => {
            const { other, ...rest } = element;
            return { ...rest, ...other };
          });
          response.data.elements = newElements;
          return {
            data: response.data,
            msg: '请求成功',
            status: 0,
          };
        },
        data: {
          businessCode: 'enterprise',
          conditions: [
            {
              key: 'name',
              compareType: 'like',
              value: '${name}',
            },
            {
              key: 'uni_code',
              compareType: 'like',
              value: '${uni_code}',
            },
            {
              key: 'industry_type',
              compareType: 'eq',
              value: '${industry_type}',
            },
            {
              key: 'enterprise_type',
              compareType: 'eq',
              value: '${enterprise_type}',
            },
            {
              key: 'rent_status',
              compareType: 'eq',
              value: '${rent_status}',
            },
          ],
          pageNum: '${page}',
          pageSize: '${perPage}',
        },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: 'table',
      // 在表格头部添加统计信息
      headerToolbar: [
        // 'filter-toggler',
        'columns-toggler',
        {
          type: 'service',
          api: {
            method: 'post',
            url: 'enterprise/getStat',
            data: {
              businessCode: 'enterprise',
              conditions: [
                {
                  key: 'name',
                  compareType: 'like',
                  value: '${name}',
                },
                {
                  key: 'uni_code',
                  compareType: 'like',
                  value: '${uni_code}',
                },
                {
                  key: 'industry_type',
                  compareType: 'eq',
                  value: '${industry_type}',
                },
                {
                  key: 'enterprise_type',
                  compareType: 'eq',
                  value: '${enterprise_type}',
                },
                {
                  key: 'rent_status',
                  compareType: 'eq',
                  value: '${rent_status}',
                },
              ],
            },
            trackExpression:
              '${name}-${uni_code}-${industry_type}-${enterprise_type}-${rent_status}',
          },
          body: [
            {
              type: 'tpl',
              tpl: `
                <div style="background: #ffffff; color: #333; ">
                  <div style="display: grid; grid-template-columns: repeat(7, 1fr); gap: 15px;  ">
                    <div style="text-align: center; background: rgba(102, 126, 234, 0.1); height: 75px; border-radius: 8px; display: flex; flex-direction: column; justify-content: center; border: 1px solid rgba(102, 126, 234, 0.2);">
                      <div style="font-size: 20px; font-weight: bold; color: #667eea; margin-bottom: 5px; white-space: nowrap;">\${totalCnt || 0}</div>
                      <div style="font-size: 14px; color: #666; white-space: nowrap; font-weight: 500;">企业总数</div>
                    </div>
                    <div style="text-align: center; background: rgba(102, 126, 234, 0.1);border-radius: 8px; display: flex; flex-direction: column; justify-content: center; border: 1px solid rgba(102, 126, 234, 0.2);">
                      <div style="font-size: 20px; font-weight: bold; color: #667eea; margin-bottom: 5px; white-space: nowrap;">\${onScaleCnt || 0}</div>
                      <div style="font-size: 14px; color: #666; white-space: nowrap;font-weight: 500;">规上企业数</div>
                    </div>
                    <div style="text-align: center; background: rgba(102, 126, 234, 0.1);border-radius: 8px; display: flex; flex-direction: column; justify-content: center; border: 1px solid rgba(102, 126, 234, 0.2);">
                      <div style="font-size: 20px; font-weight: bold; color: #667eea; margin-bottom: 5px; white-space: nowrap;">\${highTechCnt || 0}</div>
                      <div style="font-size: 14px; color: #666; white-space: nowrap;font-weight: 500;">高新企业数</div>
                    </div>
                    <div style="text-align: center; background: rgba(102, 126, 234, 0.1);border-radius: 8px; display: flex; flex-direction: column; justify-content: center; border: 1px solid rgba(102, 126, 234, 0.2);">
                      <div style="font-size: 18px; font-weight: bold; color: #667eea; margin-bottom: 5px; white-space: nowrap;">\${lastYearValue || 0}万</div>
                      <div style="font-size: 12px; color: #666; white-space: nowrap;font-weight: 500;">上年度总产值</div>
                    </div>
                    <div style="text-align: center; background: rgba(102, 126, 234, 0.1);border-radius: 8px; display: flex; flex-direction: column; justify-content: center; border: 1px solid rgba(102, 126, 234, 0.2);">
                      <div style="font-size: 18px; font-weight: bold; color: #667eea; margin-bottom: 5px; white-space: nowrap;">\${currentYearValue || 0}万</div>
                      <div style="font-size: 12px; color: #666; white-space: nowrap;font-weight: 500;">本年度预计总产值</div>
                    </div>
                    <div style="text-align: center; background: rgba(102, 126, 234, 0.1);border-radius: 8px; display: flex; flex-direction: column; justify-content: center; border: 1px solid rgba(102, 126, 234, 0.2);">
                      <div style="font-size: 18px; font-weight: bold; color: #667eea; margin-bottom: 5px; white-space: nowrap;">\${lastYearIncome || 0}万</div>
                      <div style="font-size: 12px; color: #666; white-space: nowrap;font-weight: 500;">上年度主营业务收入</div>
                    </div>
                    <div style="text-align: center; background: rgba(102, 126, 234, 0.1);border-radius: 8px; display: flex; flex-direction: column; justify-content: center; border: 1px solid rgba(102, 126, 234, 0.2);">
                      <div style="font-size: 18px; font-weight: bold; color: #667eea; margin-bottom: 5px; white-space: nowrap;">\${currentYearIncome || 0}万</div>
                      <div style="font-size: 12px; color: #666; white-space: nowrap;font-weight: 500;">本年度预计主营业务收入</div>
                    </div>
                  </div>
                </div>
              `,
            },
          ],
        },
      ],
      columns: [
        {
          type: 'operation',
          label: '企业名称',
          fixed: 'left', // 固定在右侧
          buttons: [
            //查看
            {
              type: 'button',
              size: 'md',

              label: '${name}',
              level: 'link',
              actionType: 'drawer',
              drawer: {
                size: 'lg',
                title: '企业详情',
                actions: [],
               className:'enterpriseDetail ',
                body: [
                  {
                    type: 'form',
                    columnCount: 2,
                     labelWidth:100,
                    disabledOn: 'true',
                    className: 'form-b-sm',
                    rules: [],
                    body:  enterpriseData('preview'),
                  },
                ],
              },
            },
          ],
        },
        // {
        //   "name": "name",
        //   "label": "企业名称",
        //   "type": "text"
        // },
        {
          name: 'uni_code',
          label: '企业信用代码',
          type: 'text',
        },
        {
          type: 'tpl',
          name: 'industry_type',
          label: '行业分类',
          width: 100,
          tpl: "<span class='${industry_type==1 ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200\" : (industry_type==2 ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200\" : (industry_type==3 ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 border border-purple-200\" : (industry_type==4 ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-pink-100 text-pink-800 border border-pink-200\" : \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200\")))}'>${industry_type==1 ? '车联网' : (industry_type==2 ? '低空经济' : (industry_type==3 ? '数字基建' : (industry_type==4 ? '智能制造' : '其他' )))}</span>",
        },
        {
          type: 'tpl',
          name: 'enterprise_type',
          label: '企业类型',
          tpl: "<span class='${enterprise_type==5 ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 border border-indigo-200\" : (enterprise_type==4 ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200\" : (enterprise_type==3 ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200\" : (enterprise_type==2 ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200\" : (enterprise_type==1 ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200\" : \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200\"))))}'>${enterprise_type==5 ? '股份制' : (enterprise_type==4 ? '国有' : (enterprise_type==3 ? '民营' : (enterprise_type==2 ? '合资' : (enterprise_type==1 ? '独资' : '其他'))))}</span>",
        },

        {
          name: 'establish_date',
          label: '注册成立时间',
          width: 100,
          type: 'date',
          valueFormat: 'x',
        },
        {
          name: 'settle_date',
          label: '入驻日期',
          width: 100,
          type: 'date',
          valueFormat: 'x',
        },
        /* {
          "name": "address",
          "label": "所在房源",
          "type": "text"
        }, */
        {
          type: 'tpl',
          name: 'address',
          label: '所在房源',
          width: 150,
          tpl: "${address[0]}${address[1]}${address.length>2 ? '...' : ''}",
          popOver: {
            trigger: 'hover',
            position: 'left-top',
            showIcon: false,
            body: {
              type: 'tpl',
              tpl: '${address}',
            },
          },
        },
        {
          name: 'contacts',
          label: '联系人',
          type: 'text',
          width: 100,
        },
        {
          name: 'contacts_way_desensitization',
          label: '联系方式',
          type: 'text',
        },
        {
          type: 'tpl',
          name: 'rent_status',
          label: '入驻状态',
          tpl: "<span class='${rent_status===false ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200\" : \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200\"}'>${rent_status===false ? '未入驻' : '入驻中'}</span>",
        },
        {
          type: 'operation',
          label: '操作',
          fixed: 'right', // 固定在右侧
          width: 300,
          fixed: 'right', // 固定在右侧
          buttons: [
            //查看
            {
              type: 'button',
              level: 'primary',
              label: '查看',
              actionType: 'drawer',
              drawer: {
                size: 'lg',
                title: '企业详情',
                  className: 'enterpriseDetail',
                actions: [],
                body: [
                  {
                    type: 'form',
                    columnCount: 2,
                    labelWidth: 100,
                    disabledOn: 'true',
                    className: 'form-b-sm',
                    rules: [],
                    body:  enterpriseData('preview'),
                  },
                ],
              },
            },
            //编辑
            {
              type: 'button',

              label: '编辑',
              level: 'primary',
              disabledOn: 'is_editable == false',
              actionType: 'drawer',
              drawer: {
                size: 'lg',
                title: '编辑',
                className: 'enterpriseDetail',
                actions: [
                  {
                    type: 'button',
                    size: 'md',
                    actionType: 'cancel',
                    label: '取消',
                    level: 'default',
                    primary: true,
                  },
                  {
                    label: '确认',
                    actionType: 'confirm',
                    primary: true,
                    confirmText:
                      '点击编辑，经审批人审批后该企业记录进行信息更正，审批进程可在查看-审批记录中进行查看。是否确认修改？',
                    //"reload": "tab-s",
                    close: true,
                    type: 'button',
                    size: 'md',
                    api: {
                      method: 'post',
                      url: `enterprise/formSubmit/approve`,
                      messages: {
                        success: '操作成功！',
                      },
                      requestAdaptor: (rest) => {
                        let { ...other } = rest.data;
                        let datas = {
                          ...other,
                        };

                        // 转换入驻状态的布尔值为数字值
                        if (rest.context.hasOwnProperty('rent_status')) {
                          rest.context.rent_status =
                            rest.context.rent_status === 'true' ? 1 : 0;
                        }

                        const selectedKeys = [
                          'special_type',
                          'name',
                          'uni_code',
                          'industry_type',
                          'enterprise_type',
                          'is_high_tech',
                          'is_innovative',
                          'establish_date',
                          'settle_date',
                          'contacts',
                          'contacts_way',
                          'registered_capital',
                          'is_mount',
                          'honors',
                          'desc',
                          'rent_status',
                        ];
                        const [extractedData, jsonData] =
                          extractValuesAndConvert(rest.context, selectedKeys);
                        datas.element = extractedData;
                        datas.element.other = jsonData;
                        return {
                          ...rest,
                          data: datas,
                        };
                      },
                      data: {
                        businessCode: 'enterprise',
                        action: 'update',
                        element: {
                          '&': '$$',
                          contacts_way: '$contacts_way',
                        },
                        conditions: [
                          {
                            key: 'id',
                            value: '$id',
                          },
                        ],
                      },
                    },
                  },
                ],
                body: [
                  {
                    type: 'form',
                    columnCount: 2,
                    labelWidth: 100,
                  
                    rules: {},
                    className: 'form-b-sm',
                    body: enterpriseData('edit'),
                  },
                ],
              },
            },
            //删除
            {
              type: 'button',
              level: 'danger',
              actionType: 'ajax',
              label: '删除',
              disabledOn: 'is_editable == false',
              confirmText:
                '点击删除，经审批人审批后该企业进行删除，审批进程可在查看-审批记录中进行查看。是否确认删除？',
              api: {
                method: 'post',
                url: `enterprise/formSubmit/approve`,
                data: {
                  businessCode: 'enterprise',
                  action: 'delete',
                  conditions: [
                    {
                      key: 'id',
                      value: '${id}',
                    },
                  ],
                },
              },
            },
            {
              label: '审批记录',
              type: 'button',
              level: 'primary',
              // "actionType": "link",
              // "link": "#/workbench/lease/addfollow?id=$id",
              onClick: (e, item) => {
                openExamine(item);
              },
            },
          ],
        },
      ],
      alwaysShowPagination: true,
      // "autoFillHeight": false,
      autoFillHeight: {
        height: 450,
      },
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      filter: {
        title: '',
        submitText: '搜索',
        controls: [
          {
            type: 'text',
            name: 'name',
            label: '企业名称',
            size: 'md',
            placeholder: '请输入企业名称',
          },
          {
            type: 'text',
            name: 'uni_code',
            label: '企业信用代码',
            size: 'md',
            placeholder: '请输入企业信用代码',
          },
          {
            type: 'select',
            name: 'industry_type',
            label: '行业分类',
            size: 'md',
            placeholder: '请选择行业分类',
            value: '',
            options: [
              { label: '车联网', value: '1' },
              { label: '低空经济', value: '2' },
              { label: '数字基建', value: '3' },
              { label: '智能制造', value: '4' },
              { label: '其他', value: '0' },
            ],
          },
          {
            type: 'select',
            name: 'enterprise_type',
            size: 'md',
            //"multiple": true,
            label: '企业类型',
            placeholder: '请选择企业类型',
            value: '',
            options: [
              { label: '独资', value: '1' },
              { label: '合资', value: '2' },
              { label: '民营', value: '3' },
              { label: '国有', value: '4' },
              { label: '股份制', value: '5' },
              { label: '其他', value: '0' },
            ],
          },
          {
            type: 'select',
            name: 'rent_status',
            label: '入驻状态',
            size: 'md',
            placeholder: '请选择入驻状态',
            value: '',
            options: [
              { label: '全部', value: '' },
              { label: '入驻中', value: '1' },
              { label: '未入驻', value: '0' },
            ],
          },
          {
            type: 'reset',
            label: '重置',
          },
          {
            type: 'submit',
            label: '搜索',
            primary: true,
          },
          {
            type: 'button',
            size: 'md',
            label: '一键导出',
            onClick: function (e, props) {
              // console.log(props);
              // 获取当前筛选项的值
              const filterData = props.data || {};
              const conditions = [
                {
                  key: 'name',
                  compareType: 'like',
                  value: filterData.name || '',
                },
                {
                  key: 'uni_code',
                  compareType: 'like',
                  value: filterData.uni_code || '',
                },
                {
                  key: 'industry_type',
                  compareType: 'eq',
                  value: filterData.industry_type || '',
                },
                {
                  key: 'enterprise_type',
                  compareType: 'eq',
                  value: filterData.enterprise_type || '',
                },
                {
                  key: 'rent_status',
                  compareType: 'eq',
                  value: filterData.rent_status || '',
                },
              ];
              downloadByCondition({
                businessCode: 'enterprise',
                conditions,
              }).then((res) => {
                let blob = new Blob([res], {
                  type: 'text/csv,charset=UTF-8',
                });
                let objectUrl = URL.createObjectURL(blob);
                const fileName = '企业数据导出.xlsx';
                const downloadLink = document.createElement('a');
                downloadLink.href = objectUrl;
                downloadLink.download = fileName;
                downloadLink.click();
              });
            },
          },
        ],
      },
    },
  ],
};
// 企业上报数据
const amisjson2 = {
  type: 'page',
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: '0px 0px 0px 0px transparent',
  },

  body: [
    {
      type: 'tpl',
      inline: true,
      wrapperComponent: '',
      syncLocation: false,
      id: 'u:3345e187f2df',
      style: {
        boxShadow: '0px 0px 0px 0px transparent',
        fontWeight: '500',
        lineHeight: '1.7',
        fontSize: '20px',
      },
    },
    {
      type: 'crud',
      syncLocation: false,
      name: 'tab-s',
      api: {
        method: 'post',
        url: `enterprise/business/getListPage`,
        data: {
          businessCode: 'enterprise_business',
          conditions: [
            {
              key: 'enterprise_name',
              compareType: 'like',
              value: '${enterprise_name}',
            },
            {
              key: 'uni_code',
              compareType: 'like',
              value: '${uni_code}',
            },
            {
              key: 'filling_range',
              compareType: 'like',
              value: '${filling_range}',
            },
            {
              key: 'type',
              value: '0',
            },
          ],
          pageNum: '${page}',
          pageSize: '${perPage}',
        },
        adaptor: function (payload, response, api, context) {
          response.data.elements.forEach((it) => {
            if (it.attachments_detail && it.attachments_detail.length > 0) {
              let fileLists = it.attachments_detail.map((it) => {
                return {
                  name: it.name,
                  value: it.oss_path,
                  url: it.value,
                  state: 'uploaded',
                };
              });
              it.attachment = fileLists;
            }
          });
          return {
            data: response.data,
            msg: '请求成功',
            status: 0,
          };
        },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: 'table',
      columns: [
        {
          name: 'enterprise_name',
          label: '企业名称',
          type: 'text',
        },
        {
          name: 'filling_range',
          label: '填报日期',
          type: 'text',
        },
        {
          name: 'output_value',
          label: '产值(万元)',
          type: 'text',
        },
        {
          name: 'tax',
          label: '年度税收(万元)',
          type: 'text',
        },
        {
          name: 'profit',
          label: '利润(万元)',
          type: 'text',
        },
        {
          name: 'rd_investment',
          label: '每年研发投入(万元)',
          type: 'text',
        },
        /*   {
            "name": "turnover",
            "label": "经营额(万元)",
            "type": "text"
          }, */
        {
          name: 'employee',
          label: '在职人员数',
          type: 'text',
        },
        {
          name: "${is_high_tech==1 ? '是' : '否'}",
          label: '是否高新',
          type: 'text',
        },
        {
          type: 'operation',
          label: '操作',
          fixed: 'right', // 固定在右侧
          width: 200,
          buttons: [
            //编辑
            {
              type: 'button',
              size: 'md',
              label: '编辑',
              level: 'primary',
              actionType: 'dialog',
              dialog: {
                title: '编辑',
                size: 'lg',
                actions: [
                  {
                    type: 'button',
                    size: 'md',
                    actionType: 'cancel',
                    label: '取消',
                    level: 'default',
                    primary: true,
                  },
                  {
                    label: '确认',
                    actionType: 'confirm',
                    primary: true,
                    //"reload": "tab-s",
                    close: true,
                    type: 'button',
                    size: 'md',
                    api: {
                      method: 'post',
                      messages: {
                        success: '操作成功！',
                      },
                      url: `enterprise/business/formSubmit`,
                      requestAdaptor: (rest) => {
                        let { ...other } = rest.data;
                        let datas = {
                          ...other,
                        };
                        if (Array.isArray(datas.element.attachment)) {
                          datas.element.attachment =
                            datas?.element?.attachment
                              ?.map((item) => item.oss_path)
                              ?.join(',') || '';
                        }
                        return {
                          ...rest,
                          data: datas,
                        };
                      },
                      data: {
                        businessCode: 'enterprise_business',
                        action: 'update',
                        element: /*  '$$' */ {
                          //"filling_range": "$filling_range",
                          output_value: '$output_value',
                          tax: '$tax',
                          //"turnover": "$turnover",
                          rd_investment: '$rd_investment',
                          main_business: '$main_business',
                          is_high_tech: '$is_high_tech',
                          employee: '$employee',
                          profit: '$profit',
                          talents_num: '$talents_num',
                          pro_title_status: '$pro_title_status',
                          main_development: '$main_development',
                          patents_achieve: '$patents_achieve',
                          attachment: '$attachment',
                        },
                        conditions: [
                          {
                            key: 'id',
                            value: '$id',
                          },
                        ],
                      },
                    },
                  },
                ],
                body: [
                  {
                    type: 'form',
                    columnCount: 2,
                    labelWidth: 150,
                    rules: {},
                    body: [
                      {
                        type: 'select',
                        name: 'filling_range',
                        label: '填报范围',
                        size: 'lg',
                        required: true,
                        labelClassName: 'text-muted',
                        disabledOn: 'true',
                        inline: true,
                        source: {
                          url: `enterprise/business/getFillingRanges?fillingRange=$filling_range&type=1`,
                          adaptor: function (payload, response, api, context) {
                            return {
                              data: payload.options,
                              msg: '请求成功',
                              status: 0,
                            };
                          },
                        },
                      },
                      {
                        type: 'select',
                        name: 'uni_code',
                        label: '企业名称',
                        labelClassName: 'text-muted',
                        placeholder: '请选择企业',
                        required: true,
                        disabledOn: 'true',
                        initFetchOn: 'data.filling_range',
                        source: {
                          url: `enterprise/business/getEnterpriseList?fillingRange=$filling_range&uniCode=$uni_code`,
                        },
                      },
                      {
                        type: 'input-number',
                        placeholder: '输入企业产值',
                        name: 'output_value',
                        label: '产值(万元)',
                        required: true,
                      },
                      {
                        type: 'input-number',
                        name: 'tax',
                        label: '税收(万元)',
                        placeholder: '输入企业税收',
                        required: true,
                      },
                      {
                        type: 'input-number',
                        name: 'profit',
                        label: '利润(万元)',
                        placeholder: '输入本年度利润',
                        //"required": true,
                      },
                      {
                        type: 'input-number',
                        name: 'rd_investment',
                        label: '研发投入(万元)',
                        placeholder: '输入年度研发投入金额',
                        //"required": true,
                      },
                      {
                        name: 'is_high_tech',
                        type: 'radios',
                        label: '是否高新',
                        options: [
                          {
                            label: '是',
                            value: '1',
                          },
                          {
                            label: '否',
                            value: '0',
                          },
                        ],
                      },
                      {
                        type: 'input-text',
                        name: 'main_business',
                        label: '主营业务',
                        placeholder: '请输入企业主营业务',
                      },
                      {
                        type: 'input-number',
                        name: 'employee',
                        label: '在职人数',
                        placeholder: '输入企业本季度在职人数',
                      },
                      {
                        type: 'input-number',
                        name: 'talents_num',
                        label: '人才数量',
                        placeholder: '输入人才数量(硕士及以上)',
                      },
                      {
                        name: 'pro_title_status',
                        type: 'textarea',
                        label: '职称情况',
                        maxLength: 200,
                        placeholder:
                          '请输入职称情况(中高级及以上),最多输入200字',
                      },
                      {
                        name: 'main_development',
                        type: 'textarea',
                        label: '未来主要发展方向',
                        maxLength: 200,
                        placeholder: '输入未来主要发展方向,最多输入200字',
                      },
                      {
                        name: 'patents_achieve',
                        type: 'textarea',
                        label: '技术成果或专利情况',
                        maxLength: 200,
                        placeholder: '输入技术成果或专利情况,最多输入200字',
                      },
                      {
                        type: 'input-file',
                        name: 'attachment',
                        label: '上传附件',
                        //downloadUrl: false,
                        useChunk: false,
                        maxSize: 20990000,
                        accept:
                          '.jpg,.png,.gif,.psd,.tif,.bmp,.txt,.world,.pdf,.Excel,.doc,.docx,.wps,.xlsx,.xls,.et,.ett,.xlt,.dps,.dpt,.ppt,.pptx,.zip,.rar,.7z,.tar,.gz,.bz2,.xz',
                        multiple: true,
                        maxLength: 10,
                        receiver: {
                          method: 'POST',
                          url: 'upload',
                          adaptor: (payload) => {
                            return {
                              status: 0,
                              msg: '请求成功',
                              data: {
                                value: payload.value,
                                name: payload.fileName,
                                url: payload.url,
                              },
                            };
                          },
                        },
                        //"asBlob": true
                      },
                    ],
                  },
                ],
              },
            },
            {
              type: 'button',
              level: 'danger',
              actionType: 'ajax',
              label: '删除',
              confirmText: '是否确认删除',
              api: {
                method: 'post',
                url: `enterprise/business/formSubmit`,

                data: {
                  businessCode: 'enterprise_business',
                  action: 'delete',
                  conditions: [
                    {
                      key: 'id',
                      value: '${id}',
                    },
                  ],
                },
              },
            },
          ],
        },
      ],
      alwaysShowPagination: true,
      // "autoFillHeight": false,
      autoFillHeight: {
        height: 450,
      },
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      filter: {
        title: '',
        submitText: '搜索',
        controls: [
          {
            type: 'select',
            name: 'filling_range',
            label: '填报范围',
            size: 'md',
            source: {
              method: 'get',
              url: `enterprise/business/getFillingRanges?type=2`,
            },
          },
          {
            type: 'text',
            name: 'enterprise_name',
            label: '企业名称',
            size: 'md',
            placeholder: '请输入企业名称',
          },
          {
            type: 'text',
            name: 'uni_code',
            label: '企业信用代码',
            size: 'md',
            placeholder: '请输入企业信用代码',
          },
          {
            type: 'reset',
            label: '重置',
          },
          {
            type: 'submit',
            label: '搜索',
            primary: true,
          },
          {
            type: 'button',
            size: 'md',
            label: '新增数据',
            actionType: 'dialog',
            dialog: {
              title: '新增',
              size: 'lg',
              actions: [
                {
                  type: 'button',
                  size: 'md',
                  actionType: 'cancel',
                  label: '取消',
                  level: 'default',
                  primary: true,
                },
                {
                  label: '确认',
                  actionType: 'confirm',
                  primary: true,
                  reload: 'tab-s',
                  close: true,
                  type: 'button',
                  size: 'md',
                  api: {
                    method: 'post',
                    url: `enterprise/business/formSubmit`,
                    messages: {
                      success: '操作成功！',
                    },
                    data: {
                      businessCode: 'enterprise_business',
                      action: 'insert',
                      element: '$$',
                      conditions: [
                        {
                          key: 'id',
                          value: '$id',
                        },
                      ],
                    },
                  },
                },
                /*  {
                   "type": "input-file",
                   "name": "file",
                   "label": "文件上传",
                   "accept": "*",
                   "receiver": {
                     "url": `enterprise/business/uploadTemplate`,
                     "headers": {
                       "token": `${token()}`
                     },
                   }
                 } */
              ],
              onSubmit: 'reload',
              body: [
                {
                  type: 'form',
                  columnCount: 2,
                  initApi: {
                    method: 'get',
                    url: '/enterprise/business/getCache',
                    adaptor: function (payload, response, api, context) {
                      let data = {};
                      if (!payload || payload === true) {
                        data = {
                          is_high_tech: 0,
                        };
                      } else {
                        data = {
                          filling_range: payload.filling_range,
                          uni_code: '',
                          output_value: payload.output_value,
                          tax: payload.tax,
                          profit: payload.profit,
                          rd_investment: payload.rd_investment,
                          is_high_tech: payload.is_high_tech,
                          main_business: payload.main_business,
                          employee: payload.employee,
                          talents_num: payload.talents_num,
                          pro_title_status: payload.pro_title_status,
                          main_development: payload.main_development,
                          patents_achieve: payload.patents_achieve,
                          attachment: '',
                        };
                      }
                      return {
                        data,
                        msg: '请求成功',
                        status: 0,
                      };
                    },
                  },
                  rules: {},
                  body: [
                    {
                      type: 'select',
                      name: 'filling_range',
                      label: '填报范围',
                      required: true,
                      labelClassName: 'text-muted',
                      inline: true,
                      source: {
                        url: `enterprise/business/getFillingRanges?type=1`,
                        adaptor: function (payload, response, api, context) {
                          return {
                            data: payload.options,
                            msg: '请求成功',
                            status: 0,
                          };
                        },
                      },
                    },
                    {
                      type: 'select',
                      name: 'uni_code',
                      label: '企业名称',
                      labelClassName: 'text-muted',
                      searchable: true,
                      placeholder: '请选择企业',
                      required: true,
                      initFetchOn: 'data.filling_range',
                      source: {
                        url: `enterprise/business/getEnterpriseList?fillingRange=$filling_range`,
                      },
                    },
                    {
                      type: 'input-number',
                      placeholder: '输入企业产值',
                      name: 'output_value',
                      label: '产值(万元)',
                      required: true,
                    },
                    {
                      type: 'input-number',
                      name: 'tax',
                      label: '税收(万元)',
                      placeholder: '输入企业税收',
                      required: true,
                    },
                    {
                      type: 'input-number',
                      name: 'profit',
                      label: '利润(万元)',
                      placeholder: '输入本年度利润',
                      //"required": true,
                    },
                    {
                      type: 'input-number',
                      name: 'rd_investment',
                      label: '研发投入(万元)',
                      placeholder: '输入年度研发投入金额',
                      //"required": true,
                    },
                    {
                      name: 'is_high_tech',
                      type: 'radios',
                      label: '是否高新',
                      options: [
                        {
                          label: '是',
                          value: '1',
                        },
                        {
                          label: '否',
                          value: '0',
                        },
                      ],
                    },
                    {
                      type: 'input-text',
                      name: 'main_business',
                      label: '主营业务',
                      placeholder: '请输入企业主营业务',
                    },
                    {
                      type: 'input-number',
                      name: 'employee',
                      label: '在职人数',
                      placeholder: '输入企业本季度在职人数',
                    },
                    {
                      type: 'input-number',
                      name: 'talents_num',
                      label: '人才数量',
                      placeholder: '输入人才数量(硕士及以上)',
                    },
                    {
                      name: 'pro_title_status',
                      type: 'textarea',
                      label: '职称情况',
                      maxLength: 200,
                      placeholder: '请输入职称情况(中高级及以上),最多输入200字',
                    },
                    {
                      name: 'main_development',
                      type: 'textarea',
                      label: '未来主要发展方向',
                      maxLength: 200,
                      placeholder: '输入未来主要发展方向,最多输入200字',
                    },
                    {
                      name: 'patents_achieve',
                      type: 'textarea',
                      label: '技术成果或专利情况',
                      maxLength: 200,
                      placeholder: '输入技术成果或专利情况,最多输入200字',
                    },
                    {
                      type: 'input-file',
                      name: 'attachment',
                      label: '上传附件',
                      //downloadUrl: false,
                      useChunk: false,
                      maxSize: 20990000,
                      accept:
                        '.jpg,.png,.gif,.psd,.tif,.bmp,.txt,.world,.pdf,.Excel,.doc,.docx,.wps,.xlsx,.xls,.et,.ett,.xlt,.dps,.dpt,.ppt,.pptx,.zip,.rar,.7z,.tar,.gz,.bz2,.xz',
                      multiple: true,
                      maxLength: 10,
                      receiver: {
                        method: 'POST',
                        url: `upload`,
                      },
                      //"asBlob": true
                    },
                  ],
                },
              ],
            },
          },
          {
            type: 'button',
            size: 'md',
            label: '下载文件模板',
            /* "actionType": "download",
            "api": {
              "method": "get",
              "url": `enterprise/business/downloadTemplate`,
             
            } */
            onClick: function () {
              downloadTemplate().then((res) => {
                let blob = new Blob([res], {
                  type: 'text/csv,charset=UTF-8',
                });
                let objectUrl = URL.createObjectURL(blob);
                const fileName = '企业上报数据模板导出.xlsx';
                const downloadLink = document.createElement('a');
                downloadLink.href = objectUrl;
                downloadLink.download = fileName;
                downloadLink.click();
              });
            },
          },
          {
            type: 'button',
            size: 'md',
            label: '一键导出',
            /* "actionType": "download",
            "api": {
              "method": "get",
              "url": `enterprise/business/downloadAll`,
             
            } */
            onClick: function (e, content) {
              // console.log('导出', content.data);
              // year=xx&season=xx&enterpriseName=xx&uniCode=xx
              enterprisebusiness({
                year: content.data.filling_range?.slice(0, 4) || '',
                season: content.data.filling_range?.slice(4, 7) || '',
                enterpriseName: content.data.enterprise_name || '',
                uniCode: content.data.uni_code || '',
                type: 0,
              }).then((res) => {
                let blob = new Blob([res], {
                  type: 'text/csv,charset=UTF-8',
                });
                let objectUrl = URL.createObjectURL(blob);
                const fileName = '企业上报数据导出.xlsx';
                const downloadLink = document.createElement('a');
                downloadLink.href = objectUrl;
                downloadLink.download = fileName;
                downloadLink.click();
              });
            },
          },
          {
            type: 'input-file',
            downloadUrl: false,
            name: 'file',
            accept: '.xlsx',
            reload: 'tab-s',
            useChunk: false,
            maxSize: 20990000,
            receiver: {
              url: `enterprise/business/uploadTemplate`,
            },
            onEvent: {
              success: {
                actions: [
                  {
                    actionType: 'toast', // 执行toast提示动作
                    args: {
                      // 动作参数
                      msgType: 'success',
                      msg: '上传成功！',
                    },
                  },
                ],
              },
            },
          },
          /*   {
              "type": "input-file",
              "name": "file",
              "accept": ".xlsx",
              "reload": "tab-s",
              useChunk:false,
                    maxSize:20990000,
              "receiver": {
                "url": `enterprise/business/uploadTemplate`,
              }
            } */
          /*    ,
             {
               "type": "input-file",
               "name": "file",
               "accept": "*",
               "receiver": "/enterprise/business/uploadTemplate"
             } */
        ],
        // "actions": [
        //   {
        //     "type": "input-file",
        //     downloadUrl: false,
        //     "name": "file",
        //     "accept": ".xlsx",
        //     "reload": "tab-s",
        //     useChunk: false,
        //     maxSize: 20990000,
        //     "receiver": {
        //       "url": `enterprise/business/uploadTemplate`,

        //     },
        //     onEvent: {
        //       "success": {
        //         actions: [
        //           {
        //             "actionType": "toast", // 执行toast提示动作
        //             "args": { // 动作参数
        //               "msgType": "success",
        //               "msg": "上传成功！"
        //             }
        //           }
        //         ]
        //       }
        //     }
        //   }
        //   /* {
        //     "type": "button",
        size: 'md',
        //     "label": "一键导出",
        //     "actionType": "download",
        //     "api": `/enterprise/downloadAll`
        //   } */
        // ],
      },
    },
  ],
};
const amisjson4 = {
  type: 'page',
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: '0px 0px 0px 0px transparent',
  },

  body: [
    {
      type: 'tpl',
      inline: true,
      wrapperComponent: '',
      syncLocation: false,
      id: 'u:3345e187f2df',
      style: {
        boxShadow: '0px 0px 0px 0px transparent',
        fontWeight: '500',
        lineHeight: '1.7',
        fontSize: '20px',
      },
    },
    {
      type: 'crud',
      syncLocation: false,
      name: 'tab-s',
      api: {
        method: 'post',
        url: `enterprise/business/getListPage`,
        adaptor: function (payload, response, api, context) {
          response.data.elements.forEach((it) => {
            if (it.attachments_detail && it.attachments_detail.length > 0) {
              let fileLists = it.attachments_detail.map((it) => {
                return {
                  name: it.name,
                  value: it.oss_path,
                  url: it.value,
                  state: 'uploaded',
                };
              });
              it.attachment = fileLists;
            }
          });
          return {
            data: response.data,
            msg: '请求成功',
            status: 0,
          };
        },
        data: {
          businessCode: 'enterprise_business',
          conditions: [
            {
              key: 'enterprise_name',
              compareType: 'like',
              value: '${enterprise_name}',
            },
            {
              key: 'uni_code',
              compareType: 'like',
              value: '${uni_code}',
            },
            {
              key: 'filling_range',
              compareType: 'like',
              value: '${filling_range}',
            },
            {
              key: 'type',
              value: '1',
            },
          ],
          pageNum: '${page}',
          pageSize: '${perPage}',
        },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: 'table',
      columns: [
        {
          name: 'enterprise_name',
          label: '企业名称',
          type: 'text',
        },
        /* {
          "name": "uni_code",
          "label": "企业信用代码",
          "type": "text"
        }, */
        {
          name: 'filling_range',
          label: '填报日期',
          type: 'text',
        },
        {
          name: 'output_value',
          label: '产值(万元)',
          type: 'text',
        },
        {
          name: 'tax',
          label: '年度税收(万元)',
          type: 'text',
        },
        {
          name: 'profit',
          label: '利润(万元)',
          type: 'text',
        },
        {
          name: 'rd_investment',
          label: '每年研发投入(万元)',
          type: 'text',
        },
        /*   {
            "name": "turnover",
            "label": "经营额(万元)",
            "type": "text"
          }, */
        {
          name: 'employee',
          label: '在职人员数',
          type: 'text',
        },
        {
          name: "${is_high_tech==1 ? '是' : '否'}",
          label: '是否高新',
          type: 'text',
        },
        {
          type: 'operation',
          label: '操作',
          fixed: 'right', // 固定在右侧
          width: 200,
          buttons: [
            //编辑
            {
              type: 'button',
              size: 'md',
              label: '编辑',
              level: 'primary',
              actionType: 'dialog',
              dialog: {
                title:
                  '编辑 (执行编辑后，该数据为最终值，不随企业上报数据模板内容联动，谨慎使用)',
                size: 'lg',
                actions: [
                  {
                    type: 'button',
                    size: 'md',
                    actionType: 'cancel',
                    label: '取消',
                    level: 'default',
                    primary: true,
                  },
                  {
                    label: '确认',
                    actionType: 'confirm',
                    primary: true,
                    //"reload": "tab-s",
                    close: true,
                    type: 'button',
                    size: 'md',
                    api: {
                      method: 'post',
                      messages: {
                        success: '操作成功！',
                      },
                      url: `enterprise/business/formSubmit`,
                      requestAdaptor: (rest) => {
                        let { ...other } = rest.data;
                        let datas = {
                          ...other,
                        };
                        if (Array.isArray(datas.element.attachment)) {
                          datas.element.attachment =
                            datas?.element?.attachment
                              ?.map((item) => item.oss_path)
                              ?.join(',') || '';
                        }
                        return {
                          ...rest,
                          data: datas,
                        };
                      },
                      data: {
                        businessCode: 'enterprise_business',
                        action: 'update',
                        element: /*  '$$' */ {
                          //"filling_range": "$filling_range",
                          output_value: '$output_value',
                          tax: '$tax',
                          //"turnover": "$turnover",
                          rd_investment: '$rd_investment',
                          main_business: '$main_business',
                          is_high_tech: '$is_high_tech',
                          employee: '$employee',
                          profit: '$profit',
                          talents_num: '$talents_num',
                          pro_title_status: '$pro_title_status',
                          main_development: '$main_development',
                          patents_achieve: '$patents_achieve',
                          attachment: '$attachment',
                        },
                        conditions: [
                          {
                            key: 'id',
                            value: '$id',
                          },
                        ],
                      },
                    },
                  },
                ],
                body: [
                  {
                    type: 'form',
                    columnCount: 2,
                    labelWidth: 150,
                    rules: {},
                    body: [
                      {
                        type: 'input-text',
                        name: 'filling_range',
                        label: '填报范围',
                        size: 'lg',
                        required: true,
                        disabledOn: 'true',
                      },
                      {
                        type: 'input-text',
                        name: 'enterprise_name',
                        label: '企业名称',
                        required: true,
                        size: 'lg',
                        disabledOn: 'true',
                      },
                      {
                        type: 'input-number',
                        placeholder: '输入企业产值',
                        name: 'output_value',
                        size: 'lg',
                        label: '产值(万元)',
                        required: true,
                      },
                      {
                        type: 'input-number',
                        name: 'tax',
                        size: 'lg',
                        label: '税收(万元)',
                        placeholder: '输入企业税收',
                        required: true,
                      },
                      {
                        type: 'input-number',
                        name: 'profit',
                        size: 'lg',
                        label: '利润(万元)',
                        placeholder: '输入本年度利润',
                        //"required": true,
                      },
                      {
                        type: 'input-number',
                        name: 'rd_investment',
                        size: 'lg',
                        label: '研发投入(万元)',
                        placeholder: '输入研发投入金额',
                        //"required": true,
                      },
                      {
                        name: 'is_high_tech',
                        type: 'radios',
                        label: '是否高新',
                        size: 'lg',
                        options: [
                          {
                            label: '是',
                            value: '1',
                          },
                          {
                            label: '否',
                            value: '0',
                          },
                        ],
                      },
                      {
                        type: 'input-text',
                        name: 'main_business',
                        label: '主营业务',
                        size: 'lg',
                        placeholder: '请输入企业主营业务',
                      },
                      {
                        type: 'input-number',
                        name: 'employee',
                        size: 'lg',
                        label: '在职人数',
                        placeholder: '输入企业本季度在职人数',
                      },
                      {
                        type: 'input-number',
                        name: 'talents_num',
                        size: 'lg',
                        label: '人才数量',
                        placeholder: '输入人才数量(硕士及以上)',
                      },
                      {
                        name: 'pro_title_status',
                        type: 'textarea',
                        size: 'lg',
                        label: '职称情况',
                        maxLength: 200,
                        placeholder:
                          '请输入职称情况(中高级及以上),最多输入200字',
                      },
                      {
                        name: 'main_development',
                        size: 'lg',
                        type: 'textarea',
                        label: '未来主要发展方向',
                        maxLength: 200,
                        placeholder: '输入未来主要发展方向,最多输入200字',
                      },
                      {
                        name: 'patents_achieve',
                        type: 'textarea',
                        size: 'lg',
                        label: '技术成果或专利情况',
                        maxLength: 200,
                        placeholder: '输入技术成果或专利情况,最多输入200字',
                      },
                      {
                        type: 'input-file',
                        name: 'attachment',
                        label: '上传附件',
                        size: 'lg',
                        //downloadUrl: false,
                        useChunk: false,
                        maxSize: 20990000,
                        accept:
                          '.jpg,.png,.gif,.psd,.tif,.bmp,.txt,.world,.pdf,.Excel,.doc,.docx,.wps,.xlsx,.xls,.et,.ett,.xlt,.dps,.dpt,.ppt,.pptx,.zip,.rar,.7z,.tar,.gz,.bz2,.xz',
                        multiple: true,
                        maxLength: 10,
                        receiver: {
                          method: 'POST',
                          url: `upload`,
                          adaptor: (payload) => {
                            return {
                              status: 0,
                              msg: '请求成功',
                              data: {
                                value: payload.value,
                                name: payload.fileName,
                                url: payload.url,
                              },
                            };
                          },
                        },
                        //"asBlob": true
                      },
                    ],
                  },
                ],
              },
            },
            {
              type: 'button',
              level: 'danger',
              actionType: 'ajax',
              label: '删除',
              confirmText: '是否确认删除',
              api: {
                method: 'post',
                url: `enterprise/business/formSubmit`,

                data: {
                  businessCode: 'enterprise_business',
                  action: 'delete',
                  conditions: [
                    {
                      key: 'id',
                      value: '${id}',
                    },
                  ],
                },
              },
            },
          ],
        },
      ],
      alwaysShowPagination: true,
      // "autoFillHeight": false,
      autoFillHeight: {
        height: 450,
      },
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      filter: {
        title: '',
        submitText: '搜索',
        controls: [
          {
            type: 'select',
            name: 'filling_range',
            label: '填报范围',
            size: 'md',
            options: getTimes(),
          },
          {
            type: 'text',
            name: 'enterprise_name',
            label: '企业名称',
            size: 'md',
            placeholder: '请输入企业名称',
          },
          {
            type: 'text',
            name: 'uni_code',
            label: '企业信用代码',
            size: 'md',
            placeholder: '请输入企业信用代码',
          },
          {
            type: 'reset',
            label: '重置',
          },
          {
            type: 'submit',
            label: '搜索',
            primary: true,
          },
          {
            type: 'button',
            size: 'md',
            label: '一键导出',
            onClick: function () {
              enterprisebusiness({
                type: 1,
              }).then((res) => {
                let blob = new Blob([res], {
                  type: 'text/csv,charset=UTF-8',
                });
                let objectUrl = URL.createObjectURL(blob);
                const fileName = '企业年度上报数据导出.xlsx';
                const downloadLink = document.createElement('a');
                downloadLink.href = objectUrl;
                downloadLink.download = fileName;
                downloadLink.click();
              });
            },
          },
        ],
      },
    },
  ],
};
onMounted(() => {
  let tabs = {
    type: 'tabs',
    // "tabsMode": "radio",
    unmountOnExit: true,
    tabs: [
      {
        title: '企业列表',
        body: amisjson3,
        id: 'u:319a552aa436',
      },
      {
        title: '企业上报数据',
        body: amisjson2,
        // publicConfig({title:'能耗监测', columns, api, filter }),
        //
        id: 'u:4a07b35df9ef',
        className: 'message2',
      },
      {
        title: '企业年度上报数据',
        body: amisjson4,
        // publicConfig({title:'能耗监测', columns, api, filter }),
        //
        id: 'u:4a07b35df9ef',
        className: 'message2',
      },
    ],
    id: 'u:238ba61a4079',
    className: 'tabsClass',
    linksClassName: 'tabsTitle',
  };
  // let data = publicConfig({ columns, api, filter });
  amisjson.value = tabs;
});
</script>

<template>
  <!--   <div class="tab">
    <el-button size="default" :class="pitchId == item.id ? 'on' : ''" @click="changeId(item.id)"
      v-for="(item, index) in  tabList" :key="index">{{ item.name }}</el-button>
  </div>

  <div v-if="pitchId == 1">
    <AmisComponent :amisjson="amisjson" />
  </div>
  <div v-else>
    <AmisComponent :amisjson="amisjson2" />
  </div> -->
  <div v-if="!!amisjson" class="zhengce publicTableStyle companyList">
    <AmisComponent :amisjson="amisjson" />
    <ApprovalRecord
      v-if="dialogVisible"
      :dialogVisible="dialogVisible"
      @canleDig="canleDig"
      :data="approvalRecordData"
    >
    </ApprovalRecord>
  </div>
</template>
<style scoped lang="scss">
.tab {
  padding-top: 20px;
  padding-left: 10px;
}

.on {
  background-color: #169bd5;
  color: #fff;
}
</style>
<style lang="scss">
.companyList {
  .antd-Table-toolbar.antd-Table-headToolbar {
    height: 80px;
    .antd-Crud-toolbar-item.antd-Crud-toolbar-item--left,
    .antd-Service {
      width: 100%;
      padding: 0 8px;
    }
  }
}
.form-b-sm {
  margin-bottom: 10px;
  height: 500px;
  overflow: scroll;
  padding: 10px 20px;
}

/* Dialog居中样式 */
:deep(.cxd-Modal) {
  top: 50% !important;
  transform: translateY(-50%) !important;
  margin-top: 0 !important;
}

:deep(.cxd-Modal--lg) {
  top: 50% !important;
  transform: translateY(-50%) !important;
  margin-top: 0 !important;
}

:deep(.cxd-Modal--md) {
  top: 50% !important;
  transform: translateY(-50%) !important;
  margin-top: 0 !important;
}
</style>
