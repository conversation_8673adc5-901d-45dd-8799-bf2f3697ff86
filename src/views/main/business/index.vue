<template>
  <div>
    <!-- 经营诉求 -->
    <div v-if="!!amisjson" class="publicTableStyleNoFilter">
      <AmisComponent :amisjson="amisjson" />
    </div>
  </div>
</template>
<script setup>
import { onBeforeMount, onMounted, ref } from "vue";
import AmisComponent from "@/components/amis/index.vue";
import { perFix, publicConfig, baseEvn, token } from "@/utils/utils";
import { useStore } from "vuex";
import { useRoute, useRouter } from "vue-router";
import dayjs from "dayjs";

const route = useRoute();
const router = useRouter();
let first = {
  type: "card",
  header: {
    title:
      "${enterprise_name}<span style='color:red;font-size:12px'>${gmtCreate}</span>",
  },
  titleClassName: "headerTitle",
  data: "${first}",
  body: [
    {
      name: "demands_desc",
      label: "诉求描述",
      type: "static",
      labelClassName: "leftTitle",
    },
    {
      type: "list",
      label: "附件",
      source: "$demands_file",
      listItem: {
        body: [
          {
            type: "tpl",
            tpl: "<a href='${demands_file[index].fileUrl}'>${demands_file[index].fileName}</a>",
          },
        ],
      },
    },
  ],
  mode: "horizontal",
  id: "qqq",
};
let second = {
  // disabledOn:true,
  type: "card",
  header: {
    title:
      "${enterprise_name1}<span style='color:red;font-size:12px'>${gmtCreate1}</span>",
  },
  titleClassName: "headerTitle",
  body: [
    {
      name: "demands_desc1",
      label: "回复",
      type: "static",
      labelClassName: "leftTitle",
    },
    {
      name: "demands_image1",
      label: "图片",
      type: "input-image",
      // "src": "${img}",
      disabledOn: "true",
      labelClassName: "leftTitle",
    },
    {
      type: "list",
      label: "附件",
      source: "$demands_file1",
      listItem: {
        body: [
          {
            type: "tpl",
            tpl: "<a href='${demands_file1[index].fileUrl}'>${demands_file1[index].fileName}</a>",
          },
        ],
      },
    },
  ],

  id: "u:15008a1115bf",
};
let formData = {
  type: "page",
  title: "详情页",

  body: [
    {
      type: "form",
      title: "",
      submitText: "",
      initApi: {
        url: `billund/getList`,
        method: "post",
        data: {
          businessCode: "business_demands_record",
          conditions: [
            {
              compareType: "eq",
              key: "business_demands_id",
              value: "${id|default:undefined}",
            },
            {
              compareType: "orderBy",
              key: "gmt_create",
              isDesc: "true",
            },
          ],
        },
        responseData: {
          "&": "$$",
          items: "elements",
          enterprise_name: "${elements[0].principal_name}",
          gmtCreate: "${elements[0].gmt_create}",
          demands_desc: "${elements[0].demands_desc}",
          demands_img: "${elements[0].demands_img}",
          demands_file: "${elements[0].demands_file}",
          enterprise_name1: "${elements[1].principal_name}",
          gmtCreate1: "${elements[1].gmt_create}",
          demands_desc1: "${elements[1].demands_desc}",
          demands_image1: "${elements[1].demands_img}",
          demands_file1: "${elements[1].demands_file}",
        },
        adaptor: function (payload, response, api, context) {
          let datas = payload.elements.map((item) => {
            let { gmt_create, gmt_modify, demands_img, ...others } = item;
            return {
              ...others,
              gmt_create: dayjs(Number(gmt_create)).format("YYYY-MM-DD"),
              gmt_modify: dayjs(Number(gmt_modify)).format("YYYY-MM-DD"),
              demands_img: demands_img?.map((e) => e.fileUrl) || "",
            };
          });
          let payloads = payload;
          payloads.elements = datas;
          return {
            ...payloads,
          };
        },
      },
      className: "detailPage",
      body: [
        {
          type: "card",
          // "header": {
          //   "title": "${enterprise_name}<span style='color:red;font-size:12px'>${gmtCreate}</span>",
          // },
          titleClassName: "headerTitle",
          data: "${first}",
          body: [first, second],
          mode: "horizontal",
          id: "qqq",
          className: "detailPageTwo",
        },
      ],
    },
  ],

  actions: [],
};
const columns = [
  {
    name: "enterpriseName",
    label: "企业名称",
    type: "text",
    searchable: true,
  },
  {
    name: "realName",
    label: "姓名",
    type: "text",
    searchable: {
      type: "input-text",
      name: "realName",
      label: "姓名",
      placeholder: "输入姓名",
    },
  },
  {
    name: "phone",
    label: "手机号",
    type: "text",
    searchable: true,
  },
  {
    name: "demandsDesc",
    label: "诉求描述",
    type: "text",
  },
  {
    name: "status",
    name: "${status == 0 ? '未回复' : '已回复'}",
    label: "状态",
    type: "text",
    searchable: {
      type: "select",
      name: "status",
      label: "状态",
      placeholder: "选择状态",
      options: [
        {
          label: "未回复",
          value: "0",
        },
        {
          label: "已回复",
          value: "1",
        },
      ],
    },
  },

  {
    name: "gmtCreate",
    label: "申报诉求时间",
    type: "date",
    valueFormat: "x",
    searchable: {
      type: "input-date-range",
      name: "gmtCreate",
      label: "申报诉求时间",
      placeholder: "输入gmtCreate",
    },
  },
  {
    type: "operation",
    label: "操作",
    fixed: "right", // 固定在右侧
    width: 200,
    buttons: [
      {
        type: "button",
        label: "预览",
        // "icon": "fa fa-eye",
        actionType: "drawer",
        drawer: formData,
      },
      //回复
      {
        type: "button",
        label: "回复",
        actionType: "dialog",
        disabledOn: "this.status === '1'",
        dialog: {
          // "size": "lg",
          title: "回复",
          actions: [
            {
              type: "button",
              actionType: "cancel",
              label: "取消",
               level: "default",
              primary: true,
            },
            {
              label: "确认",
              actionType: "confirm",
              primary: true,
              //"reload": "tab-s",
              close: true,
              type: "button",
              api: {
                method: "post",
                url: `businessDemands/recover`,
                messages: {
                  success: "操作成功！",
                },

                data: {
                  // "businessCode": "business_demands_record",
                  // "action": "insert",
                  // "element": {
                  businessDemandsId: "${id|default:undefined}",
                  demandsDesc: "${demands_desc|default:undefined}",
                  demandsImg: "${demands_img|default:undefined}",
                  demandsFile: "${demands_file|default:undefined}",
                  // },
                  // "conditions": []
                },
              },
            },
          ],

          body: [
            {
              type: "form",
              columnCount: 1,
              rules: {},

              body: [
                {
                  label: "回复",
                  type: "textarea",
                  name: "demands_desc",
                  id: "u:ea2bfdba9bea",
                  required: true,
                },
                {
                  type: "input-image",
                  name: "demands_img",
                  label: "图片",
                  id: "u:f81c47c195a7",
                  required: true,
                  useChunk: false,
                  maxSize: 10495000,
                  receiver: {
                    method: "post",
                    url: `upload`,
                  },
                },
                {
                  type: "input-file",
                  name: "demands_file",
                  label: "附件",
                  id: "u:d41a4dff65af",
                  useChunk: false,
                  maxSize: 20990000,
                  accept:
                    ".jpg,.png,.gif,.psd,.tif,.bmp,.txt,.world,.pdf,.Excel,.doc,.docx,.wps,.xlsx,.xls,.et,.ett,.xlt,.dps,.dpt,.ppt,.pptx,.zip,.rar,.7z,.tar,.gz,.bz2,.xz",
                  //"required": true,
                  multiple: true,
                  maxLength: 10,
                  //"required": true,
                  downloadUrl: false,
                  receiver: {
                    method: "POST",
                    url: `upload`,
                  },
                },
              ],
            },
          ],
        },
      },
    ],
  },
];
const api = {
  method: "post",
  url: `businessDemands/page`,

  requestAdaptor: (rest) => {
    let { gmtCreate, ...other } = rest.data;
    let datas = {
      ...other,
      startDate: rest.data.gmtCreate?.split(",")[0] * 1000,
      endDate: rest.data.gmtCreate?.split(",")[1] * 1000,
    };
    return {
      ...rest,
      data: datas,
      // pageNum: page,
      // pageSize: perPage
    };
  },
  data: {
    // "businessCode":"business_demands_record",
    enterpriseName: "${enterpriseName|default:undefined}",
    realName: "${realName|default:undefined}",
    phone: "${phone|default:undefined}",
    status: "${status|default:undefined}",
    gmtCreate: "${gmtCreate|default:undefined}",
    // "startDate":'${}',
    // "endDate":1695139200000,
    pageNum: "${page|default:1}",
    pageSize: "${perPage|default:10}",
  },
  responseData: {
    "&": "$$",
    items: "records",
    // total:'total',

    //     pageNum:'',
    // pageSize:'size'
  },
};
let amisjson = ref(null);
onMounted(() => {
  let data = publicConfig({ columns, api });
  amisjson.value = data;
});
</script>
<style  lang="scss">
/* .amis-scope .headerTitle {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  text-align: right;
}

.amis-scope .leftTitle {
  line-height: 33px;
  width: 100px;
} */
.detailPage .antd-Card {
  border: 0;
}
.detailPageTwo {
  .antd-Card {
    border: 1px solid var(--Card-borderColor);
  }
}
.publicTableStyle,
.publicTableStyleNoFilter {
  .antd-FileControl {
    width: 300px;
  }
}
</style>