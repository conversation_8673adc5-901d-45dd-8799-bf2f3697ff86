<template>
  详情页
  <div v-if="!!amisjson" class="page">
    <AmisComponent :amisjson="amisjson" />
  </div>
</template>
<script setup>
import AmisComponent from "@/components/amis/index.vue";

import { ref, computed, onMounted, onUnmounted } from "vue";
const storeJson = {
  type: "page",
  body: [
    {
      type: "form",
      title: "",
      submitText: "",
      initApi: {
        url: "/mock/billund/getList",
        method: "post",
        data: {
          businessCode: "business_demands_record",
          conditions: [
            {
              compareType: "eq",
              key: "business_demands_id",
              isDesc: 1,
            },
            {
              compareType: "orderBy",
              key: "gmt_create",
              isDesc: "true",
            },
          ],
        },
        responseData: {
          "&": "$$",
          items: "elements",
          enterprise_name: "${elements[0].enterprise_name}",
          gmtCreate: "${elements[0].gmt_create}",
          demands_desc: "${elements[0].demands_desc}",
          demands_image: "${elements[0].demands_image}",
          demands_file: "${elements[0].demands_file}",
          enterprise_name1: "${elements[1].enterprise_name}",
          gmtCreate1: "${elements[1].gmt_create}",
          demands_desc1: "${elements[1].demands_desc}",
          demands_image1: "${elements[1].demands_image}",
          demands_file1: "${elements[1].demands_file}",
        },
        //                 "adaptor":(res)=>{
        //                     console.log('res',res)
        //     const { data,  ...rest } = res
        //     let datas = {
        //         businessCode: data.businessCode,
        //        first: data.elements[0],
        //        second: data.elements[1],
        //     }
        //                     return {
        //                         data:    datas,
        //   ...rest,
        // //   status: payload.code === 200 ? 0 : payload.code
        // };
        //                 }
        //  "console.log(context); // 打印上下文数据 \nreturn {\n    ...payload,\n    status: payload.code === 200 ? 0 : payload.code\n}"
        //   responseData: (res) => {
        //     console.log('res',res)
        //   }
      },
      api: {
        method: "post",
        url: "/api/billund/getList",
        data: {
          businessCode: "business_demands_record",
          conditions: [
            {
              compareType: "eq",
              key: "business_demands_id",
              isDesc: 1,
            },
            {
              compareType: "orderBy",
              key: "gmt_create",
              isDesc: "true",
            },
          ],
        },
        responseData: {
          "&": "$$",
          items: "elements",
        },
      },
      body: [
        {
          type: "card",
          header: {
            title: "aaa",
            subTitle: "",
          },

          body: [
            {
              type: "card",
              header: {
                title:
                  "${enterprise_name}<span style='color:red;font-size:12px'>${gmtCreate}</span>",
              },
              titleClassName: "headerTitle",
              data: "${first}",
              body: [
                {
                  name: "demands_desc",
                  label: "诉求描述",
                  type: "static",
                  labelClassName: "leftTitle",
                },
                {
                  name: "demands_image",
                  label: "图片",
                  type: "input-image",
                  // "src": "${img}",
                  disabledOn: "true",
                  labelClassName: "leftTitle",
                },
                {
                  name: "demands_file",
                  label: "附件",
                  type: "input-file",
                  useChunk: false,
                  downloadUrl: false,
                  maxSize: 20990000,
                  // "src": "${img}",
                  disabledOn: "true",
                  labelClassName: "leftTitle",
                },
              ],
              mode: "horizontal",
              id: "qqq",
            },
            {
              type: "card",
              header: {
                title:
                  "${enterprise_name1}<span style='color:red;font-size:12px'>${gmtCreate1}</span>",
              },
              titleClassName: "headerTitle",
              body: [
                {
                  name: "demands_desc",
                  label: "诉求描述",
                  type: "static",
                  labelClassName: "leftTitle",
                },
                {
                  name: "demands_image",
                  label: "图片",
                  type: "input-image",
                  // "src": "${img}",
                  disabledOn: "true",
                  labelClassName: "leftTitle",
                },
                {
                  name: "demands_file",
                  label: "附件",
                  type: "input-file",
                  downloadUrl: false,
                  // "src": "${img}",
                  disabledOn: "true",
                  labelClassName: "leftTitle",
                },
              ],

              id: "u:15008a1115bf",
            },
          ],
          // "actions": [
          //     {
          //         "type": "button",
          size: "md",
          //         "label": "",
          //         "actionType": "dialog",
          //         "dialog": {
          //             "title": "标题",
          //             "body": "内容"
          //         },
          //         "id": "u:a03a2dc1e63d"
          //     }
          // ],
          id: "u:ac8332393e53",
        },
      ],
    },
  ],
};
let amisjson = ref(null);
onMounted(() => {
  //   let data = publicConfig({ columns, api })
  amisjson.value = storeJson;
});
</script>
<style scoped>
.page {
  padding: 20px;
  width: 500px;
}
</style>
