<template>
    <div  class="previewDom">
            <div ref="previewDom" class="inner"></div>
    </div>
</template>

<script lang="ts" setup>

import { get } from "http";
import { onMounted, onBeforeUnmount, ref } from "vue";
import { useStore } from "vuex";
const store = useStore();
let previewDom = ref(null)
 onMounted(() => {
    setFullScreen();
    getPreviewData()
});
onBeforeUnmount(() => {
    store.commit("app/contentFullScreenChange", false);
});
const setFullScreen = () => {
    store.commit("app/contentFullScreenChange", true);
};
const getPreviewData=()=>{
    let data=JSON.parse(sessionStorage.getItem('preview'))
// console.log(data)
// dom.value.innerHTML = '暂无数据'
previewDom.value.innerHTML=data

}
</script>
<style lang="scss">
.inner{
    img{
    width: 900px !important;
}}
</style>
<style lang="scss" scoped>
.previewDom{
   width: 100%;
    // padding: 50px;
    // text-indent: 32px;
    img{
    width: 100% !important;
}
    .inner{
        padding: 10px;
        width: 920px;
        margin: 0px auto;
        border: 1px solid #eee;
   
    }
    h1{
        width: 100%;
        text-align: center;
        line-height: 50px;
    }
}

</style>
 