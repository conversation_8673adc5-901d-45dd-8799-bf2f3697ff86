<template>
  <!-- <Header/> -->
  <div class="onlineService">
    <!-- <div class="des"> 个人中心</div> -->
    <!-- <el-button size="default"  type="primary" size="default" :icon="ArrowLeftBold" @click="backHome">返回主页</el-button> -->
    <div class="mainContent">
      <!-- <div class="center">
    <div class="centerIn">
        个人中心
    </div>
    </div> -->
      <MainContent type="userCenter" />
    </div>
  </div>
</template>
<script setup >
import { onMounted, onBeforeUnmount, ref, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useStore } from "vuex";
import Header from "@/components/header/index.vue";
import MainContent from "../accountCenter/index.vue";
import { ArrowLeftBold } from "@element-plus/icons-vue";

const store = useStore();
const router = useRouter();
// import type { TabsPaneContext } from 'element-plus'

const activeName = ref("first");

onMounted(() => {
  setFullScreen();
});

onBeforeUnmount(() => {
  store.commit("app/contentFullScreenChange", false);
});
const setFullScreen = () => {
  store.commit("app/contentFullScreenChange", true);
};
const backHome = () => {
  router.push("/");
};
</script>
 
<style scoped lang='scss'>
.onlineService {
  width: 100%;
  // height: calc(100vh - 50px);
  padding: 50px 10px 0;
  // background: #fafcff top left 100% / contain no-repeat url("@/assets/images/online/bg1.png");
  overflow: scroll;
  .des {
    font-size: 26px;
    text-align: center;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
    margin-bottom: 10px;
  }
  .mainContent {
    border-radius: 6px 6px 0px 0px;
    // margin: 50px 0;
    // padding: 55px 0;
    width: 100%;
    background: white;
  }
}
</style>
<style lang="scss" scoped >
.personalTabs {
  .el-tabs__nav-wrap::after {
    background-color: rgba(221, 221, 221, 0.5);
  }
}

.pageNation {
  .el-pagination.is-background .el-pager li {
    background: transparent;
    border: 1px solid rgba(217, 217, 217, 1);
    &.is-active {
      background: rgba(90, 139, 255, 0.5);
    }
  }
}
.pageNation {
  padding-top: 20px;
  width: 100%;
  display: flex;
  justify-content: center;
}
.center {
  font-size: 1.25rem;
  /* margin: 0.94rem; */
  text-align: left;
  font-weight: 500;
  background: #f0f2f5;
  // padding: 0.94rem;
  .centerIn {
    background: #fff;
    padding: 15px;
  }
}
</style>