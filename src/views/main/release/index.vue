<script setup>
// import HelloWorld from './components/HelloWorld.vue'
// import TheWelcome from './components/TheWelcome.vue'
import AmisComponent from '@/components/amis/index.vue';
import { perFix, baseEvn, token } from '@/utils/utils';
import Introduce from './introduce/index.vue';
import Demeanour from './demeanour/index2.vue';
import dataAdministration from './dataAdministration/index.vue';

import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const router = useRouter();
function handleButtonClick(e) {
  // console.log(e, "111");
  // "link": "#/workbench/lease/addlease",
  router.push(e);
}
let pitchId = ref(1);
const tabList = ref([
  {
    name: '信息发布',
    id: 1,
  },
  {
    name: '园区简介',
    id: 2,
  },
  {
    name: '园区风采',
    id: 3,
  },
  {
    name: '数据管理',
    id: 4,
  },
]);
function changeId(id) {
  if (pitchId.value != id) {
    pitchId.value = id;
  }
}
function renderDateTime(timeStamp) {
  const date = new Date(timeStamp);
  return date.toString(); // 根据需求返回合适的日期时间格式
}
// 企业列表
const amisjson = {
  type: 'page',
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: '0px 0px 0px 0px transparent',
  },
  body: [
    {
      type: 'tpl',
      inline: true,
      wrapperComponent: '',
      syncLocation: false,
      id: 'u:3345e187f2df',
      style: {
        boxShadow: '0px 0px 0px 0px transparent',
        fontWeight: '500',
        lineHeight: '1.7',
        fontSize: '20px',
      },
    },
    {
      type: 'crud',
      syncLocation: false,
      name: 'tab-s',
      api: {
        method: 'post',
        url: `information/page`,

        requestAdaptor: (rest) => {
          // console.log(rest, 'rest');
          let { gmtCreate, gmtissue, ...other } = rest.data;
          // console.log(other,'other');
          // console.log(rest.data);
          let startTime = '';
          let endTime = '';
          let publishStartTime = '';
          let publishEndTime = '';
          let datas = [];
          if (rest.data.gmtCreate && rest.data.gmtissue) {
            startTime = rest.data.gmtCreate?.split(',')[0] * 1000 || '';
            endTime = rest.data.gmtCreate?.split(',')[1] * 1000 || '';
            publishStartTime = rest.data.gmtissue?.split(',')[0] * 1000 || '';
            publishEndTime = rest.data.gmtissue?.split(',')[1] * 1000 || '';
            datas = {
              ...other,
              startTime,
              endTime,
              publishStartTime,
              publishEndTime,
            };
          } else if (rest.data.gmtCreate && !rest.data.gmtissue) {
            startTime = rest.data.gmtCreate?.split(',')[0] * 1000 || '';
            endTime = rest.data.gmtCreate?.split(',')[1] * 1000 || '';
            datas = {
              ...other,
              startTime,
              endTime,
            };
          } else if (!rest.data.gmtCreate && rest.data.gmtissue) {
            publishStartTime = rest.data.gmtissue?.split(',')[0] * 1000 || '';
            publishEndTime = rest.data.gmtissue?.split(',')[1] * 1000 || '';
            datas = {
              ...other,
              publishStartTime,
              publishEndTime,
            };
          } else {
            datas = {
              ...other,
            };
          }

          return {
            ...rest,
            data: datas,
          };
        },
        data: {
          title: '${title}',
          type: '${type}',
          status: '${status}',
          pageNum: '${page}',
          pageSize: '${perPage}',
          gmtCreate: '${gmtCreate}',
          gmtissue: '${gmtissue}',
        },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: 'table',
      columns: [
        {
          name: 'title',
          label: '名称',
          type: 'text',
          width: 400,
        },
        {
          type: 'tpl',
          name: 'type',
          label: '类型',
           width: 120,
          tpl: "<span class='${type==\"公告\" ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200\" : (type==\"政策\" ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200\" : (type==\"党建要闻\" ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200\" : (type==\"党政法规\" ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 border border-purple-200\" : (type==\"活动\" ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200\" : (type==\"资讯\" ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200\" : (type==\"园区荣誉\" ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200\" : (type==\"政策新\" ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-cyan-100 text-cyan-800 border border-cyan-200\" : \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200\")))))))}'>${type}</span>",
        },
        {
          name: 'createBy',
          label: '创建者',
          width: 200,
          type: 'text',
        },
        {
          name: 'gmtCreate',
          label: '创建时间',
           width: 150,
          type: 'date',
          valueFormat: 'x',
        },
        {
          name: 'publishTime',
          label: '发布时间',
           width: 150,
          type: 'date',
          valueFormat: 'x',
        },
        {
          name: 'auditorName',
          label: '审核人',
           width: 200,
          type: 'text',
        },
        {
          type: 'tpl',
          label: '状态',
           width: 120,
          tpl: "<span class='${status==\"草稿\" ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200\" : (status==\"待审核\" ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200\" : \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200\")}'>${status}</span>",
        },
        {
          type: 'operation',
          label: '操作',
          fixed: 'right', // 固定在右侧
          width: 300,
          buttons: [
            //查看
            {
              label: '查看',
              type: 'button',
              level: 'primary',
              // "actionType": "link",
              // "link": "#/backstage/release/message?id=$id&st=1",
              onClick: (e, item) => {
                handleButtonClick(
                  `/backstage/release/message?id=${item?.data?.id}&st=1`
                );
              },
            },
            //查看
            {
              label: '编辑',
              type: 'button',
              level: 'primary',
              // "actionType": "link",
              visibleOn: "status !== '待审核' && status !== '已发布'",
              // "link": "#/backstage/release/message?id=$id&st=2",
              onClick: (e, item) => {
                handleButtonClick(
                  `/backstage/release/message?id=${item?.data?.id}&st=2`
                );
              },
            },
            {
              label: '审核',
              type: 'button',

              // "actionType": "link",
              visibleOn: "status == '待审核'",
              // "link": "#/backstage/release/message?id=$id&st=3",
              onClick: (e, item) => {
                handleButtonClick(
                  `/backstage/release/message?id=${item?.data?.id}&st=3`
                );
              },
            },
            {
              type: 'button',
              level: 'danger',
              actionType: 'ajax',
              label: '下架',
              confirmText: '是否确认下架',
              visibleOn: "status == '已发布'",
              api: {
                method: 'post',
                url: `billund/formSubmit`,

                data: {
                  businessCode: 'information',
                  action: 'update',
                  element: {
                    status: 0,
                  },
                  conditions: [
                    {
                      key: 'id',
                      value: '${id}',
                    },
                  ],
                },
              },
            },

            {
              type: 'button',
              level: 'primary',
              actionType: 'ajax',
              label: '提交',
              confirmText: '是否确认提交',
              visibleOn: "status == '草稿'",
              api: {
                method: 'post',
                url: `billund/formSubmit`,

                data: {
                  businessCode: 'information',
                  action: 'update',
                  element: {
                    status: 1,
                  },
                  conditions: [
                    {
                      key: 'id',
                      value: '${id}',
                    },
                  ],
                },
              },
            },
            //删除
            {
              type: 'button',
              level: 'danger',
              actionType: 'ajax',
              label: '删除',
              level: 'danger',
              confirmText: '是否确认删除',
              visibleOn: "status !== '已发布'",
              api: {
                method: 'post',
                url: `billund/formSubmit`,
                data: {
                  businessCode: 'information',
                  action: 'delete',
                  element: {},
                  conditions: [
                    {
                      key: 'id',
                      value: '${id}',
                    },
                  ],
                },
              },
            },
          ],
        },
      ],
      alwaysShowPagination: true,
      autoFillHeight: false,
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      filter: {
        title: '',
        submitText: '搜索',
        controls: [
          {
            type: 'text',
            name: 'title',
            size: 'md',
            label: '名称',
            placeholder: '请输入名称',
          },
          {
            type: 'input-date-range',
            name: 'gmtCreate',
            size: 'md',
            label: '创建时间',
          },
          {
            type: 'select',
            name: 'type',
            size: 'md',
            label: '类型',
            placeholder: '请选择类型',
            value: '',
            options: [
              { label: '全部', value: '' },
              { label: '公告', value: '1' },
              { label: '政策', value: '2' },
              { label: '党建要闻', value: '3' },
              { label: '党政法规', value: '4' },
              { label: '活动', value: '5' },
              { label: '资讯', value: '6' },
              { label: '园区荣誉', value: '8' },
              { label: '政策新', value: '7' },
            ],
          },
          {
            type: 'select',
            size: 'md',
            name: 'status',
            label: '状态',
            placeholder: '请选择状态',
            value: '',
            options: [
              { label: '全部', value: '' },
              { label: '草稿', value: '0' },
              { label: '待审核', value: '1' },
              { label: '已发布', value: '2' },
            ],
          },
          {
            type: 'input-date-range',
            name: 'gmtissue',
            label: '发布时间',
            size: 'md',
          },
          {
            type: 'reset',
            label: '重置',
          },
          {
            type: 'submit',
            label: '搜索',
            primary: true,
          },

          {
            label: '新增信息',
            type: 'button',
            size: 'md',
          
            // "actionType": "link",
            // "link": "#/backstage/release/message",
            onClick: () => {
              handleButtonClick('/backstage/release/message');
            },
          },
        ],
        onSubmit: 'reload',
        actions: [
          /* {
            "type": "button",
"size": "md",
            "label": "一键导出",
            "actionType": "download",
            "api": `/enterprise/downloadAll`
          } */
        ],
      },
    },
  ],
};
</script>

<template>
  <div class="tab">
    <div
      size="default"
      :class="pitchId == item.id ? 'on' : 'ons'"
      @click="changeId(item.id)"
      v-for="(item, index) in tabList"
      :key="index"
    >
      {{ item.name }}
    </div>
  </div>
<div class="tab-content">
  <div class="publicTableStyle" v-if="pitchId === 1">
    <AmisComponent :amisjson="amisjson" />
  </div>
  <div v-else-if="pitchId === 2">
    <Introduce />
  </div>
  <div v-else-if="pitchId === 3">
    <Demeanour />
  </div>
  <div v-else>
    <dataAdministration />
  </div>
  </div>
</template>
<style scoped lang="scss">
.tab {
  width: 100%;
  height: 50px;
  position: relative;
  overflow-y: auto;
  scrollbar-width: none;
  width: 100%;
  display: flex;
  align-items: center;
  padding-top: 10px;
  padding-left: 30px;
}
.tab-content{
  height: calc(100% - 60px);
  overflow: scroll;
  padding-top: 16px;
}
.on,.ons{
    margin-right: 12px;
}
.on {
  font-size: 1rem !important;
  padding-right: 20px;
  cursor: pointer;
  color: #2468f2;
  position: relative;

  &::after {
    content: '';
    width: calc(100% - 20px);
    height: 2px;
    background: #2468f2;
    position: absolute;
    left: 0px;
    bottom: -8px;
  }
}
.ons {
  color: #151b26;
  cursor: pointer;
  padding-right: 20px;
  font-size: 1rem !important;
}
</style>
