<template>
  <div class="box">
    <div class="top">
      <!-- 产业协会 -->
      <association></association>
      <!-- 产业基金 -->
      <fund></fund> 
    </div>
    <div class="top2">
      <!-- 政策申报 -->
      <PolicyDeclaration></PolicyDeclaration>
    </div>
    <div class="top2">
      <!-- 人才申报 -->
      <TalentApplication ref="TalentApplication" @Startediting="Startediting" @Endedit="Endedit" :isBeediting="isBeediting"></TalentApplication>
    </div>
    <div class="top2">
      <!-- 科技申报 -->
      <technology></technology>
    </div>
    <div class="top">
      <div style="width: 50%;">
        <!-- 园区概述 -->
        <ParkOverview ref="ParkOverview" @Startediting="Startediting" @Endedit="Endedit" :isBeediting="isBeediting"></ParkOverview>
        <!-- 园区成果 -->
        <ParkAchievement ref="ParkAchievement" @Startediting="Startediting" @Endedit="Endedit" :isBeediting="isBeediting"></ParkAchievement>
      </div>
       <ParkHonor></ParkHonor>
    </div>
    <div class="top2">
      <Investment></Investment>
    </div>
  </div>
</template>

<script>
import association from './components/association.vue' //产业协会
import fund from './components/fund.vue' //产业基金
import PolicyDeclaration from './components/PolicyDeclaration.vue' //政策申报
import TalentApplication from './components/TalentApplication.vue' //人才申报
import technology from './components/technology.vue' //科技申报
import ParkOverview from './components/ParkOverview.vue' //园区概述
import ParkAchievement from './components/ParkAchievement.vue' //园区成果
import ParkHonor from './components/ParkHonor.vue' //园区荣誉
import Investment from './components/Investment.vue' //年度招商情况
export default {
  name: "dataAdministration",
  components:{
    association,
    fund,
    PolicyDeclaration,
    TalentApplication,
    technology,
    ParkOverview,
    ParkAchievement,
    ParkHonor,
    Investment
  },
  data() {
    return {
      isBeediting:false
    }
  },
  created() {
  },
  methods: {
    Startediting(){
      this.isBeediting=true
    },
    Endedit(){
      this.isBeediting=false
      this.updata()
    },
    updata(){
      this.$refs.ParkAchievement.getindustryOrParkHonorList()
      this.$refs.ParkOverview.getindustryOrParkHonorList()
      this.$refs.TalentApplication.getindustryOrParkHonorList()
    }
  }
}
</script>

<style lang="scss" scoped>
@import './index.scss';
::v-deep {

  .el-table__header tr,
  .el-table__header th {
    padding: 0;
    height: 56px;
  }

  .el-table__body tr,
  .el-table__body td {
    padding: 0;
    height: 56px;
  }

  .el-overlay{
    background-color:transparent !important
  }

}
</style>