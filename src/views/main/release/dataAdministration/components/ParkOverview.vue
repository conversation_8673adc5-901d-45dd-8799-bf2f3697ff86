<template>
    <!-- 园区概述 -->
    <div class="top-left2" style="margin-top: 16px;">
        <div class="head">
            <span>园区概况</span>
        </div>
        <div class="table2">
            <el-table v-loading="loading" :header-cell-style="headercell" size="large" :data="talentsList"
                style="width: 100%">
                <el-table-column prop="parkLandArea" label="园区占地(亩）">
                    <template #default="scope">
                        <span v-if="!isEdit">{{ scope.row.parkLandArea }}</span>
                        <el-input v-else v-model="original.parkLandArea" />
                    </template>
                </el-table-column>
                <el-table-column prop="totalArea" label="总面积(万㎡)">
                    <template #default="scope">
                        <span v-if="!isEdit">{{ scope.row.totalArea }}</span>
                        <el-input v-else v-model="original.totalArea" />
                    </template>
                </el-table-column>
                <el-table-column prop="totalInvestmentArea" label="招商总面积(万㎡)">
                    <template #default="scope">
                        <span v-if="!isEdit">{{ scope.row.totalInvestmentArea }}</span>
                        <el-input v-else v-model="original.totalInvestmentArea" />
                    </template>
                </el-table-column>
                <!-- <el-table-column prop="parkTalent" label="园区人才(人)" >
                    <template #default="scope">
                        <span v-if="!isEdit">{{ scope.row.parkTalent }}</span>
                        <el-input v-else v-model="original.parkTalent" />
                    </template>
                </el-table-column> -->
                <el-table-column label="操作">
                    <template #default>
                        <span @click="edittalents" class="textBuutom">{{ isEdit ? '保存' : '编辑' }}</span>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
import { parkMessagedetailAPI, addOrUpdateAPI } from '@/api/industry'
export default {
    data() {
        return {
            isEdit: false,
            talentsList: [],
            original: {},
            loading: false,
            headercell: { background: 'linear-gradient(180deg, #f8faff 0%, #f0f5ff 100%)', color: '#1D2129', 'font-weight': '600' }
        }
    },
    props:{
        isBeediting:{
            type: Boolean,
            default:false
        }
    },
    created() {
        this.getindustryOrParkHonorList()
    },
    methods: {
        async getindustryOrParkHonorList() {
            try {
                this.loading = true
                const res = await parkMessagedetailAPI()
                this.original = res.data
                this.talentsList[0] = res.data
            } finally {
                this.loading = false
            }

        },
        async edittalents() {
            if (!this.isEdit) {
                if(this.isBeediting){
                    return this.$message.error("请保存完成后再进行其他编辑操作")
                }
                this.isEdit = true
                this.$emit("Startediting")
            } else {
                let data = this.original
                const regex = /^(0|[1-9]\d*)$/;
                const regex2= /^[0-9]+(\.[0-9]+)?$/;
                if(!regex.test(data.parkTalent)){
                 return  this.$message.error("数量不能为空且必须为整数")
                }
                if(!regex2.test(data.parkLandArea) || !regex2.test(data.totalArea) || !regex2.test(data.totalInvestmentArea)){
                 return  this.$message.error("面积不能为空且必须是正数")
                }
                const res = await addOrUpdateAPI(data)
                if (res.status == 0) {
                    await this.getindustryOrParkHonorList()
                    this.isEdit = false
                    this.$message.success("编辑成功！")
                    this.$emit("Endedit")
                }
            }

        },
    }
}
</script>

<style scoped lang="scss">
@import '../index.scss';
</style>