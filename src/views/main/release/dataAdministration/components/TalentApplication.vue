<template>
    <!-- 人才申报 -->
    <div class="top-right">
        <div class="head">
            <span>人才申报</span>
        </div>
        <div class="table2">
            <el-table v-loading="loading" :header-cell-style="headercell" size="large" :data="talentsList"
                style="width: 100%">
                <el-table-column prop="localTalent" label="乡土人才">
                    <template #default="scope">
                        <span v-if="!isEdit">{{ scope.row.localTalent }}</span>
                        <el-input v-else v-model="original.localTalent"  size="default" />
                    </template>
                </el-table-column>
                <el-table-column prop="publicServiceTalent" label="公服务人才">
                    <template #default="scope">
                        <span v-if="!isEdit">{{ scope.row.publicServiceTalent }}</span>
                        <el-input v-else v-model="original.publicServiceTalent"  size="default"/>
                    </template>
                </el-table-column>
                <el-table-column prop="emergingTalent" label="新兴人才">
                    <template #default="scope">
                        <span v-if="!isEdit">{{ scope.row.emergingTalent }}</span>
                        <el-input v-else v-model="original.emergingTalent"  size="default"/>
                    </template>
                </el-table-column>
                <el-table-column prop="youngTalent" label="青年人才">
                    <template #default="scope">
                        <span v-if="!isEdit">{{ scope.row.youngTalent }}</span>
                        <el-input v-else v-model="original.youngTalent"  size="default"/>
                    </template>
                </el-table-column>
                <el-table-column prop="skilledTalent" label="技能人才">
                    <template #default="scope">
                        <span v-if="!isEdit">{{ scope.row.skilledTalent }}</span>
                        <el-input v-else v-model="original.skilledTalent"  size="default"/>
                    </template>
                </el-table-column>
                <el-table-column prop="industryTalent" label="产业人才">
                    <template #default="scope">
                        <span v-if="!isEdit">{{ scope.row.industryTalent }}</span>
                        <el-input v-else v-model="original.industryTalent"  size="default"/>
                    </template>
                </el-table-column>
                <el-table-column prop="highTalent" label="高端人才">
                    <template #default="scope">
                        <span v-if="!isEdit">{{ scope.row.highTalent }}</span>
                        <el-input v-else v-model="original.highTalent"  size="default"/>
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template #default>
                        <span @click="edittalents" class="textBuutom">{{ isEdit ? '保存' : '编辑' }}</span>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
import { parkMessagedetailAPI, addOrUpdateAPI } from '@/api/industry'
export default {
    data() {
        return {
            isEdit: false,
            talentsList: [],
            original: {},
            loading: false,
            headercell: { background: 'linear-gradient(180deg, #f8faff 0%, #f0f5ff 100%)', color: '#1D2129', 'font-weight': '600' }
        }
    },
    props: {
        isBeediting: {
            type: Boolean,
            default: false
        }
    },
    created() {
        this.getindustryOrParkHonorList()
    },
    methods: {
        async getindustryOrParkHonorList() {
            try {
                this.loading = true
                const res = await parkMessagedetailAPI()
                if (res.data==true) {
                    let data = {
                        "localTalent": "0",
                        "publicServiceTalent": "0",
                        "emergingTalent": "0",
                        "youngTalent": "0",
                        "skilledTalent": "0",
                        "industryTalent": "0",
                        "highTalent": "0",
                        "parkLandArea": "0",
                        "totalArea": "0",
                        "totalInvestmentArea": "0",
                        "parkTalent": "0",
                        "patent": "0",
                        "achievement": "0",
                        "honor": "0",
                    }
                    const res2 = await addOrUpdateAPI(data)
                    if (res2.status == 0) {
                        this.$emit("Endedit")
                    }

                } else {
                    this.original = res.data
                    this.talentsList[0] = res.data
                }
            } finally {
                this.loading = false
            }

        },
        async edittalents() {
            if (!this.isEdit) {
                if (this.isBeediting) {
                    return this.$message.error("请保存完成后再进行其他编辑操作")
                }
                this.isEdit = true
                this.$emit("Startediting")
            } else {
                let data = this.original
                const regex = /^(0|[1-9]\d*)$/;
                if (
                    !regex.test(data.localTalent)
                    || !regex.test(data.publicServiceTalent)
                    || !regex.test(data.emergingTalent)
                    || !regex.test(data.youngTalent)
                    || !regex.test(data.skilledTalent)
                    || !regex.test(data.industryTalent)
                    || !regex.test(data.highTalent)
                ) {
                    return this.$message.error("人才数量不能为空且必须为整数")
                }
                const res = await addOrUpdateAPI(data)
                if (res.status == 0) {
                    await this.getindustryOrParkHonorList()
                    this.isEdit = false
                    this.$message.success("编辑成功！")
                    this.$emit("Endedit")
                }
            }

        },
    }
}
</script>

<style scoped lang="scss">
@import '../index.scss';
</style>