<template>
    <!-- 园区成果 -->
    <div class="top-left2" style="margin-top: 16px;">
        <div class="head">
            <span>园区成果</span>
        </div>
        <div class="table">
            <el-table v-loading="loading" :header-cell-style="headercell" size="large" :data="talentsList"
                style="width: 100%">
                <el-table-column prop="patent" label="专利">
                    <template #default="scope">
                        <span v-if="!isEdit">{{ scope.row.patent }}</span>
                        <el-input v-else v-model="original.patent" />
                    </template>
                </el-table-column>
                <el-table-column prop="achievement" label="成果">
                    <template #default="scope">
                        <span v-if="!isEdit">{{ scope.row.achievement }}</span>
                        <el-input v-else v-model="original.achievement" />
                    </template>
                </el-table-column>
                <el-table-column prop="honor" label="荣誉">
                    <template #default="scope">
                        <span v-if="!isEdit">{{ scope.row.honor }}</span>
                        <el-input v-else v-model="original.honor" />
                    </template>
                </el-table-column>
                <el-table-column label="操作">
                    <template #default>
                        <span @click="edittalents" class="textBuutom">{{ isEdit ? '保存' : '编辑' }}</span>
                    </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script>
import { parkMessagedetailAPI, addOrUpdateAPI } from '@/api/industry'
export default {
    data() {
        return {
            isEdit: false,
            talentsList: [],
            original: {},
            loading: false,
            headercell: { background: 'linear-gradient(180deg, #f8faff 0%, #f0f5ff 100%)', color: '#1D2129', 'font-weight': '600' }
        }
    },
    props:{
        isBeediting:{
            type: Boolean,
            default:false
        }
    },
    created() {
        this.getindustryOrParkHonorList()
    },
    methods: {
        async getindustryOrParkHonorList() {
            try {
                this.loading = true
                const res = await parkMessagedetailAPI()
                this.original = res.data
                this.talentsList[0] = res.data
            } finally {
                this.loading = false
            }

        },
        async edittalents() {
            if (!this.isEdit) {
                if(this.isBeediting){
                    return this.$message.error("请保存完成后再进行其他编辑操作")
                }
                this.isEdit = true
                this.$emit("Startediting")
            } else {
                let data = this.original
                const regex = /^(0|[1-9]\d*)$/;
                if(
                   !regex.test(data.patent) 
                || !regex.test(data.achievement)
                || !regex.test(data.honor) 
                ){
                 return  this.$message.error("成果数量不能为空且必须为整数")
                }
                const res = await addOrUpdateAPI(data)
                if (res.status == 0) {
                    await this.getindustryOrParkHonorList()
                    this.isEdit = false
                    this.$message.success("编辑成功！")
                    this.$emit("Endedit")
                }
            }

        },
    }
}
</script>

<style scoped lang="scss">
@import '../index.scss';
</style>