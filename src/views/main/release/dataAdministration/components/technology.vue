<template>
  <!-- 科技申报 -->
  <div class="top-left">
    <div class="head">
      <span>科技申报</span>
      <el-button
        size="default"
        :disabled="total >= 20"
        class="addButtton"
        @click="addIndustrial(true)"
        type="primary"
        >添加</el-button
      >
    </div>
    <div class="table">
      <el-table
        v-loading="loading"
        :header-cell-style="headercell"
        size="large"
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column prop="year" label="年份" />
        <el-table-column prop="firstQuarter" label="Q1" />
        <el-table-column prop="secondQuarter" label="Q2" />
        <el-table-column prop="thirdQuarter" label="Q3" />
        <el-table-column prop="fourthQuarter" label="Q4" />
        <el-table-column label="操作">
          <template #default="scope">
            <span @click="addIndustrial(false, scope.row)" class="textBuutom"
              >编辑</span
            >
            <span
              @click="del(scope.row.id)"
              class="textBuutom"
              style="margin-left: 15px"
              >删除</span
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="paging">
        <span class="text"
          >共<span class="total">{{ total }}</span
          >条数据</span
        >
        <el-pagination
          background
          layout="prev, pager, next"
          :total="+total"
          :current-page="pageNum"
          :page-size="pageSize"
          @current-change="pageNumchange"
        />
      </div>
    </div>
  </div>
  <el-dialog
    v-model="dialogVisible"
    :title="isAdd ? '新增' : '编辑'"
    width="30%"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      label-width="60px"
      ref="ruleFormRef"
      :model="ruleForm"
      :rules="rules"
      class="demo-ruleForm"
      status-icon
       size="default"
    >
      <el-form-item label="年份" prop="year">
        <el-select v-model="ruleForm.year" placeholder="请选择年份">
          <el-option
            v-for="(item, index) in yarnList"
            :key="index"
            :label="item"
            :value="item"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="Q1" prop="firstQuarter">
        <el-input v-model="ruleForm.firstQuarter" />
      </el-form-item>
      <el-form-item label="Q2" prop="secondQuarter">
        <el-input v-model="ruleForm.secondQuarter" />
      </el-form-item>
      <el-form-item label="Q3" prop="thirdQuarter">
        <el-input v-model="ruleForm.thirdQuarter" />
      </el-form-item>
      <el-form-item label="Q4" prop="fourthQuarter">
        <el-input v-model="ruleForm.fourthQuarter" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button size="default" class="addButtton" @click="handleClose"
          >取消</el-button
        >
        <el-button
          size="default"
          :disabled="btnLoading"
          class="addButtton"
          type="primary"
          @click="addsubmit()"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  scienceDeclarelistAPI,
  addscienceDeclareAPI,
  deletescienceDeclareAPI,
  updatescienceDeclareAPI,
} from '@/api/industry';
export default {
  data() {
    return {
      tableData: [],
      pageSize: 5,
      pageNum: 1,
      total: 0,
      loading: false,
      btnLoading: false,
      isAdd: false,
      ruleForm: {
        year: '',
        firstQuarter: '',
        secondQuarter: '',
        thirdQuarter: '',
        fourthQuarter: '',
      },
      particulars: {},
      yarnList: [],
      rules: {
        year: [{ required: true, message: '年份为必填', trigger: 'change' }],
        firstQuarter: [
          {
            pattern: /^(0|[1-9]\d*)$/,
            message: '季度数据必须为正整数',
            trigger: 'blur',
          },
        ],
        secondQuarter: [
          {
            pattern: /^(0|[1-9]\d*)$/,
            message: '季度数据必须为正整数',
            trigger: 'blur',
          },
        ],
        thirdQuarter: [
          {
            pattern: /^(0|[1-9]\d*)$/,
            message: '季度数据必须为正整数',
            trigger: 'blur',
          },
        ],
        fourthQuarter: [
          {
            pattern: /^(0|[1-9]\d*)$/,
            message: '季度数据必须为正整数',
            trigger: 'blur',
          },
        ],
      },
      type: 1, //1产业协会 2产业基金 3园区荣誉
      dialogVisible: false,
      headercell: {
        background: 'linear-gradient(180deg, #f8faff 0%, #f0f5ff 100%)',
        color: '#1D2129',
        'font-weight': '600',
      },
    };
  },
  created() {
    this.getindustryOrParkHonorList();
    var currentYear = new Date().getFullYear();
    for (var i = currentYear; i >= currentYear - 4; i--) {
      this.yarnList.push(i);
    }
  },
  methods: {
    pageNumchange(e) {
      this.pageNum = e;
      this.getindustryOrParkHonorList();
    },
    async getindustryOrParkHonorList() {
      try {
        this.loading = true;
        const res = await scienceDeclarelistAPI({
          yarn: null,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        });
        this.tableData = res.data.records;
        this.total = res.data.total;
      } finally {
        this.loading = false;
      }
    },
    async addsubmit() {
      await this.$refs.ruleFormRef.validate();
      try {
        this.btnLoading = true;
        let data = {
          year: this.ruleForm.year,
          firstQuarter: this.ruleForm.firstQuarter,
          secondQuarter: this.ruleForm.secondQuarter,
          thirdQuarter: this.ruleForm.thirdQuarter,
          fourthQuarter: this.ruleForm.fourthQuarter,
        };
        if (!this.isAdd) {
          data.id = this.particulars?.id;
        }
        const res = this.isAdd
          ? await addscienceDeclareAPI(data)
          : await updatescienceDeclareAPI(data);
        if (res.status == 0) {
          this.handleClose();
          this.isAdd
            ? this.$message.success('新增成功！')
            : this.$message.success('编辑成功！');
          this.getindustryOrParkHonorList();
        }
      } finally {
        this.btnLoading = false;
      }
    },
    del(id) {
      this.$confirm('是否确认删除？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: '',
      }).then(async () => {
        await deletescienceDeclareAPI({
          id,
        });
        // 刷新产业链
        this.getindustryOrParkHonorList();
        this.$message({
          type: 'success',
          message: '删除成功!',
        });
      });
    },
    handleClose() {
      this.dialogVisible = false;
      this.$refs.ruleFormRef.resetFields();
    },
    addIndustrial(isAdd, del) {
      this.isAdd = isAdd;
      this.particulars = del;
      this.dialogVisible = true;
      if (!isAdd) {
        this.ruleForm.year = this.particulars.year;
        this.ruleForm.firstQuarter = this.particulars.firstQuarter;
        this.ruleForm.secondQuarter = this.particulars.secondQuarter;
        this.ruleForm.thirdQuarter = this.particulars.thirdQuarter;
        this.ruleForm.fourthQuarter = this.particulars.fourthQuarter;
      } else {
        this.ruleForm = {
          year: '',
          firstQuarter: '',
          secondQuarter: '',
          thirdQuarter: '',
          fourthQuarter: '',
        };
      }
    },
  },
};
</script>

<style scoped lang="scss">
@import '../index.scss';
</style>
