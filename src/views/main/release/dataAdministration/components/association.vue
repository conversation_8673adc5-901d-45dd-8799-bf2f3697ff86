<template>
  <!-- 产业协会 -->
  <div class="top-left">
    <div class="head">
      <span>产业协会</span>
      <el-button
        size="default"
        class="addButtton"
        type="primary"
        :disabled="total >= 20"
        @click="addIndustrial(true)"
        >添加</el-button
      >
    </div>
    <div class="table">
      <el-table
        v-loading="loading"
        :header-cell-style="headercell"
        size="large"
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column prop="keyword" width="380" label="协会名称" />
        <el-table-column 
        label="操作">
          <template #default="scope">
            <span @click="addIndustrial(false, scope.row)" class="textBuutom"
              >编辑</span
            >
            <span
              @click="del(scope.row.id)"
              class="textBuutom"
              style="margin-left: 15px"
              >删除</span
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="paging">
        <span class="text"
          >共<span class="total">{{ total }}</span
          >条数据</span
        >
        <el-pagination
          background
          layout="prev, pager, next"
          :total="+total"
          :current-page="pageNum"
          :page-size="pageSize"
          @current-change="pageNumchange"
        />
      </div>
    </div>
  </div>
  <el-dialog
    v-model="dialogVisible"
    :title="isAdd ? '新增' : '编辑'"
    width="30%"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="ruleFormRef"
      :model="ruleForm"
      :rules="rules"
      class="demo-ruleForm"
      status-icon
       size="default"
    >
      <el-form-item label="名称" prop="keyword">
        <el-input v-model="ruleForm.keyword" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button size="default" class="addButtton" @click="handleClose"
          >取消</el-button
        >
        <el-button
          size="default"
          :disabled="btnLoading"
          class="addButtton"
          type="primary"
          @click="addsubmit()"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import {
  industryOrParkHonorListAPI,
  addindustryOrParkHonorAPI,
  deleteindustryOrParkHonorAPI,
  updateindustryOrParkHonorAPI,
} from '@/api/industry';
export default {
  data() {
    return {
      tableData: [],
      pageSize: 5,
      pageNum: 1,
      total: 0,
      loading: false,
      btnLoading: false,
      isAdd: false,
      ruleForm: {
        keyword: '',
      },
      particulars: {},
      rules: {
        keyword: [
          { required: true, message: '名称为必填', trigger: 'blur' },
          { max: 20, message: '最多输入20个字符', trigger: 'blur' },
        ],
      },
      type: 1, //1产业协会 2产业基金 3园区荣誉
      dialogVisible: false,
      headercell: {
        background: 'linear-gradient(180deg, #f8faff 0%, #f0f5ff 100%)',
        color: '#1D2129',
        'font-weight': '600',
      },
    };
  },
  created() {
    this.getindustryOrParkHonorList();
  },
  methods: {
    pageNumchange(e) {
      this.pageNum = e;
      this.getindustryOrParkHonorList();
    },
    async getindustryOrParkHonorList() {
      try {
        this.loading = true;
        const res = await industryOrParkHonorListAPI({
          type: this.type,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        });
        this.tableData = res.data.records;
        this.total = res.data.total;
      } finally {
        this.loading = false;
      }
    },
    async addsubmit() {
      await this.$refs.ruleFormRef.validate();
      try {
        this.btnLoading = true;
        let data = {
          type: this.type,
          keyword: this.ruleForm.keyword,
        };
        let data1 = {
          type: this.type,
          keyword: this.ruleForm.keyword,
          id: this.particulars?.id,
        };
        const res = this.isAdd
          ? await addindustryOrParkHonorAPI(data)
          : await updateindustryOrParkHonorAPI(data1);
        if (res.status == 0) {
          this.handleClose();
          this.isAdd
            ? this.$message.success('新增成功！')
            : this.$message.success('编辑成功！');
          this.getindustryOrParkHonorList();
        }
      } finally {
        this.btnLoading = false;
      }
    },
    del(id) {
      this.$confirm('是否确认删除？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: '',
      }).then(async () => {
        await deleteindustryOrParkHonorAPI({
          id,
        });
        // 刷新产业链
        this.getindustryOrParkHonorList();
        this.$message({
          type: 'success',
          message: '删除成功!',
        });
      });
    },
    handleClose() {
      this.dialogVisible = false;
      this.$refs.ruleFormRef.resetFields();
    },
    addIndustrial(isAdd, del) {
      this.isAdd = isAdd;
      this.particulars = del;
      this.dialogVisible = true;
      if (!isAdd) {
        this.ruleForm.keyword = this.particulars.keyword;
      } else {
        this.ruleForm.keyword = '';
      }
    },
  },
};
</script>

<style scoped lang="scss">
@import '../index.scss';
</style>
