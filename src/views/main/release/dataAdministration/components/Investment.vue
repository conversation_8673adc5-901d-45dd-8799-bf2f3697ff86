<template>
  <!-- 年度招商情况 -->
  <div class="top-left">
    <div class="head">
      <span>年度招商情况</span>
      <el-button
        size="default"
        class="addButtton"
        :disabled="total >= 20"
        @click="addIndustrial(true)"
        type="primary"
        >添加</el-button
      >
    </div>
    <div class="table">
      <el-table
        v-loading="loading"
        :header-cell-style="headercell"
        :data="tableData"
        style="width: 100%"
      >
        <el-table-column prop="enterpriseName" label="企业名称" />
        <el-table-column prop="businessScope" label="经营范围" />
        <el-table-column
          style="display: flex; align-items: center"
          prop="passCount"
          label="企业图片"
        >
          <template #default="scope">
            <el-image
              style="width: 40px; height: 40px"
              :src="scope.row.realImageUrl"
              :fit="fit"
            />
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <span @click="addIndustrial(false, scope.row)" class="textBuutom"
              >编辑</span
            >
            <span
              @click="del(scope.row.id)"
              class="textBuutom"
              style="margin-left: 15px"
              >删除</span
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="paging">
        <span class="text"
          >共<span class="total">{{ total }}</span
          >条数据</span
        >
        <el-pagination
          background
          layout="prev, pager, next"
          :total="+total"
          :current-page="pageNum"
          :page-size="pageSize"
          @current-change="pageNumchange"
        />
      </div>
    </div>
  </div>
  <el-dialog
    v-model="dialogVisible"
    size="default"
    :title="isAdd ? '新增' : '编辑'"
    width="30%"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      label-width="100px"
      ref="ruleFormRef"
      :model="ruleForm"
      :rules="rules"
      class="demo-ruleForm"
      status-icon
       size="default"
    >
      <el-form-item label="企业名称" prop="enterpriseName">
        <el-input
          v-model="ruleForm.enterpriseName"
          placeholder="请输入企业名称"
        />
      </el-form-item>
      <el-form-item label="经营范围" prop="businessScope">
        <el-input
          v-model="ruleForm.businessScope"
          placeholder="请输入经营范围"
        />
      </el-form-item>
      <el-form-item label="企业图片" prop="path">
        <el-upload
          class="avatar-uploader"
          :action="url"
          :show-file-list="false"
          :on-success="handleAvatarSuccess"
          :before-upload="beforeAvatarUpload"
          :accept="acceptimg"
        >
          <img v-if="imageUrlFile" :src="imageUrlFile" class="avatar" />
          <div v-else class="add"></div>
        </el-upload>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button size="default" class="addButtton" @click="handleClose"
          >取消</el-button
        >
        <el-button
          size="default"
          :disabled="btnLoading"
          class="addButtton"
          type="primary"
          @click="addsubmit()"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script>
import { baseEvn } from '@/utils/utils';
import {
  annualInvestmentSituationlistAPI,
  addannualInvestmentSituationAPI,
  deleteannualInvestmentSituationAPI,
  updataannualInvestmentSituationAPI,
} from '@/api/industry';
export default {
  data() {
    return {
      url: `${baseEvn}upload`,
      acceptimg: '.jpg,.png,.JPEG',
      tableData: [],
      pageSize: 5,
      pageNum: 1,
      total: 0,
      loading: false,
      btnLoading: false,
      isAdd: false,
      ruleForm: {
        enterpriseName: '',
        businessScope: '',
        path: '',
      },
      particulars: {},
      rules: {
        enterpriseName: [
          { required: true, message: '企业名称为必填', trigger: 'blur' },
          { max: 20, message: '最多输入20个字符', trigger: 'blur' },
        ],
        businessScope: [
          { required: true, message: '经营范围为必填', trigger: 'blur' },
          { max: 20, message: '最多输入20个字符', trigger: 'blur' },
        ],
        path: [{ required: true, message: '宣传图为必填', trigger: 'blur' }],
      },
      dialogVisible: false,
      headercell: {
        background: 'linear-gradient(180deg, #f8faff 0%, #f0f5ff 100%)',
        color: '#1D2129',
        'font-weight': '600',
      },
      imageUrlFile: null,
    };
  },
  created() {
    this.getindustryOrParkHonorList();
  },
  methods: {
    handleAvatarSuccess(res, file) {
      // console.log(res, "res");
      this.ruleForm.path = res.data.value;
      this.imageUrlFile = URL.createObjectURL(file.raw);
    },
    beforeAvatarUpload(rawFile) {
      let formatList = this.acceptimg.split(',');
      const lastIndex = rawFile.name.lastIndexOf('.');
      const result = rawFile.name.substring(lastIndex);
      const isExists = formatList.includes(result);
      if (!isExists) {
        this.$message.error('您上传的文件格式不规范!');
        return false;
      } else if (rawFile.size / 1024 / 1024 > 20) {
        this.$message.error('上传图片大小不能超过 20MB!');
        return false;
      }
      return true;
    },
    pageNumchange(e) {
      this.pageNum = e;
      this.getindustryOrParkHonorList();
    },
    async getindustryOrParkHonorList() {
      try {
        this.loading = true;
        const res = await annualInvestmentSituationlistAPI({
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        });
        this.tableData = res.data.records;
        this.total = res.data.total;
      } finally {
        this.loading = false;
      }
    },
    async addsubmit() {
      await this.$refs.ruleFormRef.validate();
      try {
        this.btnLoading = true;
        let data = {
          enterpriseName: this.ruleForm.enterpriseName,
          businessScope: this.ruleForm.businessScope,
          path: this.ruleForm.path,
        };
        if (!this.isAdd) {
          data.id = this.particulars?.id;
        }
        const res = this.isAdd
          ? await addannualInvestmentSituationAPI(data)
          : await updataannualInvestmentSituationAPI(data);
        if (res.status == 0) {
          this.handleClose();
          this.isAdd
            ? this.$message.success('新增成功！')
            : this.$message.success('编辑成功！');
          this.getindustryOrParkHonorList();
        }
      } finally {
        this.btnLoading = false;
      }
    },
    del(id) {
      this.$confirm('是否确认删除？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: '',
      }).then(async () => {
        await deleteannualInvestmentSituationAPI({
          id,
        });
        // 刷新产业链
        this.getindustryOrParkHonorList();
        this.$message({
          type: 'success',
          message: '删除成功!',
        });
      });
    },
    handleClose() {
      this.dialogVisible = false;
      this.$refs.ruleFormRef.resetFields();
    },
    addIndustrial(isAdd, del) {
      this.isAdd = isAdd;
      this.particulars = del;
      this.dialogVisible = true;
      if (!isAdd) {
        this.ruleForm.enterpriseName = this.particulars.enterpriseName;
        this.ruleForm.businessScope = this.particulars.businessScope;
        this.ruleForm.path = this.particulars.path;
        this.imageUrlFile = this.particulars.realImageUrl;
      } else {
        this.ruleForm = {
          enterpriseName: '',
          businessScope: '',
          path: '',
        };
        this.imageUrlFile = null;
      }
    },
  },
};
</script>

<style>
.add {
  cursor: pointer;
  width: 36px;
  height: 36px;
  background: no-repeat url('@/assets/svg/add.svg');
  background-size: contain;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  width: 100px;
  height: 100px;
}

.avatar-uploader .el-upload:hover {
  border-color: #437bff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px !important;
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
</style>
<style scoped lang="scss">
@import '../index.scss';
</style>
