<template>
  <div v-if="!preview" class="det">
    <div class="title">
      <h3>
        {{
          state == "ck"
            ? "查看"
            : state == "bj"
            ? "编辑"
            : state == "xy"
            ? "续租"
            : "新增"
        }}信息
      </h3>
      <span @click="returnFn" class="btn">X</span>
    </div>
    <div class="boxes">
      <el-form
        :model="form"
        ref="form"
        :rules="rules"
        size="default"
        :disabled="state == 'ck'"
        style="width: 100%"
        label-width="140px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="form.title" placeholder="请输入标题,最多30字" />
        </el-form-item>
        <el-form-item prop="type" label="类型">
          <el-select v-model="form.type" placeholder="请选择类型">
            <el-option
              v-for="item in useType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="input_type" label="文章格式类型">
          <el-select
            @change="changtype"
            v-model="form.input_type"
            placeholder="请选择文章格式类型"
          >
            <el-option
              v-for="item in formatType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="form.type == 2"
          prop="is_declare"
          label="是否可申报"
        >
          <el-radio-group v-model="form.is_declare">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="展示图">
          <el-upload
            class="avatar-uploader"
            :action="url"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload"
          >
            <img v-if="form.imageUrl" :src="form.imageUrl" class="avatar" />

            <el-icon v-else class="avatar-uploader-icon">
              <div class="add"></div>
            </el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item prop="visiable" label="可见">
          <el-radio-group v-model="form.visiable">
            <el-radio :label="'1'">所有人可见</el-radio>
            <el-radio :label="'2'">仅园区账号可见</el-radio>
            <el-radio :label="'3'">仅政务账号可见</el-radio>
            <el-radio :label="'4'">仅企业账号可见</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="form.input_type == 2" label="链接" prop="link_url">
          <el-input v-model="form.link_url" placeholder="请输入链接" />
        </el-form-item>
        <el-form-item v-if="form.input_type == 1" label="内容" prop="content">
          <!--   <tinymce v-model="form.content"></tinymce> -->
          <div class="editor">
            <Toolbar
              style="border-bottom: 1px solid #ccc"
              :editor="editorRef"
              :defaultConfig="toolbarConfig"
              :mode="mode"
            />
            <Editor
              isDisabled="true"
              style="height: 450px; overflow-y: hidden"
              v-model="form.content"
              :defaultConfig="editorConfig"
              :mode="mode"
              @onCreated="handleCreated"
            />
          </div>
        </el-form-item>
        <el-form-item v-if="state !== 'ck'" class="footer-btn">
          <el-button size="default" @click="returnFn">取消</el-button>
          <el-button size="default" type="primary" @click="onSubmits"
            >保存</el-button
          >
          <el-button
            size="default"
            v-if="state == 'tj'"
            type="primary"
            @click="onSubmits(66)"
            >提交</el-button
          >
        </el-form-item>
      </el-form>
    </div>
  </div>
  <div v-else class="det">
    <div class="title">
      <h3>审核</h3>
      <span @click="returnFn" class="btn">X</span>
    </div>
    <div class="boxes">
      <div class="head">
        <h2>{{ form.title }}</h2>
        <!--  <h3>{{ form.type }}</h3> -->
        <div v-if="form.input_type == 2">
          链接<a :href="form.link_url" target="_blank">{{ form.link_url }}</a>
        </div>
        <div style="width: 100%; height: 300px" v-else>
          <div v-html="form.content" style="width: 100%; height: 300px"></div>
        </div>
      </div>
      <div  class="footer-btn">
        <el-button class="check" size="default" @click="dialogVisible = true" type="primary"
          >审核</el-button
        >
      </div>
      <el-dialog
        v-if="dialogVisible"
        :close-on-click-modal="false"
        v-model="dialogVisible"
        title="审核"
        width="30%"
      >
        <el-form
          label-position="top"
          :model="formaudit"
          ref="formaudit"
          :rules="auditrules"
          size="default"
          style="width: 100%"
          label-width="140px"
        >
          <el-form-item prop="status" label="审核结果">
            <el-radio-group v-model="formaudit.status">
              <el-radio :label="'2'">审核通过</el-radio>
              <el-radio :label="'0'">审核不通过</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审核日期" prop="auditTime">
            <el-date-picker
              v-model="formaudit.auditTime"
              type="date"
              placeholder="请选择审核日期"
              value-format="x"
              :size="size"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button size="default" @click="dialogVisible = false"
              >取消</el-button
            >
            <el-button size="default" type="primary" @click="present">
              确认
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { useRoute, useRouter } from "vue-router";
import {
  queryByIdAPI,
  formSubmitAPI,
  auditAPI,
} from "@/api/system/propertyRepair";
import { baseEvn } from "@/utils/utils";
import tinymce from "@/components/tinymce/index.vue";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import "@wangeditor/editor/dist/css/style.css";
import { onBeforeUnmount, ref, shallowRef, onMounted } from "vue";
export default {
  data() {
    return {
      editorRef: shallowRef(),
      route: useRoute(),
      router: useRouter(),
      enterprisesx: [],
      mode: "default",
      editorConfig: {
        placeholder: "请输入内容...",
        MENU_CONF: {
          uploadImage: {
            // 上传图片请求接口路径
            server: `${baseEvn}uploadAndReturnOssUrl`,
            // 后端接收的文件名称
            fieldName: "file",
            maxFileSize: 10 * 1024 * 1024, // 上传图片10M
            // 上传的图片类型
            allowedFileTypes: ["image/*"],
            // 小于该值就插入 base64 格式（而不上传），默认为 0
            base64LimitSize: 10 * 1024, // 10MB
            // 自定义上传图片返回格式【后端返回的格式】
            customInsert(res, insertFn) {
              if (res.status !== "0") {
                ElMessage.error("上传文件失败，" + res.message);
                return;
              }
              // 从 res 中找到 url alt href ，然后插入图片 ,根据后端实际返回的字段来
              insertFn(res.data, "", res.data);
            },

            // 将 meta 拼接到 url 参数中，默认 false
            metaWithUrl: true,
            // 单个文件上传成功之后
            onSuccess(file, res) {
              if (res.status == "0") {
                ElMessage.success(`${file.name} 上传成功`);
                return;
              } else {
                ElMessage.warning(`${file.name} 上传出了点异常`);
                return;
              }
              // console.log(`${file.name} 上传成功`, res)
              //ElMessage.success(`${file.name} 上传成功`, res)
            },
            // 单个文件上传失败
            onFailed(file, res) {
              ElMessage.error(`${file.name} 上传失败`);
            },
            // 上传错误，或者触发 timeout 超时
            onError(file, err, res) {
              // console.log(err, res)
              ElMessage.error(`${file.name} 上传出错`);
            },
          },
        },
      },
      toolbarConfig: {
        excludeKeys: [
          "todo", // 代办

          // 上传图片
          {
            key: "group-image",
            title: "图片",
            iconSvg:
              '<svg viewBox="0 0 1024 1024"><path d="M959.877 128…l224.01-384 256 320h64l224.01-192z"></path></svg>',
            menuKeys: Array(2),
          },
          // 上传视频
          "group-video",

          "insertTable", // 插入表格

          "codeBlock", // 代码块
        ],
      },
      form: {
        link_url: "",
        is_declare: null,
      },
      formaudit: {},
      preview: false,
      imageUrlFile: null,
      state: "",
      dialogVisible: false,
      auditrules: {
        status: [{ required: true, message: "审核结果必填", trigger: "blur" }],
        auditTime: [
          { required: true, message: "审核日期必填", trigger: "blur" },
        ],
      },
      rules: {
        title: [{ required: true, message: "标题必填", trigger: "blur" }],
        type: [{ required: true, message: "类型必填", trigger: "blur" }],
        is_declare: [
          { required: true, message: "是否可申报必填", trigger: "blur" },
        ],
        input_type: [
          { required: true, message: "文章格式类型必填", trigger: "blur" },
        ],
        link_url: [{ required: true, message: "链接必填", trigger: "blur" }],
        content: [{ required: true, message: "内容必填", trigger: "blur" }],
        imageUrl: [{ required: true, message: "展示图必填", trigger: "blur" }],
        visiable: [
          { required: true, message: "可见范围必填", trigger: "blur" },
        ],
      },
      useType: [
        { label: "公告", value: "1" },
        { label: "政策", value: "2" },
        { label: "党建要闻", value: "3" },
        { label: "党政法规", value: "4" },
        { label: "活动", value: "5" },
        { label: "资讯", value: "6" },
        { label: "政策新", value: "7" },
        { label: "园区荣誉", value: "8" },
      ],
      formatType: [
        { label: "链接", value: "2" },
        { label: "原文内容", value: "1" },
      ],
      url: `${baseEvn}upload`,
    };
  },
  components: {
    tinymce,
    Editor,
    Toolbar,
  },
  created() {
    this.url = baseEvn + "upload";
    // console.log('this.$route.query', this.$route.query);
    if (this.$route.query.id) {
      if (this.$route.query.st == 3) {
        this.preview = true;
      }
      if (this.$route.query.st == 1) {
        this.state = "ck";
      } else {
        this.state = "bj";
      }
      this.getOneAPI();
    } else {
      this.state = "tj";
    }
  },
  watch: {
    /*         'form.link_url'(e) {
            if (e != '' && e != null) {
                this.form.content = ''
            }
        },
        'form.type'(e) {
            if (e !== '2') {
                this.form.is_declare = ''
            }
        } */
  },
  methods: {
    handleCreated(editor) {
      this.editorRef = editor; // 记录 editor 实例，重要！
      // console.log('editorConfig.value.MENU_CONF', editor.getConfig())
      // editor.disable()
      //     const toolbar = DomEditor.getToolbar(editorRef.value)
      // const curToolbarConfig = toolbar.getConfig()
      // console.log( curToolbarConfig.toolbarKeys ) // 当前菜单排序和分组
    },
    async present() {
      await this.$refs.formaudit.validate();
      const res = await auditAPI({
        id: this.$route.query.id,
        status: this.formaudit.status,
        auditTime: this.formaudit.auditTime,
      });
      if (res.status == 0) {
        this.$message.success("操作成功！");
        this.returnFn();
      }
    },
    handleAvatarSuccess(res, file) {
      // console.log(res, 'res');
      this.form.imageUrl = URL.createObjectURL(file.raw);
      this.imageUrlFile = res.data.value;
    },
    beforeAvatarUpload(file) {
      const isLt2M = file.size / 1024 / 1024 < 5;
      if (!isLt2M) {
        this.$message.error("上传图片大小不能超过 5MB!");
      }
      return isLt2M;
    },
    async getOneAPI() {
      const res = await queryByIdAPI({
        id: this.$route.query.id,
      });
      this.form.title = res.data.title; //标题
      this.form.type = res.data.type; //类型
      this.form.link_url = res.data.linkUrl; //链接
      this.form.visiable = res.data.visiable; //可见
      this.form.imageUrl = res.data.showImageUrl;
      this.form.is_declare = res.data.isDeclare;
      this.form.input_type = res.data.inputType;
      if (res.data.isDeclare) {
        this.form.is_declare = 1;
      } else {
        this.form.is_declare = 0;
      }
      (this.imageUrlFile = res.data.imgurl),
        (this.form.content = res.data.content);
    },
    changtype() {
      this.form.is_declare = "";
      this.form.content = "";
      this.form.link_url = "";
    },
    async onSubmits(state) {
      if (!this.form.content && !this.form.link_url) {
        return this.$message.error("链接和内容必须填写一个才能提交哦~");
      }
      await this.$refs.form.validate();
      let imageUrlFile = null;
      if (this.imageUrlFile) {
        imageUrlFile = this.imageUrlFile;
      }
      let data = [];
      if (this.state == "tj") {
        let is_declare = null;
        if (this.form.type == 2) {
          is_declare = this.form.is_declare;
        }
        data = {
          businessCode: "information",
          action: "insert",
          element: {
            title: this.form.title, //标题
            type: this.form.type, //类型
            is_declare, //是否可以申报
            link_url: this.form.link_url,
            input_type: this.form.input_type,
            visiable: this.form.visiable, //可见范围
            image_url: imageUrlFile, //图片url
            content: this.form.content, //内容
          },
          conditions: [],
        };
        if (state === 66) {
          data.element.status = 1;
        }
      } else {
        let is_declare = null;
        if (this.form.type == 2) {
          is_declare = this.form.is_declare;
        }
        data = {
          businessCode: "information",
          action: "update",
          element: {
            title: this.form.title, //标题
            type: this.form.type, //类型
            is_declare, //是否可以申报
            link_url: this.form.link_url,
            input_type: this.form.input_type,
            visiable: this.form.visiable, //可见范围
            image_url: imageUrlFile, //图片url
            content: this.form.content, //内容
          },
          conditions: [
            {
              key: "id",
              value: this.$route.query.id,
            },
          ],
        };
      }

      const res = await formSubmitAPI(data);
      if (res.status == 0) {
        this.$message.success("操作成功！");
        this.returnFn();
      }
    },
    returnFn() {
      this.router.push("/backstage/release");
    },
  },
};
</script>
<style>
.add {
  cursor: pointer;
  width: 36px;
  height: 36px;
  background: no-repeat url("@/assets/svg/add.svg");
  background-size: contain;
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  margin-top: 10px;
}

.avatar-uploader .el-upload:hover {
  border-color: #437bff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px !important;
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
}

</style>
<style scoped lang="scss">
 .title {
  h3{
    font-weight: 600;
  }
 }
.editor{
  border: 1px solid #eee;
  border-radius: 4px;
  .w-e-bar.w-e-bar-show.w-e-toolbar{
  border-radius: 4px;

  }
}
::v-deep {
  .el-textarea__inner {
    height: 200px;
  }
  .el-select__popper,
  .el-overlay {
    z-index: 2003 !important;
  }
}

.custom-form {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.det {
  padding: 0px 40px;
  width: 100%;
  height: 100%;

  .title {
    display: flex;
    justify-content: space-between;

    .btn {
      cursor: pointer;
    }
  }
.footer-btn{
  position: fixed;
  bottom: 0;
  .check{
    margin-bottom:16px;

  }
}
  .boxes {
    width: 50%;
    height: calc(100vh - 180px);
    display: flex;
    flex-wrap: wrap;
    margin: 0px auto  ;
    margin-bottom: 80px;
    overflow-y: scroll;
    position: relative;
    // padding: 0 100px;

    .head {
      display: flex;
      flex-direction: column;
      align-items: center;
      height: auto;
      width: 90%;
    }

    .boxss {
      width: 30%;
      margin-left: 2%;
      height: 200px;
      margin-top: 30px;
      padding: 20px;
      border: 1px solid #f2f2f2;
      border-radius: 20px;
    }

    .btn {
      width: 30%;
      margin-left: 2%;
      height: 200px;
      margin-top: 30px;
      padding: 20px;
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>