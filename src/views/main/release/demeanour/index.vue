<script setup>
import AmisComponent from "@/components/amis/index.vue";
import { perFix, baseEvn, token } from "@/utils/utils";
import _ from "lodash";
import { ref } from "vue";
import { ElMessage } from "element-plus";

const getRealName = (fileArrays, type, allData, myName) => {
  if (fileArrays) {
    let fileArray = fileArrays?.split(",");
    if (type === 2) {
      let file = fileArray?.map((e, index) => {
        let data = {};
        let realName = null;
        let id = null;
        data = {
          type: type,
          title: myName[index],
          //title: '测试名称',
          url: e,
        };
        return data;
      });
      return file;
    } else {
      let file = fileArray?.map((e) => {
        let data = {};
        let realName = null;
        let id = null;
        // 如果是全路径，意味没删除，去查allData相应的数据
        if (e.slice(0, 5) === "https") {
          let findData = _.find(allData, (item) => item.show_url === e);
          if (findData) {
            realName = findData.title;
            data = {
              id: findData.id,
              type: findData.type,
              title: realName,
              url: findData.url,
            };
          }
          return data;
        } else {
          // 如果新增则正常截取
          let list = e.split("/");
          let name = list[list.length - 1].split("-")[1];
          let index = name.lastIndexOf(".");
          realName = name?.slice(0, index);
          data = {
            type: type,
            title: realName,
            url: e,
          };
          return data;
        }
      });
      return file;
    }
  }
};
let drawerData = (title, state) => {
  return {
    type: "page",
    title: "大屏视频展示管理",

    id: "u:07e183dd88f8",
    body: [
      {
        id: "u:775fee587691",
        type: "form",
        initApi: {
          url: `billund/getList`,
          headers: {
            token: `${token()}`,
          },
          method: "post",
          data: {
            businessCode: "park_style",
            conditions: [
              {
                compareType: "orderBy",
                key: "id",
                isDesc: false,
              },
            ],
          },
          adaptor: function (payload, response, api, context) {
            let video = [];
            let selectedValue = "";
            let list = payload.elements.map((item) => {
              if (item.type === "2") {
                video.push({
                  value: item.id,
                  label: item.title,
                });
                if (item.is_large_screen === "1") {
                  selectedValue = item.id;
                }
              }
            });
            let data = {
              selectedValue: selectedValue,
              video: video,
            };
            return {
              data: data,
              msg: "请求成功",
              status: 0,
            };
          },
        },
        api: {
          url: `parkStyle/setLargeScreenVideo`,
          headers: {
            token: `${token()}`,
          },
          method: "get",
          data: {
            id: "${id}",
          },
          adaptor: function (payload, response, api, context) {
            if (response.status === "0") {
              ElMessage({
                type: "success",
                message: "设置成功",
              });
              return {
                data: response.data,
                // msg:  response.msg,
                status: 0,
              };
            } else {
              return {
                data: response.data,
                msg: response.msg,
                status: response.status,
              };
            }
          },
        },
        body: [
          {
            label: "大屏视频",
            name: "id",
            required: true,
            type: "select",
            size: "lg",
            value: "${selectedValue}",
            source: "${ video}",
          },
        ],
      },
    ],
  };
};
// 企业列表
const amisjson = {
  type: "page",
  body: [
    {
      id: "u:775fee587691",
      type: "form",
      name: "thisForm",
      title: "",
      mode: "horizontal",
      dsType: "api",
      feat: "Insert",
      reload: "thisForm",
      initApi: {
        url: `billund/getList`,
        headers: {
          token: `${token()}`,
        },
        method: "post",
        data: {
          businessCode: "park_style",
          conditions: [
            {
              compareType: "orderBy",
              key: "id",
              isDesc: false,
            },
          ],
        },
        adaptor: function (payload, response, api, context) {
          // console.log(payload);
          let image = "";
          let attachment = undefined;
          let lists = [];
          let list = payload.elements.map((item) => {
            if (item.type === "1") {
              image =
                image === "" ? `${item.show_url}` : `${image},${item.show_url}`;
            } else {
              lists = [...lists, { url: item.show_url, name: item.title }];
              attachment = !attachment
                ? `${item.show_url}`
                : `${attachment},${item.show_url}`;
            }
          });
          return {
            // attachment:[{url:'attachment',name:111}],
            data: { image, attachment, lists, allData: payload.elements },
            msg: "请求成功",
            status: 0,
          };
        },
      },

      body: [
        {
          type: "button",
          size: "md",
          label: "大屏视频展示管理",
          primary: true,
          actionType: "dialog",
          dialog: drawerData("预览", "update"),
          className: "bigButton",
        },
        {
          type: "input-image",
          label: "图片上传",
          name: "image",
          autoUpload: true,
          proxy: true,
          uploadType: "fileReceptor",
          imageClassName: "r w-full",
          id: "u:b7aceb645670",
          multiple: true,
          maxLength: 9,
          useChunk: false,
          // "accept": ".jpg,.png,.PNG,.JPEG,",
          maxSize: 10495000,
          delimiter: ",",
          className: "uploadImg",
          useChunk: false,
          receiver: {
            method: "POST",
            url: `upload`,
            headers: {
              token: `${token()}`,
            },
          },
        },
        {
          type: "input-file",
          name: "attachment",
          //name: "lists",
          accept: ".csv,.mp4,.mov,.avi,.wmv",
          label: "视频上传",
          fileField: "file",
          btnLabel: "上传视频",
          maxLength: 10,
          maxSize: 268435456,
          multiple: true,
          useChunk: false,
          downloadUrl: false,
          //valueField:"url",
          //nameField:"name",
          // accept: "*",
          //   "autoFill": {
          //   "myUrl": "${fileName}"
          // },
          autoFill: {
            myUrl: "${items|pick:url}",
            myName: "${items|pick:name}",
            lastUrl: "${items|last|pick:url}",
          },
          receiver: {
            method: "POST",
            url: `uploadByPart`,
            headers: {
              token: `${token()}`,
            },
            adaptor: function (payload, response, api, context) {
              let data = {
                url: response?.data.viewUrl,
                value: response?.data.url,
                name: response?.data.fileName,
              };
              return {
                ...response,
                data,
              };
            },
          },
        },
        /*         {
          type: "tpl",
          tpl: "${myUrl}" ,
        }, */
        {
          type: "list",
          label: "附件",
          source: "${myUrl}",
          listItem: {
            body: [
              {
                type: "video",
                aspectRatio: "16:9",
                src: "${myUrl[index]}",
                className: "videoView",
              },
              {
                type: "tpl",
                tpl: "${myName[index]}",
                /*    "onEvent": {
                   "click": {
                     "actions": [
                     {
                     "actionType": "dialog",
                     "dialog": {
                       "title": "修改视频名称",
                       data:{
                        title:"${myUrl[index]}",
                       },
                       "body": {
                       "type": "form",
                       "body": [
                         {
                           "type": "input-text",
                           "name":  "title",
                           "required": true,
                           "placeholder": "请输入视频名称",
                           "label": "视频名称"
                         },
                         ],
                         
                        },
                        "actions": [
                             {
                               "type": "button",
"size": "md",
                               "label": "确认",
                               "onEvent": {
                               "click": {
                                 "actions": [
                                 {
                        "actionType": "update",
                        "data": {
                          "title": "${title}"
                        }
                      },
                                 ]
                               }
                               }
                             },
                         ]
                     }
                     },
                     ]
                   }
                }  */
              },
            ],
          },
        },
        {
          type: "button",
          size: "md",
          label: "提交",
          onEvent: {
            click: {
              actions: [
                {
                  actionType: "submit",
                  componentId: "u:775fee587691",
                },
              ],
            },
          },
          level: "primary",
          id: "u:033aeb11ea19",
          className: "buttonSubmit",
        },
      ],
      api: {
        url: `parkStyle/saveOrUpdate`,
        method: "post",
        headers: {
          token: `${token()}`,
        },
        data: {
          image: "${image}",
          file: "${attachment}",
          id: "${id}",
          allData: "${allData}",
          myName: "${myName}",
        },
        requestAdaptor: (rest) => {
          let { image, file, id, allData, myName, ...other } = rest.data;
          let images = image && getRealName(image, 1, allData);
          let files = file && getRealName(file, 2, allData, myName);
          let datas = [...images, ...files];
          return {
            ...rest,
            data: datas,
          };
        },
        adaptor: function (payload, response, api, context) {
          if (response.status === "0") {
            ElMessage({
              type: "success",
              message: "请求成功",
            });
            return {
              data: response.data,
              // msg:  response.msg,
              status: 0,
            };
          } else {
            return {
              data: response.data,
              msg: response.msg,
              status: response.status,
            };
          }
        },
      },
      actions: [],

      // resetAfterSubmit: true,
    },
  ],
  title: "",
  id: "u:c070dd0c3b4e",
};
</script>

<template>
  <AmisComponent :amisjson="amisjson" class="uploaderView" />
</template>
<style scoped lang="scss">
.tab {
  padding-top: 20px;
  padding-left: 10px;
}

.on {
  background-color: #169bd5;
  color: #fff;
}
</style>
<style  lang='scss'>
.buttonSubmit {
  margin-left: 50% !important;
}

.videoView {
  .antd-Video {
    width: 218px;
    height: 122px;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    // height: 300px !important;
  }

  video {
  }
}

.bigButton {
  margin-top: 15px !important;
  float: right;
}

.uploadImg {
  padding-top: 60px !important;
}

.uploaderView {
  .antd-Tooltip.is-invalid {
    display: none;
  }
  .antd-List-items {
    display: flex;
    flex-wrap: wrap;
  }

  .antd-List-items > * {
    flex: 0.2;
  }

  .antd-List-items > *:nth-child(2n) {
    flex: 0.2;
  }
  .antd-Video {
    width: 300px;
    height: 170px;
  }
}
</style>