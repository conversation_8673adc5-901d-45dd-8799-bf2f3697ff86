<template>
  <div class="box">
    <div class="cont">
      <div class="head">
        <el-button size="default" type="primary" @click="videodigFn">大屏视频展示管理</el-button>
        <el-dialog v-model="videodig" title="大屏视频展示管理" width="30%">
          <el-select v-model="viedvalue" class="m-2" placeholder="请选择展示视频" size="large">
            <el-option v-for="item in options" :key="item.id" :label="item.title.slice(0, 50)" :value="item.id" />
          </el-select>
          <template #footer>
            <span class="dialog-footer">
              <el-button size="default" @click="videodig = false">取消</el-button>
              <el-button size="default" type="primary" @click="showviedeo">
                确认
              </el-button>
            </span>
          </template>
        </el-dialog>
      </div>
      <div class="const">
        <div class="centerImg">
          <span class="vodeoUP">图片上传</span>
          <el-upload class="avatar-uploader" v-model:file-list="list.imgs" list-type="picture-card" :action="url"
            :on-success="handleAvatarSuccess" :on-preview="handlePictureCardPreview" :before-upload="beforeAvatarUpload"
            :accept="acceptimg" :limit="20">
            <div class="add"></div>
            <template #file="{ file }">
              <div>
                <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                   <div class="interactive"></div>
                  </span>
                  <span class="el-upload-list__item-delete" @click="handleDownload(file)">
                   <div class="edits"></div>
                  </span>
                  <span class="el-upload-list__item-delete" @click="handleRemove(file)">
                   <div class="del"></div>
                  </span>
                  <div class="flienName">{{ file.name }}</div>
                </span>
              </div>
            </template>
          </el-upload>
          <el-dialog v-model="imgnamedig" title="编辑图片名称" width="30%">
              <el-input v-model="editimgName" placeholder="请输入图片名称" />
              <template #footer>
                <span class="dialog-footer">
                  <el-button size="default" @click="imgnamedig = false">取消</el-button>
                  <el-button size="default" type="primary" @click="changeimgname">
                    确认
                  </el-button>
                </span>
              </template>
            </el-dialog>
          <el-dialog v-model="previewdialog">
            <div style="display: flex;align-items: center;justify-content: center;">
              <img :src="previewurl" class="previewimg" alt="Preview Image" />
            </div>
          </el-dialog>
        </div>
        <div class="centerVideo">
          <div class="top">
            <span class="vodeoUP">视频上传</span>

            <el-upload style="width: 500px;" v-model:file-list="list.video" :on-success="onSuccess" :on-remove="onRemove"
              :action="viedourl" multiple :before-upload="beforeUpload" :accept="accept" :limit="10">
              <el-button size="default" type="primary">上传视频</el-button>
              <span class="hint">（单个文件大小不超过 256 MB，最多可上传10个）</span>
            </el-upload>
          </div>
          <div class="videoList">
            <div v-for="(it, index) in  showvideo" :key="index" class="videalone">
              <video controls muted loop class="video" :src="it.show_url">
              </video>
              <div class="editsss">
                <span class="title">{{ it.title }}</span>
                <div @click="bg(it.title, index)" class="edit"></div>
              </div>
            </div>
            <el-dialog v-model="videonamedig" title="编辑视频名称" width="30%">
              <el-input v-model="editName" placeholder="请输入视频名称" />
              <template #footer>
                <span class="dialog-footer">
                  <el-button size="default" @click="videonamedig = false">取消</el-button>
                  <el-button size="default" type="primary" @click="changeviedoname">
                    确认
                  </el-button>
                </span>
              </template>
            </el-dialog>
          </div>

        </div>
      </div>
      <div class="bto">
        <el-button size="default" @click="reset">重置</el-button>
        <el-button size="default" type="primary" @click="submit">提交</el-button>
      </div>
    </div>

  </div>
</template>

<script>
import { mienList, setLargeScreenVideoAPI, saveOrUpdateAPI } from '@/api/system/propertyRepair'
import { ElMessage } from 'element-plus'
import { baseEvn } from '@/utils/utils'
export default {
  data() {
    return {
      list: {
        imgs: [
        ],
        video: []
      },
      "accept": ".mp4,.avi,.wmv",
      acceptimg: ".jpg,.png,.JPEG",
      videodig: false,
      videonamedig: false,
      previewdialog: false,
      previewurl: "",
      options: [],
      viedvalue: '',
      showvideo: [],
      url: `${baseEvn}upload`,
      viedourl: `${baseEvn}uploadByPart`,
      editIndex: "",
      editName: "",
      editnameIndex: "",
      editimgName:"",
      imgnamedig:false,
    }
  },
  created() {
    this.getList()
  },
  methods: {
    reset(){
      this.list={
        imgs: [
        ],
        video: []
      },
      this.showvideo = []
      this.getList()
    },
    //图片
    handleAvatarSuccess() {
      this.list.imgs.forEach(it => {
        if (it.response) {
          it.transmitUrl = it.response.data.value
        }
      })
    },
    //预览图片
    handlePictureCardPreview(i) {
      this.previewurl = i.url
      this.previewdialog = true
    },
    //编辑图片名称
    handleDownload(i){
      this.imgnamedig=true
      this.editimgName=i.name
      this.list.imgs.forEach((it,index)=>{
        if(it.url==i.url){
          this.editnameIndex=index
        }
      })
    },
    //编辑图片名称
    changeimgname(){
      this.list.imgs[this.editnameIndex].name = this.editimgName
      this.imgnamedig = false
    },
    //删除图片
    handleRemove(i){       this.list.imgs = this.list.imgs.filter((item) => item.transmitUrl != i.transmitUrl);
    },
    beforeAvatarUpload(rawFile) {
      let formatList = this.acceptimg.split(',')
      const lastIndex = rawFile.name.lastIndexOf(".");
      const result = rawFile.name.substring(lastIndex);
      const isExists = formatList.includes(result);
      if (!isExists) {
        this.$message.error('您上传的文件格式不规范!');
        return false
      } else if (rawFile.size / 1024 / 1024 > 20) {
        this.$message.error('上传图片大小不能超过 20MB!');
        return false
      }
      return true;
    },
    //视频选择
    async videodigFn() {
      this.videodig = true;
      this.options = []
      let data = { "businessCode": "park_style", "conditions": [{ "compareType": "orderBy", "key": "id", "isDesc": false }] }
      const res = await mienList(data)
      res.data.elements.forEach(it => {
        if (it.type == 2) {
          this.options.push(it)
          if (it.is_large_screen == 1) {
            this.viedvalue = it.id
          }
        }
      })
    },
    //变更视频
    async showviedeo() {
      await setLargeScreenVideoAPI({ id: this.viedvalue })
      this.videodig = false;
      ElMessage({
        type: 'success',
        message: '更改成功！'
      })
    },
    //上传成功
    onSuccess(i, e) {
      //给数据赋正确的值
      this.list.video.forEach(it => {
        if (it.response) {
          it.title = it.response.data.fileName
          it.url = it.response.data.url
          it.show_url = it.response.data.viewUrl
        }
      })
      this.showvideo.push({
        title: i.data.fileName,
        //url: it.url,
        show_url: i.data.viewUrl,
        //name:it.title
      })
    },
    //移除视频
    onRemove(i) {
      this.showvideo = this.showvideo.filter((item) => item.show_url != i.show_url);
    },
    //上传视频
    beforeUpload(rawFile) {
      let formatList = this.accept.split(',')
      const lastIndex = rawFile.name.lastIndexOf(".");
      const result = rawFile.name.substring(lastIndex);
      const isExists = formatList.includes(result);
      if (!isExists) {
        this.$message.error('您上传的文件格式不规范!');
        return false
      } else if (rawFile.size / 1024 / 1024 > 256) {
        this.$message.error('单个文件大小不能超过256mb');
        return false
      }
      return true
    },
    //提交
    async submit() {
      let img = this.list.imgs.map(it => {
        if (it.id) {
          return {
            url: it.transmitUrl,
            id: it.id,
            show_url: it.show_url,
            title: it.name,
            type: 1,
          }
        } else {
          return {
            url: it.transmitUrl,
            show_url: it.show_url,
            title: it.name,
            type: 1
          }
        }
      })
      let updata = true
      let video = this.list.video.map(it => {
        if (!it.url) {
          updata = false
        }
        if (it.id) {
          return {
            //url:it.url,
            //show_url:it.show_url,
            title: it.title,
            url: it.url,
            type: 2,
            id: it.id
          }
        } else {
          return {
            //url:it.url,
            //show_url:it.show_url,
            title: it.title,
            url: it.url,
            type: 2
          }
        }

      })
      if (!updata) {
        return this.$message.error('请等待视频上传成功')
      }
      let data = [...img, ...video]
      await saveOrUpdateAPI(data)
      this.$message.success('编辑成功！')
      this.list = {
        imgs: [],
        video: []
      },
        this.showvideo = []
      this.getList()
    },
    bg(name, index) {
      this.videonamedig = true
      this.editIndex = index
      this.editName = name
    },
    changeviedoname() {
      this.list.video[this.editIndex].name = this.editName
      this.list.video[this.editIndex].title = this.editName
      this.showvideo[this.editIndex].title = this.editName
      this.videonamedig = false
    },
    // 获取列表
    async getList() {
      let data = { "businessCode": "park_style", "conditions": [{ "compareType": "orderBy", "key": "id", "isDesc": false }] }
      const res = await mienList(data)
      res.data.elements.forEach(it => {
        if (it.type == 1) {
          this.list.imgs.push({
            id: it.id,
            url: it.show_url,
            name: it.title,
            type: it.type,
            transmitUrl: it.url
          })
        } else {
          this.list.video.push({
            id: it.id,
            title: it.title,
            type: it.type,
            url: it.url,
            show_url: it.show_url,
            name: it.title
          })
          this.showvideo.push({
            title: it.title,
            type: it.type,
            url: it.url,
            show_url: it.show_url,
            name: it.title
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.flienName{
  position: absolute;
  bottom: 30px;
  padding: 0 5px;
  font-size: 12px;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1; /* 显示一行 */
  overflow: hidden; /* 隐藏溢出部分 */
}
.add{
  cursor: pointer;
  width: 36px;
  height: 36px;
  background: no-repeat url('@/assets/svg/add.svg');
  background-size: contain;
}
.del{
  cursor: pointer;
  width: 20px;
  height: 20px;
  background: no-repeat url('@/assets/svg/del.svg');
  background-size: contain;
}
.edits{
  cursor: pointer;
  width: 20px;
  height: 20px;
  background: no-repeat url('@/assets/svg/edit.svg');
  background-size: contain;
}
.interactive{
  cursor: pointer;
  width: 20px;
  height: 20px;
  background: no-repeat url('@/assets/svg/interactive.svg');
  background-size: contain;
}
.previewimg {
  max-width: 800px;
  max-height: 800px;
}

.box {
  width: 100%;
  height: auto;
  // min-height: 1000px;
  // background-color: #fafcff;
  padding: 20px;
  // padding-top: 16px;

  .cont {
    width: 100%;
    background: #FFFFFF;
    border-radius: 10px;
    opacity: 1;
  padding: 16px 16px 32px 16px;


    .head {
      padding: 10px;
      float: right;
    }

    .const {
      padding-top: 66px;
    }

    .centerImg {
      display: flex;
      padding: 29px;

      .vodeoUP {
        font-size: 14px;
        min-width: 60px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #5D5F66;
        line-height: 32px;
        display: flex;
        margin-right: 10px;
      }
    }

    .centerVideo {
      padding: 29px;

      .top {
        display: flex;
      }

      .hint {
        font-size: 12px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #606266;
        line-height: 26px;
      }

      .vodeoUP {
        min-width: 60px;
        font-size: 14px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        color: #5D5F66;
        line-height: 32px;
        display: flex;
        margin-right: 10px;
      }

      .videoList {
        display: flex;
        flex-wrap: wrap;
        margin-left: 68px;
        margin-top: 21px;

        .videalone {
          padding-right: 20px;
          width: 238px;

          .video {
            width: 218px;
            height: 122px;
            border-radius: 4px 4px 4px 4px;
            opacity: 1;
          }
          .editsss{
            display: flex;
          }

          .title {
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .edit {
            cursor: pointer;
            width: 16px;
            height: 16px;
            margin-top: 3.5px;
            background: no-repeat url('@/assets/images/edit.png');
            background-size: contain;
          }
        }
      }
    }

    .bto {
      margin-top: 30px;
      display: flex;
      align-items: center;
      justify-content: start;
      margin-left: 90px;
      width: 100%;
    }
  }
}</style>