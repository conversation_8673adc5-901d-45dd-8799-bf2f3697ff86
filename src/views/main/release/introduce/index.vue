<template>
  <div class="introduce">
    <div class="title">
      <el-form-item label="园区简介描述：" required></el-form-item>
    </div>
    <!--  maxlength="140"  -->
    <el-input
      v-model="textarea"
      :rows="4"
      show-word-limit
      style="font-size: 14px;"
      type="textarea"
      placeholder="请输入园区简介描述"
    />
    <div class="title">
      <el-form-item label="园区简介：" required></el-form-item>
    </div>
    <div style="border: 1px solid #ccc">
      <Toolbar
        style="border-bottom: 1px solid #ccc"
        :editor="editorRef"
        :defaultConfig="toolbarConfig"
        :mode="mode"
      />
      <Editor
        isDisabled="true"
        style="height: 430px; overflow-y: hidden"
        v-model="valueHtml"
        :defaultConfig="editorConfig"
        :mode="mode"
        @onCreated="handleCreated"
      />
    </div>
    <div class="buttons">
      <el-button size="default" type="primary" @click="preview">
        预览
      </el-button>
      <el-button size="default" type="primary" @click="save"> 提交 </el-button>
      <el-button size="default" @click="empty"> 重置 </el-button>
    </div>
  </div>
</template>
  
<script lang="ts" setup>
import "@wangeditor/editor/dist/css/style.css"; // 引入 css
import { onBeforeUnmount, ref, shallowRef, onMounted } from "vue";
import { DomEditor } from "@wangeditor/editor";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import { useRoute, useRouter, RouteLocationMatched } from "vue-router";
import { perFix, baseEvn, token } from "@/utils/utils";
import { formSubmit, getDetail } from "@/api/dashboard";
import { ElMessage, ElMessageBox } from "element-plus";
const status = ref();
const router = useRouter();

const mode = "default";
const textarea = ref("");
const information = ref();
// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef();

// 内容 HTML
const valueHtml = ref("");
const toolbarConfig = {
  //  查看当前的默认配置
  // toolbarKeys
  // 如果仅仅想排除掉某些菜单，其他都保留，可以使用 excludeKeys 来配置。
  excludeKeys: [
    //   "headerSelect",// 标题
    //   "blockquote", // 引用
    //   "bold", // 加粗
    //   "underline", // 下划线
    //   "italic", // 斜体
    //   // 删除线、清除格式等
    //   "group-more-style",
    //   {
    //     key: "group-more-style",
    //     title: "更多",
    //     iconSvg:
    //       '<svg viewBox="0 0 1024 1024"><path d="M204.8 505.6…0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path></svg>',
    //     menuKeys: Array(5)
    //   },

    //   "color", // 文字颜色

    //   "bgColor", // 背景色

    //   "fontSize", // 字号

    //   "fontFamily", // 字体

    //   "lineHeight", // 行高

    //   "bulletedList", // 无序列表

    //   "numberedList", // 有序列表

    "todo", // 代办
    // 对齐
    //   "group-justify",
    //   {
    //     key: "group-justify",
    //     title: "对齐",
    //     iconSvg:
    //       '<svg viewBox="0 0 1024 1024"><path d="M768 793.6v1…72.8 102.4v102.4H51.2V102.4h921.6z"></path></svg>',
    //     menuKeys: Array(4)
    //   },
    // 缩进
    //   "group-indent",
    //   {
    //     key: "group-indent",
    //     title: "缩进",
    //     iconSvg:
    //       '<svg viewBox="0 0 1024 1024"><path d="M0 64h1024v1…32h1024v128H0z m0-128V320l256 192z"></path></svg>',
    //     menuKeys: Array(2)
    //   },

    //   "emotion",// 表情

    //   "insertLink",// 插入链接
    // 上传图片
    {
      key: "group-image",
      title: "图片",
      iconSvg:
        '<svg viewBox="0 0 1024 1024"><path d="M959.877 128…l224.01-384 256 320h64l224.01-192z"></path></svg>',
      menuKeys: Array(2),
    },
    // 上传视频
    "group-video",
    //   {
    //     key: "group-video",
    //     title: "视频",
    //     iconSvg:
    //       '<svg viewBox="0 0 1024 1024"><path d="M981.184 160….904zM384 704V320l320 192-320 192z"></path></svg>',
    //     menuKeys: Array(2)
    //   },

    "insertTable", // 插入表格

    "codeBlock", // 代码块

    //   "divider", // 分割线

    //   "undo", // 撤销

    //   "redo", // 重做

    //   "fullScreen" // 全屏
  ],
  //   insertKeys : {
  // index:79, // 插入的位置，基于当前的 toolbarKeys
  // keys: ['menu-key1', 'menu-key2']
  // }
};

const editorConfig = {
  placeholder: "请输入内容...",
  MENU_CONF: {
    uploadImage: {
      // 上传图片请求接口路径
      server: `${baseEvn}uploadAndReturnOssUrl`,
      // 后端接收的文件名称
      fieldName: "file",
      maxFileSize: 10 * 1024 * 1024, // 上传图片10M
      // 上传的图片类型
      allowedFileTypes: ["image/*"],
      // 小于该值就插入 base64 格式（而不上传），默认为 0
      base64LimitSize: 10 * 1024, // 10MB
      // 自定义上传图片返回格式【后端返回的格式】
      customInsert(res, insertFn) {
        if (res.status !== "0") {
          ElMessage.error("上传文件失败，" + res.message);
          return;
        }
        // 从 res 中找到 url alt href ，然后插入图片 ,根据后端实际返回的字段来
        insertFn(res.data, "", res.data);
      },

      // 将 meta 拼接到 url 参数中，默认 false
      metaWithUrl: true,
      // 单个文件上传成功之后
      onSuccess(file, res) {
        if (res.status == "0") {
          ElMessage.success(`${file.name} 上传成功`);
          return;
        } else {
          ElMessage.warning(`${file.name} 上传出了点异常`);
          return;
        }
        // console.log(`${file.name} 上传成功`, res)
        //ElMessage.success(`${file.name} 上传成功`, res)
      },
      // 单个文件上传失败
      onFailed(file, res) {
        ElMessage.error(`${file.name} 上传失败`);
      },
      // 上传错误，或者触发 timeout 超时
      onError(file, err, res) {
        // console.log(err, res)
        ElMessage.error(`${file.name} 上传出错`);
      },
    },
  },
};

// 模拟 ajax 异步获取内容
onMounted(() => {
  // setTimeout(() => {
  //     valueHtml.value = ''
  // }, 1500)
  // console.log('editorConfig.value.MENU_CONF', editorRef.value)
  getData();
});

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});

const handleCreated = (editor) => {
  editorRef.value = editor; // 记录 editor 实例，重要！
  // console.log('editorConfig.value.MENU_CONF', editor.getConfig())
  // editor.disable()
  //     const toolbar = DomEditor.getToolbar(editorRef.value)
  // const curToolbarConfig = toolbar.getConfig()
  // console.log( curToolbarConfig.toolbarKeys ) // 当前菜单排序和分组
};
const getData = () => {
  getDetail().then((res) => {
    if (res?.status === "0") {
      if (res.data.elements && res.data.elements.length > 0) {
        status.value = "editor";
        let data = res.data.elements[0];
        textarea.value = data.intro_desc;
        valueHtml.value = data.intro_info;
        information.value = data;
      } else {
        status.value = "add";
      }
    }
  });
};
const empty = () => {
  valueHtml.value = "";
  textarea.value = "";
  // console.log(valueHtml.value)
};
const save = () => {
  // console.log(textarea.value,valueHtml.value,'1223')
  if (textarea.value === "") {
    ElMessage({
      type: "warning",
      message: "请填写园区简介描述",
    });
    return;
  } else if (valueHtml.value === "<p><br></p>") {
    ElMessage({
      type: "warning",
      message: "请填写园区简介",
    });
    return;
  } else {
    ElMessageBox.confirm("是否确定提交", "", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
      .then(() => {
        //     console.log(valueHtml.value)
        // let html=valueHtml.value
        let submitData = {
          businessCode: "park_intro",
          action: "insert",
          element: {
            id: status.value === "editor" ? information.value.id : null,
            intro_desc: textarea.value,
            intro_info: valueHtml.value,
          },
          conditions: [],
        };

        formSubmit(submitData).then((res) => {
          ElMessage({
            type: "success",
            message: "保存成功",
          });
        });
      })
      .catch(() => {
        //   ElMessage({
        //     type: 'info',
        //     message: 'Delete canceled',
        //   })
      });
  }
};
const preview = () => {
  if (valueHtml.value !== "") {
    sessionStorage.setItem("preview", JSON.stringify(valueHtml.value));
    window.open("/#/preview");
  }
  // router.push('/preview');
};
</script>
<style  lang='scss'>
.w-e-full-screen-container {
  z-index: 1111;
}
.title {
  .el-form-item__label {
    font-size: 16px;
    line-height: 32px;
    margin-top: 20px;
  }
}
</style>
  
<style scoped lang="scss">
.introduce {
padding:0 16px 16px 32px;
  margin: 10px 30px;
  background: #fff;
  border-radius: 10px;
  /* width: 50%; */
}
.buttons {
  margin: 20px;
  width: 100%;
  display: flex;
  justify-content: center;
}
</style>