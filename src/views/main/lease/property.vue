<script setup>
import AmisComponent from '@/components/amis/index.vue';
import { ref, onMounted } from 'vue';
import addlease2 from './com/addlease2.vue';

let operationPage = ref(false);
let amisRef = ref(null);
let amisjson1 = ref({
  type: 'page',
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: '0px 0px 0px 0px transparent',
  },
  body: [
    {
      type: 'tpl',
      inline: true,
      wrapperComponent: '',
      syncLocation: false,
      id: 'u:3345e187f2df',
      style: {
        boxShadow: '0px 0px 0px 0px transparent',
        fontWeight: '500',
        lineHeight: '1.7',
        fontSize: '20px',
      },
    },
    {
      type: 'crud',
      syncLocation: false,
      name: 'tab-s',
      api: {
        method: 'post',
        url: `lease/property/agree/page`,
        requestAdaptor: (rest) => {
          let { gmtCreate, ...other } = rest.data;
          let housesIds = rest.data.housesIds?.slice(1, -1);
          housesIds = housesIds?.split(',');
          if (housesIds[0] == '') {
            housesIds = [];
          }
          let datas = {
            ...other,
            housesIds,
            moveInStartTimeStamp: rest.data.gmtCreate?.split(',')[0] * 1000,
            moveInEndTimeStamp: rest.data.gmtCreate?.split(',')[1] * 1000,
          };
          return {
            ...rest,
            data: datas,
          };
        },
        data: {
          enterpriseName: "${enterpriseName||''}",
          unifiedSocialCreditCode: "${unifiedSocialCreditCode||''}",
          status: '${status}',
          housesIds: '{$housesIds}',
          gmtCreate: '${gmtCreate}',
          pageNum: '${page||1}',
          pageSize: '${perPage||10}',
        },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: 'table',
      columns: [
        {
          type: 'operation',
          label: '企业名称',
          width: 200,
          buttons: [
            {
              type: 'button',
              size: 'md',
              level: 'link',
              label: '${enterpriseName}',
              onClick: (e, item) => {
                propertyfeeEdit(item, 'ck');
              },
            },
          ],
        },
        // {
        //   "name": "enterpriseName",
        //   "label": "企业名称",
        //   "width":260,
        //   "type": "text"
        // },
        {
          name: 'signDate',
          label: '签约日期',
          type: 'text',
        },
        {
          name: 'moveInDate',
          label: '起租日期',
          type: 'text',
        },
        {
          name: 'lengthStay',
          label: '入驻时长(月)',
          type: 'text',
        },
        {
          name: 'endDate',
          label: '结束日期',
          type: 'text',
        },
        {
          type: 'tpl',
          label: '租约状态',
          tpl: "<span class='${status==0 ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200\" : (status==1 ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200\" : \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200\")}'>${status==0 ? '待生效' : (status==1 ? '生效中' : '已失效')}</span>",
        },
        {
          type: 'operation',
          label: '操作',
          fixed: 'right', // 固定在右侧
          width: 300,
          buttons: [
            {
              label: '查看',
              type: 'button',
level: 'primary',
              onClick: (e, item) => {
                propertyfeeEdit(item, 'ck');
              },
            },
            {
              label: '编辑',
              type: 'button',
              level: 'primary',

              visibleOn: "status !== '2'",
              onClick: (e, item) => {
                propertyfeeEdit(item, 'bj');
              },
            },
            {
              type: 'button',

              actionType: 'ajax',
              label: '删除',
              level: 'danger',
              visibleOn: "editable === '1'", //0不可编辑，1可编辑

              confirmText: '是否确认删除',
              api: {
                method: 'get',
                url: `lease/property/agree/removeById`,
                data: {
                  id: '$id',
                },
              },
            },
          ],
        },
      ],
      alwaysShowPagination: true,
      autoFillHeight: false,
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      filter: {
        title: '',
        submitText: '搜索',
        controls: [
          {
            type: 'text',
            name: 'enterpriseName',
            size: 'md',
            label: '企业名称',
            placeholder: '请输入企业名称',
          },
          {
            type: 'select',
            name: 'status',
            size: 'md',
            label: '租约状态',
            placeholder: '请选择状态',
            value: '',
            options: [
              { label: '全部', value: '' },
              { label: '待生效', value: '0' },
              { label: '已生效', value: '1' },
              { label: '已失效', value: '2' },
            ],
          },
          {
            type: 'input-date-range',
            size: 'md',
            name: 'gmtCreate',
            label: '合同入驻日期',
            shortcuts: [
              {
                label: '1天前',
                startDate: "${DATEMODIFY(NOW(), -1, 'day')}",
                endDate: '${NOW()}',
              },
              {
                label: '1个月前',
                startDate: "${DATEMODIFY(NOW(), -1, 'months')}",
                endDate: '${NOW()}',
              },
              {
                label: '本季度',
                startDate: "${STARTOF(NOW(), 'quarter')}",
                endDate: "${ENDOF(NOW(), 'quarter')}",
              },
            ],
          },
          {
            type: 'reset',
            label: '重置',
          },
          {
            type: 'submit',
            label: '搜索',
            primary: true,
          },
          {
            label: '新增租约',
            type: 'button',
            size: 'md',
            onClick: () => {
              propertyfeeEdit({}, 'tj');
            },
          },
        ],
        onSubmit: 'reload',
      },
    },
  ],
});
let operationType = ref('');
let operationData = ref({});
let refreshKey = ref(0);

function propertyfeeEdit(e, type) {
  operationData.value = e.data;
  operationType.value = type;
  operationPage.value = true;
}

/**
 * 处理新增/编辑页面关闭事件
 * @param {boolean} isSave - 是否是保存操作触发的关闭
 */
function canleAddlease2(isSave = false) {
  console.log('canleAddlease2 called, isSave:', isSave);

  // 关闭操作页面
  operationPage.value = false;

  // 如果是保存操作，需要刷新列表
  if (isSave) {
    console.log('保存成功，开始刷新列表数据');

    // 使用延时确保页面切换完成后再刷新
    setTimeout(() => {
      try {
        // 方案一：使用 refreshKey 强制重新渲染（最可靠）
        const oldKey = refreshKey.value;
        refreshKey.value++;
        console.log(
          `刷新列表：refreshKey 从 ${oldKey} 更新到 ${refreshKey.value}`
        );
      } catch (error) {
        console.error('刷新列表失败:', error);

        // 备用方案：修改 amisjson1 配置强制刷新
        try {
          const newJson = JSON.parse(JSON.stringify(amisjson1.value));
          // 保持原有模板变量，只添加时间戳
          newJson.body[1].api.data._refresh = Date.now();
          amisjson1.value = newJson;
          console.log('使用备用方案刷新成功');
        } catch (fallbackError) {
          console.error('备用刷新方案也失败:', fallbackError);
        }
      }
    }, 100);
  } else {
    console.log('取消操作，不需要刷新列表');
  }
}
</script>

<template>
  <div v-if="!!amisjson1" class="zhengce publicTableStyle">
    <addlease2
      @canleAddlease2="canleAddlease2"
      :operationType="operationType"
      :operationData="operationData"
      v-if="operationPage"
    ></addlease2>
    <AmisComponent
      ref="amisRef"
      v-show="!operationPage"
      :key="refreshKey"
      :amisjson="amisjson1"
    />
  </div>
</template>
<style scoped lang="scss">
.tab {
  padding-top: 20px;
  padding-left: 10px;
}

.on {
  background-color: #169bd5;
  color: #fff;
}
</style>
