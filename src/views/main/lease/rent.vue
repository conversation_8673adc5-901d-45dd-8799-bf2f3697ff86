<script setup>
// import HelloWorld from './components/HelloWorld.vue'
// import TheWelcome from './components/TheWelcome.vue'
import AmisComponent from '@/components/amis/index.vue';
import { ref, onMounted } from 'vue';
import { perFix, baseEvn, token } from '@/utils/utils';
import { useRoute, useRouter } from 'vue-router';
import { transformData } from '@/utils/formData';
import addlease from './com/addlease.vue';
const router = useRouter();
let refreshKey = ref(0);

let tabId = ref(1);
let operationPage = ref(false);
let operationType = ref('');
let operationData = ref({});

function handleButtonClick(e) {
  router.push(e);
}
function propertyfeeEdit(e, type) {
  operationData.value = e.data;
  operationType.value = type;
  operationPage.value = true;
}
function canleAddlease2(isSave = false) {
  // tabId.value = 2;
  operationPage.value = false;
  if (isSave) {
    getAmisjson(isSave);
  }
}
const getAmisjson = (isSave) => {
  // 如果是保存操作，需要刷新列表
  if (isSave) {
    console.log('保存成功，开始刷新列表数据');

    // 使用延时确保页面切换完成后再刷新
    setTimeout(() => {
      try {
        // 方案一：使用 refreshKey 强制重新渲染（最可靠）
        const oldKey = refreshKey.value;
        refreshKey.value++;
        console.log(
          `刷新列表：refreshKey 从 ${oldKey} 更新到 ${refreshKey.value}`
        );
      } catch (error) {
        console.error('刷新列表失败:', error);

        // 备用方案：修改 amisjson1 配置强制刷新
        try {
          const newJson = JSON.parse(JSON.stringify(amisjson1.value));
          // 保持原有模板变量，只添加时间戳
          newJson.body[1].api.data._refresh = Date.now();
          amisjson1.value = newJson;
          console.log('使用备用方案刷新成功');
        } catch (fallbackError) {
          console.error('备用刷新方案也失败:', fallbackError);
        }
      }
    }, 100);
  } else {
    console.log('取消操作，不需要刷新列表');
  }
};
// 租约管理
const amisjson3 = {
  type: 'page',
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: '0px 0px 0px 0px transparent',
  },
  body: [
    {
      type: 'tpl',
      inline: true,
      wrapperComponent: '',
      syncLocation: false,
      id: 'u:3345e187f2df',
      style: {
        boxShadow: '0px 0px 0px 0px transparent',
        fontWeight: '500',
        lineHeight: '1.7',
        fontSize: '20px',
      },
    },
    {
      type: 'crud',
      syncLocation: false,
      api: {
        method: 'post',
        url: `lease/page`,

        // adaptor: function (payload, response, api, context) {
        //   debugger
        //     //  if()
        //         return {
        //           data: list,
        //           msg: "请求成功",
        //           status: 0
        //         };
        //       },
        requestAdaptor: (rest) => {
          // console.log(rest, 'rest');
          let { gmtCreate, ...other } = rest.data;
          let housesIds = rest.data.housesIds?.slice(1, -1);
          housesIds = housesIds?.split(',');
          if (housesIds[0] == '') {
            housesIds = [];
          }
          let datas = {
            ...other,
            housesIds,
            //housesIds:rest.data.housesIds?.split(','),
            moveInStartTimeStamp: rest.data.gmtCreate?.split(',')[0] * 1000,
            moveInEndTimeStamp: rest.data.gmtCreate?.split(',')[1] * 1000,
          };
          return {
            ...rest,
            data: datas,
          };
        },
        data: {
          enterpriseName: '${enterpriseName}',
          unifiedSocialCreditCode: '${unifiedSocialCreditCode}',
          status: '${status}',
          housesIds: '{$housesIds}',
          gmtCreate: '${gmtCreate}',
          pageNum: '${page}',
          pageSize: '${perPage}',
        },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: 'table',
      columns: [
        {
          type: 'operation',
          label: '企业名称',
          // className: 'text-blue-600 text-sm',
          width: 200,
          buttons: [
            {
              type: 'button',
              level: 'link',
              size: 'md',
              label: '${enterpriseName}',
              onClick: (e, item) => {
                handleButtonClick(
                  `/workbench/lease/addlease?id=${item?.data?.id}`
                );
              },
            },
          ],
        },
        {
          name: 'signDate',
          label: '签约日期',
          type: 'text',
        },
        {
          name: 'moveInDate',
          label: '起租日期',
          type: 'text',
        },
        /* {
          "name": "moveInAddress",
          "label": "入驻地址",
          "type": "text"
        }, */
        {
          type: 'tpl',
          name: 'moveInAddress',
          label: '入驻地址',
          tpl: '${moveInAddress|truncate:18}',
          popOver: {
            trigger: 'hover',
            position: 'left-top',
            showIcon: false,
            body: {
              type: 'tpl',
              tpl: '${moveInAddress}',
            },
          },
        },
        {
          name: 'lengthStay',
          label: '入驻时长(月)',
          type: 'text',
        },
        {
          name: 'endDate',
          label: '结束日期',
          type: 'text',
        },

        {
          type: 'tpl',
          label: '租约状态',
          tpl: "<span class='${status==0 ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200\" : (status==1 ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200\" : \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200\")}'>${status==0 ? '待生效' : (status==1 ? '生效中' : '已失效')}</span>",
        },
        {
          type: 'operation',
          label: '操作',
          width: 300,
          buttons: [
            {
              label: '查看',
              type: 'button',
              level: 'primary',
              onClick: (e, item) => {
                propertyfeeEdit(item, 'ck');
              },
              // onClick: (e, item) => {
              //   handleButtonClick(
              //     `/workbench/lease/addlease?id=${item?.data?.id}`
              //   );
              // },
              // "actionType": "link",
              // "link": "#/workbench/lease/addlease?id=${id}",
            },
            {
              label: '编辑',
              type: 'button',
              level: 'primary',
              visibleOn: "status !== '2'",
              onClick: (e, item) => {
                propertyfeeEdit(item, 'bj');
              },
              // onClick: (e, item) => {
              //   handleButtonClick(
              //     `/workbench/lease/addlease?id=${item?.data?.id}&st=1`
              //   );
              // },
              // "actionType": "link",
              // "link": "#/workbench/lease/addlease?id=${id}&st=1",
            },
            {
              type: 'button',
              actionType: 'ajax',
              label: '删除', //0不可编辑，1可编辑
              visibleOn: "editable === '1'",
              confirmText: '是否确认删除',
              level: 'danger',
              api: {
                method: 'get',

                url: `lease/delete`,
                data: {
                  id: '$id',
                },
              },
            },
            {
              label: '续租',
              type: 'button',
              level: 'primary',
              visibleOn: "status === '1'",
              onClick: (e, item) => {
                propertyfeeEdit(item, 'xy');
              },
              // "actionType": "link",
              // "link": "#/workbench/lease/addlease?id=${id}&st=2",
              // onClick: (e, item) => {
              //   handleButtonClick(
              //     `/workbench/lease/addlease?id=${item?.data?.id}&st=2`
              //   );
              // },
            },
            {
              type: 'button',
              actionType: 'ajax',
              label: '退租',
              level: 'danger',
              visibleOn: "status === '1'",
              confirmText: '是否确认退租',
              confirmTitle: '退租',
              api: {
                method: 'get',
                url: `lease/surrenderOfTenancy`,
                data: {
                  id: '${id}',
                  /*  "businessCode": "lease",
                  "action": "update",
                  "element": {
                    "status": "2"
                  },
                  "conditions": [
                    {
                      "key": "id",
                      "value": "${id}"
                    }
                  ] */
                },
              },
            },
          ],
        },
      ],
      alwaysShowPagination: true,
      autoFillHeight: false,
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      filter: {
        title: '',
        submitText: '搜索',
        controls: [
          {
            type: 'text',
            name: 'enterpriseName',
            size: 'md',
            label: '企业名称',
            placeholder: '请输入企业名称',
          },
          {
            type: 'text',
            name: 'unifiedSocialCreditCode',
            size: 'md',
            label: '企业社会信用代码',
            placeholder: '请输入企业社会信用代码',
          },
          {
            type: 'select',
            name: 'status',
            size: 'md',
            label: '租约状态',
            placeholder: '请选择状态',
            value: '',
            options: [
              { label: '全部', value: '' },
              { label: '待生效', value: '0' },
              { label: '已生效', value: '1' },
              { label: '已失效', value: '2' },
            ],
          },
          {
            // "type": "nested-select",
            type: 'nested-select',
            //"onlyLeaf": true,
            searchable: true, //可搜索
            multiple: true, //是否多选
            name: 'housesIds',
            //"withChildren": true,//设置 true时，选中父节点时，值里面将包含子节点的值，否则只会保留父节点的值。
            onlyChildren: true, //多选时，选中父节点时，是否只将其子节点加入到值中。
            label: '入驻地址',
            maxTagCount: 2, //	标签的最大展示数量，超出数量后以收纳浮层的方式展示，仅在多选模式开启后生效
            // "onlyChildren": true,
            source: {
              method: 'get',
              url: `houses/getBuildingTree`,
              adaptor: function (payload, response, api, context) {
                let list = payload.map((item) => {
                  return {
                    value: item.buildingId,
                    label: item.buildingName,
                    children: transformData(item.houseList),
                  };
                });
                return {
                  data: list,
                  msg: '请求成功',
                  status: 0,
                };
              },
            },
          },
          {
            type: 'input-date-range',
            name: 'gmtCreate',
            label: '合同入驻日期',
            shortcuts: [
              {
                label: '1天前',
                startDate: "${DATEMODIFY(NOW(), -1, 'day')}",
                endDate: '${NOW()}',
              },
              {
                label: '1个月前',
                startDate: "${DATEMODIFY(NOW(), -1, 'months')}",
                endDate: '${NOW()}',
              },
              {
                label: '本季度',
                startDate: "${STARTOF(NOW(), 'quarter')}",
                endDate: "${ENDOF(NOW(), 'quarter')}",
              },
            ],
          },
          {
            type: 'reset',
            label: '重置',
          },
          {
            type: 'submit',
            label: '搜索',
            primary: true,
          },
          {
            label: '新增租约',
            type: 'button',
            onClick: () => {
              propertyfeeEdit({}, 'tj');
            },
            // "actionType": "link",
            // "link": "#/workbench/lease/addlease",
            // onClick: () => {
            //   handleButtonClick('/workbench/lease/addlease');
            // },
          },
        ],
        onSubmit: 'reload',
      },
    },
  ],
};
</script>

<template>
  <div v-if="!!amisjson3" class="zhengce publicTableStyle">
    <addlease
      @canleAddlease2="canleAddlease2"
      :operationType="operationType"
      :operationData="operationData"
      v-if="operationPage"
    ></addlease>
    <AmisComponent
      v-show="!operationPage"
      :amisjson="amisjson3"
      :key="refreshKey"
    />
  </div>
</template>
<style scoped lang="scss">
.tab {
  padding-top: 20px;
  padding-left: 10px;
}

.on {
  background-color: #169bd5;
  color: #fff;
}
</style>
