<script setup>
// import HelloWorld from './components/HelloWorld.vue'
// import TheWelcome from './components/TheWelcome.vue'
import AmisComponent from '@/components/amis/index.vue';
import { ref, onMounted } from 'vue';
import { perFix, baseEvn, token } from '@/utils/utils';
import { useRoute, useRouter } from 'vue-router';
import { transformData } from '@/utils/formData';
import addlease2 from './com/addlease2.vue';
import { LEVEL } from '@antv/g2plot/lib/utils';
const router = useRouter();

let tabId = ref(1);
let operationPage = ref(false);

let operationType = ref('');
let operationData = ref({});

function handleButtonClick(e) {
  router.push(e);
}

function canleAddlease2(e) {
  operationPage.value = false;
}

// 待办租约
const amisjson2 = {
  type: 'page',
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: '0px 0px 0px 0px transparent',
  },
  body: [
    {
      type: 'tpl',
      inline: true,
      wrapperComponent: '',
      syncLocation: false,
      id: 'u:3345e187f2df',
      style: {
        boxShadow: '0px 0px 0px 0px transparent',
        fontWeight: '500',
        lineHeight: '1.7',
        fontSize: '20px',
      },
    },
    {
      type: 'crud',
      syncLocation: false,
      name: 'tab-s',
      api: {
        method: 'post',
        url: `pendingLease/pageList`,

        requestAdaptor: (rest) => {
          let { gmtCreate, ...other } = rest.data;
          let datas = {
            ...other,
            startTimeStamp: rest.data.gmtCreate?.split(',')[0] * 1000 || '',
            endTimeStamp: rest.data.gmtCreate?.split(',')[1] * 1000 || '',
          };
          return {
            ...rest,
            data: datas,
          };
        },
        data: {
          enterpriseName: '${enterpriseName}',
          submitName: '${submitName}',
          submitPhone: '${submitPhone}',
          followStatus: '${followStatus}',
          gmtCreate: '${gmtCreate}',
          pageNum: '${page}',
          pageSize: '${perPage}',
        },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: 'table',
      //列表
      columns: [
        {
          name: "${appealType==1 ? '我要续租' : '我要退租'}",
          label: '租约诉求类型',
          type: 'text',
        },
        {
          name: 'contractNumber',
          label: '合同编号',
          type: 'text',
        },
        {
          name: 'enterpriseName',
          label: '企业名称',
          type: 'text',
        },
        {
          name: 'submitName',
          label: '姓名',
          type: 'text',
        },
        {
          name: 'submitPhone',
          label: '联系方式',
          type: 'text',
        },
        {
          name: 'gmtCreate',
          label: '提交时间',
          type: 'date',
          valueFormat: 'x',
        },
        {
          name: "${followStatus==0 ? '未跟进' : '已跟进'}",
          label: '跟进状态',
          type: 'text',
        },
        {
          name: 'followPerson',
          label: '跟进人',
          type: 'text',
        },
        {
          name: 'followDate',
          label: '跟进时间',
          type: 'date',
          valueFormat: 'x',
        },
        {
          type: 'operation',
          label: '操作',
          width: 200,
          fixed: 'right', // 固定在右侧
          buttons: [
            {
              label: '查看跟进记录',
              type: 'button',
              // "actionType": "link",
              // "link": "#/workbench/lease/addfollow?id=$id",
              onClick: (e, item) => {
                handleButtonClick(
                  `/workbench/lease/addfollow?id=${item?.data?.id}`
                );
              },
            },
            {
              type: 'button',
              label: '跟进',
              level: 'primary',
              actionType: 'dialog',
              dialog: {
                title: '添加跟进记录',
                actions: [
                  {
                    type: 'button',
                    size: 'md',
                    actionType: 'cancel',
                    label: '取消',
                    level: 'default',
                    level: 'default',
                    primary: true,
                  },
                  {
                    label: '确认',
                    actionType: 'confirm',
                    primary: true,
                    //"reload": "tab-s",
                    close: true,
                    type: 'button',
                    size: 'md',
                    api: {
                      method: 'post',
                      url: `pendingLease/addFollowRecord`,
                      messages: {
                        success: '操作成功！',
                      },
                      data: {
                        pendingLeaseId: '$id',
                        remark: '$remark',
                      },
                    },
                  },
                ],
                body: [
                  {
                    type: 'form',
                    body: [
                      {
                        name: 'remark',
                        type: 'textarea',
                        label: '跟进描述',
                        required: true,
                        showCounter: true,
                        maxLength: 300,
                        placeholder: '添加跟进记录,不超过300字',
                      },
                    ],
                  },
                ],
              },
            },
          ],
        },
      ],
      alwaysShowPagination: true,
      autoFillHeight: false,
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      filter: {
        title: '',
        submitText: '搜索',
        controls: [
          {
            type: 'text',
            name: 'enterpriseName',
            size: 'md',
            label: '企业名称',
            placeholder: '请输入企业名称',
          },
          {
            type: 'text',
            size: 'md',
            name: 'submitName',
            label: '姓名',
            placeholder: '请输入姓名',
          },
          {
            type: 'text',
            name: 'submitPhone',
            size: 'md',
            label: '联系方式',
            placeholder: '请输入联系方式',
          },
          {
            type: 'input-date-range',
            name: 'gmtCreate',
            size: 'md',
            label: '提交时间',
            shortcuts: [
              {
                label: '1天前',
                startDate: "${DATEMODIFY(NOW(), -1, 'day')}",
                endDate: '${NOW()}',
              },
              {
                label: '1个月前',
                startDate: "${DATEMODIFY(NOW(), -1, 'months')}",
                endDate: '${NOW()}',
              },
              {
                label: '本季度',
                startDate: "${STARTOF(NOW(), 'quarter')}",
                endDate: "${ENDOF(NOW(), 'quarter')}",
              },
            ],
          },

          {
            type: 'select',
            name: 'followStatus',
            size: 'md',
            label: '跟进状态',
            placeholder: '请选择状态',
            value: '',
            options: [
              { label: '全部', value: '' },
              { label: '未跟进', value: '0' },
              { label: '已跟进', value: '1' },
            ],
          },
          {
            type: 'reset',
            label: '重置',
          },
          {
            type: 'submit',
            label: '搜索',
            primary: true,
          },
        ],
        onSubmit: 'reload',
        actions: [
          /*           {
                      "type": "button",
"size": "md",
                      "label": "一键导出",
                      "actionType": "download",
                      "api": `/enterprise/business/downloadTemplate`
                    } */
        ],
      },
    },
  ],
};
let amisjson = ref(null);
onMounted(() => {
  let tabs = {
    type: 'tabs',
    // "tabsMode": "radio",
    activeKey: tabId,
    tabs: [
      {
        title: '待办租约',
        body: amisjson2,
        hash: 3,
      },
    ],
    className: 'tabsClass',
    linksClassName: 'tabsTitle',
  };
  // let data = publicConfig({ columns, api, filter });
  amisjson.value = tabs;
});
</script>

<template>
  <div v-if="!!amisjson" class="zhengce publicTableStyle">
    <addlease2
      @canleAddlease2="canleAddlease2"
      :operationType="operationType"
      :operationData="operationData"
      v-if="operationPage"
    ></addlease2>
    <AmisComponent v-show="!operationPage" :amisjson="amisjson2" />
  </div>
</template>
<style scoped lang="scss">
.tab {
  padding-top: 20px;
  padding-left: 10px;
}

.on {
  background-color: #169bd5;
  color: #fff;
}
</style>
