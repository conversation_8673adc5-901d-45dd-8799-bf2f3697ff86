<!-- 租金租约 -->
<template>
  <div class="det" ref="det">
    <div v-if="!$props.origin">
      <div v-if="!showPdf" class="title">
        <h3>
          {{
            state == 'ck'
              ? '查看'
              : state == 'bj'
              ? '编辑'
              : state == 'xy'
              ? '续租'
              : '新增'
          }}租金租约
        </h3>
        <span @click="returnFn(false)" class="btn">
          <el-icon>
            <Close />
          </el-icon>
        </span>
      </div>
      <div
        v-if="showPdf"
        @click="showPdf = false"
        style="cursor: pointer"
        class="title"
      >
        <h2>返回</h2>
      </div>
    </div>
    <div v-show="!showPdf" class="boxes" ref="boxes">
      <el-form
        :model="form"
        ref="form"
        :rules="rules"
        size="default"
        :disabled="state == 'ck'"
        style="width: 100%"
        label-width="160px"
        class="two-column-form"
      >
        <div class="form-row">
          <el-form-item
            prop="enterpriseName"
            label="企业名称"
            class="form-item"
          >
            <el-select
              :append-to="appendToTarget"
              filterable
              :disabled="state !== 'tj'"
              v-model="form.enterpriseName"
              @change="changeenterprise(form.enterpriseName)"
              @visible-change="handleSelectVisibleChange"
              @click.stop="handleSelectClick"
              :popper-options="{
                modifiers: [
                  {
                    name: 'computeStyles',
                    options: {
                      adaptive: false,
                      enabled: false,
                    },
                  },
                ],
              }"
              popper-class="enterprise-select-dropdown"
              placeholder="请选择企业"
            >
              <el-option
                v-for="item in enterprisesx"
                :key="item.label"
                :label="item.label"
                :value="item.label"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="企业社会信用代码" class="form-item">
            <el-input disabled v-model="form.unifiedSocialCreditCode" />
          </el-form-item>
        </div>

        <div class="form-row">
          <el-form-item prop="housesIds" label="入驻房源" class="form-item">
            <el-cascader
              append-to-body
              :collapse-tags="true"
              v-model="form.housesIds"
              placeholder="请选择入驻房源"
              :options="treeList"
              :props="optionProps"
              :disabled="detials.editable === '0'"
              clearable
            />
          </el-form-item>

          <el-form-item prop="leaseUsage" label="用途" class="form-item">
            <el-select
              append-to-body
              v-model="form.leaseUsage"
              placeholder="请选择用途"
              :disabled="detials.editable === '0'"
            >
              <el-option
                v-for="item in useType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>

        <div class="form-row">
          <el-form-item
            label="合同编号"
            prop="contractNumber"
            class="form-item"
          >
            <el-input
              placeholder="请输入合同编号"
              v-model="form.contractNumber"
              :disabled="detials.editable === '0'"
            />
          </el-form-item>

          <el-form-item label="签约日期" class="form-item">
            <el-date-picker
              append-to-body
              v-model="form.signDate"
              type="date"
              placeholder="请选择签约日期"
              :disabled="detials.editable === '0'"
              value-format="x"
            />
          </el-form-item>
        </div>

        <div class="form-row">
          <el-form-item
            label="物业费(元/平*月)"
            prop="propertyCostsMonth"
            class="form-item"
          >
            <el-input-number
              :min="0"
              v-model="form.propertyCostsMonth"
              placeholder="请输入物业费"
              :disabled="detials.editable === '0'"
              controls-position="right"
              precision="2"
            />
          </el-form-item>
          <el-form-item
            prop="propertyPayType"
            label="物业费付款方式"
            class="form-item"
          >
            <el-select
              append-to-body
              v-model="form.propertyPayType"
              :disabled="detials.editable === '0'"
              placeholder="请选择物业费付款方式"
            >
              <el-option
                v-for="item in PayType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <div class="form-row">
          <el-form-item
            label="免租时长(月)"
            prop="lengthRentFree"
            class="form-item"
          >
            <el-input-number
              :min="0"
              v-model="form.lengthRentFree"
              placeholder="请输入免租时长"
              controls-position="right"
              :disabled="detials.editable === '0'"
            />
          </el-form-item>

          <el-form-item label="押金(元)" class="form-item">
            <el-input-number
              :min="0"
              v-model="form.deposit"
              placeholder="请输入押金"
              precision="2"
              controls-position="right"
              :disabled="detials.editable === '0'"
            />
          </el-form-item>
        </div>

        <div class="form-row">
          <el-form-item
            label="租金单价(元/平*月)"
            prop="rentMonth"
            class="form-item"
          >
            <el-input-number
              :min="0"
              :disabled="detials.editable === '0'"
              v-model="form.rentMonth"
              placeholder="请输入租金单价"
              precision="2"
              controls-position="right"
            />
            <!-- @change="changeRentMonth" -->
          </el-form-item>
          <el-form-item label="租赁面积(㎡)" prop="leaseArea" class="form-item">
            <el-input-number
              :min="0"
              :max="999999"
              v-model="form.leaseArea"
              :disabled="detials.editable === '0'"
              precision="2"
              placeholder="请输入租赁面积"
              controls-position="right"
            />
            <!-- @change="changeLeaseArea" -->
          </el-form-item>
        </div>
        <div class="form-row">
          <el-form-item label="起租日期" prop="moveInDate" class="form-item">
            <el-date-picker
              append-to-body
              v-model="form.moveInDate"
              type="date"
              placeholder="请选择起租日期"
              value-format="x"
              :disabled="detials.editable === '0'"
              @change="changTimeStatrt"
            />
          </el-form-item>

          <el-form-item label="截止时间" prop="endDate" class="form-item">
            <el-date-picker
              append-to-body
              v-model="form.endDate"
              type="date"
              placeholder="请选择截止时间"
              value-format="x"
              :disabled-date="disabledEndDate"
              :disabled="detials.editable === '0'"
              @change="changTimeEnd"
            />
          </el-form-item>
        </div>

        <div class="form-row">
          <el-form-item
            prop="lengthStay"
            label="租赁时长(月)"
            class="form-item"
          >
            <el-input-number
              ref="lengthStayInput"
              :min="0"
              v-model="form.lengthStay"
              placeholder="请输入时长"
              :disabled="detials.editable === '0'"
              controls-position="right"
            />
            <!-- @change="changeLengthStay" -->
          </el-form-item>
          <el-form-item label="总租金(元)" prop="totalRent" class="form-item">
            <el-input-number
              :min="0"
              v-model="form.totalRent"
              precision="2"
              controls-position="right"
              disabled
            />
          </el-form-item>
        </div>
        <div class="form-row">
          <el-form-item prop="payType" label="租金付款方式" class="form-item">
            <el-select
              append-to-body
              v-model="form.payType"
              placeholder="请选择租金付款方式"
              :disabled="detials.editable === '0'"
              @change="changePayType"
            >
              <el-option
                v-for="item in PayType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <!--  v-if="form.payType && form.moveInDate && form.endDate" -->
        <div id="bill-section">
          <div class="bill-header">
            <h3><span class="required-star">*</span>生成账单</h3>
            <div class="totalRent">
              <div>
                账单总金额（元）: <span>{{ form.totalRent || '0' }}</span>
              </div>

              <div class="bill-actions">
                <el-button size="default" type="primary" @click="addBill"
                  >新增账单</el-button
                >
              </div>
            </div>
          </div>
          <el-table :data="billList" style="width: 100%">
            <el-table-column prop="billName" label="账单名称" width="300">
              <template #default="scope">
                <el-input
                  :disabled="scope.row.payStatus === '1'"
                  v-model="scope.row.billName"
                  placeholder="请输入账单名称"
                />
              </template>
            </el-table-column>
            <el-table-column prop="billPeriod" label="时间">
              <template #default="scope">
                <el-date-picker
                  v-model="scope.row.billPeriod"
                  type="daterange"
                  :disabled="scope.row.payStatus === '1'"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
                <!-- 
                  @change="(val) => handleDateRangeChange(val, scope.row,scope.$index)"
                 -->
              </template>
            </el-table-column>
            <el-table-column prop="billAmount" label="账单金额(元)" width="300">
              <template #default="scope">
                <el-input-number
                  v-model="scope.row.billAmount"
                  :min="0"
                  :disabled="scope.row.payStatus === '1'"
                  placeholder="请输入金额"
                  precision="2"
                  controls-position="right"
                  style="width: 100%"
                  @blur="validateAndCalculateTotal(scope.row)"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="110">
              <template #default="scope">
                <el-button
                  size="default"
                  :disabled="scope.row.payStatus == 1"
                  type="danger"
                  @click="deleteBill(scope.$index)"
                >
                  <!--     :disabled="billList.length <= 1" -->
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="form-row">
          <el-form-item label="合同上传" class="form-item-full">
            <el-upload
              style="width: 300px"
              :on-success="successUpload"
              v-model:file-list="form.uploadFileList"
              :action="url"
              multiple
              :before-upload="beforeUpload"
              :accept="accept"
              :limit="10"
              :on-preview="clkfile"
            >
              <el-button
                size="default"
                type="default"
                :disabled="!showSubmitPic"
                class="uploadBtn"
              >
                <span class="svg">
                  <svg
                    viewBox="0 0 16 16"
                    xmlns="http://www.w3.org/2000/svg"
                    class="icon icon-upload"
                    icon="upload"
                  >
                    <path
                      d="M3 10v2.995h10V10h1v4h-1v-.005H3V14H2v-4h1Zm5.026-8 3.814 3.815-.707.707-2.616-2.617V11h-1V3.923L4.89 6.548l-.707-.706L8.026 2Z"
                      fill="currentColor"
                      fill-rule="nonzero"
                    ></path>
                  </svg>
                </span>
                <span> 上传附件 </span>
              </el-button>
            </el-upload>
          </el-form-item>
        </div>

        <div class="form-row" v-if="state !== 'ck'">
          <div class="form-buttons">
            <el-button size="default" @click="returnFn(false)">取消</el-button>
            <el-button size="default" type="primary" @click="onSubmits"
              >提交</el-button
            >
          </div>
        </div>
      </el-form>
    </div>
    <div v-show="showPdf" ref="pdfContainer" class="pdfContainer">
      <canvas ref="pdfCanvas" class="pdfBox"> </canvas>
      <div @click="previousPage" class="left">
        <el-icon>
          <ArrowLeft />
        </el-icon>
      </div>
      <div @click="nextPage" class="right">
        <el-icon>
          <ArrowRight />
        </el-icon>
      </div>
      <div style="position: fixed; bottom: 20px; left: 50px; z-index: 199">
        <!-- <button :disabled="currentPage <= 1">上一页</button>
                <span>第 {{ currentPage }} 页 / 共 {{ pageCount }} 页</span>
                <button  :disabled="currentPage >= pageCount">下一页</button> -->
        <el-icon
          style="font-size: 30px; cursor: pointer; margin-left: 20px"
          @click="downloadPdf()"
        >
          <Download />
        </el-icon>
      </div>
    </div>
    <el-image-viewer
      v-if="showPreview"
      :url-list="srcList"
      show-progress
      @close="showPreview = false"
    >
      <template #toolbar="{ actions, reset }">
        <el-icon @click="actions('zoomOut')">
          <ZoomOut />
        </el-icon>
        <el-icon
          @click="actions('zoomIn', { enableTransition: false, zoomRate: 2 })"
        >
          <ZoomIn />
        </el-icon>
        <el-icon
          @click="
            actions('clockwise', { rotateDeg: 180, enableTransition: false })
          "
        >
          <RefreshRight />
        </el-icon>
        <el-icon @click="actions('anticlockwise')">
          <RefreshLeft />
        </el-icon>
        <el-icon @click="reset">
          <Refresh />
        </el-icon>
        <el-icon @click="downloadImg()">
          <Download />
        </el-icon>
      </template>
    </el-image-viewer>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router';
import {
  getenterpriseListAPI,
  addleaseAPI,
  getOneAPI,
  updateAPI,
  detailAPI,
  listNoMountEnterpriseAPI,
} from '@/api/system/propertyRepair';
import { getNoLeaseBuildingTree, TreeAPI } from '@/api/lease';
import { baseEvn } from '@/utils/utils';
import { transformData } from '@/utils/formData';
import dayjs from 'dayjs';

export default {
  props: {
    operationType: {
      type: String,
      default: 'tj',
    },
    operationData: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      currentPage: 1, // 当前页码，初始为 1
      pageCount: 0, // PDF 总页数
      showPreview: false,
      srcList: [],
      route: useRoute(),
      router: useRouter(),
      enterprisesx: [],
      showSubmitPic: true,
      form: {},
      accept: '.jpg,.png,.pdf',
      url: `${baseEvn}upload`,
      state: '',
      rules: {
        enterpriseName: [
          { required: true, message: '企业名称必填', trigger: 'change' },
        ],
        rentMonth: [
          { required: true, message: '租金单价必填', trigger: 'blur' },
        ],
        moveInDate: [
          { required: true, message: '起租日期必填', trigger: 'change' },
        ],
        endDate: [
          { required: true, message: '截止日期必填', trigger: 'change' },
        ],
        housesIds: [
          { required: true, message: '入驻房源必填', trigger: 'change' },
        ],
        lengthStay: [
          { required: true, message: '租赁时长必填', trigger: 'blur' },
        ],
        leaseArea: [
          { required: true, message: '租赁面积必填', trigger: 'blur' },
        ],
        payType: [
          { required: true, message: '租金付款方式必填', trigger: 'change' },
        ],
      },
      PayType: [
        {
          label: '月付',
          value: '1',
        },
        {
          label: '季付',
          value: '2',
        },
        {
          label: '半年付',
          value: '5',
        },
        {
          label: '年付',
          value: '3',
        },
        {
          label: '一次性付清',
          value: '4',
        },
      ],
      useType: [
        {
          label: '生产',
          value: '1',
        },
        {
          label: '办公',
          value: '2',
        },
        {
          label: '研发',
          value: '3',
        },
        {
          label: '其他',
          value: '4',
        },
      ],
      treeList: [],
      optionProps: {
        //value: "buildingId",
        //label: "buildingName",
        //children: "houseList",
        // checkStrictly: true,
        emitPath: false,
        multiple: true,
      },
      appendToTarget: null,
      fileName: '',
      showPdf: false,
      pdfData: {},
      billList: [], // 账单列表
      detials: {},
    };
  },
  created() {
    // this.url = baseEvn + 'upload';
    // if (this.$props.origin === 'onlineService') {
    //   this.state = 'ck';
    //   this.getOneAPI();
    // } else {
    //   if (this.$route.query.id && this.$route.query.st == 1) {
    //     this.state = 'bj';
    //     this.getOneAPI();
    //   } else if (this.$route.query.id && this.$route.query.st == 2) {
    //     this.state = 'xy';
    //     this.getOneAPI();
    //   } else if (this.$route.query.id) {
    //     this.state = 'ck';
    //     this.getOneAPI();
    //   } else {
    //     this.state = 'tj';
    //   }
    // }
    // this.getenterpriseList();
    // if (this.state == 'tj') {
    //   this.getTreeList(true);
    // } else {
    //   this.getTreeList(false);
    // }
  },
  mounted() {
    this.url = baseEvn + 'upload';
    // console.log(this.operationData, 'this.operationData');

    if (this.operationData?.id) {
      this.getOneAPI();
    }
    this.state = this.operationType;
    console.log(this.state, 'this.state');
    this.getenterpriseList();
    if (this.state == 'tj') {
      this.getTreeList(true);
    } else {
      this.getTreeList(false);
    }
    // console.log('?/mounted');
    this.appendToTarget = this.$refs.det;
    // console.log(this.appendToTarget, '  this.det');
  },
  watch: {
    dialogVisible() {
      this.textarea = '';
    },
    'form.uploadFileList': {
      deep: true,
      handler: function (val) {
        if (val.length < 10) {
          this.showSubmitPic = true;
        } else {
          this.showSubmitPic = false;
        }
      },
    },
  },
  methods: {
    async loadPdf(rawFile) {
      try {
        this.pdfData = rawFile;
        let url = rawFile.path || rawFile.url;
        const loadingTask = window.pdfjsLib.getDocument(url);
        this.pdf = await loadingTask.promise;
        this.pageCount = this.pdf.numPages;
        await this.renderPage(this.currentPage);
      } catch (error) {
        console.error('加载 PDF 失败:', error);
      }
    },
    successUpload(e, e2) {
      this.form.uploadFileList[this.form.uploadFileList.length - 1] = {
        name: e2.name,
        url: e.data.url,
        value: e.data.value,
      };
      this.fileName = e2.name;
    },
    addfj() {
      if (!this.showSubmitPic) {
        this.$message.error('附件最多只能上传10个哦~');
      }
    },
    downloadPdf() {
      let objectUrl = this.pdfData.path || this.pdfData.url;
      const fileName = this.pdfData.name;
      const downloadLink = document.createElement('a');
      downloadLink.href = objectUrl;
      downloadLink.download = fileName;
      downloadLink.click();
    },
    downloadImg(e) {
      let objectUrl = this.srcList[0];
      const fileName = this.fileName;
      const downloadLink = document.createElement('a');
      downloadLink.href = objectUrl;
      downloadLink.download = fileName;
      downloadLink.click();
    },
    clkfile(rawFile) {
      const extension = rawFile.name.split('.').pop().toLowerCase();
      const allowedFormats = ['png', 'jpg', 'pdf'];
      if (allowedFormats.includes(extension)) {
        if (extension.toLowerCase() === 'pdf') {
          this.loadPdf(rawFile);
          this.showPdf = true;
        } else {
          let objectUrl = rawFile.path || rawFile.url;
          this.fileName = rawFile.name;
          this.showPreview = true;
          this.srcList = [objectUrl];
        }
      } else {
        this.$message.error('只能预览pdf、png、jpg格式的文件');
        return;
      }
    },
    async renderPage(pageNumber) {
      if (pageNumber < 1 || pageNumber > this.pageCount) {
        return;
      }
      this.currentPage = pageNumber;
      try {
        const page = await this.pdf.getPage(pageNumber);
        const viewport = page.getViewport({ scale: 1.5 });
        const canvas = this.$refs.pdfCanvas;
        const context = canvas.getContext('2d');
        canvas.height = viewport.height;
        canvas.width = viewport.width;
        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        };
        await page.render(renderContext).promise;
      } catch (error) {
        console.error(`渲染第 ${pageNumber} 页失败:`, error);
      }
    },
    async previousPage() {
      if (this.currentPage > 1) {
        await this.renderPage(this.currentPage - 1);
      }
    },
    async nextPage() {
      if (this.currentPage < this.pageCount) {
        await this.renderPage(this.currentPage + 1);
      }
    },
    beforeUpload(rawFile) {
      let formatList = this.accept.split(',');
      const lastIndex = rawFile.name.lastIndexOf('.');
      const result = rawFile.name.substring(lastIndex);
      const isExists = formatList.includes(result);
      if (!isExists) {
        this.$message.error('您上传的文件格式不规范!');
        return false;
      } else if (rawFile.size / 1024 / 1024 > 20) {
        this.$message.error('单个文件大小不能超过20mb');
        return false;
      }
      return true;
    },
    replaceNullsWithEmptyStrings(obj) {
      for (let key in obj) {
        if (obj[key] === null) {
          obj[key] = undefined;
        }
      }
      return obj;
    },
    async getOneAPI() {
      try {
        const res = await detailAPI({
          id: this.operationData?.id,
        });

        if (!res.data) {
          this.$message.error('获取数据失败');
          return;
        }
        this.detials = res.data;
        const data = this.replaceNullsWithEmptyStrings(res.data);

        // 公共字段映射
        const commonFields = this.mapCommonFields(data);
        //debugger
        Object.assign(this.form, commonFields);
        this.billList = res?.data.bills.map((item) => {
          return {
            ...item,
            billPeriod: [item.billStartDate, item.billEndDate],
          };
        });

        // 根据不同状态处理特殊字段
        if (this.state === 'xy') {
          this.handleRenewalState(data);
        } else {
          this.handleNormalState(data);
        }

        // 处理时间戳转换
        this.convertTimestamps();

        // 计算总租金
        this.calculateTotalRent();
      } catch (error) {
        console.error('获取详情失败:', error);
        this.$message.error('获取数据失败，请重试');
      }
    },

    // 映射公共字段
    mapCommonFields(data) {
      return {
        enterpriseName: data.enterpriseName,
        unifiedSocialCreditCode: data.unifiedSocialCreditCode,
        moveInDate: data.moveInDate,
        endDate: data.endDate,
        housesIds: data.houseIds,
        lengthStay: data.lengthStay,
        payType: data.payType,
        propertyPayType: data.propertyPayType,
        leaseUsage: data.leaseUsage,
        leaseArea: data.leaseArea,
        rentMonth: this.convertCurrency(data.rentMonth),
      };
    },

    // 处理续租状态的特殊逻辑
    handleRenewalState(data) {
      // 续租时重置某些字段，设置新的起租日期
      const endDate = Date.parse(data.endDate);
      this.form.moveInDate = endDate + 86400000; // 原合同结束日期的下一天
      this.form.endDate = endDate;

      // 续租时不继承的字段
      delete this.form.signDate;
      delete this.form.deposit;
      delete this.form.propertyCostsMonth;
      delete this.form.contractNumber;
      delete this.form.totalRent;
      delete this.form.lengthRentFree;
      delete this.form.uploadFileList;
    },

    // 处理正常状态（查看、编辑）
    handleNormalState(data) {
      Object.assign(this.form, {
        signDate: data.signDate,
        deposit: this.convertCurrency(data.deposit),
        propertyCostsMonth: this.convertCurrency(data.propertyCostsMonth),
        contractNumber: data.contractNumber,
        totalRent: this.convertCurrency(data.totalRent),
        lengthRentFree: data.lengthRentFree,
        uploadFileList: data.uploadFileList || [],
      });
    },

    // 转换货币单位（分转元）
    convertCurrency(value) {
      return value ? value : 0;
    },

    // 处理时间戳转换
    convertTimestamps() {
      const timeFields = ['signDate', 'moveInDate', 'endDate'];
      timeFields.forEach((field) => {
        if (this.form[field] && typeof this.form[field] === 'string') {
          this.form[field] = Date.parse(this.form[field]);
        }
      });
    },
    async onSubmits() {
      await this.$refs.form.validate();

      // 校验生成账单的必填项
      if (this.billList.length > 0) {
        const billValidationResult = this.validateBills();
        if (!billValidationResult.isValid) {
          this.$message.error(billValidationResult.message);
          return;
        }
      }

      let uploadFileList = [];
      if (this.form.uploadFileList) {
        if (this.form.uploadFileList.length > 0) {
          uploadFileList = this.form.uploadFileList.map((it) => {
            return {
              name: it.name,
              path: it.ossPath || it.value,
            };
          });
        }
      }
      let data = {
        unifiedSocialCreditCode: this.form.unifiedSocialCreditCode, //社会信用代码
        enterpriseName: this.form.enterpriseName, //企业名称
        signDate: this.form.signDate, //签约日期
        moveInDate: this.form.moveInDate, //起租日期
        endDate: this.form.endDate, //起租日期
        housesIds: this.form.housesIds, //入驻房源
        lengthStay: this.form.lengthStay, //租赁时长
        rentMonth: this.form.rentMonth * 100, //租金单价
        deposit: this.form.deposit * 100, //押金
        propertyCostsMonth: this.form.propertyCostsMonth * 100, //物业费
        payType: this.form.payType, //租金付款方式
        propertyPayType: this.form.propertyPayType, //物业费付款方式
        contractNumber: this.form.contractNumber, //合同编号
        leaseUsage: this.form.leaseUsage, //用途
        totalRent: this.form.totalRent * 100, //总租金
        lengthRentFree: this.form.lengthRentFree, //免租时长
        leaseArea: this.form.leaseArea, //租赁面积
        id: this.operationData?.id,
        uploadFileList,
        bills: this.billList.map((e) => {
          // 处理时间格式
          let startDate, endDate;
          if (Array.isArray(e.billPeriod)) {
            startDate = e.billPeriod[0];
            endDate = e.billPeriod[1];
          } else if (
            typeof e.billPeriod === 'string' &&
            e.billPeriod.includes('-')
          ) {
            const dates = e.billPeriod.split('-');
            startDate = dayjs(dates[0]).format('YYYY-MM-DD HH:mm:ss');
            endDate = dayjs(dates[1]).format('YYYY-MM-DD HH:mm:ss');
          }

          return {
            billName: e.billName,
            billAmount: e.billAmount,
            billStartDate: startDate,
            billEndDate: endDate,
            payStatus: e.payStatus || 0,
            id: e.id || '',
          };
        }), // 添加账单数据
      };
      // console.log(data);

      //  state == 'ck'
      //     ? '查看'
      //     : state == 'bj'
      //     ? '编辑'
      //     : state == 'xy'
      //     ? '续租'
      //     : '新增'
      const res =
        this.state == 'tj' || this.state == 'xy'
          ? await addleaseAPI(data)
          : await updateAPI(data);
      if (res.status == 0) {
        this.$message.success('操作成功！');
        this.returnFn(true);
      }
    },

    // 处理日期范围变化
    handleDateRangeChange(val, row, index) {
      //debugger
      // if (row.billPeriod && Array.isArray(row.billPeriod)) {
      //   // 将日期数组转换为字符串格式
      //   const startDate = row.billPeriod[0]
      //     // .replace(/-/g, '');
      //   const endDate = row.billPeriod[1]
      //     // .replace(/-/g, '');
      //   // row.billPeriod = `${startDate}-${endDate}`;
      // }
    },

    // 校验账单必填项
    validateBills() {
      for (let i = 0; i < this.billList.length; i++) {
        const bill = this.billList[i];
        const billNumber = i + 1;

        // 校验账单名称
        if (!bill.billName || bill.billName.trim() === '') {
          return {
            isValid: false,
            message: `第${billNumber}条账单的名称不能为空`,
          };
        }

        // 校验时间
        if (
          !bill.billPeriod ||
          (Array.isArray(bill.billPeriod) && bill.billPeriod.length !== 2)
        ) {
          return {
            isValid: false,
            message: `第${billNumber}条账单的时间不能为空`,
          };
        }
        if (
          bill.billAmount === null ||
          bill.billAmount === undefined ||
          bill.billAmount < 0
        ) {
          return {
            isValid: false,
            message: `第${billNumber}条账单的金额不能为空`,
          };
        }
      }

      return {
        isValid: true,
        message: '',
      };
    },

    async getTreeList(is) {
      let data = {
        status: 0,
      };
      const res = is ? await getNoLeaseBuildingTree(data) : await TreeAPI();
      this.treeList = res.data.map((item) => {
        return {
          value: item.buildingId,
          label: item.buildingName,
          children: transformData(item.houseList),
        };
      });
    },
    changeenterprise(e) {
      if (e) {
        this.enterprisesx.map((it) => {
          if (it.label == e) {
            this.form.unifiedSocialCreditCode = it.value;
          }
        });
      }
    },
    async present() {
      const res = await addFollowRecord({
        pendingLeaseId: this.$route.query.id,
        remark: this.textarea,
      });
      this.dialogVisible = false;
    },
    async getenterpriseList() {
      const res = await listNoMountEnterpriseAPI();
      this.enterprisesx = res.data.map((item) => {
        return {
          value: item.uniCode,
          label: item.name,
        };
      });
    },
    returnFn(e) {
      this.$emit('canleAddlease2', e);
      // this.router.push('/workbench/lease/rent');
    },
    // 计算租赁时长的方法
    calculateLengthStay() {
      if (this.form.moveInDate && this.form.endDate) {
        // 使用dayjs库处理时间戳
        const startDate = dayjs(parseInt(this.form.moveInDate));
        const endDate = dayjs(parseInt(this.form.endDate));

        if (endDate.isBefore(startDate) || endDate.isSame(startDate)) {
          this.form.lengthStay = 0;
          return;
        }
        // 使用dayjs计算月份差异
        const months = endDate.diff(startDate, 'month', true); // true表示返回精确的小数
        // 如果日期在同一天则加1
        if (startDate.date() === endDate.date()) {
          this.form.lengthStay = Math.ceil(months) + 1;
          return;
        }
        // 不足月按满月计算，所以向上取整
        this.form.lengthStay = Math.ceil(months);
        this.calculateTotalRent();
      }
    },
    // 当选择租金付款方式时生成账单
    changePayType() {
      this.generateBills();
    },

    // 生成账单
    generateBills() {
      if (!this.form.payType || !this.form.moveInDate || !this.form.endDate) {
        this.billList = [];
        return;
      }
      const startDate = new Date(parseInt(this.form.moveInDate));
      const endDate = new Date(parseInt(this.form.endDate));

      const payType = this.form.payType;

      this.billList = [];
      let currentDate = new Date(startDate);
      let billIndex = 1;

      // 根据付款方式确定间隔月数
      let intervalMonths = 1; // 默认月付
      switch (payType) {
        case '1': // 月付
          intervalMonths = 1;
          break;
        case '2': // 季付
          intervalMonths = 3;
          break;
        case '5': // 半年付
          intervalMonths = 6;
          break;
        case '3': // 年付
          intervalMonths = 12;
          break;
        case '4': // 一次性付清
          intervalMonths = null;
          break;
      }

      if (payType === '4') {
        // 一次性付清，只生成一个账单
        this.billList.push({
          billName: '第一次账单',
          billPeriod: [
            this.formatDateToYMD(startDate),
            this.formatDateToYMD(endDate),
          ],
          billAmount: 0,
        });
      } else {
        // 按周期生成账单
        while (currentDate <= endDate) {
          let nextDate = new Date(currentDate);
          nextDate.setMonth(nextDate.getMonth() + intervalMonths);
          // 计算本期结束日期
          let periodEndDate = new Date(nextDate);
          periodEndDate.setDate(periodEndDate.getDate() - 1);

          // 如果本期结束日期超过或等于总结束日期，则以总结束日期为准
          if (periodEndDate >= endDate) {
            periodEndDate = new Date(endDate);
          }

          this.billList.push({
            billName: `第${this.numberToChinese(billIndex)}次账单`,
            billPeriod: [
              this.formatDateToYMD(currentDate),
              this.formatDateToYMD(periodEndDate),
            ],
            billAmount: 0,
          });

          billIndex++;

          // 如果本期结束日期已经达到总结束日期，退出循环
          if (periodEndDate.getTime() >= endDate.getTime()) {
            break;
          }

          // 下一期开始日期是本期结束日期的下一天
          currentDate = new Date(periodEndDate);
          currentDate.setDate(currentDate.getDate() + 1);
        }
      }

      // 生成账单后计算总租金
      this.calculateTotalRent();
    },

    // 格式化日期为YYYY-MM-DD格式
    formatDateToYMD(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },

    // 数字转中文
    numberToChinese(num) {
      const chinese = [
        '一',
        '二',
        '三',
        '四',
        '五',
        '六',
        '七',
        '八',
        '九',
        '十',
      ];
      if (num <= 10) {
        return chinese[num - 1];
      } else if (num < 20) {
        return '十' + chinese[num - 11];
      } else if (num % 10 === 0) {
        return chinese[Math.floor(num / 10) - 1] + '十';
      } else {
        return (
          chinese[Math.floor(num / 10) - 1] + '十' + chinese[(num % 10) - 1]
        );
      }
    },

    // 新增账单
    addBill() {
      const newIndex = this.billList.length + 1;
      this.billList.push({
        billName: `第${this.numberToChinese(newIndex)}次账单`,
        billPeriod: null, // 改为null，让用户手动选择
        billAmount: 0,
      });
      this.calculateTotalRent();
    },

    // 删除账单
    deleteBill(index) {
      if (this.billList.length > 1) {
        this.billList.splice(index, 1);
        // 重新编号账单名称
        this.billList.forEach((bill, idx) => {
          if (
            bill.billName.includes('第') &&
            bill.billName.includes('次账单')
          ) {
            bill.billName = `第${this.numberToChinese(idx + 1)}次账单`;
          }
        });
        this.calculateTotalRent();
      }
    },
    changTimeStatrt(e) {
      // console.log(e, this.form.moveInDate);
      this.calculateLengthStay();
      // 重新生成账单
      if (this.form.payType) {
        this.generateBills();
      }
      // 将光标定位到租赁时长输入框
      this.focusLengthStayInput();
    },

    changTimeEnd(e) {
      // console.log(e, this.form.endDate);
      this.calculateLengthStay();
      // 重新生成账单
      if (this.form.payType) {
        this.generateBills();
      }
      // 将光标定位到租赁时长输入框
      this.focusLengthStayInput();
    },

    // 将光标定位到租赁时长输入框
    focusLengthStayInput() {
      this.$nextTick(() => {
        if (this.$refs.lengthStayInput) {
          this.$refs.lengthStayInput.focus();
        }
      });
    },

    // 限制截止时间只能选择起租日期之后的日期
    disabledEndDate(time) {
      if (!this.form.moveInDate) {
        return false;
      }
      const moveInDate = new Date(parseInt(this.form.moveInDate));
      return time.getTime() <= moveInDate.getTime();
    },
    handleSelectVisibleChange(visible) {
      // 当下拉框显示/隐藏时，调整其他日期选择器的z-index
      const datePickerElements = document.querySelectorAll('.el-date-editor');
      if (visible) {
        datePickerElements.forEach((el) => {
          el.style.zIndex = '1000';
        });
      } else {
        datePickerElements.forEach((el) => {
          el.style.zIndex = '';
        });
      }
    },

    handleSelectClick(e) {
      e.preventDefault();
      e.stopPropagation();
      // 确保日期选择器不会同时打开
      const datePickerElements = document.querySelectorAll(
        '.el-date-editor input'
      );
      datePickerElements.forEach((el) => {
        el.blur();
      });
    },
    // // 月租金
    // changeRentMonth() {
    //   this.calculateTotalRent();
    // },
    // // 租赁时长(月)
    // changeLengthStay() {
    //   this.calculateTotalRent();
    // },
    // // /请输入租赁面积
    // changeLeaseArea() {
    //   this.calculateTotalRent();
    // },
    // 校验账单金额并计算总租金
    validateAndCalculateTotal(billRow) {
      // 校验金额是否为有效数字
      if (billRow.billAmount === null || billRow.billAmount === undefined) {
        billRow.billAmount = 0;
      }

      // 确保金额为非负数
      if (billRow.billAmount < 0) {
        billRow.billAmount = 0;
        this.$message.warning('账单金额不能为负数');
      }

      // 校验小数位数（element-plus的precision已经处理了格式，这里做额外校验）
      if (billRow.billAmount !== null && billRow.billAmount !== undefined) {
        // 转换为2位小数
        billRow.billAmount = Math.round(billRow.billAmount * 100) / 100;
      }

      // 校验通过后计算总租金
      this.calculateTotalRent();
    },

    // 计算总租金
    calculateTotalRent() {
      // 计算所有账单金额的总和
      this.form.totalRent = this.billList.reduce((total, bill) => {
        return total + (parseFloat(bill.billAmount) || 0);
      }, 0);
    },
  },
};
</script>

<style scoped lang="scss">
@import './bill.scss';
.pdfContainer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  .pdfBox {
    width: auto;
    height: auto;
    max-width: 100%;
  }
  .right {
    position: fixed;
    bottom: 50%;
    cursor: pointer;
    right: 10%;
    font-size: 40px;
  }
  .left {
    position: fixed;
    cursor: pointer;
    bottom: 50%;
    left: 10%;
    font-size: 40px;
  }
}

::v-deep {
  .el-textarea__inner {
    height: 200px;
  }
}

.custom-form {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.det {
  padding: 0px 40px;
  width: 100%;
  height: 100%;

  .title {
    display: flex;
    justify-content: space-between;

    .btn {
      cursor: pointer;
      font-size: 25px;
    }
  }

  .boxes {
    width: 80%;
    height: auto;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    padding: 25px;
    background: white;
    border-radius: 10px;
    box-shadow: 4px 4px 10px 0px #eef1f8;
    margin-bottom: 25px;
    // 小屏幕适配
    @media (max-width: 1600px) {
      width: 100%;
    }

    .boxss {
      width: 30%;
      margin-left: 2%;
      height: 200px;
      margin-top: 30px;
      padding: 20px;
      border: 1px solid #f2f2f2;
      border-radius: 20px;
    }

    .btn {
      width: 30%;
      margin-left: 2%;
      height: 200px;
      margin-top: 30px;
      padding: 20px;
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .two-column-form {
    .form-row {
      display: flex;
      gap: 20px;
      // margin-bottom: 10px;

      .form-item {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .form-item-full {
        width: 100%;
        margin-bottom: 0 !important;
      }
    }
  }
  // 统一设置所有表单组件的宽度为300px
  :deep(.el-input),
  :deep(.el-select),
  :deep(.el-date-editor),
  :deep(.el-input-number),
  :deep(.el-cascader) {
    width: 300px !important;
  }

  // 确保级联选择器的宽度
  :deep(.el-cascader .el-input) {
    width: 300px !important;
  }

  // 数字输入框特殊处理
  :deep(.el-input-number .el-input) {
    width: 300px !important;
  }
}

.form-buttons {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
