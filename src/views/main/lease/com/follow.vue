<template>
  <div class="det">
    <div class="title">
      <h2>跟进记录</h2>
      <span @click="returnFn" class="btn">X</span>
    </div>
    <div class="boxes">
      <div class="boxss" v-for="(item, index) in deletes" :key="index">
        <div>跟进人：{{ item.person }}</div>
        <div>跟进时间：{{ item.timeData }}</div>
        <div>跟进描述：{{ item.remark }}</div>
      </div>
      <div class="btn">
        <el-button size="default" @click="dialogVisible = true" type="primary"
          >添加跟进记录</el-button
        >
      </div>
      <el-dialog
        v-model="dialogVisible"
        title="添加跟进记录"
        width="30%"
        :before-close="handleClose"
      >
        <el-input
          style="height: 200px"
          size="default"
          v-model="textarea"
          maxlength="300"
          placeholder="请输入跟进记录，最多输入300字"
          show-word-limit
          type="textarea"
        />
        <template #footer>
          <span class="dialog-footer">
            <el-button size="default" @click="dialogVisible = false"
              >取消</el-button
            >
            <el-button size="default" type="primary" @click="present">
              确认
            </el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { useRoute, useRouter } from "vue-router";
import { getListAPI, addFollowRecord } from "@/api/system/propertyRepair";
import { formatDate } from "@/utils/formData";
export default {
  data() {
    return {
      route: useRoute(),
      router: useRouter(),
      deletes: [],
      dialogVisible: false,
      textarea: "",
    };
  },
  created() {
    this.getdetails();
  },
  watch: {
    dialogVisible() {
      this.textarea = "";
    },
  },
  methods: {
    async present() {
      if (this.textarea == "") {
        return this.$message.error("跟进记录不能为空");
      } else {
        const res = await addFollowRecord({
          pendingLeaseId: this.$route.query.id,
          remark: this.textarea,
        });
        this.dialogVisible = false;
        this.getdetails();
      }
    },
    async getdetails() {
      const res = await getListAPI({
        businessCode: "pending_lease_follow_record",
        conditions: [
          {
            compareType: "ge",
            key: "pending_lease_id",
            value: this.$route.query.id,
          },
        ],
      });
      this.deletes = res.data.elements;
      this.deletes.forEach((it) => {
        it.timeData = formatDate(
          "yyyy-MM-dd HH:mm:ss",
          new Date(+it.follow_date)
        );
      });
    },
    returnFn() {
      this.router.push("/workbench/lease/commission");
      //this.router.go(1);
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep {
  .el-textarea__inner {
    height: 200px;
  }
}

.det {
  padding: 0px 40px;
  width: 100%;
  height: 100%;

  .title {
    display: flex;
    justify-content: space-between;

    .btn {
      cursor: pointer;
    }
  }

  .boxes {
    width: 100%;
    height: auto;
    display: flex;
    flex-wrap: wrap;

    .boxss {
      width: 30%;
      margin-left: 2%;
      height: 200px;
      margin-top: 30px;
      padding: 20px;
      border: 1px solid #f2f2f2;
      border-radius: 20px;
    }

    .btn {
      width: 30%;
      margin-left: 2%;
      height: 200px;
      margin-top: 30px;
      padding: 20px;
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>