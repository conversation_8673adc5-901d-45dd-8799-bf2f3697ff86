<!-- 新增租约 物业费租约 -->
<template>
  <div class="det">
    <div v-if="!$props.origin">
      <div v-if="!showPdf" class="title">
        <h3>
          {{
            state == 'ck'
              ? '查看'
              : state == 'bj'
              ? '编辑'
              : state == 'xy'
              ? '续租'
              : '新增'
          }}物业费租约
        </h3>
        <span @click="returnFn(false)" class="btn">
          <el-icon>
            <Close />
          </el-icon>
        </span>
      </div>
      <div
        v-if="showPdf"
        @click="showPdf = false"
        style="cursor: pointer"
        class="title"
      >
        <h2>返回</h2>
      </div>
    </div>
    <div v-show="!showPdf" class="boxes">
      <el-form
        :model="form"
        ref="form"
        :rules="rules"
        size="default"
        :disabled="state == 'ck'"
        style="width: 100%"
        label-width="140px"
        class="two-column-form"
      >
        <div class="form-row">
          <el-form-item
            prop="enterpriseName"
            label="企业名称"
            class="form-item"
          >
            <el-select
              filterable
              :append-to="appendToTarget"
              @click.native.stop="handleSelectClick"
              :disabled="state !== 'tj'"
              v-model="form.enterpriseName"
              @change="changeenterprise(form.enterpriseName)"
              @visible-change="handleSelectVisibleChange"
              :popper-options="{
                modifiers: [
                  {
                    name: 'computeStyles',
                    options: {
                      adaptive: false,
                      enabled: false,
                    },
                  },
                ],
              }"
              popper-class="enterprise-select-dropdown"
              placeholder="请选择企业"
            >
              <el-option
                v-for="item in enterprisesx"
                :key="item.label"
                :label="item.label"
                :value="item.label"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="企业社会信用代码" class="form-item">
            <el-input disabled v-model="form.unifiedSocialCreditCode" />
          </el-form-item>
        </div>

        <div class="form-row">
          <el-form-item label="签约日期" class="form-item">
            <el-date-picker
              append-to-body
              v-model="form.signDate"
              type="date"
              placeholder="请选择签约日期"
              value-format="x"
              :disabled="operationData?.editable === '0'"
            />
          </el-form-item>

          <el-form-item label="押金" prop="deposit" class="form-item">
            <el-input-number
              :min="0"
              v-model="form.deposit"
              placeholder="请输入押金"
              precision="2"
              controls-position="right"
              :disabled="operationData?.editable === '0'"
            />
          </el-form-item>
        </div>

        <div class="form-row">
          <el-form-item label="租赁面积(㎡)" prop="areas" class="form-item">
            <el-input-number
              :min="0"
              v-model="form.areas"
              placeholder="请输入租赁面积"
              controls-position="right"
              :disabled="operationData?.editable === '0'"
              @change="onAreasOrPropertyCostChange"
            />
          </el-form-item>
          <el-form-item
            label="物业费(元/平*月)"
            prop="propertyCostsMonth"
            class="form-item"
          >
            <el-input-number
              :min="0"
              v-model="form.propertyCostsMonth"
              placeholder="请输入物业费"
              precision="2"
              controls-position="right"
              :disabled="operationData?.editable === '0'"
              @change="onAreasOrPropertyCostChange"
            />
          </el-form-item>
        </div>

        <div class="form-row">
          <el-form-item label="起租日期" prop="moveInDate" class="form-item">
            <el-date-picker
              append-to-body
              v-model="form.moveInDate"
              type="date"
              placeholder="请选择起租日期"
              value-format="x"
              :disabled="operationData?.editable === '0'"
              @change="changeStartDate"
            />
          </el-form-item>
          <el-form-item label="截止时间" prop="endDate" class="form-item">
            <el-date-picker
              append-to-body
              v-model="form.endDate"
              type="date"
              placeholder="请选择截止时间"
              value-format="x"
              :disabled-date="disabledEndDate"
              :disabled="operationData?.editable === '0'"
              @change="changeEndDate"
            />
          </el-form-item>
        </div>
        <div class="form-row">
          <el-form-item
            prop="lengthStay"
            label="租赁时长(月)"
            class="form-item"
          >
            <el-input-number
              ref="lengthStayInput"
              :min="0"
              v-model="form.lengthStay"
              placeholder="请输入时长"
              controls-position="right"
              :disabled="operationData?.editable === '0'"
              @change="calculateTotalRent"
            />
          </el-form-item>
          <el-form-item label="总物业费(元)" prop="totalRent" class="form-item">
            <el-input-number
              disabled
              :min="0"
              precision="2"
              v-model="form.totalRent"
              placeholder="请输入总租金"
              controls-position="right"
            />
          </el-form-item>
        </div>
        <div class="form-row">
          <el-form-item prop="payType" label="结算方式" class="form-item">
            <el-select
              append-to-body
              v-model="form.payType"
              placeholder="请选择结算方式"
              :disabled="operationData?.editable === '0'"
              @change="changePayType"
            >
              <el-option
                v-for="item in PayType"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </div>
        <!--      v-if="form.payType && form.moveInDate && form.endDate" -->
        <div id="bill-section">
          <div class="bill-header">
            <h3><span class="required-star">*</span>生成账单</h3>
            <div class="bill-actions">
              <el-button size="default" type="primary" @click="addBill"
                >新增账单</el-button
              >
            </div>
          </div>
          <el-table :data="billList" style="width: 100%">
            <el-table-column prop="billName" label="账单名称" width="300">
              <template #default="scope">
                <el-input
                  v-model="scope.row.billName"
                  placeholder="请输入账单名称"
                  :disabled="scope.row.payStatus == 1"
                />
              </template>
            </el-table-column>
            <el-table-column prop="billPeriod" label="时间">
              <template #default="scope">
                <!-- <el-input
                  v-model="scope.row.billPeriod"
                  placeholder="请输入时间范围"
        
                /> -->
                <el-date-picker
                  v-model="scope.row.billPeriod"
                  type="daterange"
                  :disabled="scope.row.payStatus === '1'"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD"
                  style="width: 100%"
                />
                <!-- 
                  @change="(val) => handleDateRangeChange(val, scope.row,scope.$index)"
                 -->
              </template>
            </el-table-column>
            <el-table-column prop="billAmount" label="账单金额(元)" width="300">
              <template #default="scope">
                <el-input-number
                  v-model="scope.row.billAmount"
                  :min="0"
                  precision="2"
                  placeholder="请输入金额"
                  controls-position="right"
                  style="width: 100%"
                  :disabled="scope.row.payStatus == 1"
                />
              </template>
            </el-table-column>
            <el-table-column label="操作" width="110">
              <template #default="scope">
                <el-button
                  size="default"
                  type="danger"
                  :disabled="scope.row.payStatus == 1"
                  @click="deleteBill(scope.$index)"
                >
                  <!--     :disabled="billList.length <= 1" -->
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div class="form-row">
          <el-form-item label="合同上传" class="form-item-full">
            <el-upload
              style="width: 300px"
              :on-success="successUpload"
              v-model:file-list="form.uploadFileList"
              :action="url"
              multiple
              :before-upload="beforeUpload"
              :accept="accept"
              :limit="10"
              :on-preview="clkfile"
            >
            <el-button
                size="default"
                type="default"
                :disabled="!showSubmitPic"
                class="uploadBtn"
              >
                <span class="svg">
                  <svg
                    viewBox="0 0 16 16"
                    xmlns="http://www.w3.org/2000/svg"
                    class="icon icon-upload"
                    icon="upload"
                  >
                    <path
                      d="M3 10v2.995h10V10h1v4h-1v-.005H3V14H2v-4h1Zm5.026-8 3.814 3.815-.707.707-2.616-2.617V11h-1V3.923L4.89 6.548l-.707-.706L8.026 2Z"
                      fill="currentColor"
                      fill-rule="nonzero"
                    ></path>
                  </svg>
                </span>
                <span> 上传附件 </span>
                
                </el-button
              >
            </el-upload>
          </el-form-item>
        </div>

        <div class="form-row" v-if="state !== 'ck'">
          <div class="form-buttons">
            <el-button size="default" @click="returnFn(false)">取消</el-button>
            <el-button size="default" type="primary" @click="onSubmits"
              >提交</el-button
            >
          </div>
        </div>
      </el-form>
    </div>
    <div v-show="showPdf" ref="pdfContainer" class="pdfContainer">
      <canvas ref="pdfCanvas" class="pdfBox"> </canvas>
      <div @click="previousPage" class="left">
        <el-icon>
          <ArrowLeft />
        </el-icon>
      </div>
      <div @click="nextPage" class="right">
        <el-icon>
          <ArrowRight />
        </el-icon>
      </div>
      <div style="position: fixed; bottom: 20px; left: 50px; z-index: 199">
        <!-- <button :disabled="currentPage <= 1">上一页</button>
                <span>第 {{ currentPage }} 页 / 共 {{ pageCount }} 页</span>
                <button  :disabled="currentPage >= pageCount">下一页</button> -->
        <el-icon
          style="font-size: 30px; cursor: pointer; margin-left: 20px"
          @click="downloadPdf()"
        >
          <Download />
        </el-icon>
      </div>
    </div>
    <el-image-viewer
      v-if="showPreview"
      :url-list="srcList"
      show-progress
      @close="showPreview = false"
    >
      <template
        #toolbar="{ actions, prev, next, reset, activeIndex, setActiveItem }"
      >
        <el-icon @click="actions('zoomOut')">
          <ZoomOut />
        </el-icon>
        <el-icon
          @click="actions('zoomIn', { enableTransition: false, zoomRate: 2 })"
        >
          <ZoomIn />
        </el-icon>
        <el-icon
          @click="
            actions('clockwise', { rotateDeg: 180, enableTransition: false })
          "
        >
          <RefreshRight />
        </el-icon>
        <el-icon @click="actions('anticlockwise')">
          <RefreshLeft />
        </el-icon>
        <el-icon @click="reset">
          <Refresh />
        </el-icon>
        <el-icon @click="downloadImg()">
          <Download />
        </el-icon>
      </template>
    </el-image-viewer>
  </div>
</template>
<script>
import { useRoute, useRouter } from 'vue-router';
import {
  getenterpriseListAPI,
  addleaseAPI,
  getOneAPI,
  updateAPI,
  detailAPI,
  listNoMountEnterpriseAPI,
  insertOrUpdateAPI,
} from '@/api/system/propertyRepair';
import { baseEvn } from '@/utils/utils';
import dayjs from 'dayjs';

export default {
  props: {
    operationType: {
      type: String,
      default: 'tj',
    },
    operationData: {
      type: Object,
      default: () => {},
    },
 
  },
  data() {
    return {
      currentPage: 1, // 当前页码，初始为 1
      pageCount: 0, // PDF 总页数
      showPreview: false,
      srcList: [],
      route: useRoute(),
      router: useRouter(),
      enterprisesx: [],
      showSubmitPic: true,
      form: {},
      accept: '.jpg,.png,.pdf',
      url: `${baseEvn}upload`,
      state: '',
      rules: {
        enterpriseName: [
          { required: true, message: '企业名称必填', trigger: 'change' },
        ],
        moveInDate: [
          { required: true, message: '起租日期必填', trigger: 'change' },
        ],
        endDate: [
          { required: true, message: '截止日期必填', trigger: 'change' },
        ],
        housesIds: [
          { required: true, message: '入驻房源必填', trigger: 'change' },
        ],
        lengthStay: [
          { required: true, message: '租赁时长必填', trigger: 'blur' },
        ],
        areas: [{ required: true, message: '租赁面积必填', trigger: 'blur' }],
        // totalRent: [{ required: true, message: '总租金必填', trigger: 'blur' }],
        propertyCostsMonth: [
          { required: true, message: '物业费必填', trigger: 'blur' },
        ],
        payType: [
          { required: true, message: '结算方式必填', trigger: 'change' },
        ],
      },
      PayType: [
        {
          label: '月付',
          value: '1',
        },
        {
          label: '季付',
          value: '2',
        },
        {
          label: '半年付',
          value: '5',
        },
        {
          label: '年付',
          value: '3',
        },
        {
          label: '一次性付清',
          value: '4',
        },
      ],
      treeList: [],
      optionProps: {
        //value: "buildingId",
        //label: "buildingName",
        //children: "houseList",
        // checkStrictly: true,
        emitPath: false,
        multiple: true,
      },
      appendToTarget: null,
      fileName: '',
      showPdf: false,
      pdfData: {},
      enterprises: [],
      billList: [],
    };
  },
  created() {
  
  },
  mounted() {
      this.url = baseEvn + 'upload';
    // console.log(this.operationData, 'this.operationData');

    if (this.operationData?.enterpriseId) {
      this.getOneAPI();
    }
    this.state = this.operationType;
    this.getenterpriseList();
    this.appendToTarget = this.$refs.selectContainer;
  },
  watch: {
    dialogVisible() {
      this.textarea = '';
    },
    'form.uploadFileList': {
      deep: true,
      handler: function (val) {
        if (val.length < 10) {
          this.showSubmitPic = true;
        } else {
          this.showSubmitPic = false;
        }
      },
    },
  },
  methods: {
    changeenterprise(e) {
      if (e) {
        this.enterprisesx.map((it) => {
          if (it.label == e) {
            this.form.unifiedSocialCreditCode = it.value;
            this.form.enterpriseId = it.enterpriseId;
          }
        });
      }
    },
    async loadPdf(rawFile) {
      // console.log(rawFile, rawFile);
      try {
        this.pdfData = rawFile;
        let url = rawFile.path || rawFile.url || rawFile.value;
        const loadingTask = window.pdfjsLib.getDocument(url);
        this.pdf = await loadingTask.promise;
        this.pageCount = this.pdf.numPages;
        await this.renderPage(this.currentPage);
      } catch (error) {
        console.error('加载 PDF 失败:', error);
      }
    },
    successUpload(e, e2) {
      this.form.uploadFileList[this.form.uploadFileList.length - 1] = {
        name: e2.name,
        url: e.data.url,
        value: e.data.value,
      };
      this.fileName = e2.name;
    },
    handleSelectVisibleChange(visible) {
      // 当下拉框显示/隐藏时，调整其他日期选择器的z-index
      const datePickerElements = document.querySelectorAll('.el-date-editor');
      if (visible) {
        datePickerElements.forEach((el) => {
          el.style.zIndex = '1000';
        });
      } else {
        datePickerElements.forEach((el) => {
          el.style.zIndex = '';
        });
      }
    },
    handleSelectClick(e) {
      e.preventDefault();
      e.stopPropagation();
      // 确保日期选择器不会同时打开
      const datePickerElements = document.querySelectorAll(
        '.el-date-editor input'
      );
      datePickerElements.forEach((el) => {
        el.blur();
      });
    },
    addfj() {
      if (!this.showSubmitPic) {
        this.$message.error('附件最多只能上传10个哦~');
      }
    },
    downloadPdf() {
      let objectUrl = this.pdfData.path || this.pdfData.value;
      const fileName = this.pdfData.name;
      const downloadLink = document.createElement('a');
      downloadLink.href = objectUrl;
      downloadLink.download = fileName;
      downloadLink.click();
    },
    downloadImg(e) {
      let objectUrl = this.srcList[0];
      const fileName = this.fileName;
      const downloadLink = document.createElement('a');
      downloadLink.href = objectUrl;
      downloadLink.download = fileName;
      downloadLink.click();
    },
    clkfile(rawFile) {
      const extension = rawFile.name.split('.').pop().toLowerCase();
      const allowedFormats = ['png', 'jpg', 'pdf'];
      if (allowedFormats.includes(extension)) {
        if (extension.toLowerCase() === 'pdf') {
          this.loadPdf(rawFile);
          this.showPdf = true;
        } else {
          let objectUrl = rawFile.path || rawFile.url || rawFile.value;
          this.fileName = rawFile.name;
          this.showPreview = true;
          this.srcList = [objectUrl];
        }
      } else {
        this.$message.error('只能预览pdf、png、jpg格式的文件');
        return;
      }
    },
    async renderPage(pageNumber) {
      if (pageNumber < 1 || pageNumber > this.pageCount) {
        return;
      }
      this.currentPage = pageNumber;
      try {
        const page = await this.pdf.getPage(pageNumber);
        const viewport = page.getViewport({ scale: 1.5 });
        const canvas = this.$refs.pdfCanvas;
        const context = canvas.getContext('2d');
        canvas.height = viewport.height;
        canvas.width = viewport.width;
        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        };
        await page.render(renderContext).promise;
      } catch (error) {
        console.error(`渲染第 ${pageNumber} 页失败:`, error);
      }
    },
    async previousPage() {
      if (this.currentPage > 1) {
        await this.renderPage(this.currentPage - 1);
      }
    },
    async nextPage() {
      if (this.currentPage < this.pageCount) {
        await this.renderPage(this.currentPage + 1);
      }
    },
    beforeUpload(rawFile) {
      let formatList = this.accept.split(',');
      const lastIndex = rawFile.name.lastIndexOf('.');
      const result = rawFile.name.substring(lastIndex);
      const isExists = formatList.includes(result);
      if (!isExists) {
        this.$message.error('您上传的文件格式不规范!');
        return false;
      } else if (rawFile.size / 1024 / 1024 > 20) {
        this.$message.error('单个文件大小不能超过20mb');
        return false;
      }
      return true;
    },
    replaceNullsWithEmptyStrings(obj) {
      for (let key in obj) {
        if (obj[key] === null) {
          obj[key] = undefined;
        }
      }
      return obj;
    },
    async getOneAPI() {
      this.form.enterpriseId = this.operationData.enterpriseId;
      this.form.id = this.operationData.id;
      this.form.enterpriseName = this.operationData.enterpriseName;
      this.form.unifiedSocialCreditCode = this.operationData.uniCode;
      this.form.signDate = this.operationData.signDate
        ? new Date(this.operationData.signDate).getTime()
        : null;
      this.form.moveInDate = new Date(this.operationData.moveInDate).getTime();
      this.form.endDate = new Date(this.operationData.endDate).getTime();
      this.form.lengthStay = this.operationData.lengthStay;
      this.form.propertyCostsMonth = this.operationData.propertyCostsMonth;
      this.form.payType = this.operationData.payType;
      this.form.areas = this.operationData.areas;
      this.form.deposit = this.operationData.deposit;
      this.form.totalRent = this.operationData.totalRent;
      this.form.uploadFileList = this.operationData.uploadFileDetailList || [];
      this.billList = this.operationData.bills?.map((e) => {
        return {
          ...e,
          billPeriod: [e.billStartDate, e.billEndDate],
        };
      });
    },
    async onSubmits() {
      await this.$refs.form.validate();

      // 校验生成账单的必填项
      if (this.billList.length > 0) {
        const billValidationResult = this.validateBills();
        if (!billValidationResult.isValid) {
          this.$message.error(billValidationResult.message);
          return;
        }
      }

      let uploadFileList = [];
      if (this.form.uploadFileList) {
        if (this.form.uploadFileList.length > 0) {
          // console.log(this.form.uploadFileList);

          uploadFileList = this.form.uploadFileList.map((it) => {
            return {
              name: it.name,
              path: it.oss_path || it.value,
            };
          });
        }
      }
      let data = {
        uniCode: this.form.unifiedSocialCreditCode, //社会信用代码
        enterpriseId: this.form.enterpriseId, //企业id
        enterpriseName: this.form.enterpriseName, //企业名称
        signDate: this.form.signDate ? this.form.signDate : this.form.signDate, //签约日期
        moveInDate: this.form.moveInDate, //起租日期
        endDate: this.form.endDate, //起租日期
        lengthStay: this.form.lengthStay,
        propertyCostsMonth: this.form.propertyCostsMonth,
        deposit: this.form.deposit,
        areas: this.form.areas,
        payType: this.form.payType, //租金付款方式
        totalRent: this.form.totalRent, //总租金
        uploadFileList,
        bills: this.billList.map((e) => {
          return {
            billName: e.billName,
            billAmount: e.billAmount,
            billStartDate: e.billPeriod[0],
            billEndDate: e.billPeriod[1],
            payStatus: e.payStatus || 0,
            id: e.id || '',
          };
        }), // 添加账单数据
      };
      if (this.form.id) {
        data.id = this.form.id;
      }
      const res = await insertOrUpdateAPI(data);
      if (res.status == 0) {
        this.$message.success('操作成功！');
        this.returnFn(true);
        // 关闭新增页面并更新列表
      }
    },

    // 校验账单必填项
    validateBills() {
      for (let i = 0; i < this.billList.length; i++) {
        const bill = this.billList[i];
        const billNumber = i + 1;

        // 校验账单名称
        if (!bill.billName || bill.billName.trim() === '') {
          return {
            isValid: false,
            message: `第${billNumber}条账单的名称不能为空`,
          };
        }

        // 校验时间
        if (
          !bill.billPeriod ||
          (Array.isArray(bill.billPeriod) && bill.billPeriod.length !== 2)
        ) {
          return {
            isValid: false,
            message: `第${billNumber}条账单的时间不能为空`,
          };
        }
        // 校验金额
        if (
          bill.billAmount === null ||
          bill.billAmount === undefined ||
          bill.billAmount < 0
        ) {
          return {
            isValid: false,
            message: `第${billNumber}条账单的金额不能为空`,
          };
        }
      }

      return {
        isValid: true,
        message: '',
      };
    },

    async present() {
      const res = await addFollowRecord({
        pendingLeaseId: this.$route.query.id,
        remark: this.textarea,
      });
      this.dialogVisible = false;
    },
    async getenterpriseList() {
      const res = await listNoMountEnterpriseAPI();
      this.enterprisesx = res.data.map((item) => {
        return {
          value: item.uniCode,
          label: item.name,
          enterpriseId: item.id,
        };
      });
      this.enterprises = this.enterprisesx;
    },
    returnFn(e) {
      this.$emit('canleAddlease2',e);
      // canleAddlease2()
      // this.router.go(0);
      //this.router.push('/workbench/lease')
    },

    // 限制截止时间只能选择起租日期之后的日期
    disabledEndDate(time) {
      if (!this.form.moveInDate) {
        return false;
      }
      const moveInDate = new Date(this.form.moveInDate);
      return time.getTime() <= moveInDate.getTime();
    },

    // 计算总租金的方法
    calculateTotalRent() {
      if (
        this.form.propertyCostsMonth &&
        this.form.areas &&
        this.form.lengthStay
      ) {
        this.form.totalRent =
          this.form.propertyCostsMonth * this.form.areas * this.form.lengthStay;
      } else {
        this.form.totalRent = 0;
      }
    },

    // 计算租赁时长的方法
    calculateLengthStay() {
      if (this.form.moveInDate && this.form.endDate) {
        const startDate = dayjs(parseInt(this.form.moveInDate));
        const endDate = dayjs(parseInt(this.form.endDate));

        if (endDate.isBefore(startDate) || endDate.isSame(startDate)) {
          this.form.lengthStay = 0;
          this.calculateTotalRent();
          return;
        }

        // 使用dayjs计算月份差异
        const months = endDate.diff(startDate, 'month', true); // true表示返回精确的小数
        // 如果日期在同一天则加1
        if (startDate.date() === endDate.date()) {
          this.form.lengthStay = Math.ceil(months) + 1;
          return;
        }
        // 不足月按满月计算，所以向上取整
        this.form.lengthStay = Math.ceil(months);

        this.calculateTotalRent();
      }
    },

    // 计算两个日期之间的月数（不足月按满月计算）
    calculateMonthsBetweenDates(startDate, endDate) {
      if (!startDate || !endDate) {
        return 0;
      }

      const start = dayjs(startDate);
      const end = dayjs(endDate);

      if (end.isBefore(start) || end.isSame(start)) {
        return 0;
      }

      // 使用dayjs计算月份差异，true表示返回精确的小数
      const months = end.diff(start, 'month', true);
      // 不足月按满月计算，所以向上取整
      return Math.ceil(months);
    },

    // 计算月数的简化方法
    calculateMonths(startDate, endDate) {
      if (!startDate || !endDate) {
        return 0;
      }
      const start = dayjs(startDate);
      const end = dayjs(endDate);

      if (end.isBefore(start)) {
        return 0;
      }
      if (start.date() === end.date()) {
        return Math.ceil(end.diff(start, 'month', true)) + 1;
      } else {
        return Math.ceil(end.diff(start, 'month', true));
      }
      // 使用dayjs计算月份差异
      // const months = end.diff(start, 'month', true);
      // return Math.ceil(months);
    },

    // 起租日期变化时触发
    changeStartDate() {
      this.calculateLengthStay();
      // 重新生成账单
      if (this.form.payType) {
        this.generateBills();
      }
      // 将光标定位到租赁时长输入框
      this.focusLengthStayInput();
    },

    // 截止时间变化时触发
    changeEndDate() {
      this.calculateLengthStay();
      // 重新生成账单
      if (this.form.payType) {
        this.generateBills();
      }
      // 将光标定位到租赁时长输入框
      this.focusLengthStayInput();
    },

    // 将光标定位到租赁时长输入框
    focusLengthStayInput() {
      this.$nextTick(() => {
        if (this.$refs.lengthStayInput) {
          this.$refs.lengthStayInput.focus();
        }
      });
    },

    // 当选择租金付款方式时生成账单
    changePayType() {
      this.generateBills();
    },

    // 生成账单
    generateBills() {
      if (!this.form.payType || !this.form.moveInDate || !this.form.endDate) {
        this.billList = [];
        return;
      }
      // 修复日期处理 - 直接使用字符串创建Date对象
      const startDate = new Date(this.form.moveInDate);
      const endDate = new Date(this.form.endDate);
      const payType = this.form.payType;

      this.billList = [];
      let currentDate = new Date(startDate);
      let billIndex = 1;

      // 根据付款方式确定间隔月数
      let intervalMonths = 1; // 默认月付
      switch (payType) {
        case '1': // 月付
          intervalMonths = 1;
          break;
        case '2': // 季付
          intervalMonths = 3;
          break;
        case '5': // 半年付
          intervalMonths = 6;
          break;
        case '3': // 年付
          intervalMonths = 12;
          break;
        case '4': // 一次性付清
          intervalMonths = null;
          break;
      }

      if (payType === '4') {
        // 一次性付清，只生成一个账单
        const totalMonths = this.calculateMonths(startDate, endDate);
        const billAmount = this.calculateBillAmount(totalMonths);

        this.billList.push({
          billName: '第一次账单',
          billPeriod: this.formatDateRange(startDate, endDate),
          billAmount: billAmount,
        });
      } else {
        // 按周期生成账单
        while (currentDate <= endDate) {
          let nextDate = new Date(currentDate);
          nextDate.setMonth(nextDate.getMonth() + intervalMonths);

          // 计算本期结束日期
          let periodEndDate = new Date(nextDate);
          periodEndDate.setDate(periodEndDate.getDate() - 1);

          // 如果本期结束日期超过或等于总结束日期，则以总结束日期为准
          if (periodEndDate >= endDate) {
            periodEndDate = new Date(endDate);
          }

          // 计算当前账单周期的月数
          const billMonths = this.calculateMonths(currentDate, periodEndDate);
          const billAmount = this.calculateBillAmount(billMonths);

          this.billList.push({
            billName: `第${this.numberToChinese(billIndex)}次账单`,
            billPeriod: this.formatDateRange(currentDate, periodEndDate),
            billAmount: billAmount,
          });

          billIndex++;

          // 如果本期结束日期已经达到总结束日期，退出循环
          if (periodEndDate.getTime() >= endDate.getTime()) {
            break;
          }

          // 下一期开始日期是本期结束日期的下一天
          currentDate = new Date(periodEndDate);
          currentDate.setDate(currentDate.getDate() + 1);
        }
      }
    },

    // 计算账单金额
    calculateBillAmount(months) {
      if (!this.form.propertyCostsMonth || !this.form.areas || !months) {
        return 0;
      }

      // 账单金额 = 物业费(元/㎡月) × 租赁面积(㎡) × 月数
      return this.form.propertyCostsMonth * this.form.areas * months;
    },

    // 格式化日期范围
    formatDateRange(startDate, endDate) {
      const formatDate = (date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
      };

      return [formatDate(startDate), formatDate(endDate)];
      // `${formatDate(startDate)}-${formatDate(endDate)}`;
    },

    // 数字转中文
    numberToChinese(num) {
      const chinese = [
        '一',
        '二',
        '三',
        '四',
        '五',
        '六',
        '七',
        '八',
        '九',
        '十',
      ];
      if (num <= 10) {
        return chinese[num - 1];
      } else if (num < 20) {
        return '十' + chinese[num - 11];
      } else if (num % 10 === 0) {
        return chinese[Math.floor(num / 10) - 1] + '十';
      } else {
        return (
          chinese[Math.floor(num / 10) - 1] + '十' + chinese[(num % 10) - 1]
        );
      }
    },

    // 新增账单
    addBill() {
      const newIndex = this.billList.length + 1;
      this.billList.push({
        billName: `第${this.numberToChinese(newIndex)}次账单`,
        billPeriod: '',
        billAmount: 0,
      });
    },

    // 删除账单
    deleteBill(index) {
      if (this.billList.length > 1) {
        this.billList.splice(index, 1);
        // 重新编号账单名称
        this.billList.forEach((bill, idx) => {
          if (
            bill.billName.includes('第') &&
            bill.billName.includes('次账单')
          ) {
            bill.billName = `第${this.numberToChinese(idx + 1)}次账单`;
          }
        });
      }
    },

    // 租赁面积或物业费变化时触发
    onAreasOrPropertyCostChange() {
      this.calculateTotalRent();
      // 更新已生成账单的金额
      // this.updateBillAmounts();
      this.generateBills();
    },

    // 更新账单金额
    updateBillAmounts() {
      if (this.billList.length === 0) {
        return;
      }

      this.billList.forEach((bill) => {
        // 从账单时间段计算月数
        const months = this.calculateMonthsFromPeriod(bill.billPeriod);
        // 重新计算账单金额
        bill.billAmount = this.calculateBillAmount(months);
      });
    },

    // 从账单时间段字符串计算月数
    calculateMonthsFromPeriod(period) {
      if (!period || !period.includes('-')) {
        return 0;
      }

      const [startDateStr, endDateStr] = period.split('-');

      // 解析日期字符串 YYYYMMDD
      const startYear = parseInt(startDateStr.substring(0, 4));
      const startMonth = parseInt(startDateStr.substring(4, 6)) - 1;
      const startDay = parseInt(startDateStr.substring(6, 8));

      const endYear = parseInt(endDateStr.substring(0, 4));
      const endMonth = parseInt(endDateStr.substring(4, 6)) - 1;
      const endDay = parseInt(endDateStr.substring(6, 8));

      const startDate = new Date(startYear, startMonth, startDay);
      const endDate = new Date(endYear, endMonth, endDay);

      return this.calculateMonthsBetweenDates(startDate, endDate);
    },
  },
};
</script>

<style scoped lang="scss">
@import './bill.scss';

.pdfContainer {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .pdfBox {
    width: auto;
    height: auto;
    max-width: 100%;
  }

  .right {
    position: fixed;
    bottom: 50%;
    cursor: pointer;
    right: 10%;
    font-size: 40px;
  }

  .left {
    position: fixed;
    cursor: pointer;
    bottom: 50%;
    left: 10%;
    font-size: 40px;
  }
}

::v-deep {
  .el-textarea__inner {
    height: 200px;
  }
}

.custom-form {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.det {
  padding: 0px 40px;
  width: 100%;
  height: 100%;

  .title {
    display: flex;
    justify-content: space-between;

    .btn {
      cursor: pointer;
      font-size: 25px;
    }
  }

  .boxes {
    width: 80%;
    height: auto;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    padding: 25px;
    background: white;
    border-radius: 10px;
    box-shadow: 4px 4px 10px 0px #eef1f8;
    margin-bottom: 25px;
    // 小屏幕适配
    @media (max-width: 1600px) {
      width: 100%;
      
    }
    .boxss {
      width: 30%;
      margin-left: 2%;
      height: 200px;
      margin-top: 30px;
      padding: 20px;
      border: 1px solid #f2f2f2;
      border-radius: 20px;
    }

    .btn {
      width: 30%;
      margin-left: 2%;
      height: 200px;
      margin-top: 30px;
      padding: 20px;
      border-radius: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .two-column-form {
    .form-row {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;

      .form-item {
        flex: 1;
        margin-bottom: 0 !important;
      }

      .form-item-full {
        width: 100%;
        margin-bottom: 0 !important;
      }
    }

    .form-buttons {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      gap: 10px;
    }

    // 统一设置所有表单组件的宽度为300px
    :deep(.el-input),
    :deep(.el-select),
    :deep(.el-date-editor),
    :deep(.el-input-number),
    :deep(.el-cascader) {
      width: 300px !important;
    }

    // 确保级联选择器的宽度
    :deep(.el-cascader .el-input) {
      width: 300px !important;
    }

    // 数字输入框特殊处理
    :deep(.el-input-number .el-input) {
      width: 300px !important;
    }
  }
}


</style>

<!-- 当为选中结算方式 需要自动生成账单（账单展示的形式为表格）。
1、账单金额手动填充（必填），账单名称（必填）、时间系统（必填）自动生成可修改。账单名称、时间、金额，都可修改
2、生成的物业费账单可以自定义添加或者删除 增加一行或者减少一行，
新增的一条展示在最下方
3、账单名称按照顺序来生成名称 ，例如第一次账单、第二次账单。
4、账单时间生成的规则为：起租时间为2024年3月12日，截止时间为2024年12月01日，选择的付款方式为季付。则自动生成3个账单，分别为20240312-20240611、20240612-20240911、20240912-20241201（最后不足季度日期，也需要生成账单）
月度为1个月，季度为3个月，半年度为6个月，年度为12个月，一次性付清就是一个账单

6、需要在租金付款方式选择后自动生成对应的租金账单表格，支持手动修改和增删账单条目。
7、在点击提交的时候 生成账单 要校验所有必填项
8、生成账单中名称、时间和金额都是必填项
9、生成账单标题前加一个*号，样式为红色，代表必填项 -->
