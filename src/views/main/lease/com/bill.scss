#bill-section {
  margin: 20px 0;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 10px;

  .bill-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    .totalRent {
      display: flex;
      justify-content: center;
      align-items: center;
      div {
        padding: 0 20px;
        font-size: 18px;
        font-weight: 500;
        span {
          font-weight: 600;
          color: #3370ff;
        }
      }
    }
    h3 {
      margin: 0;
      color: #333;
      font-size: 16px;
      font-weight: 600;

      .required-star {
        color: #f56c6c;
        margin-right: 4px;
      }
    }

    .bill-actions {
      display: flex;
      gap: 10px;
    }
  }

  // 账单区域内的输入框统一设置为250px
  :deep(.el-input),
  :deep(.el-input-number) {
    width: 250px !important;
  }

  // 数字输入框特殊处理
  :deep(.el-input-number .el-input) {
    width: 250px !important;
  }
}

.uploadBtn:not(.is-disabled){
  color: #3370ff;
  border:1px solid #3370ff;}
.uploadBtn{
  .svg{
    font-size: 17px; 
    padding-right: 5px;
  }
}
