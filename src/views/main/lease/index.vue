<script setup>
// import HelloWorld from './components/HelloWorld.vue'
// import TheWelcome from './components/TheWelcome.vue'
import AmisComponent from '@/components/amis/index.vue';
import { ref, onMounted } from 'vue';
import { perFix, baseEvn, token } from '@/utils/utils';
import { useRoute, useRouter } from 'vue-router';
import { transformData } from '@/utils/formData';
import addlease2 from './com/addlease2.vue';
const router = useRouter();

let pitchId = ref(1);
let tabId = ref(1);
let operationPage = ref(false);
let amisjson1 = ref({
  type: 'page',
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: '0px 0px 0px 0px transparent',
  },
  body: [
    {
      type: 'tpl',
      inline: true,
      wrapperComponent: '',
      syncLocation: false,
      id: 'u:3345e187f2df',
      style: {
        boxShadow: '0px 0px 0px 0px transparent',
        fontWeight: '500',
        lineHeight: '1.7',
        fontSize: '20px',
      },
    },
    {
      type: 'crud',
      syncLocation: false,
      name: 'tab-s',
      api: {
        method: 'post',
        url: `lease/property/agree/page`,
        requestAdaptor: (rest) => {
          let { gmtCreate, ...other } = rest.data;
          let housesIds = rest.data.housesIds?.slice(1, -1);
          housesIds = housesIds?.split(',');
          if (housesIds[0] == '') {
            housesIds = [];
          }
          let datas = {
            ...other,
            housesIds,
            moveInStartTimeStamp: rest.data.gmtCreate?.split(',')[0] * 1000,
            moveInEndTimeStamp: rest.data.gmtCreate?.split(',')[1] * 1000,
          };
          return {
            ...rest,
            data: datas,
          };
        },
        data: {
          enterpriseName: '${enterpriseName}',
          unifiedSocialCreditCode: '${unifiedSocialCreditCode}',
          status: '${status}',
          housesIds: '{$housesIds}',
          gmtCreate: '${gmtCreate}',
          pageNum: '${page}',
          pageSize: '${perPage}',
        },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: 'table',
      columns: [
        {
          name: 'enterpriseName',
          label: '企业名称',
          width: 260,
          type: 'text',
        },
        {
          name: 'signDate',
          label: '签约日期',
          type: 'text',
        },
        {
          name: 'moveInDate',
          label: '起租日期',
          type: 'text',
        },
        {
          name: 'lengthStay',
          label: '入驻时长(月)',
          type: 'text',
        },
        {
          name: 'endDate',
          label: '结束日期',
          type: 'text',
        },
        {
          name: "${status==0 ? '待生效' : (status==1 ?  '生效中' : '已失效')}",
          label: '租约状态',
          type: 'text',
        },
        {
          type: 'operation',
          label: '操作',
          width: 300,
          fixed: 'right', // 固定在右侧
          buttons: [
            {
              label: '查看',
              type: 'button',
              size: 'md',
              onClick: (e, item) => {
                propertyfeeEdit(item, 'ck');
              },
            },
            {
              label: '编辑',
              type: 'button',
              size: 'md',
              visibleOn: "status !== '2'",
              onClick: (e, item) => {
                propertyfeeEdit(item, 'bj');
              },
            },
            {
              type: 'button',
              size: 'md',
              actionType: 'ajax',
              label: '删除',
              confirmText: '是否确认删除',
              api: {
                method: 'get',
                url: `lease/property/agree/removeById`,
                data: {
                  id: '$id',
                },
              },
            },
          ],
        },
      ],
      alwaysShowPagination: true,
      autoFillHeight: false,
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      filter: {
        title: '',
        submitText: '搜索',
        controls: [
          {
            type: 'text',
            name: 'enterpriseName',
            size: 'md',
            label: '企业名称',
            placeholder: '请输入企业名称',
          },
          {
            type: 'select',
            size: 'md',
            name: 'status',
            label: '租约状态',
            placeholder: '请选择状态',
            value: '',
            options: [
              { label: '全部', value: '' },
              { label: '待生效', value: '0' },
              { label: '已生效', value: '1' },
              { label: '已失效', value: '2' },
            ],
          },
          {
            type: 'input-date-range',
            size: 'md',
            name: 'gmtCreate',
            label: '合同入驻日期',
            shortcuts: [
              {
                label: '1天前',
                startDate: "${DATEMODIFY(NOW(), -1, 'day')}",
                endDate: '${NOW()}',
              },
              {
                label: '1个月前',
                startDate: "${DATEMODIFY(NOW(), -1, 'months')}",
                endDate: '${NOW()}',
              },
              {
                label: '本季度',
                startDate: "${STARTOF(NOW(), 'quarter')}",
                endDate: "${ENDOF(NOW(), 'quarter')}",
              },
            ],
          },
          {
            type: 'reset',
            label: '重置',
          },
          {
            type: 'submit',
            label: '搜索',
            primary: true,
          },
          {
            label: '新增租约',
            type: 'button',
            size: 'md',
            onClick: () => {
              propertyfeeEdit({}, 'tj');
            },
          },
        ],
        onSubmit: 'reload',
      },
    },
  ],
});
let operationType = ref('');
let operationData = ref({});
const tabList = ref([
  {
    name: '租金租约管理',
    id: 1,
  },
  {
    name: '待办租约',
    id: 2,
  },
]);
function changeId(id) {
  if (pitchId.value != id) {
    pitchId.value = id;
  }
}
function handleButtonClick(e) {
  router.push(e);
}
function propertyfeeEdit(e, type) {
  operationData.value = e.data;
  operationType.value = type;
  operationPage.value = true;
}
function canleAddlease2(e) {
  tabId.value = 2;
  operationPage.value = false;
}
// 租约管理
const amisjson3 = {
  type: 'page',
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: '0px 0px 0px 0px transparent',
  },
  body: [
    {
      type: 'tpl',
      inline: true,
      wrapperComponent: '',
      syncLocation: false,
      id: 'u:3345e187f2df',
      style: {
        boxShadow: '0px 0px 0px 0px transparent',
        fontWeight: '500',
        lineHeight: '1.7',
        fontSize: '20px',
      },
    },
    {
      type: 'crud',
      syncLocation: false,
      api: {
        method: 'post',
        url: `lease/page`,

        // adaptor: function (payload, response, api, context) {
        //   debugger
        //     //  if()
        //         return {
        //           data: list,
        //           msg: "请求成功",
        //           status: 0
        //         };
        //       },
        requestAdaptor: (rest) => {
          // console.log(rest, 'rest');
          let { gmtCreate, ...other } = rest.data;
          let housesIds = rest.data.housesIds?.slice(1, -1);
          housesIds = housesIds?.split(',');
          if (housesIds[0] == '') {
            housesIds = [];
          }
          let datas = {
            ...other,
            housesIds,
            //housesIds:rest.data.housesIds?.split(','),
            moveInStartTimeStamp: rest.data.gmtCreate?.split(',')[0] * 1000,
            moveInEndTimeStamp: rest.data.gmtCreate?.split(',')[1] * 1000,
          };
          return {
            ...rest,
            data: datas,
          };
        },
        data: {
          enterpriseName: '${enterpriseName}',
          unifiedSocialCreditCode: '${unifiedSocialCreditCode}',
          status: '${status}',
          housesIds: '{$housesIds}',
          gmtCreate: '${gmtCreate}',
          pageNum: '${page}',
          pageSize: '${perPage}',
        },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: 'table',
      columns: [
        {
          name: 'enterpriseName',
          label: '企业名称',
          width: 260,
          type: 'text',
        },
        {
          name: 'signDate',
          label: '签约日期',
          type: 'text',
        },
        {
          name: 'moveInDate',
          label: '起租日期',
          type: 'text',
        },
        /* {
          "name": "moveInAddress",
          "label": "入驻地址",
          "type": "text"
        }, */
        {
          type: 'tpl',
          name: 'moveInAddress',
          label: '入驻地址',
          tpl: '${moveInAddress|truncate:18}',
          popOver: {
            trigger: 'hover',
            position: 'left-top',
            showIcon: false,
            body: {
              type: 'tpl',
              tpl: '${moveInAddress}',
            },
          },
        },
        {
          name: 'lengthStay',
          label: '入驻时长(月)',
          type: 'text',
        },
        {
          name: 'endDate',
          label: '结束日期',
          type: 'text',
        },
        {
          name: "${status==0 ? '待生效' : (status==1 ?  '生效中' : '已失效')}",
          label: '租约状态',
          type: 'text',
        },
        {
          type: 'operation',
          label: '操作',
          width: 300,
          fixed: 'right', // 固定在右侧
          buttons: [
            {
              label: '查看',
              type: 'button',
              size: 'md',
              onClick: (e, item) => {
                handleButtonClick(
                  `/workbench/lease/addlease?id=${item?.data?.id}`
                );
              },
              // "actionType": "link",
              // "link": "#/workbench/lease/addlease?id=${id}",
            },
            {
              label: '编辑',
              type: 'button',
                    level: 'primary',
              size: 'md',
              visibleOn: "status !== '2'",
              onClick: (e, item) => {
                handleButtonClick(
                  `/workbench/lease/addlease?id=${item?.data?.id}&st=1`
                );
              },
              // "actionType": "link",
              // "link": "#/workbench/lease/addlease?id=${id}&st=1",
            },
            {
              type: 'button',
              size: 'md',
              actionType: 'ajax',
              label: '删除',
              level: 'danger',
              //"visibleOn": "status !== '1'",
              confirmText: '是否确认删除',
              api: {
                method: 'get',

                url: `lease/delete`,
                data: {
                  id: '$id',
                },
              },
            },
            {
              label: '续租',
              type: 'button',
              size: 'md',
              visibleOn: "status === '1'",
              // "actionType": "link",
              // "link": "#/workbench/lease/addlease?id=${id}&st=2",
              onClick: (e, item) => {
                handleButtonClick(
                  `/workbench/lease/addlease?id=${item?.data?.id}&st=2`
                );
              },
            },
            {
              type: 'button',
              size: 'md',
              actionType: 'ajax',
              label: '退租',
              visibleOn: "status === '1'",
              confirmText: '是否确认退租',
              confirmTitle: '退租',
              api: {
                method: 'get',
                url: `lease/surrenderOfTenancy`,
                data: {
                  id: '${id}',
                  /*  "businessCode": "lease",
                  "action": "update",
                  "element": {
                    "status": "2"
                  },
                  "conditions": [
                    {
                      "key": "id",
                      "value": "${id}"
                    }
                  ] */
                },
              },
            },
          ],
        },
      ],
      alwaysShowPagination: true,
      autoFillHeight: false,
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      filter: {
        title: '',
        submitText: '搜索',
        controls: [
          {
            type: 'text',
            name: 'enterpriseName',
            size: 'md',
            label: '企业名称',
            placeholder: '请输入企业名称',
          },
          {
            type: 'text',
            name: 'unifiedSocialCreditCode',
            size: 'md',
            label: '企业社会信用代码',
            placeholder: '请输入企业社会信用代码',
          },
          {
            type: 'select',
            name: 'status',
            size: 'md',
            label: '租约状态',
            placeholder: '请选择状态',
            value: '',
            options: [
              { label: '全部', value: '' },
              { label: '待生效', value: '0' },
              { label: '已生效', value: '1' },
              { label: '已失效', value: '2' },
            ],
          },
          {
            // "type": "nested-select",
            type: 'nested-select',
            //"onlyLeaf": true,
            searchable: true, //可搜索
            multiple: true, //是否多选
            name: 'housesIds',
            //"withChildren": true,//设置 true时，选中父节点时，值里面将包含子节点的值，否则只会保留父节点的值。
            onlyChildren: true, //多选时，选中父节点时，是否只将其子节点加入到值中。
            size: 'md',
            label: '入驻地址',
            maxTagCount: 2, //	标签的最大展示数量，超出数量后以收纳浮层的方式展示，仅在多选模式开启后生效
            // "onlyChildren": true,
            source: {
              method: 'get',
              url: `houses/getBuildingTree`,
              adaptor: function (payload, response, api, context) {
                let list = payload.map((item) => {
                  return {
                    value: item.buildingId,
                    label: item.buildingName,
                    children: transformData(item.houseList),
                  };
                });
                return {
                  data: list,
                  msg: '请求成功',
                  status: 0,
                };
              },
            },
          },
          {
            type: 'input-date-range',
            name: 'gmtCreate',
            size: 'md',
            label: '合同入驻日期',
            shortcuts: [
              {
                label: '1天前',
                startDate: "${DATEMODIFY(NOW(), -1, 'day')}",
                endDate: '${NOW()}',
              },
              {
                label: '1个月前',
                startDate: "${DATEMODIFY(NOW(), -1, 'months')}",
                endDate: '${NOW()}',
              },
              {
                label: '本季度',
                startDate: "${STARTOF(NOW(), 'quarter')}",
                endDate: "${ENDOF(NOW(), 'quarter')}",
              },
            ],
          },
          {
            type: 'reset',
            label: '重置',
          },
          {
            type: 'submit',
            label: '搜索',
            primary: true,
          },
          {
            label: '新增租约',
            type: 'button',
            size: 'md',
            // "actionType": "link",
            // "link": "#/workbench/lease/addlease",
            onClick: () => {
              handleButtonClick('/workbench/lease/addlease');
            },
          },
        ],
        onSubmit: 'reload',
      },
    },
  ],
};
// 待办租约
const amisjson2 = {
  type: 'page',
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: '0px 0px 0px 0px transparent',
  },
  body: [
    {
      type: 'tpl',
      inline: true,
      wrapperComponent: '',
      syncLocation: false,
      id: 'u:3345e187f2df',
      style: {
        boxShadow: '0px 0px 0px 0px transparent',
        fontWeight: '500',
        lineHeight: '1.7',
        fontSize: '20px',
      },
    },
    {
      type: 'crud',
      syncLocation: false,
      name: 'tab-s',
      api: {
        method: 'post',
        url: `pendingLease/pageList`,

        requestAdaptor: (rest) => {
          let { gmtCreate, ...other } = rest.data;
          let datas = {
            ...other,
            startTimeStamp: rest.data.gmtCreate?.split(',')[0] * 1000 || '',
            endTimeStamp: rest.data.gmtCreate?.split(',')[1] * 1000 || '',
          };
          return {
            ...rest,
            data: datas,
          };
        },
        data: {
          enterpriseName: '${enterpriseName}',
          submitName: '${submitName}',
          submitPhone: '${submitPhone}',
          followStatus: '${followStatus}',
          gmtCreate: '${gmtCreate}',
          pageNum: '${page}',
          pageSize: '${perPage}',
        },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: 'table',
      //列表
      columns: [
        {
          name: "${appealType==1 ? '我要续租' : '我要退租'}",
          label: '租约诉求类型',
          type: 'text',
        },
        {
          name: 'contractNumber',
          label: '合同编号',
          type: 'text',
        },
        {
          name: 'enterpriseName',
          label: '企业名称',
          type: 'text',
        },
        {
          name: 'submitName',
          label: '姓名',
          type: 'text',
        },
        {
          name: 'submitPhone',
          label: '联系方式',
          type: 'text',
        },
        {
          name: 'gmtCreate',
          label: '提交时间',
          type: 'date',
          valueFormat: 'x',
        },
        {
          name: "${followStatus==0 ? '未跟进' : '已跟进'}",
          label: '跟进状态',
          type: 'text',
        },
        {
          name: 'followPerson',
          label: '跟进人',
          type: 'text',
        },
        {
          name: 'followDate',
          label: '跟进时间',
          type: 'date',
          valueFormat: 'x',
        },
        {
          type: 'operation',
          label: '操作',
          width: 200,
          fixed: 'right', // 固定在右侧
          buttons: [
            {
              label: '查看跟进记录',
              type: 'button',
              size: 'md',
              // "actionType": "link",
              // "link": "#/workbench/lease/addfollow?id=$id",
              onClick: (e, item) => {
                handleButtonClick(
                  `/workbench/lease/addfollow?id=${item?.data?.id}`
                );
              },
            },
            {
              type: 'button',
              size: 'md',
              label: '跟进',
              actionType: 'dialog',
              dialog: {
                title: '添加跟进记录',
                actions: [
                  {
                    type: 'button',
                    size: 'md',
                    actionType: 'cancel',
                    label: '取消',
                    level: 'default',
                    primary: true,
                  },
                  {
                    label: '确认',
                    actionType: 'confirm',
                    primary: true,
                    //"reload": "tab-s",
                    close: true,
                    type: 'button',
                    size: 'md',
                    api: {
                      method: 'post',
                      url: `pendingLease/addFollowRecord`,
                      messages: {
                        success: '操作成功！',
                      },
                      data: {
                        pendingLeaseId: '$id',
                        remark: '$remark',
                      },
                    },
                  },
                ],
                body: [
                  {
                    type: 'form',
                    body: [
                      {
                        name: 'remark',
                        type: 'textarea',
                        label: '跟进描述',
                        required: true,
                        showCounter: true,
                        maxLength: 300,
                        placeholder: '添加跟进记录,不超过300字',
                      },
                    ],
                  },
                ],
              },
            },
          ],
        },
      ],
      alwaysShowPagination: true,
      autoFillHeight: false,
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      filter: {
        title: '',
        submitText: '搜索',
        controls: [
          {
            type: 'text',
            name: 'enterpriseName',
            size: 'md',
            label: '企业名称',
            placeholder: '请输入企业名称',
          },
          {
            type: 'text',
            name: 'submitName',
            size: 'md',
            label: '姓名',
            placeholder: '请输入姓名',
          },
          {
            type: 'text',
            name: 'submitPhone',
            size: 'md',
            label: '联系方式',
            placeholder: '请输入联系方式',
          },
          {
            type: 'input-date-range',
            name: 'gmtCreate',
            size: 'md',
            label: '提交时间',
            shortcuts: [
              {
                label: '1天前',
                startDate: "${DATEMODIFY(NOW(), -1, 'day')}",
                endDate: '${NOW()}',
              },
              {
                label: '1个月前',
                startDate: "${DATEMODIFY(NOW(), -1, 'months')}",
                endDate: '${NOW()}',
              },
              {
                label: '本季度',
                startDate: "${STARTOF(NOW(), 'quarter')}",
                endDate: "${ENDOF(NOW(), 'quarter')}",
              },
            ],
          },

          {
            type: 'select',
            name: 'followStatus',
            size: 'md',
            label: '跟进状态',
            placeholder: '请选择状态',
            value: '',
            options: [
              { label: '全部', value: '' },
              { label: '未跟进', value: '0' },
              { label: '已跟进', value: '1' },
            ],
          },
          {
            type: 'reset',
            label: '重置',
          },
          {
            type: 'submit',
            label: '搜索',
            primary: true,
          },
        ],
        onSubmit: 'reload',
        actions: [
          /*           {
                      "type": "button",
"size": "md",
                      "label": "一键导出",
                      "actionType": "download",
                      "api": `/enterprise/business/downloadTemplate`
                    } */
        ],
      },
    },
  ],
};
let amisjson = ref(null);
onMounted(() => {
  let tabs = {
    type: 'tabs',
    // "tabsMode": "radio",
    activeKey: tabId,
    tabs: [],
    className: 'tabsClass',
    linksClassName: 'tabsTitle',
  };
  // let data = publicConfig({ columns, api, filter });
  amisjson.value = tabs;
});
</script>

<template>
  <!--  <div class="tab">
    <el-button size="default" :class="pitchId == item.id ? 'on' : ''" @click="changeId(item.id)"
      v-for="(item, index) in  tabList" :key="index">{{ item.name }}</el-button>
  </div>

  <div v-if="pitchId == 1">
    <AmisComponent :amisjson="amisjson" />
  </div>
  <div v-else>
    <AmisComponent :amisjson="amisjson2" />
  </div> -->
  <div v-if="!!amisjson" class="zhengce publicTableStyle">
    <addlease2
      @canleAddlease2="canleAddlease2"
      :operationType="operationType"
      :operationData="operationData"
      v-if="operationPage"
    ></addlease2>
    <AmisComponent v-show="!operationPage" :amisjson="amisjson" />
  </div>
</template>
<style scoped lang="scss">
.tab {
  padding-top: 20px;
  padding-left: 10px;
}

.on {
  background-color: #169bd5;
  color: #fff;
}
</style>
