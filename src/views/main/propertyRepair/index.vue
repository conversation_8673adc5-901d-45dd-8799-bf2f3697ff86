<script setup>
// import HelloWorld from './components/HelloWorld.vue'
// import TheWelcome from './components/TheWelcome.vue'
import AmisComponent from '@/components/amis/index.vue';
import { ref, onMounted, onBeforeMount } from 'vue';
import { perFix, baseEvn, token, skipURL } from '@/utils/utils';
import { useRoute, useRouter } from 'vue-router';
import { ownList } from '@/api/idicc';
const router = useRouter();
let resourceCode = JSON.parse(localStorage.getItem('resourceCode'));
let haveCoe = resourceCode?.includes('propertyAssignment');
let showBtn = ref(false);
function handleButtonClick(e) {
  // console.log(e, "111");
  // "link": "#/workbench/lease/addlease",
  router.push(e);
}
onBeforeMount(() => {
  ownList(skipURL).then((res) => {
    let rolesList = res?.result?.selected?.roles;
    let nameList = rolesList.map((item) => {
      return item.roleName;
    });
    showBtn.value = nameList.includes('物业管理员');
    if (showBtn.value) {
      amisjson.body[1]?.columns[
        amisjson.body[1]?.columns.length - 1
      ]?.buttons.push({
        type: 'button',
        size: 'md',
        label: '指派',
        actionType: 'dialog',
        // 根据haveCoe显影
        visibleOn: "${dealStatus === '0'}",
        dialog: {
          title: '派发维修',
          actions: [
            {
              type: 'button',
              size: 'md',
              actionType: 'cancel',
              label: '取消',
              level: 'default',
              primary: true,
            },
            {
              label: '确认',
              actionType: 'confirm',
              primary: true,
              //"reload": "tab-s",
              close: true,
              type: 'button',
              size: 'md',
              api: {
                method: 'post',
                url: `propertyRepair/assign`,
                data: {
                  id: '$id',
                  repairUsername: '$repairUsername',
                },
              },
            },
          ],
          body: [
            {
              type: 'form',
              body: [
                {
                  type: 'select',
                  name: 'repairUsername',
                  label: '派发人员',
                  required: true,
                  labelClassName: 'text-muted',
                  inline: true,
                  source: {
                    method: 'get',
                    url: `propertyRepair/ListRepairMan`,

                    adaptor: function (payload, response, api, context) {
                      // console.log(payload);
                      let list = payload?.map((item) => {
                        return {
                          value: item.username,
                          label: item.realName,
                        };
                      });
                      // console.log(list, 'list');
                      return {
                        data: list,
                        msg: '请求成功',
                        status: 0,
                      };
                    },
                  },
                },
              ],
            },
          ],
        },
      });
    }
  });
});
let pitchId = ref(1);
const tabList = ref([
  {
    name: '物业报修',
    id: 1,
  } /* ,
    {
        name: '维修人员管理',
        id: 2
    }, */,
]);
function changeId(id) {
  if (pitchId.value != id) {
    pitchId.value = id;
  }
}
// 物业报修
let amisjson = {
  type: 'page',
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: '0px 0px 0px 0px transparent',
  },
  body: [
    {
      type: 'tpl',
      inline: true,
      wrapperComponent: '',
      syncLocation: false,
      id: 'u:3345e187f2df',
      style: {
        boxShadow: '0px 0px 0px 0px transparent',
        fontWeight: '500',
        lineHeight: '1.7',
        fontSize: '20px',
      },
    },
    /*  {
             "type": "divider",
             "id": "u:37cdf1cb7b99",
             "lineStyle": "solid"
         }, */
    {
      type: 'crud',
      syncLocation: false,
      name: 'tab-s',
      api: {
        method: 'post',
        url: `propertyRepair/pageList`,
        data: {
          '&': '$$',
          pageNum: '${page}',
          pageSize: '${perPage}',
        },
        //       adaptor: function (payload, response, api, context) {
        //     return {
        //       data: [{
        //         workOrderNum:'1',
        //         phone:'2222',
        //         dealStatus:'0'
        //       },{
        //         workOrderNum:'1',
        //         phone:'2222',
        //         dealStatus:'1'
        //       },{
        //         workOrderNum:'1',
        //         phone:'2222',
        //         dealStatus:'2'
        //       }],
        //       msg: "请求成功",
        //       status: 0
        //     };
        //   },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: 'table',
      columns: [
        {
          name: 'workOrderNum',
          label: '工单编号',
          type: 'text',
        },
        {
          name: 'submitName',
          label: '姓名',
          type: 'text',
        },
        {
          name: 'phone',
          label: '手机号',
          type: 'text',
        },
        {
          name: 'enterpriseName',
          label: '企业名称',
          type: 'text',
        },
        {
          name: 'address',
          label: '报修地址',
          type: 'text',
        },
        {
          name: "${dealStatus == 0 ? '待分配' : (dealStatus == 1 ? '待维修' : '已完成')}",
          label: '处理状态',
          type: 'text',
        },
        {
          name: 'remark',
          label: '报修描述',
          type: 'text',
        },
        {
          name: 'repairMan',
          label: '维修人员',
          type: 'text',
        },
        {
          name: 'gmtCreate',
          label: '创建时间',
          type: 'date',
          valueFormat: 'x',
        },
        {
          type: 'operation',
          label: '操作',
          fixed: 'right', // 固定在右侧
          width: 150,
          buttons: [
            {
              label: '查看',
              type: 'button',
              size: 'md',
              level: 'info',
              // "actionType": "link",
              // "link": "#/workbench/propertyRepair/detail?id=$id",
              onClick: (e, item) => {
                handleButtonClick(
                  `/workbench/propertyRepair/detail?id=${item?.data?.id}`
                );
              },
            },
          ],
        },
      ],
      alwaysShowPagination: true,
      // "autoFillHeight": false,
      autoFillHeight: {
        height: 450,
      },
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      filter: {
        title: '',
        submitText: '搜索',
        controls: [
          {
            type: 'text',
            name: 'workOrderNum',
            label: '工单编号',
            size: 'md',
            placeholder: '请输入工单编号',
          },
          {
            type: 'text',
            name: 'submitName',
            label: '姓名',
            size: 'md',
            placeholder: '请输入姓名',
          },
          {
            type: 'text',
            name: 'phone',
            label: '手机号',
            size: 'md',
            placeholder: '请输入手机号',
          },
          {
            type: 'text',
            name: 'repairMan',
            label: '维修人员',
            size: 'md',
            placeholder: '请输入维修人员姓名',
          },
          {
            type: 'select',
            name: 'dealStatus',
            label: '处理状态',
            size: 'md',
            placeholder: '请选择状态',
            value: '',
            options: [
              { label: '全部', value: '' },
              { label: '待分配', value: '0' },
              { label: '待维修', value: '1' },
              { label: '已完成', value: '2' },
            ],
          },
          {
            type: 'reset',
            label: '重置',
          },
          {
            type: 'submit',
            label: '搜索',
            primary: true,
          },
        ],
        onSubmit: 'reload',
        actions: [
          /*                     {
                                            "type": "button",
"size": "md",
                                            "label": "一键导出",
                                            "actionType": "download",
                                            "api": `/enterprise/business/downloadTemplate`
                                        } */
        ],
      },
    },
  ],
};
// 维修人员管理
const amisjson2 = {
  type: 'page',
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: '0px 0px 0px 0px transparent',
  },
  body: [
    {
      type: 'tpl',
      inline: true,
      wrapperComponent: '',
      syncLocation: false,
      id: 'u:3345e187f2df',
      style: {
        boxShadow: '0px 0px 0px 0px transparent',
        fontWeight: '500',
        lineHeight: '1.7',
        fontSize: '20px',
      },
    },
    {
      type: 'divider',
      id: 'u:37cdf1cb7b99',
      lineStyle: 'solid',
    },
    {
      type: 'crud',
      syncLocation: false,
      name: 'tab-s',
      api: {
        method: 'post',
        url: `repairMan/pageList`,
        data: {
          workNum: '${workNum}',
          name: '${name}',
          phone: '${phone}',
          pageNum: '${page}',
          pageSize: '${perPage}',
        },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: 'table',
      //列表
      columns: [
        {
          name: 'workNum',
          label: '工号',
          type: 'text',
        },
        {
          name: 'name',
          label: '姓名',
          type: 'text',
        },
        {
          name: 'username',
          label: '账号',
          type: 'text',
        },
        {
          name: 'phone',
          label: '手机号',
          type: 'text',
        },
        {
          name: 'remark',
          label: '备注',
          type: 'text',
        },
        {
          type: 'operation',
          label: '操作',
          fixed: 'right', // 固定在右侧
          width: 200,
          buttons: [
            {
              type: 'button',
              size: 'md',
                    level: 'primary',
              label: '编辑',
              actionType: 'dialog',
              dialog: {
                title: '编辑人员',
                actions: [
                  {
                    type: 'button',
                    size: 'md',
                    actionType: 'cancel',
                    label: '取消',
                    level: 'default',
                    primary: true,
                  },
                  {
                    label: '确认',
                    actionType: 'confirm',
                    primary: true,
                    reload: 'tab-s',
                    close: true,
                    type: 'button',
                    size: 'md',
                    api: {
                      method: 'post',
                      url: `repairMan/update`,
                      data: {
                        remark: '$remark',
                        id: '$id',
                        workNum: '$workNum',
                      },
                    },
                  },
                ],
                //表格
                body: [
                  {
                    type: 'form',
                    rules: {},
                    body: [
                      {
                        type: 'input-text',
                        name: 'workNum',
                        label: '工号',
                        required: true,
                      },
                      {
                        type: 'select',
                        name: 'username',
                        label: '派发人员',
                        required: true,
                        labelClassName: 'text-muted',
                        inline: true,
                        disabledOn: 'true',
                        source: {
                          method: 'get',
                          url: `lease/getUsersByCurrentOrgCode`,

                          adaptor: function (payload, response, api, context) {
                            let list = payload?.map((item) => {
                              return {
                                value: item.username,
                                label: item.name,
                              };
                            });
                            return {
                              data: list,
                              msg: '请求成功',
                              status: 0,
                            };
                          },
                        },
                      },
                      {
                        name: 'remark',
                        type: 'textarea',
                        label: '备注',
                        showCounter: true,
                        maxLength: 100,
                        placeholder: '请输入备注,不超过100字',
                      },
                    ],
                  },
                ],
              },
            },
            {
              type: 'button',
              size: 'md',
              actionType: 'ajax',
              label: '删除',
              level: 'danger',
              confirmText: '是否确认删除',
              api: {
                method: 'get',
                url: `repairMan/delete?id=$id`,
              },
            },
          ],
        },
      ],
      alwaysShowPagination: true,
      autoFillHeight: false,
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      //筛选
      filter: {
        title: '筛选',
        submitText: '搜索',
        controls: [
          {
            type: 'text',
            name: 'workNum',
            size: 'md',
            label: '工号',
            placeholder: '请输入工号',
          },
          {
            type: 'text',
            size: 'md',
            name: 'name',
            label: '姓名',
            placeholder: '请输入姓名',
          },
          {
            type: 'text',
            name: 'phone',
            size: 'md',
            label: '手机号',
            placeholder: '请输入手机号',
          },
        ],
        onSubmit: 'reload',
        actions: [
          {
            type: 'reset',
            label: '重置',
          },
          {
            type: 'submit',
            label: '搜索',
            primary: true,
          },
          {
            type: 'button',
            size: 'md',
            label: '添加人员',
            actionType: 'dialog',
            dialog: {
              title: '添加人员',
              actions: [
                {
                  type: 'button',
                  size: 'md',
                  actionType: 'cancel',
                  label: '取消',
                  level: 'default',
                  primary: true,
                },
                {
                  label: '确认',
                  actionType: 'confirm',
                  primary: true,
                  reload: 'tab-s',
                  close: true,
                  type: 'button',
                  size: 'md',
                  api: {
                    method: 'post',
                    url: `repairMan/add`,
                    data: '$$',
                  },
                },
              ],
              //表格
              body: [
                {
                  type: 'form',
                  rules: {},
                  body: [
                    {
                      type: 'input-text',
                      name: 'workNum',
                      label: '工号',
                      required: true,
                    },
                    {
                      type: 'select',
                      name: 'username',
                      label: '派发人员',
                      required: true,
                      labelClassName: 'text-muted',
                      inline: true,
                      source: {
                        method: 'get',
                        url: `lease/getUsersByCurrentOrgCode`,
                        adaptor: function (payload, response, api, context) {
                          let list = payload?.map((item) => {
                            return {
                              value: item.username,
                              label: item.name,
                            };
                          });
                          return {
                            data: list,
                            msg: '请求成功',
                            status: 0,
                          };
                        },
                      },
                    },
                    {
                      name: 'remark',
                      type: 'textarea',
                      label: '备注',
                      showCounter: true,
                      maxLength: 100,
                      placeholder: '请输入备注,不超过100字',
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  ],
};
</script>

<template>
  <!--   <div class="tab">
        <el-button size="default" :class="pitchId == item.id ? 'on' : ''" @click="changeId(item.id)"
            v-for="(item, index) in  tabList" :key="index">{{ item.name }}</el-button>
    </div>
 -->
  <div class="publicTableStyle" v-if="pitchId == 1">
    <AmisComponent :amisjson="amisjson" />
  </div>
  <div v-else>
    <AmisComponent :amisjson="amisjson2" />
  </div>
</template>
<style scoped lang="scss">
.tab {
  padding-top: 20px;
  padding-left: 10px;
}

.on {
  background-color: #169bd5;
  color: #fff;
}
</style>
