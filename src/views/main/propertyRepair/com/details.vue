<template>
  <div class="det">
    <div v-if="!$props.origin">
      <div class="title">
        <h2>报修情况</h2>
        <span @click="returnFn" class="btn">X</span>
      </div>
    </div>
    <div v-if="!deletes">
      <NoData />
    </div>
    <div v-else>
      <div class="top">
        <div class="gir3">
          <span>工单编号：{{ deletes.workOrderNum }}</span>
          <span>姓名：{{ deletes.submitName }}</span>
          <span>电话：{{ deletes.phone }}</span>
        </div>
        <div class="gir3">
          <span>企业：{{ deletes.enterpriseName }}</span>
          <span>预约上门时间：{{ deletes.timeData }}</span>
          <span>报修时间：{{ deletes.gmtCreate }}</span>
        </div>
        <div class="gir3">
          <span>报修地点：{{ deletes.address }}</span>
          <span style="width: 66%">报修描述：{{ deletes.remark }}</span>
        </div>
        <div class="bgc">
          <span>图片：</span>
          <img
            v-for="(item, index) in deletes.uploadFileList"
            :key="index"
            style="width: 100px; height: 100px; margin: 0 10px"
            :src="item.path"
          >
        </div>
      </div>
      <div class="centre">
        <h2>指派情况</h2>
        <div class="gir3">
          <span>维修人员：{{ deletes.repairMan }}</span>
          <span>电话：{{ deletes.repairPhone }}</span>
          <span>指派时间：{{ deletes.assignDate }}</span>
        </div>
      </div>
      <div class="centre">
        <h2>维修情况</h2>
        <div
          style="margin-bottom: 20px"
          v-for="(item, index) in deletes.repairRecordList"
          :key="index"
        >
          <div class="gir3">
            <span>维修时间：{{ item.gmtCreate }}</span>
            <span style="width: 66%">维修描述：{{ item.remark }}</span>
          </div>
          <div class="bgc">
            <span>图片：</span>
            <img
              v-for="(item, index) in item.uploadFileList"
              :key="index"
              style="width: 100px; height: 100px; margin: 0 10px"
              :src="item.path"
              fit="fill"
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from "vue-router";
import { propertyRepairAPI } from "@/api/system/propertyRepair";
import NoData from "@/components/noData/index.vue";
import { ref, defineProps, onMounted, watch, onBeforeUnmount } from "vue";

const route = useRoute();
const router = useRouter();
let deletes = ref(null);

const props = defineProps(["origin", "id"]);
watch(
  props,
  (newVal) => {
    deletes.value = null;
    getDetails();
  },
  { deep: true }
);
watch(
  route.query.id,
  (newVal) => {
    deletes.value = null;
    getDetails();
  },
  { deep: true }
);
onMounted(() => {
  deletes.value = null;
  getDetails();
});
const getDetails = () => {
  let id = route.query.id;
  if (props.origin === "propertyRepair") {
    id = props.id;
  }
  propertyRepairAPI({
    id,
  }).then((res) => {
    if (res.data) {
      deletes.value = res.data;
      deletes.value.timeData =
        deletes.value.appointmentStartDate +
        "-" +
        deletes.value.appointmentEndDate.slice(10);
    }
  });
};
const returnFn = () => {
  router.push("/workbench/propertyRepair");
};
</script>

<style scoped lang="scss">
.det {
  padding: 0px 40px;
  width: 100%;
  height: 100%;

  .title {
    display: flex;
    justify-content: space-between;

    .btn {
      cursor: pointer;
    }
  }

  .top {
    .gir3 {
      width: 100%;
      display: flex;
      padding-bottom: 20px;

      span {
        width: 32%;
      }
    }

    .bgc {
      display: flex;
      flex-wrap: wrap;
    }
  }

  .centre {
    margin-top: 40px;

    .gir3 {
      width: 100%;
      display: flex;
      padding-bottom: 20px;

      span {
        width: 32%;
      }
    }

    .bgc {
      display: flex;
      flex-wrap: wrap;
    }
  }
}
</style>
