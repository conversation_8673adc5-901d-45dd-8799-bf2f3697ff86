<script setup>
import AmisComponent from '@/components/amis/index.vue';
import ApprovalRecord from '@/components/dialog/ApprovalRecord.vue';
import { ref, onMounted } from 'vue';
import { enterpriseData, transformObject } from '@/utils/enterpriseFormData';

let pitchId = ref(1);
const dialogVisible = ref(false);
const approvalRecordData = ref([]);
const canleDig = () => {
  approvalRecordData.value = [];
  dialogVisible.value = false;
};
const openExamine = async (item) => {
  const data = {
    businessScope: 'enterprise', //类型 企业、租金、租约
    approveId: item.data.id, //消息id
    //"relationId": item.data.id//原id
  };
  // const res = await approvalListAPI(data)
  approvalRecordData.value = data;
  dialogVisible.value = true;
};
// const FromDatas = enterpriseData();
// function WhetherDisable() {
//   let From = FromDatas.map((item) => ({ ...item }));
//   // From[0].disabledOn = 'true';
//   // From[1].disabledOn = 'true';
//   return From;
// }
function extractValuesAndConvert(obj, keysToExtract) {
  // 提取指定键的值
  const extracted = keysToExtract.reduce((acc, key) => {
    if (obj?.hasOwnProperty(key)) {
      acc[key] = obj[key];
    }
    return acc;
  }, {});

  // 构建剩余数据对象
  const remaining = Object.keys(obj).reduce((acc, key) => {
    if (!keysToExtract.includes(key)) {
      acc[key] = obj[key];
    }
    return acc;
  }, {});

  // 转换为格式化的JSON字符串
  const remainingJSON = JSON.stringify(remaining, null, 2);

  return [extracted, remainingJSON];
}

let amisjson = ref(null);
// 企业列表
const amisjson3 = {
  type: 'page',
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: '0px 0px 0px 0px transparent',
  },
  body: [
    {
      type: 'tpl',
      inline: true,
      wrapperComponent: '',
      syncLocation: false,
      id: 'u:3345e187f2df',
      style: {
        boxShadow: '0px 0px 0px 0px transparent',
        fontWeight: '500',
        lineHeight: '1.7',
        fontSize: '20px',
      },
    },
    {
      type: 'crud',
      syncLocation: false,
      name: 'tab-s',
      api: {
        method: 'post',
        url: `approve/enterprise/own/page`,
        requestAdaptor: (rest) => {
          // console.log(rest, "rest");
          let { gmtCreate, ...other } = rest.data;
          let datas = {
            ...other,
            gmtCreateStart: rest.data.gmtCreate?.split(',')[0],
            gmtCreateEnd: rest.data.gmtCreate?.split(',')[1],
            statusList: [],
          };
          if (rest.data.status) {
            datas.statusList[0] = rest.data.status;
          }
          return {
            ...rest,
            data: datas,
          };
        },
        adaptor: function (payload, response, api, context) {
          const newElements = response.data.records.map((element) => {
            const { other, ...rest } = element;
            return { ...rest, ...other };
          });
          response.data.elements = newElements;
          return {
            data: response.data,
            msg: '请求成功',
            status: 0,
          };
        },
        data: {
          enterpriseName: '${enterpriseName}',
          enterpriseUniCode: '${enterpriseUniCode}',
          gmtCreate: '${gmtCreate}',
          operateType: 1,
          status: '${status}',
          pageNum: '${page}',
          pageSize: '${perPage}',
        },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: 'table',
      columns: [
        {
          type: 'operation',
          label: '企业名称',
          width: 200,
          buttons: [
            {
              type: 'button',
              level: 'link',
              size: 'md',
              label: '${enterpriseName}',
              actionType: 'drawer',
              drawer: {
                size: 'lg',
                title: '企业详情',
                className: 'enterpriseDetail ',
                actions: [],
                body: [
                  {
                    type: 'form',
                    columnCount: 2,
                    labelWidth: 100,
                    className: 'form-b-sm',
                    disabledOn: 'true',
                    rules: [],
                    initApi: {
                      method: 'get',
                      url: '/approve/detail?id=${id}',
                      adaptor: function (payload, response, api, context) {
                        const transformedObj = transformObject(
                          payload.afterData.element
                        );
                        let data = transformedObj;
                        return {
                          data,
                          msg: '请求成功',
                          status: 0,
                        };
                      },
                    },
                    body: enterpriseData('priview'),
                  },
                ],
              },
            },
          ],
        },

        {
          name: 'enterpriseUniCode',
          label: '企业信用代码',
          type: 'text',
        },
        {
          name: "${enterpriseType==5 ? '股份制' : (enterpriseType==4 ? '国有' : (enterpriseType==3 ? '民营' : (enterpriseType==2 ? '合资' : (enterpriseType==1 ? '独资' : '其他'))))}",
          label: '企业类型',
          type: 'tpl',
          tpl: '<span class="inline-block px-2 py-1 text-xs font-medium rounded-full ${enterpriseType==1 ? "bg-blue-100 text-blue-800" : enterpriseType==2 ? "bg-green-100 text-green-800" : enterpriseType==3 ? "bg-yellow-100 text-yellow-800" : enterpriseType==4 ? "bg-purple-100 text-purple-800" : enterpriseType==5 ? "bg-red-100 text-red-800" : "bg-gray-100 text-gray-800"}">${enterpriseType==5 ? "股份制" : (enterpriseType==4 ? "国有" : (enterpriseType==3 ? "民营" : (enterpriseType==2 ? "合资" : (enterpriseType==1 ? "独资" : "其他"))))}</span>',
        },
        {
          name: 'establishDate',
          label: '注册成立时间',
          type: 'text',
        },
        {
          name: 'gmtCreate',
          label: '操作时间',
          type: 'text',
        },
        {
          name: "${status==5 ? '待审批' : (status==6 ? '审批通过' : (status==7 ? '审批不通过' : (status==8 ? '执行失败' : '')))}",
          label: '审批状态',
          type: 'tpl',
          tpl: '<span class="inline-block px-2 py-1 text-xs font-medium rounded-full ${status==5 ? "bg-yellow-100 text-yellow-800" : status==6 ? "bg-green-100 text-green-800" : status==7 ? "bg-red-100 text-red-800" : status==8 ? "bg-gray-100 text-gray-800" : "bg-gray-100 text-gray-800"}">${status==5 ? "待审批" : (status==6 ? "审批通过" : (status==7 ? "审批不通过" : (status==8 ? "执行失败" : "")))}</span>',
        },
        {
          type: 'operation',
          label: '操作',
          fixed: 'right', // 固定在右侧
          width: 300,
          buttons: [
            //查看
            {
              type: 'button',
              level: 'primary',
              label: '查看',
              actionType: 'drawer',
              drawer: {
                size: 'lg',
                title: '企业详情',
                className: 'enterpriseDetail',
                actions: [],
                body: [
                  {
                    type: 'form',
                    columnCount: 2,
                    labelWidth: 100,
                    disabledOn: 'true',
                    className: 'form-b-sm',
                    rules: [],
                    initApi: {
                      method: 'get',
                      url: '/approve/detail?id=${id}',
                      adaptor: function (payload, response, api, context) {
                        const transformedObj = transformObject(
                          payload.afterData.element
                        );
                        let data = transformedObj;
                        data.rent_status =
                          transformedObj.rent_status == 1 ? 'true' : 'false';
                        return {
                          data,
                          msg: '请求成功',
                          status: 0,
                        };
                      },
                    },
                    body: enterpriseData('preview'),
                  },
                ],
              },
            },
            //重新提交
            {
              type: 'button',
              level: 'primary',
              label: '重新提交',
              actionType: 'drawer',
              visibleOn: 'status == 7',
              drawer: {
                size: 'lg',
                title: '重新提交',
                className: 'enterpriseDetail',
                actions: [
                  {
                    type: 'button',
                    size: 'md',
                    actionType: 'cancel',
                    label: '取消',
                    level: 'default',
                    primary: true,
                  },
                  {
                    label: '确认',
                    actionType: 'confirm',
                    primary: true,
                    reload: 'tab-s',
                    close: true,
                    type: 'button',
                    size: 'md',
                    api: {
                      method: 'post',
                      url: `enterprise/formSubmit/approve/reSubmit`,
                      messages: {
                        success: '操作成功！',
                      },
                      requestAdaptor: (rest) => {
                        let { ...other } = rest.data;
                        let datas = {
                          ...other,
                        };

                        // 转换入驻状态的布尔值为数字值
                        if (rest.context.hasOwnProperty('rent_status')) {
                          rest.context.rent_status =
                            rest.context.rent_status === 'true' ? 1 : 0;
                        }

                        const selectedKeys = [
                          'special_type',
                          'name',
                          'uni_code',
                          'industry_type',
                          'enterprise_type',
                          'is_high_tech',
                          'is_innovative',
                          'establish_date',
                          'settle_date',
                          'contacts',
                          'contacts_way',
                          'registered_capital',
                          'is_mount',
                          'honors',
                          'desc',
                          'rent_status',
                        ];
                        const [extractedData, jsonData] =
                          extractValuesAndConvert(rest.context, selectedKeys);
                        datas.afterData.element = extractedData;
                        datas.afterData.element.other = jsonData;
                        return {
                          ...rest,
                          data: datas,
                        };
                      },
                      data: {
                        afterData: {
                          businessCode: 'enterprise',
                          action: 'insert',
                          conditions: [],
                          element: {
                            '&': '$$',
                            contacts_way: '$contacts_way',
                          },
                        },
                        id: '${id}',
                      },
                    },
                  },
                ],
                body: [
                  {
                    type: 'form',
                    columnCount: 2,
                    labelWidth: 100,
                    className: 'form-b-sm',
                    rules: {},
                    initApi: {
                      method: 'get',
                      url: '/approve/detail?id=${id}',
                      adaptor: function (payload, response, api, context) {
                        const transformedObj = transformObject(
                          payload.afterData.element
                        );
                        let data = transformedObj;
                        return {
                          data,
                          msg: '请求成功',
                          status: 0,
                        };
                      },
                    },
                    body: enterpriseData('edit'),
                  },
                ],
              },
            },
            //删除
            {
              type: 'button',
              level: 'danger',
              actionType: 'ajax',
              visibleOn: 'status == 6',
              label: '删除',
              confirmText: '是否确认删除',
              api: {
                method: 'get',
                url: 'approve/remove?id=${id}',
              },
            },
            {
              label: '审批记录',
              type: 'button',
              level: 'primary',
              // "actionType": "link",
              // "link": "#/workbench/lease/addfollow?id=$id",
              onClick: (e, item) => {
                openExamine(item);
              },
            },
          ],
        },
      ],
      alwaysShowPagination: true,
      // "autoFillHeight": false,
      autoFillHeight: {
        height: 450,
      },
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      filter: {
        title: '',
        submitText: '搜索',
        controls: [
          {
            type: 'text',
            name: 'enterpriseName',
            label: '企业名称',
            size: 'md',
            placeholder: '请输入企业名称',
          },
          {
            type: 'text',
            name: 'enterpriseUniCode',
            label: '企业信用代码',
            size: 'md',
            placeholder: '请输入企业信用代码',
          },
          {
            type: 'select',
            name: 'status',
            size: 'md',
            label: '审批状态',
            placeholder: '请选择审批状态',
            value: '',
            options: [
              { label: '全部', value: '' },
              { label: '待审批', value: '5' },
              { label: '审批通过', value: '6' },
              { label: '审批不通过', value: '7' },
            ],
          },
          {
            type: 'input-date-range',
            name: 'gmtCreate',
            label: '操作时间',
            valueFormat: 'YYYY-MM-DD',
          },
          {
            type: 'reset',
            label: '重置',
          },
          {
            type: 'submit',
            label: '搜索',
            primary: true,
          },
          {
            type: 'button',
            size: 'md',
            label: '添加企业',
            actionType: 'dialog',
            dialog: {
              size: 'lg',
              title: '添加企业',
              className: 'enterpriseDetail ',
              data: {},
              actions: [
                {
                  type: 'button',
                  size: 'md',
                  actionType: 'cancel',
                  label: '取消',
                  level: 'default',
                  primary: true,
                },
                {
                  label: '确认',
                  actionType: 'confirm',
                  primary: true,
                  reload: 'tab-s',
                  close: true,
                  type: 'button',
                  size: 'md',
                  api: {
                    method: 'post',
                    messages: {
                      success: '操作成功！',
                    },
                    url: `enterprise/formSubmit/approve`,
                    requestAdaptor: (rest) => {
                      let { ...other } = rest.data;
                      let datas = {
                        ...other,
                      };
                      // 转换入驻状态的布尔值为数字值
                      if (rest.context.hasOwnProperty('rent_status')) {
                        rest.context.rent_status =
                          rest.context.rent_status === 'true' ? 1 : 0;
                      }
                      const selectedKeys = [
                        'special_type',
                        'name',
                        'uni_code',
                        'industry_type',
                        'enterprise_type',
                        'is_high_tech',
                        'is_innovative',
                        'establish_date',
                        'settle_date',
                        'contacts',
                        'contacts_way',
                        'registered_capital',
                        'is_mount',
                        'honors',
                        'desc',
                        'rent_status',
                      ];
                      const [extractedData, jsonData] = extractValuesAndConvert(
                        rest.context,
                        selectedKeys
                      );
                      datas.element = extractedData;
                      datas.element.other = jsonData;
                      return {
                        ...rest,
                        data: datas,
                      };
                    },
                    data: {
                      businessCode: 'enterprise',
                      action: 'insert',
                      //"element": "$$",
                      conditions: [],
                    },
                  },
                },
              ],
              onSubmit: 'reload',
              //表格
              body: [
                {
                  type: 'form',
                  columnCount: 2,
                  labelWidth: 100,
                  className: 'form-b-sm add',
                  rules: [],
                  body: enterpriseData('edit'),
                },
              ],
            },
          },
        ],
      },
    },
  ],
};
onMounted(() => {
  let tabs = amisjson3;
  amisjson.value = tabs;
});
</script>
<template>
  <div v-if="!!amisjson" class="zhengce publicTableStyle conmpanyaddList">
    <AmisComponent :amisjson="amisjson" />
    <ApprovalRecord
      v-if="dialogVisible"
      :dialogVisible="dialogVisible"
      @canleDig="canleDig"
      :data="approvalRecordData"
    >
    </ApprovalRecord>
  </div>
</template>
<style scoped lang="scss">
.tab {
  padding-top: 20px;
  padding-left: 10px;
}

.on {
  background-color: #169bd5;
  color: #fff;
}
.conmpanyaddList {
  ::v-deep {
    .antd-Modal-content {
      padding: 24px;
      .antd-Modal-body {
        height: 65vh;
        overflow: scroll;
        padding: 0 24px;
      }
    }
  }
}
</style>
