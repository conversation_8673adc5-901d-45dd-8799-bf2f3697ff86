<template>
    <div class="box">
        <div class="title">
            <span style="position: relative; font-weight: 500;padding-right: 10px;">消息提醒 <div v-if="unread!=0" :class=" Number(unread)>99 ? 'unreads'  : 'unread'">{{ Number(unread)>99 ? '99+' : unread }}</div></span>
        </div>
        <div v-loading="loading" class="listBox">
            <div class="listItem" v-for="(item, index) in messageList" :key="index">
                <div class="titleTetx"> {{ item?.title }}</div>
                <div class="content"> {{ item?.content }}</div>
                <div class="msgTime"> {{ item.msgTime }}</div>
                <div class="btnBox">
                    <div class="pressBtn"
                        v-if="item.msgType == 9 || item.msgType == 10 || item.msgType == 11 || item.msgType == 12"
                        @click="pressSbTodosth(item.params, item.msgType)">
                        催办
                    </div>
                    <div class="delBtn" @click="deleteMessge(item.id)">
                        删除
                    </div>
                </div>
            </div>
            <div class="nodata" v-if="!loading && messageList.length == 0">
                 暂无数据
            </div>
        </div>
        <el-pagination class="pagination" v-model:current-page="pageNum" layout="total, prev, pager, next"
            :total="Number(total)" :page-size="pageSize" size="default" @current-change="handleCurrentChange">
        </el-pagination>
    </div>
</template>
<script lang="ts" setup>
import { ref, defineProps, onMounted } from 'vue'
import {messageCountUnReadAPI, messageMyPageAPI, messageRemoveSingleAPI, leaseRentRemindAPI, leaseRentOverdueAPI, leasePropertyRemindAPI, leasePropertyOverdueAPI } from '@/api/user'
import { ElMessage, ElMessageBox } from 'element-plus'
const messageList = ref([])
const total = ref(0)
const unread = ref(0)
const loading = ref(false)
const pageNum = ref(1)
const pageSize = ref(5)
const handleCurrentChange = (e: any) => {
    pageNum.value = e
    getList()
}
const getList = () => {
    loading.value = true
    messageMyPageAPI({
        pageNum: pageNum.value,
        pageSize: pageSize.value
    }).then((res) => {
        getunreadNum()
        messageList.value = res.data.records
        total.value = res.data.total
    }).finally(() => {
        loading.value = false;
    });
}
const getunreadNum = () => {
    messageCountUnReadAPI({
    }).then((res) => {
        unread.value = res.data.unread
    })
}
const pressSbTodosth = (id: any, type: any) => {
    let datas = JSON.parse(id);
    let data = {
        enterpriseId: datas.enterpriseId
    }
    ElMessageBox.confirm(
        '是否对该企业进行催办？',
        '催办',
        {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(() => {
            if (type == 9) {
                leaseRentRemindAPI(data).then((res) => {
                    if (res.status == 0) {
                        ElMessage({
                            message: '操作成功！',
                            type: 'success',
                            plain: true,
                        })
                    }
                })
            } else if (type == 10) {
                leasePropertyRemindAPI(data).then((res) => {
                    if (res.status == 0) {
                        ElMessage({
                            message: '操作成功！',
                            type: 'success',
                            plain: true,
                        })
                    }
                })
            } else if (type == 11) {
                leaseRentOverdueAPI(data).then((res) => {
                    if (res.status == 0) {
                        ElMessage({
                            message: '操作成功！',
                            type: 'success',
                            plain: true,
                        })
                    }
                })
            } else if (type == 12) {
                leasePropertyOverdueAPI(data).then((res) => {
                    if (res.status == 0) {
                        ElMessage({
                            message: '操作成功！',
                            type: 'success',
                            plain: true,
                        })
                    }
                })
            }
        })
        .catch(() => {
        })
}
const deleteMessge = (id: any) => {
    ElMessageBox.confirm(
        '是否将该消息提醒删除？',
        '提醒',
        {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            type: 'warning',
        }
    )
        .then(() => {
            messageRemoveSingleAPI({
                msgId: id
            }).then((res) => {
                if (res.status == 0) {
                    ElMessage({
                        message: '操作成功！',
                        type: 'success',
                        plain: true,
                    })
                    getList()
                }
            })
        })
        .catch(() => {
        })
}
onMounted(() => {
    getList()
})
</script>
<style lang="scss" scoped>
.nodata{
    width: 100%;
    height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
}
.box {
    background-color: #FAFCFF;
    min-height: calc(100vh - 100px);
    position: relative;
    padding: 0 0 0 20px;

    .pagination {
        display: flex;
        justify-content: flex-end;
        margin-right: 44px;
    }

    .title {
        font-size: 20px;
        font-weight: normal;
        color: #1E3149;
        margin-bottom: 20px;
        position: relative;
        margin-top: 8px;
        padding: 0 16px;
        .unread{
            position: absolute;
            // right: -18px;
              right: -30px;
            top: 0px;
             padding: 2px 6px;
            // width: 16px;
            // height: 16px;
            background-color: red;;
            border-radius: 10px;
            font-size: 12px;
            color: #FFFFFF;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .unreads{
            position: absolute;
            right: -30px;
            top: 0px;
            padding: 2px 6px;
            // width: 20px;
            // height: 20px;
            background-color: red;;
           border-radius: 10px;
            font-size: 12px;
            color: #FFFFFF;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }

    .listBox {
        height: calc(100% - 100px);
        overflow: scroll;
        .listItem {
            width: calc(100% - 44px);
            margin-left: 5px;
            height: 140px;
            border-radius: 12px;
            margin-bottom: 16px;
            box-shadow: 0px 4px 14px 0px rgba(217, 217, 217, 0.25);
            padding: 17px 22px;
            box-sizing: border-box;
            background-color: #fff;
            position: relative;

            .titleTetx {
                font-size: 20px;
                font-weight: 500;
                color: rgba(0, 0, 0, 0.85);
                margin-bottom: 8px;
            }

            .content {
                font-size: 14px;
                font-weight: normal;
                color: #4E5969;
                line-height: 20px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
                overflow: hidden;
                text-overflow: ellipsis;
                min-height: 40px;
            }


            .msgTime {
                color: rgba(0, 0, 0, 0.45);
                font-size: 14px;
                font-weight: 350;
                line-height: 22px;
                margin-top: 8px;
            }

            .btnBox {
                position: absolute;
                height: 100%;
                display: flex;
                align-items: center;
                right: 29px;
                top: 0;

                .delBtn {
                    width: 70px;
                    height: 32px;
                    border-radius: 2px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #FFFFFF;
                    border: 1px solid #1C91FF;
                    font-size: 14px;
                    font-weight: 350;
                    color: #1C91FF;
                    cursor: pointer;
                }

                .pressBtn {
                    width: 70px;
                    height: 32px;
                    border-radius: 2px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: #3370FF;
                    font-size: 14px;
                    font-weight: 350;
                    color: #FFFFFF;
                    cursor: pointer;
                    margin-right: 20px;
                }
            }
        }
    }
}
</style>
