<script setup>
// import HelloWorld from './components/HelloWorld.vue'
// import TheWelcome from './components/TheWelcome.vue'
import AmisComponent from '@/components/amis/index.vue';
import { ref } from 'vue';
import { perFix, baseEvn, token } from '@/utils/utils';
import { downloadIndustryChannel } from '@/api/industry';
// 产业渠道
const amisjson2 = {
  type: 'page',
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: '0px 0px 0px 0px transparent',
  },
  body: [
    {
      type: 'tpl',
      inline: true,
      wrapperComponent: '',
      syncLocation: false,
      id: 'u:3345e187f2df',
      style: {
        boxShadow: '0px 0px 0px 0px transparent',
        fontWeight: '500',
        lineHeight: '1.7',
        fontSize: '20px',
      },
    },
    {
      type: 'crud',
      syncLocation: false,
      name: 'tab-s',
      api: {
        method: 'post',
        url: `industry/channels/page`,

        requestAdaptor: (rest) => {
          // console.log(rest, 'rest');
          let { ...other } = rest.data;
          let datas = {
            ...other,
          };
          return {
            ...rest,
            data: datas,
          };
        },
        data: {
          name: '${name}',
          contacts: '${contacts}',
          contactsWay: '${contactsWay}',
          pageNum: '${page}',
          pageSize: '${perPage}',
        },
      },
      errors: {
        ajax: {
          handler: 'handleAjaxError',
        },
      },
      functions: {
        handleAjaxError:
          "function(ctx) { console.log('接口请求失败:', ctx.error); }",
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: 'table',
      //列表
      columns: [
        {
          name: 'name',
          label: '名称',
          type: 'text',
          className: 'text-primary',
          onEvent: {
            click: {
              actions: [
                {
                  actionType: 'dialog',
                  dialog: {
                    type: 'dialog',
                    title: '查看详情',
                    actions: [
                      {
                        type: 'button',
                        size: 'md',
                        actionType: 'cancel',
                        label: '确认',
                        primary: true,
                      },
                    ],
                    body: [
                      {
                        type: 'form',
                        initApi: {
                          url: `investment/management/detail`,
                          method: 'get',
                          data: {
                            id: '${id}',
                          },
                        },
                        rules: {},
                        body: [
                          {
                            type: 'input-text',
                            name: 'name',
                            label: '名称',
                            required: true,
                            disabledOn: 'true',
                          },
                          {
                            type: 'input-text',
                            name: 'contacts',
                            label: '联系人',
                            required: true,
                            disabledOn: 'true',
                          },
                          {
                            type: 'input-text',
                            name: 'contactsWay',
                            label: '联系方式',
                            required: true,
                            disabledOn: 'true',
                          },

                          {
                            type: 'textarea',
                            name: 'note',
                            label: '简介',
                            disabledOn: 'true',
                          },
                        ],
                      },
                    ],
                  },
                },
              ],
            },
          },
        },
        {
          name: 'contacts',
          label: '联系人',
          type: 'text',
        },

        {
          name: 'contactsWay',
          label: '联系方式',
          type: 'text',
        },
        {
          name: 'note',
          label: '简介',
          type: 'text',
          width: 600,
          className: 'text-wrap', // 添加文本换行样式
          // 或使用自定义样式
          // classNameExpr: "\"text-wrap word-break\"",
        },
        // {
        //   name: 'note',
        //   label: '简介',
        //   type: 'text',
        // },

        {
          type: 'operation',
          label: '操作',
          width: 200,
          buttons: [
            {
              type: 'button',
              level: 'primary',
              label: '查看',
              actionType: 'dialog',
              dialog: {
                // size: 'lg',
                title: '查看详情',
                actions: [
                  {
                    type: 'button',
                    size: 'md',
                    actionType: 'cancel',
                    label: '确认',
                    primary: true,
                  },
                ],
                body: [
                  {
                    type: 'form',
                    // columnCount: 4,
                    initApi: {
                      url: `investment/management/detail`,

                      method: 'get',
                      data: {
                        id: '${id}',
                      },
                    },
                    rules: {},
                    body: [
                      {
                        type: 'input-text',
                        name: 'name',
                        label: '名称',
                        required: true,
                        disabledOn: 'true',
                      },
                      {
                        type: 'input-text',
                        name: 'contacts',
                        label: '联系人',
                        required: true,
                        disabledOn: 'true',
                      },
                      {
                        type: 'input-text',
                        name: 'contactsWay',
                        label: '联系方式',
                        required: true,
                        disabledOn: 'true',
                      },

                      {
                        type: 'textarea',
                        name: 'note',
                        label: '简介',
                        disabledOn: 'true',
                      },
                    ],
                  },
                ],
              },
            },
            {
              type: 'button',
              label: '编辑',
              level: 'primary',
              actionType: 'dialog',
              dialog: {
                title: '编辑',
                actions: [
                  {
                    type: 'button',
                    size: 'md',
                    actionType: 'cancel',
                    label: '取消',
                    level: 'default',
                    primary: true,
                  },
                  {
                    label: '确认',
                    actionType: 'confirm',
                    primary: true,
                    //"reload": "tab-s",
                    close: true,
                    type: 'button',
                    size: 'md',
                    api: {
                      method: 'post',
                      url: `industry/channels/insertOrUpdate`,
                      messages: {
                        success: '操作成功！',
                      },

                      requestAdaptor: (rest) => {
                        // console.log(rest, 'rest');
                        let { ...other } = rest.data;
                        // console.log(rest.data, 'rest.data');
                        let datas = {
                          ...other,
                        };
                        return {
                          ...rest,
                          data: datas,
                        };
                      },
                      data: {
                        name: '${name}',
                        contacts: '${contacts}',
                        billAmount: '${billAmountStr}',
                        contactsWay: '${contactsWay}',
                        note: '${note}',
                        id: '${id}',
                      },
                    },
                  },
                ],
                // form
                body: [
                  {
                    type: 'form',
                    rules: {},
                    body: [
                      {
                        type: 'input-text',
                        name: 'name',
                        required: true,
                        label: '名称',
                      },
                      {
                        type: 'input-text',
                        name: 'contacts',
                        required: true,
                        label: '联系人',
                      },
                      {
                        type: 'input-text',
                        name: 'contactsWay',
                        required: true,
                        label: '联系方式',
                      },
                      {
                        type: 'textarea',
                        name: 'note',
                        label: '简介',
                      },
                    ],
                  },
                ],
              },
            },
            {
              type: 'button',
              actionType: 'ajax',
              label: '删除',
              level: 'danger',
              confirmText: '是否确认删除',
              api: {
                method: 'get',
                url: `industry/channels/removeById`,

                data: {
                  id: '${id}',
                },
              },
            },
          ],
        },
      ],
      alwaysShowPagination: true,
      // "autoFillHeight": false,
      autoFillHeight: {
        height: 450,
      },
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      //筛选
      filter: {
        title: '',
        submitText: '搜索',
        controls: [
          {
            type: 'text',
            name: 'name',
            size: 'md',
            label: '名称',
            placeholder: '请输入名称',
          },

          {
            type: 'text',
            name: 'contacts',
            size: 'md',
            label: '联系人',
          },
          {
            type: 'text',
            size: 'md',
            name: 'contactsWay',
            label: '联系方式',
          },
          {
            type: 'reset',
            label: '重置',
          },
          {
            type: 'submit',
            label: '搜索',
            primary: true,
          },
          /*  {
             "type": "button",
"size": "md",
             "label": "一键发送所有账单",
             "actionType": "download",
             "api": `/enterprise/business/downloadTemplate`
           }, */
          {
            type: 'button',
            size: 'md',
            label: '新增',
            level:'primary',
            actionType: 'dialog',
            dialog: {
              title: '新增',
              data: {},
              actions: [
                {
                  type: 'button',
                  size: 'md',
                  actionType: 'cancel',
                  label: '取消',
                  level: 'default',
                  primary: true,
                },
                {
                  label: '确认',
                  actionType: 'confirm',
                  primary: true,
                  reload: 'tab-s',
                  close: true,
                  type: 'button',
                  size: 'md',
                  api: {
                    method: 'post',
                    url: `industry/channels/insertOrUpdate`,

                    data: {
                      name: '${name}',
                      contacts: '${contacts}',
                      contactsWay: '${contactsWay}',
                      note: '${note}',
                    },
                  },
                },
              ],
              // form
              body: [
                {
                  type: 'form',
                  rules: {},
                  body: [
                    {
                      type: 'input-text',
                      name: 'name',
                      required: true,
                      label: '名称',
                    },
                    {
                      type: 'input-text',
                      name: 'contacts',
                      required: true,
                      label: '联系人',
                    },
                    {
                      type: 'input-text',
                      name: 'contactsWay',
                      required: false,
                      label: '联系方式',
                      // validations: {
                      //   matchRegexp: '/^1[3-9]\\d{9}$/',
                      // },
                      // validationErrors: {
                      //   matchRegexp: '请输入正确的手机号码格式',
                      // },
                    },
                    {
                      type: 'textarea',
                      name: 'note',
                      maxLength: 200,
                      label: '简介',
                    },
                  ],
                },
              ],
            },
          },
          {
            type: 'button',
            size: 'md',
            label: '一键导出',
            onClick: function () {
              downloadIndustryChannel().then((res) => {
                let blob = new Blob([res], {
                  type: 'text/csv,charset=UTF-8',
                });
                let objectUrl = URL.createObjectURL(blob);
                const fileName = '产业渠道数据导出.xlsx';
                const downloadLink = document.createElement('a');
                downloadLink.href = objectUrl;
                downloadLink.download = fileName;
                downloadLink.click();
              });
            },
          },
        ],
        onSubmit: 'reload',
        // "actions": [

        // ]
      },
    },
  ],
};
</script>

<template>
  <div class="publicTableStyle">
    <AmisComponent :amisjson="amisjson2" />
  </div>
</template>
<style scoped lang="scss">
.tab {
  padding-top: 20px;
  padding-left: 10px;
}

.on {
  background-color: #169bd5;
  color: #fff;
}
</style>
<style lang="scss">
.width200 {
  input {
    width: 200px !important;
  }
}
.text-wrap {
  max-width: 500px !important;
  word-break: break-all;
}
</style>
