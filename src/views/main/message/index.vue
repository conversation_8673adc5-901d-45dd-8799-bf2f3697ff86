<template>
  <div>
    <!-- 消息通知 -->
    <div v-if="!!amisjson" class="zhengce publicTableStyle">
      <AmisComponent :amisjson="amisjson" />
    </div>
  </div>
</template>
<script setup>
import { onBeforeMount, onMounted, ref } from 'vue';
import AmisComponent from '@/components/amis/index.vue';
import { perFix, publicConfig, baseEvn, token } from '@/utils/utils';
import { useStore } from 'vuex';
import { useRoute, useRouter } from 'vue-router';
import { messageTab } from './setMessage';
const route = useRoute();
const router = useRouter();

let formData = (title, state) => {
  return {
    // "size": "lg",
    title: title,
    actions: [
      {
        type: 'button',
        size: 'md',
        actionType: 'cancel',
        label: '取消',
        level: 'default',
        primary: true,
      },
      {
        label: '确认',
        actionType: 'confirm',
        primary: true,
        reload: 'tab-s',
        close: true,
        type: 'button',
        size: 'md',
        api: {
          method: 'post',
          url: `message/addMessage`,
          messages: {
            success: '操作成功！',
          },
          headers: {
            token: `${token()}`,
          },
          requestAdaptor: (rest) => {
            let { deptList, ...other } = rest.data;
            let datas = {
              ...other,
              deptList:
                deptList.lastIndexOf(',') === -1
                  ? [deptList]
                  : deptList.split(','),
            };
            return {
              ...rest,
              data: datas,
            };
          },
        },
      },
    ],

    body: [
      {
        type: 'form',
        columnCount: 1,
        rules: {},

        body: [
          {
            type: 'input-text',
            label: '标题',
            name: 'title',
            id: 'u:2e21df42228e',
            required: true,
            placeholder: '请输入标题',

            size: 'lg',
          },
          {
            type: 'textarea',
            label: '消息内容',
            name: 'content',
            id: 'u:5e97b18cfe9b',
            required: true,
            placeholder: '请输入消息内容',
            size: 'lg',
          },

          {
            label: '推送部门',
            name: 'deptList',
            // "type": "nested-select",
            type: 'tree-select',
            onlyLeaf: true,
            multiple: true, //多选
            required: true, //必填
            maxTagCount: 1, //标签最大展示数量
            withChildren: true, //选中父节点带上子节点的值
            //"onlyChildren": true,//多选只要子节点
            searchable: true, //可搜索
            size: 'lg',
            disabledOn: `${state !== 'insert'}`,
            source: {
              method: 'get',
              url: `systems/getDeptTreeByOrgCode`,
              headers: {
                token: `${token()}`,
              },
              // data: {
              //   businessCode: "enterprise",
              // },
              //     "labelField": "name",
              //   "valueField": "uni_code",
              /* "responseData": {
                  "options": "${items|pick:label~houseName,value~houseId}"
                } */
              adaptor: function (payload, response, api, context) {
                let arr = [];
                arr[0] = payload;
                function buildHierarchy(arr) {
                  return arr.map((item) => {
                    if (item.childDeptList?.length > 0) {
                      return {
                        value: item.id,
                        label: item.userDeptName,
                        icon: '2',
                        children: buildHierarchy(item.childDeptList),
                      };
                    } else {
                      return {
                        value: item.id,
                        label: item.userDeptName,
                        icon: '2',
                      };
                    }
                  });
                }
                let list = arr.map((item) => {
                  return {
                    value: item.id,
                    label: item.userDeptName,
                    children: buildHierarchy(item.childDeptList),
                  };
                }); //层级最高无限制
                // console.log(list);
                return {
                  data: list,
                  msg: '请求成功',
                  status: 0,
                };
              },
            },
          },
        ],
      },
    ],
  };
};
let tabs1 = (title, state) => {
  return [
    {
      id: 'u:cdb841d81c01',
      type: 'form',
      className: 'formContent',
      body: [
        {
          value:
            "${['平台消息','政策申报' ,'物业报修','经营诉求','经营数据上报','租约提醒','账单提醒','账单逾期','能耗提醒','租金提醒','物业费提醒','租金逾期','物业费逾期'][msgType] }",
          label: '消息类型',
          name: 'text',
          type: 'input-text',
          readOnly: true,
          // required:false
        },
        {
          type: 'textarea',
          label: '消息内容',
          name: 'content',
          id: 'u:5e97b18cfe9b',
          readOnly: true,
        },
      ],
      title: '',
      actions: [],
      // "mode": "horizontal",
      // "dsType": "api",
      // "feat": "Insert",
      // ...tabs1((title, state)),
      // "actions": tabs1((title, state)),
      // "resetAfterSubmit": true
    },
  ];
};
let read = (list) => {
  return {
    type: 'page',
    title: '',
    id: 'u:07e183dd88f8',
    body: [
      {
        type: 'crud',
        syncLocation: false,
        source: list,
        columns: [
          {
            name: 'realName',
            label: '',
            type: 'text',
            id: 'u:f829b0b7eea2',
          },
          {
            name: 'dept',
            label: '',
            type: 'text',
            id: 'u:05d57f0eb66f',
          },
        ],
        bulkActions: [],
        itemActions: [],
        id: 'u:0cf7d03af6cb',
      },
    ],
    actions: [],
  };
};
let tabs2 = () => {
  return {
    type: 'page',
    title: '',
    id: 'u:07e183dd88f8',
    className: 'formContent',
    initApi: {
      url: `message/getMessageReceiveRetail`,
      headers: {
        token: `${token()}`,
      },
      method: 'get',
      data: {
        msgId: "${id|default:''}",
      },
      responseData: {
        '&': '$$',
      },
    },
    body: [
      {
        type: 'tabs',
        tabsMode: 'radio',
        className: 'tabsFormContent',
        tabs: [
          {
            // ''
            title: '${readCount}人已读',
            body: read('$readList'),
            id: 'u:319a552aa436',
          },
          {
            title: '${unreadCount}人未读',
            body: read('$unreadList'),
            id: 'u:4a07b35df9ef',
          },
        ],
        id: 'u:238ba61a4079',
        // className: "tabsClass",
        // linksClassName: "tabsTitle",
      },
    ],
    actions: [],
  };
};
let drawerData = (title, state) => {
  return {
    type: 'page',
    title: '${title}详情',

    id: 'u:07e183dd88f8',
    body: [
      {
        type: 'tabs',
        data: '${items}',
        className: 'messageDrawer',
        tabs: [
          {
            title: '消息详情',
            body: tabs1(),
            id: 'u:319a552aa436',
          },
          {
            title: '推送结果详情',
            body: tabs2(),
            id: 'u:4a07b35df9ef',
            className:'detailTab'
          },
        ],
        id: 'u:238ba61a4079',
        onEvent: {
          //     "change": {
          //       "weight": 0,
          //       "actions": [
          //         {
          //           "ignoreError": false,
          //           "outputVar": "responseResult",
          //           "actionType": "ajax",
          //           "options": {},
          //           "api": {
          //             url: `message/getMessageReceiveRetail`,
          //           headers: {
          //             token: `${token()}`,
          //           },
          //           method: "get",
          //           data: {
          //             msgId:"${id|default:''}",
          //           },
          //           adaptor:(payload)=>{
          // console.log(payload)
          // return payload
          //           },
          //           responseData: {
          //             "&": "$$",
          //             items:{
          //               readCount: 'readCount',
          // readList:'readList',
          // unreadCount:'unreadCount',
          // unreadList:'unreadList',
          //             },
          //             readCount: 'readCount',
          // readList:'readList',
          // unreadCount:'unreadCount',
          // unreadList:'unreadList',
          //           },
          //           }
          //         }
          //       ]
          //     }
        },
        // className: "tabsClass",
        // linksClassName: "tabsTitle",
      },
    ],
    actions: [],
  };
};
const columns = [
  {
    name: 'title',
    label: '消息标题',
    type: 'text',
    fixed: 'left',
  },
  {
    name: 'content',
    label: '推送内容',
    width: 300,
    type: 'text',
  },
  {
    //   [
    //   // PLATFORM( 0, "平台消息"),     POLICY_DECLARATION( 1, "政策申报"),     PROPERTY_REPAIR( 2, "物业报修"),     BUSINESS_DEMAND( 3, "经营诉求"),     ENTERPRISE_BUSINESS( 4, "经营数据上报"),     LEASE_REMIND( 5, "租约提醒"),     BILL_REMIND( 6, "账单提醒"),     BILL_OVERDUE( 7, "账单逾期"),     ENERGY_REMIND( 8, "能耗提醒"),

    // ]
    // value:"${title}",
    name: "${['平台消息','政策申报' ,'物业报修','经营诉求','经营数据上报','租约提醒','账单提醒','账单逾期','能耗提醒','租金提醒','物业费提醒','租金逾期','物业费逾期'][msgType] }",
    width: 200,
    label: '消息类型',
    type: 'tpl',
    tpl: '<span class="inline-block px-2 py-1 text-xs font-medium rounded-full ${msgType==0 ||msgType==1 ? "bg-blue-100 text-blue-800" : msgType==2 ? "bg-green-100 text-green-800" : msgType==3 ? "bg-purple-100 text-purple-800" : msgType==4 ? "bg-yellow-100 text-yellow-800" :msgType==5 ||msgType==6||msgType==8||msgType==9||msgType==10 ? "bg-pink-100 text-pink-800": msgType==11||msgType==7||msgType==12 ? "bg-red-200 text-red-900" : "bg-gray-100 text-gray-800"}">${["平台消息","" ,"物业报修","经营诉求","经营数据上报","租约提醒","账单提醒","账单逾期","能耗提醒","","","租金逾期","物业费逾期"][msgType] || "-"}</span>',
  },
  {
    name: 'pushWay',
    name: "${pushWay == 0 ? '自动' : '定时'}",
    label: '推送方式',
    type: 'tpl',
    tpl: '<span class="inline-block px-2 py-1 text-xs font-medium rounded-full ${pushWay == 0 ? "bg-green-100 text-green-800" : "bg-blue-100 text-blue-800"}">${pushWay == 0 ? "自动" : "定时"}</span>',
  },
  {
    name: 'msgTime',
    label: '推送时间',
    type: 'text',
    // searchable: {
    //   type: "input-date-range",
    //   name: "gmtCreate",
    //   label: "推送时间",
    //   placeholder: "输入推送时间",
    // },
  },

  {
    type: 'operation',
    label: '操作',
    width: 200,
    fixed: 'right',
    buttons: [
      {
        // icon: "fa fa-eye",
        label: '预览',
        type: 'button',
        level: 'primary',
        actionType: 'drawer',
        drawer: drawerData('预览', 'update'),
      },
    ],
  },
];
const api = {
  method: 'post',
  url: `message/page`,
  headers: {
    token: `${token()}`,
  },
  data: {
    msgTimeStart: '${msgTimeStart|default:undefined}',
    msgTimeEnd: '${msgTimeEnd|default:undefined}',
    msgType: '${msgType|default:undefined}',
    pageNum: '${page|default:1}',
    pageSize: '${perPage|default:10}',
  },
  requestAdaptor: (rest) => {
    let { msgTimeStart, msgTimeEnd, ...other } = rest.data;
    let datas = {
      ...other,
      msgTimeStart: msgTimeStart ? `${msgTimeStart * 1000}` : undefined,
      msgTimeEnd: msgTimeEnd ? `${msgTimeEnd * 1000}` : undefined,
    };
    return {
      ...rest,
      data: datas,
    };
  },
  responseData: {
    '&': '$$',
    items: 'records',
  },
};
const filter = {
  title: '',
  submitText: '',
  controls: [
    {
      type: 'input-date-range',
      name: 'msgTimeStart',
      extraName: 'msgTimeEnd',

      label: '推送时间',
    },

    {
      type: 'select',
      name: 'msgType',
      label: '消息类型',
      size: 'md',
      placeholder: '选择状态',
      options: [
        {
          type: 'PLATFORM',
          label: '平台消息',
          value: '0',
        },
        {
          type: 'PROPERTY_REPAIR',
          label: '物业报修',
          value: '2',
        },
        {
          type: 'BUSINESS_DEMAND',
          label: '经营诉求',
          value: '3',
        },
        {
          type: 'ENTERPRISE_BUSINESS',
          label: '经营数据上报',
          value: '4',
        },
        {
          type: 'LEASE_REMIND',
          label: '租约提醒',
          value: '5',
        },
        {
          type: 'BILL_REMIND',
          label: '账单提醒',
          value: '6',
        },
        {
          type: 'BILL_OVERDUE',
          label: '账单逾期',
          value: '7',
        },
        {
          type: 'ENERGY_REMIND',
          label: '能耗提醒',
          value: '8',
        },
        {
          type: 'RENT_OVERDUE',
          label: '租金逾期',
          value: '11',
        },
        {
          type: 'PROPERTY_FEE_OVERDUE',
          label: '物业费逾期',
          value: '12',
        },
      ],
      // options: [
      //   {
      //     label: "平台通知",
      //     value: "0",
      //   },
      //   {
      //     label: "政策申报消息",
      //     value: "1",
      //   },
      //   {
      //     label: "账单消息",
      //     value: "2",
      //   },
      // ],
    },
    {
      type: 'reset',
      name: 'gmtCreate',
      label: '重置',
    },
    {
      type: 'submit',
      label: '搜索',
      primary: true,
    },
    {
      type: 'button',
      size: 'md',
      label: '新增',
      primary: true,
      actionType: 'dialog',
      dialog: formData('新增推送消息', 'insert'),
    },
    // {
    //   "type": "button",
    // "size": "md",
    //   "label": "批量导出",
    //   "primary": true
    // },
  ],
  onSubmit: 'reload',
};
let amisjson = ref(null);
onMounted(() => { 
  let tabs = {
    type: 'tabs',
    // "tabsMode": "radio",
    tabs: [
      {
        title: '消息推送列表',
        body: publicConfig({ columns, api, filter }),
        id: 'u:319a552aa436',
      },
      {
        title: '系统消息推送设置',
        body: messageTab,
        // publicConfig({title:'能耗监测', columns, api, filter }),
        //
        id: 'u:4a07b35df9ef',
        className: 'message2',
      },
    ],
    id: 'u:238ba61a4079',
    className: 'tabsClass',
    linksClassName: 'tabsTitle',
  };
  // let data = publicConfig({ columns, api, filter });
  amisjson.value = tabs;
});
</script>
<style lang="scss">
.amis-scope .headerTitle {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  text-align: right;
}

.amis-scope .leftTitle {
  line-height: 33px;
  width: 100px;
}
.zhengce {
  .test {
    display: inline-block;
    font-size: 16px;
    text-align: center;
    width: 100%;
    font-weight: 500;
  }
  .title {
    font-size: 18px;
    width: 100%;
    font-weight: 500;
  }
  // .p-border {
  //   border: 1px solid #eee;
  //   margin: 10px 50px;
  // }
  .input-number {
    margin: 0 15px;
  }
}
.tabsTitleTwst {
  background: #fff;
  .antd-Tabs-link {
    border-radius: 4px;
    padding: 15px  16px !important;
    margin-right: 20px !important;
  }
}
.formLists {
  width: 450px;
  margin: 20px 100px;
}
.saveBtn {
  width: 80px;
  margin-left: calc(50% - 40px) !important;
}
#app1 .publicTableStyle .messageDrawer {
  .antd-Tabs-linksContainer-wrapper {
    background: #fff !important;
  }
  .formContent {
    margin-top: 24px;
    .tabsFormContent{

 
    .antd-Page-content{
         .antd-Page-main{
          .antd-Page-body{
 padding-top: 16px;

          }

         background: #fff !important;
      }
     
    }
       }
  }
  .tabsFormContent{
    .antd-Tabs-linksContainer-wrapper{
   
 margin-bottom: 16px;
    }
     
  }
}

.message2{
  padding: 0 30px !important;
  background: #fafcff ;
 
  .messageTabs{
    padding: 24px 32px 32px 32px;
background: #fff;
border-radius: 10px;
 box-shadow: 0px 4px 12px 0px #EEF1F8 ;
 .antd-Tabs-content{
margin-top:16px;
border-radius: 10px;


 }
 .antd-Panel.antd-Panel--default.antd-Panel--form{
margin-bottom: 0;
border-radius: 10px;

 }
    .tabsTitle {
background: #fff !important;
margin-bottom: 0 !important;
    }
  }
}
</style>
