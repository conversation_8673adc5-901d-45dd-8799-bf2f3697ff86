import { perFix, publicConfig, baseEvn, token } from "@/utils/utils";
import { size } from 'lodash';
let tabs1 = (num, state) => {
  let options = {
    // 经营数据上报
    operating: [{ label: "每个季度第一周（一周每天提醒）", value: "0" },
    { label: "每个季度第一天", value: "1" },],
    // 账单提醒
    billReminder: [
      { label: "账单日期前7天（每天0点）", value: "0" },
      { label: "账单日期前3天（每天0点）", value: "1" },
    ],
    // /账单逾期 
    billOverdue: [
      { label: "逾期后每日提醒（每天0点）", value: "0" },
      { label: "逾期后每周提醒（每周一0点）", value: "1" },
    ],
    // /账单逾期 
    lease: [
      { label: "租约到期前三个月（每月1号0点）", value: "0" },
      { label: "租约到期前两个月（每月1号0点）", value: "1" },
      { label: "租约到期前一个月（每月1号0点）", value: "2" },
    ],
    //租金和物业费提醒
    RENT_REMIND: [
      { label: "账单日期前7天（每天0点）", value: "0" },
      { label: "账单日期前3天（每天0点）", value: "1" },
    ],
    PROPERTY_REMIND: [
      { label: "账单日期前7天（每天0点）", value: "0" },
      { label: "账单日期前3天（每天0点）", value: "1" },
    ],
    //租金和物业费逾期
    RENT_OVERDUE: [
      { label: "账单到期后提醒（每天0点）", value: "0" },
    ],
    PROPERTY_OVERDUE: [
      { label: "账单到期后提醒（每天0点）", value: "0" },
    ],
  }
  let selectType = {
    'PROPERTY_OVERDUE': {
      name: '物业费逾期',
      type: '12',

    },
    'RENT_OVERDUE': {
      name: '租金逾期',
      type: '11',

    },
    'PROPERTY_REMIND': {
      name: '物业费提醒',
      type: '10',

    },
    'RENT_REMIND': {
      name: '租金提醒',
      type: '9',

    },
    'energy': {
      name: '能耗提醒',
      type: '8',

    },
    'billOverdue': {
      name: '账单逾期',
      type: '7',

    }, 'billReminder': {
      name: '账单提醒',
      type: '6',

    }, 'lease': {
      name: '租约提醒',
      type: '5',

    }, 'business': {
      name: '经营诉求',
      type: '3',

    },
    'repair': {
      name: '物业报修',
      type: '2',

    },
    'operating': {
      name: '经营数据上报',
      type: '4',
    },
    'PLATFORM': {
      name: '平台消息',
      type: '0',
    }
  }
  // 'operating','energy','billOverdue','billReminder','lease','business','repair','policy','operating'
  let obj = (type) => {
    return ["policy", "repair", "business", "energy", 'operating'].includes(num)
      ? {
        type: "select",
        label: "推送频次：",
        required: true,
        name: type === 'business' ? 'businessScheduleType' : 'adminScheduleType',
        disabledOn: "true",
        id: "u:e77abd061e3b",
        value: "0",
        options: [
          {
            label: '实时',
            value: '0'
          }
        ]
      }
      : {
        type: "select",
        name: type === 'business' ? 'businessTimingType' : 'adminTimingType',
        label: "推送频次：",
        labelClassName: "text-muted",
        placeholder: "请选推送频次：",
        required: true,
        options: options[num]

      };
  }

  let business = [
    {
      type: "tpl",
      tpl: "企业",
      inline: true,
      wrapperComponent: "h3",
      id: "u:a1e19dc90771",
    },
    {
      type: "switch",
      label: "推送开关：",
      option: "",
      name: "businessEnable",
      required: true,
      falseValue: 0,
      trueValue: 1,
      id: "u:86d51ee2c27b",
      value: 1,
    },
    obj('business'),
  ];
  let repair = [
    {
      type: "tpl",
      tpl: "物业",
      inline: true,
      wrapperComponent: "h3",
      id: "u:a1e19dc90771",
    },
    {
      type: "switch",
      label: "推送开关：",
      option: "",
      name: "repairEnable",
      required: true,
      falseValue: 0,
      trueValue: 1,
      id: "u:86d51ee2c27b",
      value: 1,
    },
    {
      type: "input-text",
      label: "推送频次：",
      required: true,
      name: "",
      disabledOn: "true",
      id: "u:e77abd061e3b",
      value: "实时",
    },

    {
      type: "select",
      name: "repairReceivers",
      label: "推送对象：",
      // labelClassName: "text-muted",
      placeholder: "请选推送对象",
      required: true,
      multiple: true,
      // initFetchOn: "data.filling_range",
      source: {
        method: "get",
        url: `systems/getAllRoles`,
        headers: {
          token: `${token()}`,
        },
        adaptor: function (payload, response, api, context) {
          let list = payload?.map((item) => {
            return {
              value: item.roleName,
              label: item.roleName,
            };
          });
          return {
            data: list,
            msg: "请求成功",
            status: 0,
          };
        },
      },
    },
  ]
  if (num === "operating") {
    business = [
      {
        type: "tpl",
        tpl: "企业",
        inline: true,
        wrapperComponent: "h3",
        id: "u:a1e19dc90771",
      },
      {
        type: "switch",
        label: "推送开关：",
        option: "",
        name: "businessEnable",
        required: true,
        falseValue: 0,
        trueValue: 1,
        id: "u:86d51ee2c27b",
        value: 1,
      },
      {
        type: "select",
        name: 'businessTimingType',
        label: "推送频次：",
        labelClassName: "text-muted",
        placeholder: "请选推送频次：",
        required: true,
        options: options[num]

      }
      // obj('business'),
    ];
  }
  let admin = [
    {
      type: "tpl",
      tpl: "园区",
      inline: true,
      wrapperComponent: "h3",
      id: "u:a1e19dc90771",
    },
    {
      type: "switch",
      label: "推送开关：",
      option: "",
      name: "adminEnable",
      required: true,
      falseValue: 0,
      trueValue: 1,
      id: "u:86d51ee2c27b",
      value: 1,
    },
    obj('admin'),
    {
      type: "select",
      name: "adminReceivers",
      label: "推送对象：",
      labelClassName: "text-muted",
      placeholder: "请选推送对象",
      required: true,
      multiple: true,
      // initFetchOn: "data.filling_range",
      source: {
        method: "get",
        url: `systems/getAllRoles`,
        headers: {
          token: `${token()}`,
        },
        adaptor: function (payload, response, api, context) {
          let list = payload?.map((item) => {
            return {
              value: item.roleName,
              label: item.roleName,
            };
          });
          return {
            data: list,
            msg: "请求成功",
            status: 0,
          };
        },
      },
    },
  ];
  if (num === "energy") {
    // 能耗提醒
    return [
      {
        id: "u:cdb841d81c01",
        type: "form",
        initApi: {
          url: `message/schedule/getByType`,
          headers: {
            token: `${token()}`,
          },
          method: "get",
          data: {
            type: selectType[num].type,
          },
          responseData: {
            "&": "$$",
            adminEnable: "${park.enable|default:undefined}",
            adminScheduleType: "${park.scheduleType|default:undefined}",
            adminTimingType: "${park.timingType|default:undefined}",
            adminReceivers: "${park.receivers|default:undefined}",
            // enable: "${adminEnable|default:undefined}",
            // msgType:selectType[num].type,
            // msgObj:0,//消息属性, 0 园区 1 企业 2 维修人员
            // scheduleType:  "${adminScheduleType|default:undefined}",  
            // timingType: "${adminTimingType|default:undefined}",  //* 消息调度类型 * 0 实时 1 定时
            // receiverType:1,//消息接受方式，0 指定企业 1 指定角色
            // receivers: "${receivers|default:undefined}",  
          },
        },
        className: "formLists",
        body: [
          ...admin,
          {
            type: "submit",
            label: "保存",
            primary: true,
            className: "saveBtn",
            "actionType": "ajax",
            api: {
              method: "post",
              url: `message/schedule/save`,
              headers: {
                token: `${token()}`,
              },
              data: {
                configs: [
                  // admin:
                  {
                    enable: "${adminEnable|default:0}",
                    msgType: selectType[num].type,
                    msgObj: 0,//消息属性, 0 园区 1 企业 2 维修人员
                    scheduleType: "${adminScheduleType|default:undefined}",
                    timingType: "${adminTimingType|default:undefined}",  //* 消息调度类型 * 0 实时 1 定时
                    receiverType: 1,//消息接受方式，0 指定企业 1 指定角色
                    receivers: "${adminReceivers|default:undefined}",
                  },
                ]
              },
            },
          },
        ],
        title: "",
        actions: [],
        mode: "horizontal",
      },
    ];
  } else if (num === "billReminder") {
    // 账单提醒
    return [
      {
        id: "u:cdb841d81c01",
        type: "form",
        className: "formLists",
        initApi: {
          url: `message/schedule/getByType`,
          headers: {
            token: `${token()}`,
          },
          method: "get",
          data: {
            type: selectType[num].type,
          },
          responseData: {
            "&": "$$",
            businessEnable: "${enterprise.enable|default:undefined}",
            businessScheduleType: "${enterprise.scheduleType|default:undefined}",
            businessTimingType: "${enterprise.timingType|default:undefined}",
            receivers: "${enterprise.receivers|default:undefined}",
            // enable: "${adminEnable|default:undefined}",
            // msgType:selectType[num].type,
            // msgObj:0,//消息属性, 0 园区 1 企业 2 维修人员
            // scheduleType:  "${adminScheduleType|default:undefined}",  
            // timingType: "${adminTimingType|default:undefined}",  //* 消息调度类型 * 0 实时 1 定时
            // receiverType:1,//消息接受方式，0 指定企业 1 指定角色
            // receivers: "${receivers|default:undefined}",  
          },
        },
        body: [
          ...business,
          {
            type: "submit",
            label: "保存",
            primary: true,
            className: "saveBtn",
            "actionType": "ajax",
            api: {
              method: "post",
              url: `message/schedule/save`,
              headers: {
                token: `${token()}`,
              },
              data: {
                configs: [
                  // business
                  {
                    enable: "${businessEnable|default:0}",//是否启用
                    msgType: selectType[num].type,//消息类型
                    msgObj: 1,//消息属性, 0 园区 1 企业 2 维修人员
                    scheduleType: "${businessScheduleType|default:1}",    //* 消息调度类型 * 0 实时 1 定时
                    timingType: "${businessTimingType|default:undefined}",
                    receiverType: 0,//消息接受方式，0 指定企业 1 指定角色
                    receivers: null,
                  },
                ]




              },
            },
          },
        ],
        title: "",
        actions: [],
        mode: "horizontal",
      },
    ];
  } else if (num === 'repair') {
    return [
      {
        id: "u:cdb841d81c01",
        type: "form",
        className: "formLists",
        initApi: {
          url: `message/schedule/getByType`,
          headers: {
            token: `${token()}`,
          },
          method: "get",
          data: {
            type: selectType[num].type,
          },
          responseData: {
            "&": "$$",
            adminEnable: "${park.enable|default:undefined}",
            adminScheduleType: "${park.scheduleType|default:undefined}",
            adminTimingType: "${park.timingType|default:undefined}",
            adminReceivers: "${park.receivers|default:undefined}",
            // :"${park.receivers|default:undefined}", 

            businessEnable: "${enterprise.enable|default:undefined}",
            businessScheduleType: "${enterprise.scheduleType|default:undefined}",
            businessTimingType: "${enterprise.timingType|default:undefined}",
            receivers: "${enterprise.receivers|default:undefined}",

            repairEnable: "${repairMan.enable|default:undefined}",
            repairScheduleType: "${repairMan.scheduleType|default:undefined}",
            repairTimingType: "${repairMan.timingType|default:undefined}",
            repairReceivers: "${repairMan.receivers|default:undefined}",
            // enable: "${adminEnable|default:undefined}",
            // msgType:selectType[num].type,
            // msgObj:0,//消息属性, 0 园区 1 企业 2 维修人员
            // scheduleType:  "${adminScheduleType|default:undefined}",  
            // timingType: "${adminTimingType|default:undefined}",  //* 消息调度类型 * 0 实时 1 定时
            // receiverType:1,//消息接受方式，0 指定企业 1 指定角色
            // receivers: "${receivers|default:undefined}",  
          },
        },
        body: [
          ...admin,
          ...business,
          ...repair,
          {
            type: "submit",
            label: "保存",
            primary: true,
            className: "saveBtn",
            "actionType": "ajax",
            api: {
              method: "post",
              url: `message/schedule/save`,
              headers: {
                token: `${token()}`,
              },
              data: {
                configs: [
                  // business
                  {
                    enable: "${businessEnable|default:0}",//是否启用
                    msgType: selectType[num].type,//消息类型
                    msgObj: 1,//消息属性, 0 园区 1 企业 2 维修人员
                    scheduleType: "${businessScheduleType|default:1}",    //* 消息调度类型 * 0 实时 1 定时
                    timingType: "${businessTimingType|default:undefined}",
                    receiverType: 0,//消息接受方式，0 指定企业 1 指定角色
                    receivers: null,
                  },
                  // admin:
                  {
                    enable: "${adminEnable|default:0}",
                    msgType: selectType[num].type,
                    msgObj: 0,//消息属性, 0 园区 1 企业 2 维修人员
                    scheduleType: "${adminScheduleType|default:1}",   //* 推送频次： * 0 实时 1 定时
                    timingType: "${adminTimingType|default:undefined}",  //* 定时  
                    receiverType: 1,//消息接受方式，0 指定企业 1 指定角色
                    receivers: "${adminReceivers|default:undefined}",
                  },
                  // // repair:
                  {
                    enable: "${repairEnable|default:0}",
                    msgType: selectType[num].type,
                    msgObj: 2,//消息属性, 0 园区 1 企业 2 维修人员
                    scheduleType: 0, //* 推送频次： * 0 实时 1 定时
                    // timingType: "${default:undefined}",
                    receiverType: 1,//消息接受方式，0 指定企业 1 指定角色
                    receivers: "${repairReceivers|default:undefined}",
                  }
                ]




              },
            },
          },
        ],
        title: "",
        actions: [],
        mode: "horizontal",
      },
    ]
  } else {
    return [
      {
        id: "u:cdb841d81c01",
        type: "form",
        className: "formLists",
        initApi: {
          url: `message/schedule/getByType`,
          headers: {
            token: `${token()}`,
          },
          method: "get",
          data: {
            type: selectType[num].type,
          },
          responseData: {
            "&": "$$",
            adminEnable: "${park.enable|default:undefined}",
            adminScheduleType: "${park.scheduleType|default:undefined}",
            adminTimingType: "${park.timingType|default:undefined}",
            adminReceivers: "${park.receivers|default:undefined}",
            // :"${park.receivers|default:undefined}", 

            businessEnable: "${enterprise.enable|default:undefined}",
            businessScheduleType: "${enterprise.scheduleType|default:undefined}",
            businessTimingType: "${enterprise.timingType|default:undefined}",
            receivers: "${enterprise.receivers|default:undefined}",

            // repairEnable:"${repairMan.enable|default:undefined}",
            // repairScheduleType:"${repairMan.scheduleType|default:undefined}", 
            // repairTimingType:"${repairMan.timingType|default:undefined}", 
            // repairReceivers:"${repairMan.receivers|default:undefined}", 
            // enable: "${adminEnable|default:undefined}",
            // msgType:selectType[num].type,
            // msgObj:0,//消息属性, 0 园区 1 企业 2 维修人员
            // scheduleType:  "${adminScheduleType|default:undefined}",  
            // timingType: "${adminTimingType|default:undefined}",  //* 消息调度类型 * 0 实时 1 定时
            // receiverType:1,//消息接受方式，0 指定企业 1 指定角色
            // receivers: "${receivers|default:undefined}",  
          },
        },
        body: [
          ...admin,
          ...business,
          {
            type: "submit",
            label: "保存",
            primary: true,
            className: "saveBtn",
            "actionType": "ajax",
            api: {
              method: "post",
              url: `message/schedule/save`,
              headers: {
                token: `${token()}`,
              },
              data: {
                configs: [
                  // business
                  {
                    enable: "${businessEnable|default:0}",//是否启用
                    msgType: selectType[num].type,//消息类型
                    msgObj: 1,//消息属性, 0 园区 1 企业 2 维修人员
                    scheduleType: "${businessScheduleType|default:1}",    //* 消息调度类型 * 0 实时 1 定时
                    timingType: "${businessTimingType|default:undefined}",
                    receiverType: 0,//消息接受方式，0 指定企业 1 指定角色
                    receivers: null,
                  },
                  // admin:
                  {
                    enable: "${adminEnable|default:0}",
                    msgObj: "${msgObj|default:undefined}",
                    msgType: selectType[num].type,
                    msgObj: 0,//消息属性, 0 园区 1 企业 2 维修人员
                    scheduleType: "${adminScheduleType|default:1}",
                    timingType: "${adminTimingType|default:undefined}",  //* 消息调度类型 * 0 实时 1 定时
                    receiverType: 1,//消息接受方式，0 指定企业 1 指定角色
                    receivers: "${adminReceivers|default:undefined}",
                  },
                  // // repair:
                  // {
                  //   msgObj:2,//消息属性, 0 园区 1 企业 2 维修人员
                  //   scheduleType:'0',
                  //   receiverType:null,//消息接受方式，0 指定企业 1 指定角色
                  //   receivers:  null,
                  // }
                ]




              },
            },
          },
        ],
        title: "",
        actions: [],
        mode: "horizontal",
      },
    ];
  }
};
export const messageTab = {
  type: "page",
  title: "",
className:'messageTabs',
  id: "u:07e183dd88f8",
  body: [
    {
      type: "tabs",
      tabsMode: "radio",
      className:'tabsTitle',
      tabs: [
        {
          title: "物业报修",
          body: tabs1("repair"),
          id: "u:4a07b35df9ef",
        },
        {
          title: "经营诉求",
          body: tabs1("business"),
          id: "u:4a07b35df9e",
        },
        {
          title: "租约提醒",
          body: tabs1("lease"),
          id: "u:4a07b35df9ef",
        },
        {
          title: "账单提醒",
          body: tabs1("billReminder"),
          id: "u:4a07b35df9ef",
        },
        {
          title: "账单逾期",
          body: tabs1("billOverdue"),
          id: "u:4a07b35df9ef",
        },
        {
          title: "能耗提醒",
          body: tabs1("energy"),
          id: "u:4a07b35df9ef",
        },
        {
          title: "经营数据上报",
          body: tabs1("operating"),
          id: "u:4a07b35df9ef",
        },
        {
          title: "租金提醒",
          body: tabs1("RENT_REMIND"),
          id: "u:4a07b35df9ef",
        },
        {
          title: "物业费提醒",
          body: tabs1("PROPERTY_REMIND"),
          id: "u:4a07b35df9ef",
        },
        {
          title: "租金逾期",
          body: tabs1("RENT_OVERDUE"),
          id: "u:4a07b35df9ef",
        },
        {
          title: "物业费逾期",
          body: tabs1("PROPERTY_OVERDUE"),
          id: "u:4a07b35df9ef",
        },
      ],
      id: "u:238ba61a4079",
      // className: "tabsClass",
      linksClassName: "tabsTitleTwst",
    },
  ],
  actions: [],
};
