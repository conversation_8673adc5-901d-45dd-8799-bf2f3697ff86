<template>
  <div>
    <!-- 能耗监测 -->
    <div v-if="!!amisjson" class="zhengce publicTableStyle">
      <AmisComponent :amisjson="amisjson" />
    </div>
  </div>
</template>
<script setup>
import { onBeforeMount, onMounted, ref } from 'vue';
import AmisComponent from '@/components/amis/index.vue';
import { perFix, publicConfig, baseEvn, token } from '@/utils/utils';
import { useStore } from 'vuex';
import { useRoute, useRouter } from 'vue-router';
import { waringApi, waringFilter, waringColumns } from './warning';
import { transformData } from '@/utils/formData';
const route = useRoute();
const router = useRouter();

let formData = (title, state) => {
  return {
    // "size": "lg",
    title: title,
    actions: [
      {
        type: 'button',
        size: 'md',
        actionType: 'cancel',
        label: '取消',
        level: 'default',
        primary: true,
      },
      {
        label: '确认',
        actionType: 'confirm',
        primary: true,
        reload: 'tab-s',
        close: true,
        type: 'button',
        size: 'md',
        api: {
          method: 'post',
          messages: {
            success: '操作成功！',
          },
          url: `energy/formSubmit`,
          data: {
            businessCode: 'energy',
            action: state,
            element: {
              house_id:
                state === 'insert'
                  ? '${house_id|default:undefined}'
                  : undefined,
              table_num: '${table_num|default:undefined}',
              used: '${used|default:undefined}',
              energy_type:
                state === 'insert'
                  ? '${energy_type|default:undefined}'
                  : undefined,
              filling_range:
                state === 'insert'
                  ? '${filling_range|default:undefined}'
                  : undefined,
              uni_code:
                state === 'insert'
                  ? '${uni_code|default:undefined}'
                  : undefined,
              cost: '${cost|default:undefined}',

              // "name": "${name|default:undefined}",
            },
            conditions:
              state === 'insert'
                ? []
                : [
                    {
                      key: 'id',
                      value: '${id|default:undefined}',
                    },
                  ],
          },
        },
      },
    ],

    body: [
      {
        type: 'form',
        columnCount: 1,
        rules: {},

        body: [
          {
            type: 'nested-select',
            //"onlyLeaf": true,
            searchable: true, //可搜索
            multiple: true, //是否多选
            //"name": "houseIds",
            hideNodePathLabel: true,
            //"withChildren": true,//设置 true时，选中父节点时，值里面将包含子节点的值，否则只会保留父节点的值。
            onlyChildren: true, //多选时，选中父节点时，是否只将其子节点加入到值中。
            //"label": "所属楼栋楼层",
            maxTagCount: 2, //	标签的最大展示数量，超出数量后以收纳浮层的方式展示，仅在多选模式开启后生效
            disabledOn: `${state !== 'insert'}`,
            // "type": "nested-select",
            //"type": "tree-select",
            //"onlyLeaf": true,
            //"searchable": true,
            name: 'house_id',
            label: '设备地址',
            size: 'lg',
            source: {
              method: 'get',
              url: `houses/getBuildingTree`,
              adaptor: function (payload, response, api, context) {
                let list = payload.map((item) => {
                  return {
                    value: item.buildingId,
                    label: item.buildingName,
                    children: transformData(item.houseList),
                  };
                });
                return {
                  data: list,
                  msg: '请求成功',
                  status: 0,
                };
              },
            },
          },
          {
            label: '所属企业',
            name: 'uni_code',
            type: 'select',
            searchable: true,
            required: true,
            size: 'lg',
            disabledOn: `${state !== 'insert'}`,
            source: {
              method: 'post',
              url: `enterprise/getList`,

              data: {
                businessCode: 'enterprise',
              },
              /* "responseData": {
                "options": "${items|pick:label~houseName,value~houseId}"
              } */
              adaptor: function (payload, response, api, context) {
                let list = payload?.elements.map((item) => {
                  return {
                    value: item.uni_code,
                    label: item.name,
                  };
                });
                return {
                  data: list,
                  msg: '请求成功',
                  status: 0,
                };
              },
            },
          },
          {
            label: '能耗类型',
            type: 'select',
            name: 'energy_type',
            id: 'u:ea2bfdba9bea',
            size: 'lg',
            disabledOn: `${state !== 'insert'}`,
            required: true,
            options: [
              { label: '水', value: '0' },
              { label: '电', value: '1' },
            ],
            // 能耗类型, 0 水,1 电
          },
          {
            // "type": "nested-select",
            type: 'tree-select',
            onlyLeaf: true,
            searchable: true,
            name: 'filling_range',
            label: '读表时间',
            disabledOn: `${state !== 'insert'}`,
            size: 'lg',
            required: true,
            // "onlyChildren": true,
            source: {
              method: 'get',
              url: `enterprise/business/getFillingRanges?type=1`,

              /* "responseData": {
                "options": "${items|pick:label~houseName,value~houseId}"
              } */
              adaptor: function (payload, response, api, context) {
                let list = payload?.options;
                return {
                  data: list,
                  msg: '请求成功',
                  status: 0,
                };
              },
            },
          },
          {
            label: '表读数',
            type: 'input-number',
            name: 'table_num',
            id: 'u:ea2bfdba9bea',
            size: 'lg',
            required: true,
          },

          {
            label: '能耗量',
            type: 'input-number',
            name: 'used',
            id: 'u:ea2bfdba9bea',
            size: 'lg',
            required: true,
          },
          {
            label: '能耗费用',
            type: 'input-number',
            name: 'cost',
            id: 'u:ea2bfdba9bea',
            size: 'lg',
            required: true,
          },
        ],
      },
    ],
  };
};
let formData2 = (title, state) => {
  return {
    // "size": "lg",
    title: title,
    data: {},
    actions: [
      {
        type: 'button',
        size: 'md',
        actionType: 'cancel',
        label: '取消',
        level: 'default',
        primary: true,
      },
      {
        label: '确认',
        actionType: 'confirm',
        primary: true,
        reload: 'tab-s',
        close: true,
        type: 'button',
        size: 'md',
        api: {
          method: 'post',
          url: `energy/formSubmit`,
          messages: {
            success: '操作成功！',
          },
          data: {
            businessCode: 'energy',
            action: state,
            element: {
              house_id:
                state === 'insert'
                  ? '${house_id|default:undefined}'
                  : undefined,
              table_num: '${table_num|default:undefined}',
              used: '${used|default:undefined}',
              energy_type:
                state === 'insert'
                  ? '${energy_type|default:undefined}'
                  : undefined,
              filling_range:
                state === 'insert'
                  ? '${filling_range|default:undefined}'
                  : undefined,
              uni_code:
                state === 'insert'
                  ? '${uni_code|default:undefined}'
                  : undefined,
              cost: '${cost|default:undefined}',

              // "name": "${name|default:undefined}",
            },
            conditions:
              state === 'insert'
                ? []
                : [
                    {
                      key: 'id',
                      value: '${id|default:undefined}',
                    },
                  ],
          },
        },
      },
    ],

    body: [
      {
        type: 'form',
        columnCount: 1,
        rules: {},

        body: [
          {
            type: 'nested-select',
            //"onlyLeaf": true,
            searchable: true, //可搜索
            //"multiple": true,//是否多选
            hideNodePathLabel: true,
            //"name": "houseIds",
            //"withChildren": true,//设置 true时，选中父节点时，值里面将包含子节点的值，否则只会保留父节点的值。
            onlyChildren: true, //多选时，选中父节点时，是否只将其子节点加入到值中。
            maxTagCount: 2, //	标签的最大展示数量，超出数量后以收纳浮层的方式展示，仅在多选模式开启后生效
            disabledOn: `${state !== 'insert'}`,
            // "type": "tree-select",
            onlyLeaf: true,
            id: 'u:ea2bfdba9bea22',
            // "multiple": true,
            name: 'house_id',
            label: '设备地址',
            onlyLeaf: true,
            // "maxTagCount": 1,
            // "onlyChildren": true,
            required: true,
            size: 'lg',
            source: {
              method: 'get',
              url: `houses/getBuildingTree`,
              adaptor: function (payload, response, api, context) {
                let list = payload.map((item) => {
                  return {
                    value: item.buildingId,
                    label: item.buildingName,
                    children: transformData(item.houseList),
                  };
                });
                return {
                  data: list,
                  msg: '请求成功',
                  status: 0,
                };
              },
            },
          },
          {
            label: '所属企业',
            name: 'uni_code',
            type: 'select',
            searchable: true,
            required: true,
            size: 'lg',
            disabledOn: `${state !== 'insert'}`,
            source: {
              method: 'post',
              url: `enterprise/getList`,

              data: {
                businessCode: 'enterprise',
              },
              /* "responseData": {
                "options": "${items|pick:label~houseName,value~houseId}"
              } */
              adaptor: function (payload, response, api, context) {
                let list = payload?.elements.map((item) => {
                  return {
                    value: item.uni_code,
                    label: item.name,
                  };
                });
                return {
                  data: list,
                  msg: '请求成功',
                  status: 0,
                };
              },
            },
          },
          {
            label: '能耗类型',
            type: 'select',
            name: 'energy_type',
            id: 'u:ea2bfdba9bea',
            size: 'lg',
            disabledOn: `${state !== 'insert'}`,
            required: true,
            options: [
              { label: '水', value: '0' },
              { label: '电', value: '1' },
            ],
            // 能耗类型, 0 水,1 电
          },
          {
            type: 'select',
            searchable: true,
            name: 'filling_range',
            label: '读表时间',
            disabledOn: `${state !== 'insert'}`,
            size: 'lg',
            // "onlyChildren": true,
            source: {
              method: 'get',
              url: `enterprise/business/getFillingRanges?type=1`,

              /* "responseData": {
                "options": "${items|pick:label~houseName,value~houseId}"
              } */
              adaptor: function (payload, response, api, context) {
                let list = payload?.options;
                return {
                  data: list,
                  msg: '请求成功',
                  status: 0,
                };
              },
            },
          },
          {
            label: '表读数',
            type: 'input-number',
            name: 'table_num',
            id: 'u:ea2bfdba9bea',
            size: 'lg',
            required: true,
          },

          {
            label: '能耗量',
            type: 'input-number',
            name: 'used',
            id: 'u:ea2bfdba9bea',
            size: 'lg',
            required: true,
          },
          {
            label: '能耗费用',
            type: 'input-number',
            name: 'cost',
            id: 'u:ea2bfdba9bea',
            size: 'lg',
            required: true,
          },
        ],
      },
    ],
  };
};

const columns = [
  {
    name: 'address',
    label: '设备地址',
    type: 'text',
  },
  {
    name: 'name',
    label: '所属企业',
    type: 'text',
  },
  {
    name: '能耗类型',
    name: "${energy_type == 0 ? '水' :'电'}",
    label: '状态',
    type: 'tpl',
    tpl: '<span class="inline-block px-2 py-1 text-xs font-medium rounded-full ${energy_type == 0 ? "bg-blue-100 text-blue-800" : "bg-yellow-100 text-yellow-800"}">${energy_type == 0 ? "水" : "电"}</span>',
  },
  {
    name: 'table_num',
    label: '表读数',
    type: 'text',
  },
  {
    name: 'filling_range',
    label: '读表时间范围',
    type: 'text',
  },
  {
    name: 'used',
    label: '能耗量',
    type: 'text',
  },

  {
    name: 'cost',
    label: '能耗费用',
    type: 'text',
  },
  {
    type: 'operation',
    label: '操作',
    fixed: 'right', // 固定在右侧
    width: 200,
    buttons: [
      {
        // "icon": "fa fa-edit",
        label: '编辑',
        level: 'primary',
        type: 'button',

        actionType: 'dialog',
        dialog: formData('编辑', 'update'),
      },
      {
        // "icon": "fa fa-delete",
        type: 'button',
        size: 'md',
        level: 'danger',
        actionType: 'ajax',
        label: '删除',
        confirmText: '是否确认删除',
        api: {
          method: 'post',
          url: `energy/formSubmit`,
          data: {
            businessCode: 'energy',
            action: 'delete',
            conditions: [
              {
                key: 'id',
                value: '${id}',
              },
            ],
          },
        },
      },
    ],
  },
];
const api = {
  method: 'post',
  url: `energy/getListPage`,
  requestAdaptor: (rest) => {
    // console.log(rest, "rest");
    let { gmtCreate, ...other } = rest.data;
    rest.data.conditions.forEach((it) => {
      if (it.key === 'house_id') {
        it.values = it.values ? it.values?.split(',') : [];
      }
    });
    let datas = {
      ...other,
    };
    return {
      ...rest,
      data: datas,
    };
  },
  data: {
    businessCode: 'energy',
    conditions: [
      {
        key: 'name',
        compareType: 'like',
        value: "${enterpriseName|default:''}",
      },
      {
        compareType: 'eq',
        key: 'filling_range',
        value: "${filling_range|default:''}",
      },
      {
        compareType: 'eq',
        key: 'house_id',
        values: "${house_id|default:''}",
      },
      {
        compareType: 'eq',
        key: 'energy_type',
        value: "${energy_type|default:''}",
      },
    ],
    pageNum: '${page|default:1}',
    pageSize: '${perPage|default:10}',
  },
  responseData: {
    '&': '$$',
    items: 'records',
  },
};
const filter = {
  title: '',
  submitText: '',
  controls: [
    {
      type: 'select',
      // "multiple": true,
      name: 'filling_range',
      size: 'md',
      label: '读表时间',
      maxTagCount: 3,
      onlyChildren: true,
      source: {
        method: 'get',
        url: `enterprise/business/getFillingRanges?type=1`,

        /* "responseData": {
          "options": "${items|pick:label~houseName,value~houseId}"
        } */
        adaptor: function (payload, response, api, context) {
          let list = payload?.options;
          return {
            data: list,
            msg: '请求成功',
            status: 0,
          };
        },
      },
    },
    {
      type: 'nested-select',
      //"onlyLeaf": true,
      searchable: true, //可搜索
      multiple: true, //是否多选
      onlyChildren: true, //多选时，选中父节点时，是否只将其子节点加入到值中。
      maxTagCount: 1, //	标签的最大展示数量，超出数量后以收纳浮层的方式展示，仅在多选模式开启后生效
      name: 'house_id',
      label: '设备地址',
      size: 'md',
      hideNodePathLabel: true,
      source: {
        method: 'get',
        url: `houses/getBuildingTree`,
        adaptor: function (payload, response, api, context) {
          let list = payload.map((item) => {
            return {
              value: item.buildingId,
              label: item.buildingName,
              children: transformData(item.houseList),
            };
          });
          return {
            data: list,
            msg: '请求成功',
            status: 0,
          };
        },
      },
    },

    {
      type: 'select',
      name: 'energy_type',
      label: '能耗类型',
      size: 'md',
      placeholder: '选择状态',
      options: [
        { label: '水', value: '0' },
        { label: '电', value: '1' },
      ],
    },
    {
      type: 'text',
      name: 'enterpriseName',
      label: '企业名称',
      size: 'md',
      placeholder: '请输入企业名称',
    },
    {
      type: 'reset',
      name: 'gmtCreate',
      label: '重置',
    },
    {
      type: 'submit',
      label: '搜索',
      primary: true,
    },
    {
      type: 'button',
      size: 'md',
      label: '新增',
      primary: true,
      actionType: 'dialog',
      dialog: formData2('新增', 'insert'),
    },
    // {
    //   "type": "button",
    // "size": "md",
    //   "label": "批量导出",
    //   "primary": true
    // },
  ],
  onSubmit: 'reload',
  // "actions": [
  //   {
  //     "type": "reset",
  //     "label": "重置"
  //   },
  //   {
  //     "type": "submit",
  //     "label": "搜索",
  //     "primary": true
  //   },

  // ]
};
let amisjson = ref(null);
onMounted(() => {
  let tabs = {
    type: 'tabs',
    // "tabsMode": "radio",
    tabs: [
      {
        title: '能耗监测',
        body: publicConfig({ columns, api, filter }),
        id: 'u:319a552aa436',
      },
      {
        title: '能耗预警',
        body: publicConfig({
          columns: waringColumns,
          api: waringApi,
          filter: waringFilter,
        }),
        id: 'u:4a07b35df9ef',
      },
    ],
    id: 'u:238ba61a4079',
    className: 'tabsClass',
    linksClassName: 'tabsTitle',
  };
  // let data = publicConfig({ columns, api, filter })
  amisjson.value = tabs;
});
</script>
<style lang="scss">
.boxf {
  background-color: #fff;
  padding: 10px;
  margin-bottom: 20px;
}
.amis-scope .headerTitle {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  text-align: right;
}

.amis-scope .leftTitle {
  line-height: 33px;
  width: 100px;
}
.zhengce {
  .drawer-customer {
    .test {
      display: inline-block;
      font-size: 16px;
      font-weight: 500;
      text-align: center;
      width: 100%;
      font-weight: 500;
      padding: 10px;
    }
    .title {
      font-size: 18px;
      width: 100%;
      font-weight: 500;
    }
    .p-warning {
      width: 100%;
      border: 1px solid #eee;
      border-radius: 4px;
      margin: 10px 20px;
      padding: 0 0 20px 0;
      .p-1 {
        justify-content: flex-start;
        .antd-Container.antd-Container--xs {
          display: flex !important;
          justify-content: left;
          align-items: center;
        }
      }

      .antd-Container {
        padding: 0;
        .card-title {
          font-weight: 600;
          font-size: 14px;
        }
        .antd-Flex:first-child {
          font-size: 18px;
          padding: 10px 24px;
          background: #f7f7f7;
        }
        .antd-Flex {
          padding: 5px 24px;
        }
        .antd-Number-handler-wrap {
          outline: 0;
        }
        // .antd-Number-handler,
        //      .antd-Number.antd-Number--borderFull,
        .antd-Number-input-wrap {
          background: #f7f7f7;
        }
      }
    }
    .input-number {
      margin: 0 15px;
    }
  }
  .antd-Each-placeholder {
    text-align: center;
    padding: 20px;
    color: #0000009c;
  }
}
</style>
