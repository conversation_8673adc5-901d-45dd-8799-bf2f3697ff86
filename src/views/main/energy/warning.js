import { perFix, publicConfig, baseEvn, token } from '@/utils/utils';
import { transformData } from '@/utils/formData';
const handleData = (title, state) => {
  return {
    title: title,
    actions: [
      {
        type: 'button',
        size: 'md',
        actionType: 'cancel',
        label: '取消',
        level: 'default',
        primary: true,
      },
      {
        label: '确认',
        actionType: 'confirm',
        primary: true,
        reload: 'tab-s',
        close: true,
        type: 'button',
        size: 'md',
        api: {
          method: 'post',
          url: `energy/warn/handle`,
          messages: {
            success: '操作成功！',
          },
          headers: {
            token: `${token()}`,
          },
          data: {
            id: '${id|default:undefined}',
            handle: '${handle|default:undefined}',
          },
        },
      },
    ],
    body: [
      {
        type: 'form',
        columnCount: 1,
        rules: {},
        body: [
          {
            label: '处理描述',
            type: 'textarea',
            name: 'handle',
            id: 'u:ea2bfdba9bea',
            // "size": "",
            required: true,
            minRows: 6,
          },
        ],
      },
    ],
  };
};
const handleLog = (title, state) => {
  return {
    type: 'page',
    title: '处理记录',
    body: [
      {
        type: 'page',
        initApi: {
          url: `energy/warn/queryWarnRecordList`,
          method: 'get',
          data: {
            id: '${id|default:undefined}',
          },
          adaptor: function (payload, response, api, context) {
            // console.log(payload, 'payload');
            let arr = {};
            arr.arr = payload;
            return arr;
          },
        },
        body: {
          type: 'each',
          name: 'arr',
          items: {
            type: 'tpl',
            tpl: "<div class='boxf'><span>处理人：</span></span><span><%= data.handlePerson %></span><br><span>处理时间：</span></span><span><%= data.gmtModify %></span><br><span>处理描述：</span></span><span><%= data.handleDesc %></span><br></div>",
          },
        },
        /*  body: [
             // {
             //     name: 'handle_status',
             //     name: "${handle_status === '0' ? '未处理' :'已处理'}",
             //     label: "处理状态",
             //     type: "date",
             //     // "src": "${img}",
             //     disabledOn: "true",
             //     labelClassName: "leftTitle",
             //   },
             {
               name: "handlePerson",
               label: "处理人",
               type: "static",
               labelClassName: "leftTitle",
             },
             // policyTitle
             //                       gmtCreate
             {
               name: "gmtModify",
               label: "处理时间",
               type: "date",
               "valueFormat": "x",
               labelClassName: "leftTitle",
             },
 
             {
               name: "handleDesc",
               label: "处理描述",
               type: "static",
               labelClassName: "leftTitle",
             },
           ], */
      },
    ],
    actions: [],
  };
};

let formData = (title, state) => {
  return {
    size: 'lg',
    title: title,
    className: 'drawer-customer',
    actions: [
      {
        type: 'button',
        size: 'md',
        actionType: 'cancel',
        label: '取消',
        level: 'default',
        primary: true,
      },
      {
        label: '确认',
        actionType: 'confirm',
        primary: true,
        reload: 'tab-s',
        close: true,
        type: 'button',
        size: 'md',
        api: {
          method: 'post',
          url: `energy/warn/rule/setRule`,
          headers: {
            token: `${token()}`,
          },
          data: {
            electric: {
              level0: {
                ratioMin: '${ratioMax|default:undefined}',
                ratioMax: '${ratioMin|default:undefined}',
                dosageMin: '${dosageMax|default:undefined}',
                dosageMax: '${dosageMin|default:undefined}',
              },
              level1: {
                ratioMax: '${ratioMin1|default:undefined}',
                ratioMin: '${ratioMax1|default:undefined}',
                dosageMax: '${dosageMin1|default:undefined}',
                dosageMin: '${dosageMax1|default:undefined}',
              },
            },
            water: {
              level0: {
                ratioMax: '${ratioMin3|default:undefined}',
                ratioMin: '${ratioMax3|default:undefined}',
                dosageMax: '${dosageMin3|default:undefined}',
                dosageMin: '${dosageMax3|default:undefined}',
              },
              level1: {
                ratioMax: '${ratioMin4|default:undefined}',
                ratioMin: '${ratioMax4|default:undefined}',
                dosageMax: '${dosageMin4|default:undefined}',
                dosageMin: '${dosageMax4|default:undefined}',
              },
            },
          },
        },
      },
    ],

    body: [
      {
        type: 'form',
        columnCount: 1,
        rules: {},
        initApi: {
          url: `energy/warn/rule/getRule`,
          method: 'get',
          headers: {
            token: `${token()}`,
          },
          data: {},
          responseData: {
            '&': '$$',
            items: 'elements',
            ratioMin: '${electric.level0.ratioMax|default:undefined}',
            ratioMax: '${electric.level0.ratioMin|default:undefined}',
            dosageMin: '${electric.level0.dosageMax|default:undefined}',
            dosageMax: '${electric.level0.dosageMin|default:undefined}',
            ratioMin1: '${electric.level1.ratioMax|default:undefined}',
            ratioMax1: '${electric.level1.ratioMin|default:undefined}',
            dosageMin1: '${electric.level1.dosageMax|default:undefined}',
            dosageMax1: '${electric.level1.dosageMin|default:undefined}',
            ratioMin3: '${water.level0.ratioMax|default:undefined}',
            ratioMax3: '${water.level0.ratioMin|default:undefined}',
            dosageMin3: '${water.level0.dosageMax|default:undefined}',
            dosageMax3: '${water.level0.dosageMin|default:undefined}',
            ratioMin4: '${water.level1.ratioMax|default:undefined}',
            ratioMax4: '${water.level1.ratioMin|default:undefined}',
            dosageMin4: '${water.level1.dosageMax|default:undefined}',
            dosageMax4: '${water.level1.dosageMin|default:undefined}',
          },
        },
        body: [
          {
            type: 'flex',
            className: 'p-1  p-warning',
            items: [
              {
                type: 'container',
                body: [
                  // 用电
                  {
                    type: 'flex',
                    className: 'p-1 ',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '用电预警规则：',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:182e0e2bc97e',
                            className: 'title',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                  // 异常范围
                  {
                    type: 'flex',
                    className: 'p-1 ',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '异常范围',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:182e0e2bc97e',
                            className: 'test',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                  //   环比季度预警：
                  {
                    type: 'flex',
                    className: 'p-1 ',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '环比季度预警：',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:182e0e2bc97e',
                            className: 'card-title',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                  // 环比季度预警：
                  {
                    type: 'flex',
                    className: 'p-1 ',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '环比上季度用量超过  ',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:4eab55d21f8f',
                          },
                          {
                            type: 'input-number',
                            label: '',
                            name: 'ratioMin',
                            keyboard: true,
                            id: 'u:4f1fe98fb959',
                            step: 1,
                            size: 'sm',
                            mode: 'inline',
                            className: 'input-number',
                          },
                          {
                            type: 'tpl',
                            tpl: '%或低于',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:ee48e71f47a7',
                          },

                          {
                            type: 'input-number',
                            label: '',
                            name: 'ratioMax',
                            keyboard: true,
                            id: 'u:be4ce49d4838',
                            step: 1,
                            size: 'sm',
                            mode: 'inline',
                            className: 'input-number',
                          },
                          {
                            type: 'tpl',
                            tpl: '%时预警',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:6392720018ce',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                  //   用量预警：
                  {
                    type: 'flex',
                    className: 'p-1',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '用量预警：',
                            inline: true,
                            // className: 'p-title',
                             className: 'card-title',
                            wrapperComponent: '',
                            id: 'u:182e0e2bc97e',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                  // 用量预警：
                  {
                    type: 'flex',
                    className: 'p-1',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '用量超过            ',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:4eab55d21f8f',
                          },
                          {
                            type: 'input-number',
                            label: '',
                            name: 'dosageMin',
                            keyboard: true,
                            id: 'u:4f1fe98fb959',
                            step: 1,
                            size: 'sm',
                            mode: 'inline',
                            className: 'input-number',
                          },
                          {
                            type: 'tpl',
                            tpl: 'kw/h或低于',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:ee48e71f47a7',
                          },

                          {
                            type: 'input-number',
                            label: '',
                            name: 'dosageMax',
                            keyboard: true,
                            id: 'u:be4ce49d4838',
                            step: 1,
                            size: 'sm',
                            mode: 'inline',
                            className: 'input-number',
                          },
                          {
                            type: 'tpl',
                            tpl: 'kw/h时预警。',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:6392720018ce',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                  // 警告范围
                  {
                    type: 'flex',
                    className: 'p-1',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '警告范围',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:182e0e2bc97e',
                            className: 'test',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                  //   环比季度预警：
                  {
                    type: 'flex',
                    className: 'p-1',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '环比季度预警：',
                            inline: true,
                            wrapperComponent: '',
                            className: 'card-title',
                            id: 'u:182e0e2bc97e',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                  // 环比季度预警：
                  {
                    type: 'flex',
                    className: 'p-1',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '环比上季度用量超过    ',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:4eab55d21f8f',
                          },
                          {
                            type: 'input-number',
                            label: '',
                            name: 'ratioMin1',
                            keyboard: true,
                            id: 'u:4f1fe98fb959',
                            step: 1,
                            size: 'sm',
                            mode: 'inline',
                            className: 'input-number',
                          },
                          {
                            type: 'tpl',
                            tpl: '%或低于',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:ee48e71f47a7',
                          },

                          {
                            type: 'input-number',
                            label: '',
                            name: 'ratioMax1',
                            keyboard: true,
                            id: 'u:be4ce49d4838',
                            step: 1,
                            size: 'sm',
                            mode: 'inline',
                            className: 'input-number',
                          },
                          {
                            type: 'tpl',
                            tpl: '%时预警',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:6392720018ce',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                  //   用量预警：
                  {
                    type: 'flex',
                    className: 'p-1',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '用量预警：',
                            inline: true,
                             className: 'card-title',
                            wrapperComponent: '',
                            id: 'u:182e0e2bc97e',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                  // 用量预警：
                  {
                    type: 'flex',
                    className: 'p-1',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '用量超过            ',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:4eab55d21f8f',
                          },
                          {
                            type: 'input-number',
                            label: '',
                            name: 'dosageMin1',
                            keyboard: true,
                            id: 'u:4f1fe98fb959',
                            step: 1,
                            size: 'sm',
                            mode: 'inline',
                            className: 'input-number',
                          },
                          {
                            type: 'tpl',
                            tpl: 'kw/h或低于',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:ee48e71f47a7',
                          },

                          {
                            type: 'input-number',
                            label: '',
                            name: 'dosageMax1',
                            keyboard: true,
                            id: 'u:be4ce49d4838',
                            step: 1,
                            size: 'sm',
                            mode: 'inline',
                            className: 'input-number',
                          },
                          {
                            type: 'tpl',
                            tpl: 'kw/h时预警。',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:6392720018ce',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                ],
                size: 'xs',
                style: {
                  position: 'static',
                  display: 'block',
                  flex: '1 1 auto',
                  flexGrow: 1,
                  flexBasis: 'auto',
                },
                wrapperBody: false,
                isFixedHeight: false,
                isFixedWidth: false,
                id: 'u:446774e379ab',
              },
            ],
          },
          //   用水
          {
            type: 'flex',
            className: 'p-1  p-warning',
            items: [
              {
                type: 'container',
                body: [
                  {
                    type: 'flex',
                    className: 'p-1',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '用水预警规则：',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:182e0e2bc97e',
                            className: 'title',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                  // 异常范围
                  {
                    type: 'flex',
                    className: 'p-1',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '异常范围',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:182e0e2bc97e',
                            className: 'test',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                  //   环比季度预警：
                  {
                    type: 'flex',
                    className: 'p-1',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '环比季度预警：',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:182e0e2bc97e',
                            className: 'card-title',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                  // 环比季度预警：
                  {
                    type: 'flex',
                    className: 'p-1',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '环比上季度用量超过    ',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:4eab55d21f8f',
                          },
                          {
                            type: 'input-number',
                            label: '',
                            name: 'ratioMin3',
                            keyboard: true,
                            id: 'u:4f1fe98fb959',
                            step: 1,
                            size: 'sm',
                            mode: 'inline',
                            className: 'input-number',
                          },
                          {
                            type: 'tpl',
                            tpl: '%或低于',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:ee48e71f47a7',
                          },

                          {
                            type: 'input-number',
                            label: '',
                            name: 'ratioMax3',
                            keyboard: true,
                            id: 'u:be4ce49d4838',
                            step: 1,
                            size: 'sm',
                            mode: 'inline',
                            className: 'input-number',
                          },
                          {
                            type: 'tpl',
                            tpl: '%时预警',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:6392720018ce',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                  //   用量预警：
                  {
                    type: 'flex',
                    className: 'p-1',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '用量预警：',
                            inline: true,
                             className: 'card-title',
                            wrapperComponent: '',
                            id: 'u:182e0e2bc97e',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                  // 用量预警：
                  {
                    type: 'flex',
                    className: 'p-1',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '用量超过            ',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:4eab55d21f8f',
                          },
                          {
                            type: 'input-number',
                            label: '',
                            name: 'dosageMin3',
                            keyboard: true,
                            id: 'u:4f1fe98fb959',
                            step: 1,
                            size: 'sm',
                            mode: 'inline',
                            className: 'input-number',
                          },
                          {
                            type: 'tpl',
                            tpl: 't或低于',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:ee48e71f47a7',
                          },

                          {
                            type: 'input-number',
                            label: '',
                            name: 'dosageMax3',
                            keyboard: true,
                            id: 'u:be4ce49d4838',
                            step: 1,
                            size: 'sm',
                            mode: 'inline',
                            className: 'input-number',
                          },
                          {
                            type: 'tpl',
                            tpl: 't时预警。',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:6392720018ce',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                  // 警告范围
                  {
                    type: 'flex',
                    className: 'p-1',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '警告范围',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:182e0e2bc97e',
                            className: 'test',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                  //   环比季度预警：
                  {
                    type: 'flex',
                    className: 'p-1',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '环比季度预警：',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:182e0e2bc97e',
                            className: 'card-title',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                  // 环比季度预警：
                  {
                    type: 'flex',
                    className: 'p-1',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '环比上季度用量超过  ',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:4eab55d21f8f',
                          },
                          {
                            type: 'input-number',
                            label: '',
                            name: 'ratioMin4',
                            keyboard: true,
                            id: 'u:4f1fe98fb959',
                            step: 1,
                            size: 'sm',
                            mode: 'inline',
                            className: 'input-number',
                          },
                          {
                            type: 'tpl',
                            tpl: '%或低于',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:ee48e71f47a7',
                          },

                          {
                            type: 'input-number',
                            label: '',
                            name: 'ratioMax4',
                            keyboard: true,
                            id: 'u:be4ce49d4838',
                            step: 1,
                            size: 'sm',
                            className: 'input-number',
                            mode: 'inline',
                          },
                          {
                            type: 'tpl',
                            tpl: '%时预警',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:6392720018ce',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                  //   用量预警：
                  {
                    type: 'flex',
                    className: 'p-1',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '用量预警：',
                            inline: true,
                             className: 'card-title',
                            wrapperComponent: '',
                            id: 'u:182e0e2bc97e',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                  // 用量预警：
                  {
                    type: 'flex',
                    className: 'p-1',
                    items: [
                      {
                        type: 'container',
                        body: [
                          {
                            type: 'tpl',
                            tpl: '用量超过      ',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:4eab55d21f8f',
                          },
                          {
                            type: 'input-number',
                            label: '',
                            name: 'dosageMin4',
                            keyboard: true,
                            id: 'u:4f1fe98fb959',
                            step: 1,
                            size: 'sm',
                            mode: 'inline',
                            className: 'input-number',
                          },
                          {
                            type: 'tpl',
                            tpl: 't或低于',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:ee48e71f47a7',
                          },

                          {
                            type: 'input-number',
                            label: '',
                            name: 'dosageMax4',
                            keyboard: true,
                            id: 'u:be4ce49d4838',
                            step: 1,
                            size: 'sm',
                            mode: 'inline',
                            className: 'input-number',
                          },
                          {
                            type: 'tpl',
                            tpl: 't时预警。',
                            inline: true,
                            wrapperComponent: '',
                            id: 'u:6392720018ce',
                          },
                        ],
                        size: 'xs',
                        style: {
                          position: 'static',
                          display: 'block',
                          flex: '1 1 auto',
                          flexGrow: 1,
                          flexBasis: 'auto',
                        },
                        wrapperBody: false,
                        isFixedHeight: false,
                        isFixedWidth: false,
                        id: 'u:446774e379ab',
                      },
                    ],
                    style: {
                      position: 'relative',
                    },
                    id: 'u:8a5c34da5573',
                  },
                ],
                size: 'xs',
                style: {
                  position: 'static',
                  display: 'block',
                  flex: '1 1 auto',
                  flexGrow: 1,
                  flexBasis: 'auto',
                },
                wrapperBody: false,
                isFixedHeight: false,
                isFixedWidth: false,
                id: 'u:446774e379ab',
              },
            ],
          },
        ],
      },
    ],
  };
};
export const waringApi = {
  method: 'post',
  url: `energy/warn/getListPage`,
  headers: {
    token: `${token()}`,
  },
  requestAdaptor: (rest) => {
    // console.log(rest, 'rest');
    let { gmtCreate, ...other } = rest.data;
    rest.data.conditions.forEach((it) => {
      if (it.key === 'house_id') {
        it.values = it.values ? it.values?.split(',') : [];
      }
    });
    let datas = {
      ...other,
    };
    return {
      ...rest,
      data: datas,
    };
  },
  data: {
    businessCode: 'energy_warn',
    conditions: [
      {
        key: 'name',
        compareType: 'like',
        value: "${enterpriseName|default:''}",
      },
      {
        key: 'energy_type',
        compareType: 'like',
        value: "${energy_type|default:''}",
      },
      {
        key: 'handle_status',
        compareType: 'like',
        value: "${handle_status|default:''}",
      },
      {
        key: 'warn_status',
        compareType: 'like',
        value: "${warn_status|default:''}",
      },
      {
        key: 'house_id',
        compareType: 'like',
        values: "${house_id|default:''}",
      },
    ],
    pageNum: '${page|default:1}',
    pageSize: '${perPage|default:10}',
  },
  responseData: {
    '&': '$$',
    items: 'records',
  },
};

export const waringFilter = {
  title: '',
  submitText: '',
  controls: [
    {
      type: 'text',
      name: 'enterpriseName',
      label: '企业名称',
      size: 'md',
      placeholder: '请输入企业名称',
    },
    {
      type: 'nested-select',
      searchable: true, //可搜索
      multiple: true, //是否多选
      onlyChildren: true, //多选时，选中父节点时，是否只将其子节点加入到值中。
      maxTagCount: 1, //	标签的最大展示数量，超出数量后以收纳浮层的方式展示，仅在多选模式开启后生效
      name: 'house_id',
      label: '设备地址',
      multiple: true,
      hideNodePathLabel: true,
      size: 'md',
      source: {
        method: 'get',
        url: `houses/getBuildingTree`,
        adaptor: function (payload, response, api, context) {
          let list = payload.map((item) => {
            return {
              value: item.buildingId,
              label: item.buildingName,
              children: transformData(item.houseList),
            };
          });
          return {
            data: list,
            msg: '请求成功',
            status: 0,
          };
        },
      },
    },

    {
      type: 'select',
      name: 'warn_status',
      label: '预警状态',
      size: 'md',
      placeholder: '选择状态',
      // = 0 ? '异常' :'警告'}",
      options: [
        { label: '异常', value: '0' },
        { label: '警告', value: '1' },
      ],
    },
    {
      type: 'select',
      name: 'handle_status',
      label: '处理状态',
      size: 'md',
      placeholder: '选择状态',
      // 0 ? '未处理' :'已处理'}",
      options: [
        { label: '未处理', value: '0' },
        { label: '已处理', value: '1' },
      ],
    },
    {
      type: 'select',
      name: 'energy_type',
      label: '能耗类型',
      size: 'md',
      placeholder: '选择状态',
      options: [
        { label: '水', value: '0' },
        { label: '电', value: '1' },
      ],
    },
    {
      type: 'reset',
      label: '重置',
      //   "name": "gmtCreate",
      //   "onEvent": {
      //     "click": {
      //       "actions": [
      //         {
      //           "enterpriseName": "",
      //           "house_id": "",
      //           "warn_status": "",
      //           "handle_status": "",
      //           "energy_type": "",
      //         }
      //       ]
      //     }
      //   },
    },
    {
      type: 'submit',
      label: '搜索',
      primary: true,
    },
    {
      type: 'button',
      size: 'md',
      label: '预警设置',
      primary: true,
      actionType: 'drawer',
      drawer: formData('预警设置', 'insert'),
    },
  ],
  onSubmit: 'reload',
};
export const waringColumns = [
  {
    name: "${warn_status == 0 ? '异常' :'警告'}",
    label: '预警状态',
    type: 'text',
  },
  {
    name: "${handle_status == 0 ? '未处理' :'已处理'}",
    label: '处理状态',
    type: 'text',
  },
  {
    name: 'address',
    label: '设备地址',
    type: 'text',
  },
  {
    name: 'name',
    label: '所属企业',
    type: 'text',
  },
  {
    name: "${energy_type == 0 ? '水' :'电'}",
    label: '能耗类型',
    type: 'text',
  },
  {
    name: 'table_num',
    label: '表读数',
    type: 'text',
  },
  {
    name: 'filling_range',
    label: '读表时间范围',
    type: 'text',
  },
  {
    name: 'used',
    label: '能耗量',
    type: 'text',
  },

  {
    name: 'cost',
    label: '能耗费用',
    type: 'text',
  },
  {
    type: 'operation',
    label: '操作',
    width: 200,
    buttons: [
      {
        // "icon": "fa fa-edit",
        type: 'button',
        label: '处理记录',
        actionType: 'drawer',
        drawer: handleLog('处理记录', 'view'),
      },
      {
        // "icon": "fa fa-edit",
        type: 'button',
        level: 'primary',
        label: '处理',
        actionType: 'dialog',
        dialog: handleData('处理', 'insert'),
      },
      {
        // "icon": "fa fa-delete",
        type: 'button',
        level: 'danger',
        actionType: 'ajax',
        label: '删除',
        confirmText: '是否确认删除',
        api: {
          method: 'get',
          url: `energy/warn/delete`,
          data: {
            id: '${id}',
          },
        },
      },
    ],
  },
];
