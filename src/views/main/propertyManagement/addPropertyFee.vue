<script setup>
import AmisComponent from '@/components/amis/index.vue';
import ApprovalRecord from '@/components/dialog/ApprovalRecord.vue';
import { approvalListAPI } from '@/api/user';
import { ref, onMounted } from 'vue';
import { findIndex, min } from 'lodash';
import {
  getEnterpriseId,
  getPropertyTime,
  getLeaseBillDetail,
} from '@/api/lease';
const dialogVisible = ref(false);
const ApprovalRecordList = ref([]);
const canleDig = () => {
  ApprovalRecordList.value = [];
  dialogVisible.value = false;
};
const openExamine = async (item) => {
  const data = {
    businessScope: 'lease_property', //类型 企业、租金、租约
    approveId: item.data.id, //消息id
    //"relationId": item.data.id//原id
  };
  // const res = await approvalListAPI(data)
  ApprovalRecordList.value = data;
  dialogVisible.value = true;
};

const viewDraw = {
  size: 'lg',
  title: '查看',
  actions: [
    {
      type: 'button',
      actionType: 'confirm',
      label: '关闭',
      primary: true,
    },
  ],
  body: [
    {
      type: 'form',
      columnCount: 2,
      initApi: {
        method: 'get',
        url: '/approve/detail?id=${id}',
        adaptor: function (payload, response, api, context) {
          let transformedObj = payload.afterData;
          transformedObj.filePath = transformedObj.fileDetails;
          return {
            data: transformedObj,
            msg: '请求成功',
            status: 0,
          };
        },
      },
      body: [
        {
          type: 'input-text',
          name: 'enterpriseName',
          label: '企业名称',
          fixed: 'left',
          disabledOn: 'true',
        },
        {
          type: 'input-text',
          name: 'leaseBillName',
          label: '账单',
          width: 150,
          disabledOn: 'true',
        },
        {
          type: 'input-date',
          name: 'alreadyPayDate',
          visibleOn: '${alreadyPayDate}',
          label: '已缴至日期',
          disabledOn: 'true',
          valueFormat: 'YYYY-MM-DD',
        },
        {
          type: 'input-text',
          name: 'houseAreas',
          label: '租赁面积',
          disabledOn: 'true',
        },
        {
          type: 'input-text',
          name: 'leaseDate',
          label: '租房期限',
           width: 200,
          disabledOn: 'true',
        },
        {
          type: 'input-text',
          name: 'monthProperty',
          label: '物业费(元/平*月)',
          disabledOn: 'true',
        },
        {
          type: 'select',
          name: 'payType',
          label: '结算方式',
          multiple: true,
          disabledOn: 'true',
          options: [
            {
              label: '月付',
              value: '1',
            },
            {
              label: '季付',
              value: '2',
            },
            {
              label: '半年付',
              value: '5',
            },
            {
              label: '年付',
              value: '3',
            },
            {
              label: '一次性付清',
              value: '4',
            },
          ],
        },
        {
          type: 'input-text',
          name: 'payDateName',
          label: '收缴情况',
          width: 200,
          disabledOn: 'true',
        },

        {
          type: 'input-text',
          name: 'payProperty',
          label: '本次收缴金额（元）',
          disabledOn: 'true',
        },
        {
          type: 'input-date',
          name: 'payDate',
          required: true,
          label: '企业收缴日期',
          valueFormat: 'x',
          disabledOn: 'true',
        },
        {
          name: 'files',
          type: 'static-images',
          enlargeAble: 'true',
          label: '收缴凭证',
          disabledOn: 'true',
          source: '${files}',
        },
      ],
    },
  ],
};
// 物业费新增
const amisjson = {
  type: 'page',
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: '0px 0px 0px 0px transparent',
  },
  body: [
    {
      type: 'tpl',
      inline: true,
      wrapperComponent: '',
      syncLocation: false,
      id: 'u:3345e187f2df',
      style: {
        boxShadow: '0px 0px 0px 0px transparent',
        fontWeight: '500',
        lineHeight: '1.7',
        fontSize: '20px',
      },
    },
    {
      type: 'crud',
      syncLocation: false,
      name: 'tab-s',
      // 查询接口1
      api: {
        method: 'post',
        url: `approve/lease/property/own/page`,
        data: {
          enterpriseName: '${enterpriseName}',
          createUser: '${createUser}',
          gmtCreateStart: '${gmtCreateStart}',
          gmtCreateEnd: '${gmtCreateEnd}',
          statusList: '${status?[status]:[]}',
          pageNum: '${page}',
          pageSize: '${perPage}',
          operateType: 1,
        },
        adaptor: function (payload, response, api, context) {
          let payTypeList = [
            {
              label: '月付',
              value: '1',
            },
            {
              label: '季付',
              value: '2',
            },
            {
              label: '半年付',
              value: '5',
            },
            {
              label: '年付',
              value: '3',
            },
            {
              label: '一次性付清',
              value: '4',
            },
          ];
          let list = payload.records.map((item) => {
            item.filePath = item.fileDetails;
            item.afterData.payDate = item.afterData.payDate
              ? item.afterData.payDate.slice(0, 10)
              : '';
            return {
              ...item,
              ...item.afterData,
              leaseDate:
                item.afterData.leaseStartDate.slice(0, 10) +
                '至' +
                item.afterData.leaseEndDate.slice(0, 10),
              payDateName:
                item.afterData.payStartDate.slice(0, 10) +
                '至' +
                item.afterData.payEndDate.slice(0, 10),
              payTypeName: payTypeList.find(
                (it) => it.value == item.afterData.payType
              ).label,
            };
          });
          return {
            data: {
              ...payload,
              records: list,
            },
            msg: '请求成功',
            status: 0,
          };
        },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: 'table',
      // 表头1
      columns: [
        {
          type: 'operation',
          label: '企业名称',
          width: 200,
          fixed: 'left', // 固定在左侧
          buttons: [
            {
              type: 'button',
              level: 'link',
               size: 'md',
              label: '${enterpriseName}',
              // "disabledOn": "operateType === '3'",
              actionType: 'dialog',

              dialog: viewDraw,
            },
          ],
        },
        {
          name: 'afterData.leaseBillName',
          label: '账单名称',
          width: 200,
          type: 'text',
        },
        {
          name: 'leaseDate',
          label: '租房期限',
          width: 250,
          type: 'text',
        },
        {
          name: 'monthProperty',
          label: '物业费（元/平*月）',
          type: 'text',
        },
        {
          name: 'payDateName',
          label: '收缴情况',
          width: 250,
          type: 'text',
        },
        {
          name: 'payProperty',
          label: '本次收缴金额（元）',
          type: 'text',
        },
        {
          name: 'gmtCreate',
          label: '操作时间',
          width: 250,
          type: 'text',
        },
        {
          type: 'tpl',
          name: 'status',
          width: 150,
          label: '审批状态',
          tpl: "<span class='${status==5 ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200\" : (status==6 ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200\" : (status==7 ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200\" : (status==8 ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200\" : \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200\")))}'>${status==5 ? '待审批' : (status==6 ? '审批通过' : (status==7 ? '审批不通过' : (status==8 ? '执行失败' : '')))}</span>",
        },
        {
          type: 'operation',
          label: '操作',
          width: 280,
          fixed: 'right', // 固定在左侧
          buttons: [
            //查看
            {
              type: 'button',
              label: '查看',
              level: 'primary',
              actionType: 'dialog',
              dialog: viewDraw,
            },
            //审批记录
            {
              label: '审批记录',
              type: 'button',
              level: 'primary',
              onClick: (e, item) => {
                openExamine(item);
              },
            },
            //重新提交
            {
              type: 'button',
              label: '重新提交',
              level: 'primary',
              actionType: 'dialog',
              visibleOn: '${status==7}',
              dialog: {
                size: 'lg',
                title: '重新提交',
                actions: [
                  {
                    type: 'button',
                    actionType: 'cancel',
                    label: '取消',
                    level: 'default',
                    primary: true,
                  },
                  {
                    label: '确认',
                    actionType: 'confirm',
                    primary: true,
                    reload: 'tab-s',
                    close: true,
                    type: 'button',
                    api: {
                      method: 'post',
                      messages: {
                        success: '操作成功！',
                      },
                      url: `lease/property/approval/reSubmit`,
                      data: {
                        afterData: '$$',
                        id: '$id',
                      },
                      requestAdaptor: (rest) => {
                        let { ...other } = rest.data;
                        // console.log( rest,' rest.data')
                        let datas = {
                          leaseBillId: rest.body.leaseBillId, // 添加账单ID
                          ...other,

                          // leaseBillName :payload.leaseBill.billName; // 添加账单名称
                        };
                        if (Array.isArray(datas.filePath)) {
                          datas.filePath =
                            datas?.filePath
                              ?.map((item) => item.value)
                              ?.join(',') || '';
                        }
                        return {
                          ...rest,
                          data: datas,
                        };
                      },
                    },
                  },
                ],
                onSubmit: 'reload',
                //编辑表单1
                body: [
                  {
                    type: 'form',
                    columnCount: 2,
                    rules: [],
                    initApi: {
                      method: 'get',
                      url: '/approve/detail?id=${id}',
                      adaptor: function (payload, response, api, context) {
                        let transformedObj = payload.afterData;
                        transformedObj.filePath = transformedObj.fileDetails;
                        return {
                          data: transformedObj,
                          msg: '请求成功',
                          status: 0,
                        };
                      },
                    },
                    body: [
                      {
                        type: 'select',
                        name: 'enterpriseId',
                        label: '企业名称',
                        labelClassName: 'text-muted',
                        placeholder: '请选择企业',
                        labelField: 'name',
                        valueField: 'id',
                        searchable: true,
                        required: true,
                        disabledOn: 'true',
                        source: {
                          method: 'get',
                          url: `enterprise/listAllEnterprise`,
                        },
                      },
                      {
                        type: 'input-text',
                        name: 'uniCode',
                        label: '企业信用代码',
                        visibleOn: '${enterpriseId}',
                        disabledOn: 'true',
                        required: true,
                      },
                      {
                        type: 'hidden',
                        name: 'leaseBillId',
                        value: '${leaseBillId}',
                      },

                      {
                        type: 'input-text',
                        name: 'leaseBillName',
                        label: '账单',
                        disabledOn: 'true',
                        valueField: 'houseAreas',
                        required: true,
                      },
                      {
                        type: 'input-date',
                        name: 'alreadyPayDate',
                        label: '已缴至日期',
                        visibleOn: '${alreadyPayDate}',
                        disabledOn: 'true',
                        required: false,
                        valueFormat: 'Unix Millisecond Timestamp',
                      },
                      {
                        type: 'input-text',
                        name: 'houseAreas',
                        label: '租赁面积',
                        valueField: 'houseAreas',
                        required: true,
                      },
                      {
                        type: 'input-date-range',
                        name: 'leaseStartDate',
                        extraName: 'leaseEndDate',
                        label: '租房期限',
                        valueFormat: 'YYYY-MM-DD HH:mm:ss',
                      },
                      {
                        type: 'input-number',
                        name: 'monthProperty',
                        precision: 2,
                        step: 0.01,
                        max: 99999999.99,
                        label: '物业费(元/平*月)',
                        required: true,
                      },
                      {
                        type: 'select',
                        name: 'payType',
                        label: '结算方式',
                        required: true,
                        options: [
                          {
                            label: '月付',
                            value: '1',
                          },
                          {
                            label: '季付',
                            value: '2',
                          },
                          {
                            label: '半年付',
                            value: '5',
                          },
                          {
                            label: '年付',
                            value: '3',
                          },
                          {
                            label: '一次性付清',
                            value: '4',
                          },
                        ],
                      },
                      {
                        type: 'input-date-range',
                        name: 'payStartDate',
                        extraName: 'payEndDate',
                        label: '收缴情况',
                        onDisable: 'true',
                        valueFormat: 'YYYY-MM-DD HH:mm:ss',
                      },
                      {
                        type: 'input-number',
                        name: 'payProperty',
                        precision: 2,
                        step: 0.01,
                        label: '本次收缴金额（元）',
                        max: 99999999.99,
                        required: true,
                      },
                      {
                        type: 'input-date',
                        name: 'payDate',
                        required: true,
                        label: '企业收缴日期',
                        valueFormat: 'x',
                      },
                      {
                        type: 'input-file',
                        name: 'filePath',
                        label: '收缴凭证',
                        useChunk: false,
                        maxSize: 20990000,
                        accept: '.jpg,.png,.gif,.bmp,.jepg,.webp',
                        multiple: true,
                        maxLength: 5,
                        receiver: {
                          method: 'POST',
                          url: `upload`,
                        },
                      },
                    ],
                  },
                ],
              },
            },
            //删除
            {
              type: 'button',
              actionType: 'ajax',
              label: '删除',
              level: 'danger',
              visibleOn: '${status==6||status==7}',
              confirmText: '是否确认删除？',
              api: {
                method: 'get',
                url: 'approve/removeOwnApprove?id=${id}',
              },
            },
          ],
        },
      ],
      // 查询条件_
      alwaysShowPagination: true,
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      filter: {
        title: '',
        submitText: '搜索',
        controls: [
          {
            type: 'text',
            name: 'enterpriseName',
            size: 'md',
            label: '企业名称',
            placeholder: '请输入企业名称',
          },
          {
            type: 'text',
            name: 'createUser',
            size: 'md',
            label: '新增操作人',
            placeholder: '请输入新增操作人姓名',
          },
          {
            type: 'select',
            name: 'status',
            size: 'md',
            label: '审批状态',
            placeholder: '请选择审批状态',
            value: '',
            options: [
              {
                label: '全部',
                value: '',
              },
              {
                label: '待审批',
                value: '5',
              },
              {
                label: '审批通过',
                value: '6',
              },
              {
                label: '审批不通过',
                value: '7',
              },
            ],
          },
          {
            type: 'input-date-range',
            name: 'gmtCreateStart',
            extraName: 'gmtCreateEnd',
            size: 'md',
            label: '操作时间',
            onDisable: 'true',
            valueFormat: 'YYYY-MM-DD HH:mm:ss',
          },
          {
            type: 'reset',
            label: '重置',
          },
          {
            type: 'submit',
            label: '搜索',
            primary: true,
          },
          // 新增1
          {
            type: 'button',
            label: '新增',
            primary: true,
            actionType: 'dialog',
            dialog: {
              size: 'lg',
              title: '新增',
              data: {
                is_mount: 0,
              },
              actions: [
                {
                  type: 'button',
                  actionType: 'cancel',
                  label: '取消',
                  level: 'default',
                  primary: true,
                },
                {
                  label: '确认',
                  actionType: 'confirm',
                  primary: true,
                  reload: 'tab-s',
                  close: true,
                  type: 'button',
                  api: {
                    method: 'post',
                    messages: {
                      success: '操作成功！',
                    },
                    url: `lease/property/insertOrUpdate`,
                    requestAdaptor: (rest) => {
                        let {payPropertyCopy, ...restData } = rest.data;
                      let datas = {
                        ...restData,
                        leaseEndDate: new Date(
                          rest.data.leaseEndDate
                        ).getTime(),
                        leaseStartDate: new Date(
                          rest.data.leaseStartDate
                        ).getTime(),
                        payEndDate: new Date(rest.data.payEndDate).getTime(),
                        payStartDate: new Date(
                          rest.data.payStartDate
                        ).getTime(),
                        // leaseBillId:rest.data.leaseId
                      };
                        datas.alreadyPayDate = datas.alreadyPayDate === '' ? null : datas.alreadyPayDate;
                      return {
                        ...rest,
                        data: datas,
                      };
                    },
                    data: '$$',
                  },
                },
              ],
              onSubmit: 'reload',
              //新增表单1
              body: [
                {
                  type: 'form',
                  columnCount: 2,
                  rules: [
                    {
                      rule: 'data.payEndDate',
                      message: '收缴情况结束时间不能为空',
                      name: ['payEndDate'],
                    },
                    {
                      rule: 'data.leaseEndDate',
                      message: '租房期限结束时间不能为空',
                      name: ['leaseEndDate'],
                    },
                  ],
                  initFetch: false,
                  body: [
                    {
                      type: 'select',
                      name: 'enterpriseId',
                      label: '企业名称',
                      labelClassName: 'text-muted',
                      placeholder: '请选择企业',
                      labelField: 'name',
                      valueField: 'id',
                      searchable: true,
                      required: true,
                      source: {
                        method: 'get',
                        url: `enterprise/listAllEnterprise`,
                      },
                      onChange: function (value, oldValue, model, form) {
                        form.setValueByName('alreadyPayDate', '');
                        if (value) {
                          // 当企业选择改变时，获取企业详细信息
                          getEnterpriseId({
                            enterpriseId: value,
                          }).then(function (result) {
                            if (result && result.data.length > 0) {
                              const enterprise = result?.data?.[0];
                              form.setValueByName(
                                'uniCode',
                                enterprise.uniCode || ''
                              );
                            }
                          });
                          getPropertyTime({
                            enterpriseId: value,
                          }).then(function (res) {
                            if (res.status === '0') {
                              res.data !== true &&
                                form.setValueByName('alreadyPayDate', res.data);
                            }
                          });
                        } else {
                          form.setValueByName('uniCode', '');
                          form.setValueByName('alreadyPayDate', null);
                        }
                      },
                    },
                    {
                      type: 'input-text',
                      name: 'uniCode',
                      label: '企业信用代码',
                      visibleOn: '${enterpriseId}',
                      disabledOn: 'true',
                      required: true,
                    },
                    {
                      type: 'select',
                      name: 'leaseBillId',
                      label: '账单',
                      labelClassName: 'text-muted',
                      placeholder: '请选择账单',
                      labelField: 'billName',
                      valueField: 'id',
                      searchable: true,
                      visibleOn: '${uniCode}',
                      required: true,
                      source: {
                        method: 'get',
                        url: 'lease/queryAvailableLeaseBills?type=2&unicode=${uniCode}',
                      },
                      onChange: function (value, oldValue, model, form) {
                        // 当账单选择改变时，自动设置账单名称
                        if (value) {
                          getLeaseBillDetail({
                            leaseBillId: value,
                          }).then(function (result) {
                            if (result && result.data) {
                              let payload = result.data;

                              let payload0 = {};

                              payload0.houseAreas =
                                payload.leasePropertyAgree.areas;
                              payload0.leaseStartDate =
                                payload.leasePropertyAgree.moveInDate;
                              payload0.leaseEndDate =
                                payload.leasePropertyAgree.endDate;
                              payload0.payType =
                                payload.leasePropertyAgree.payType;
                              payload0.monthProperty =
                                payload.leasePropertyAgree.propertyCostsMonth;
                              payload0.payStartDate =
                                payload.leaseBill.billStartDate;
                              payload0.payEndDate =
                                payload.leaseBill.billEndDate;
                              payload0.payProperty =
                                payload.leaseBill.billAmount;
                              payload0.leaseBillName =
                                payload.leaseBill.billName; // 添加账单名称
                                payload0.payPropertyCopy = payload.leaseBill.billAmount;
                              for (let key in payload0) {

                                form.setValueByName(key, payload0[key]);
                              }
                            }
                          });
                        }
                      },
                    },
                    {
                      type: 'input-date',
                      name: 'alreadyPayDate',
                      label: '已缴至日期',
                      visibleOn: '${alreadyPayDate}',
                      required: false,
                      disabledOn: 'true',
                      valueFormat: 'YYYY-MM-DD',
                    },
                    {
                      type: 'input-number',
                      name: 'houseAreas',
                      label: '租赁面积',
                      valueField: 'houseAreas',
                      required: true,
                      precision: 2,
                      step: 1,
                      max: 99999999,
                      min: 0,
                    },
                    {
                      type: 'input-date-range',
                      name: 'leaseStartDate',
                      extraName: 'leaseEndDate',
                      label: '租房期限',
                      valueFormat: 'YYYY-MM-DD HH:mm:ss',
                      required: true,
                    },
                    {
                      type: 'input-number',
                      name: 'monthProperty',
                      precision: 2,
                      step: 0.01,
                      max: 99999999,
                      label: '物业费(元/平*月)',
                      required: true,
                      min: 0,
                    },
                    {
                      type: 'select',
                      name: 'payType',
                      label: '结算方式',
                      required: true,
                      options: [
                        {
                          label: '月付',
                          value: '1',
                        },
                        {
                          label: '季付',
                          value: '2',
                        },
                        {
                          label: '半年付',
                          value: '5',
                        },
                        {
                          label: '年付',
                          value: '3',
                        },
                        {
                          label: '一次性付清',
                          value: '4',
                        },
                      ],
                    },
                    {
                      type: 'input-date-range',
                      name: 'payStartDate',
                      minDate: '${alreadyPayDateNext}',
                      extraName: 'payEndDate',
                      label: '收缴情况',
                      valueFormat: 'YYYY-MM-DD HH:mm:ss',
                      required: true,
                    },
{
                      type: 'input-text',
                      name: 'payPropertyCopy',
                      label: '本次账单金额（元）',
                      disabledOn: 'true',
                    },
                    {
                      type: 'input-number',
                      name: 'payProperty',
                      precision: 2,
                      step: 1,
                   max: '${payPropertyCopy || 99999999.99}',
                      label: '本次收缴金额（元）',
                      required: true,
                      min: 0,
                    },
                    {
                      type: 'input-date',
                      name: 'payDate',
                      required: true,
                      label: '企业收缴日期',
                      valueFormat: 'x',
                    },
                    {
                      type: 'input-file',
                      name: 'filePath',
                      label: '收缴凭证',
                      //downloadUrl: false,
                      useChunk: false,
                      maxSize: 20990000,
                      accept: '.jpg,.png,.gif,.bmp,.jepg,.webp',
                      multiple: true,
                      maxLength: 5,
                      receiver: {
                        method: 'POST',
                        url: `upload`,
                      },
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  ],
};
</script>

<template>
  <div v-if="!!amisjson" class="zhengce publicTableStyle">
    <AmisComponent :amisjson="amisjson" />
    <ApprovalRecord
      v-if="dialogVisible"
      :dialogVisible="dialogVisible"
      @canleDig="canleDig"
      :data="ApprovalRecordList"
    ></ApprovalRecord>
  </div>
</template>
<style scoped lang="scss">
.tab {
  padding-top: 20px;
  padding-left: 10px;
}

.on {
  background-color: #169bd5;
  color: #fff;
}
</style>
