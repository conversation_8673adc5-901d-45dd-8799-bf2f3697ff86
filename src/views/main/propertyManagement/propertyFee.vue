<script setup>
import AmisComponent from '@/components/amis/index.vue';
import { exportStatisticsList, exportEnterpriseList } from '@/api/updata';
import { ref, onMounted } from 'vue';
import ApprovalRecord from '@/components/dialog/ApprovalRecord.vue';
import dayjs from 'dayjs';
const dialogVisible = ref(false);
const ApprovalRecordList = ref([]);
const canleDig = () => {
  ApprovalRecordList.value = [];
  dialogVisible.value = false;
};
const openExamine = async (item) => {
  const data = {
    businessScope: 'lease_property', //类型 企业、租金、租约
    relationId: item.data.id,
  };
  // const res = await approvalListAPI(data)
  ApprovalRecordList.value = data;
  dialogVisible.value = true;
};
const checkDetial = {
  size: 'xl',
  title: ' 查看账单 - ${name}',
  actions: [
    {
      type: 'button',
      actionType: 'confirm',
      label: '关闭',
      primary: true,
    },
  ],
  body: [
    {
      type: 'crud',
      api: {
        method: 'get',
        url: `lease/queryAllLeaseBills`,
        data: {
          unicode: '${uniCode}',
          type: 2,
          // enterpriseName: '${name}',
          // pageNum: '${page|default:1}',
          // pageSize: '${perPage|default:10}',
        },
      },
      columns: [
        {
          name: 'billName',
          label: '账单名称',
          type: 'text',
        },
        {
          name: 'billStartDate',
          label: '账单起始时间',
          type: 'text',
        },
        {
          name: 'billEndDate',
          label: '账单结束时间',
          type: 'text',
        },
        {
          name: 'billAmount',
          label: '收缴金额（元）',
          type: 'text',
        },

        {
          name: "${['待支付','已全部缴纳','已部分缴纳'][payStatus]||'-'}",
          label: '账单处理情况',
          type: 'tpl',
          tpl: '<span class="inline-block px-2 py-1 text-xs font-medium rounded-full ${payStatus == 0 ? "bg-yellow-100 text-yellow-800" : payStatus == 1 ? "bg-green-100 text-green-800" : payStatus == 2 ? "bg-blue-100 text-blue-800" : "bg-gray-100 text-gray-800"}">${["待支付","已全部缴纳","已部分缴纳"][payStatus]||"-"}</span>',
        },
      ],
      perPage: 10,
      perPageAvailable: [10, 20],
      alwaysShowPagination: true,
    },
  ],
};
let amisjson = ref(null);
const dialogBody = [
  {
    type: 'input-text',
    name: 'enterpriseName',
    label: '企业名称',
    disabledOn: 'true',
  },
  {
    name: 'leaseBillName',
    label: '账单',
    type: 'input-text',
    disabledOn: 'true',
  },
  {
    type: 'input-text',
    name: 'uniCode',
    label: '企业信用代码',
    disabledOn: 'true',
  },
  {
    type: 'input-text',
    name: 'houseAreas',
    label: '租赁面积',
    disabledOn: 'true',
  },
  {
    type: 'input-text',
    name: 'leaseDate',
    label: '租房期限',
    disabledOn: 'true',
  },
  {
    type: 'input-text',
    name: 'monthProperty',
    label: '物业费(元/平*月)',
    disabledOn: 'true',
  },
  {
    type: 'select',
    name: 'payType',
    label: '结算方式',
    multiple: true,
    disabledOn: 'true',
    options: [
      {
        label: '月付',
        value: '1',
      },
      {
        label: '季付',
        value: '2',
      },
      {
        label: '半年付',
        value: '5',
      },
      {
        label: '年付',
        value: '3',
      },
      {
        label: '一次性付清',
        value: '4',
      },
    ],
  },
  {
    type: 'input-text',
    name: 'payDateName',
    label: '收缴情况',
    disabledOn: 'true',
  },
  {
    type: 'input-date',
    name: 'alreadyPayDate',
    label: '已缴至日期',
    visibleOn: '${alreadyPayDate}',
    disabledOn: 'true',
    valueFormat: 'YYYY-MM-DD',
  },
  {
    type: 'input-text',
    name: 'payProperty',
    label: '本次收缴金额（元）',
    disabledOn: 'true',
  },
  {
    type: 'input-date',
    name: 'payDate',
    required: true,
    disabledOn: 'true',
    label: '企业收缴日期',
    valueFormat: 'x',
  },
  {
    name: 'files',
    type: 'static-images',
    enlargeAble: 'true',
    label: '收缴凭证',
    disabledOn: 'true',
    source: '${files}',
  },
];
// 物业费管理
const amisjson3 = {
  type: 'page',
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: '0px 0px 0px 0px transparent',
  },
  body: [
    {
      type: 'tpl',
      inline: true,
      wrapperComponent: '',
      syncLocation: false,
      id: 'u:3345e187f2df',
      style: {
        boxShadow: '0px 0px 0px 0px transparent',
        fontWeight: '500',
        lineHeight: '1.7',
        fontSize: '20px',
      },
    },
    {
      type: 'crud',
      syncLocation: false,
      name: 'tab-s',
      // 查询接口1
      api: {
        method: 'post',
        url: `lease/property/page`,
        data: {
          enterpriseName: '${enterpriseName}',
          uniCode: '${uniCode}',
          payType: '${payType}',
          pageNum: '${page}',
          pageSize: '${perPage}',
        },
        adaptor: function (payload, response, api, context) {
          let payTypeList = [
            {
              label: '月付',
              value: '1',
            },
            {
              label: '季付',
              value: '2',
            },
            {
              label: '半年付',
              value: '5',
            },
            {
              label: '年付',
              value: '3',
            },
            {
              label: '一次性付清',
              value: '4',
            },
          ];
          let list = payload.records.map((item) => {
            item.filePath = item.fileDetails;
            // item.payDate = item.payDate ? item.payDate.slice(0, 10) : '';
            return {
              ...item,
              leaseDate:
                item.leaseStartDate.slice(0, 10) +
                '至' +
                item.leaseEndDate.slice(0, 10),
              payDateName:
                item.payStartDate.slice(0, 10) +
                '至' +
                item.payEndDate.slice(0, 10),
              payTypeName: payTypeList.find((it) => it.value == item.payType)
                .label,
            };
          });
          return {
            data: {
              ...payload,
              records: list,
            },
            msg: '请求成功',
            status: 0,
          };
        },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: 'table',
      // 表头1
      columns: [
        {
          type: 'operation',
          label: '企业名称',
          width: 200,
          fixed: 'left',
          buttons: [
            {
              name: 'enterpriseName',
              label: '${enterpriseName}',
              type: 'button',
              level: 'link',
               size: 'md',
              actionType: 'dialog',
              dialog: {
                size: 'lg',
                title: '详情',
                actions: [
                  {
                    type: 'button',
                    actionType: 'confirm',
                    label: '关闭',
                    primary: true,
                  },
                ],
                body: [
                  {
                    type: 'form',
                    columnCount: 2,
                    body: dialogBody,
                  },
                ],
              },
            },
          ],
        },
        {
          name: 'leaseBillName',
          label: '账单',
          width: 140,
          type: 'text',
        },
        {
          name: 'payDate',
          label: '企业收缴日期',
          type: 'date',
        },

        {
          name: 'houseAreas',
          label: '租赁面积',
          type: 'text',
        },
        {
          name: 'leaseDate',
          label: '租房期限',
          width: 200,
          type: 'text',
        },
        {
          name: 'monthProperty',
          label: '物业费(元/平*月)',
          type: 'text',
        },
        // {
        //   name: 'payTypeName',
        //   label: '结算方式',
        //   type: 'text',
        //         width: 140,
        // },
        {
          name: 'payTypeName',
          label: '结算方式',
          width: 140,
          type: 'tpl',
          tpl: '<span class="inline-block px-2 py-1 text-xs font-medium rounded-full ${payTypeName == "月付" ? "bg-blue-100 text-blue-800" : payTypeName == "季付" ? "bg-green-100 text-green-800" : payTypeName == "半年付" ? "bg-yellow-100 text-yellow-800" : payTypeName == "年付" ? "bg-purple-100 text-purple-800" : payTypeName == "一次性付清" ? "bg-red-100 text-red-800" : "bg-gray-100 text-gray-800"}">${payTypeName}</span>',
        },
        {
          name: 'payDateName',
          label: '收缴情况',
          width: 200,
          type: 'text',
        },
        {
          name: 'payProperty',
          label: '本次收缴金额（元）',
          type: 'text',
        },
        {
          type: 'operation',
          label: '操作',
          width: 280,
          fixed: 'right', // 固定在右侧
          buttons: [
            //查看1
            {
              type: 'button',
              label: '查看',
              level: 'primary',
              actionType: 'dialog',
              dialog: {
                size: 'lg',
                title: '详情',
                actions: [
                  {
                    type: 'button',
                    actionType: 'confirm',
                    label: '关闭',
                    primary: true,
                  },
                ],
                body: [
                  {
                    type: 'form',
                    columnCount: 2,
                    body: dialogBody,
                  },
                ],
              },
            },
            //审批记录
            {
              label: '审批记录',
              type: 'button',
              level: 'primary',
              // "actionType": "link",
              // "link": "#/workbench/lease/addfollow?id=$id",
              onClick: (e, item) => {
                openExamine(item);
              },
            },
            //编辑1
            {
              type: 'button',
              label: '编辑',
              level: 'primary',
              actionType: 'dialog',
              visibleOn: '${isEditable}',
              dialog: {
                size: 'lg',
                title: '编辑',
                actions: [
                  {
                    type: 'button',
                    actionType: 'cancel',
                    label: '取消',
                    level: 'default',
                    primary: true,
                  },
                  {
                    label: '确认',
                    actionType: 'confirm',
                    primary: true,
                    reload: 'tab-s',
                    close: true,
                    type: 'button',
                    confirmText:
                      '点击编辑，经审批人审批后该企业物业费记录进行信息更正，审批进程可在查看-审批记录中进行查看。是否确认修改？',
                    api: {
                      method: 'post',
                      messages: {
                        success: '操作成功！',
                      },
                      url: `lease/property/insertOrUpdate`,
                      data: {
                        '&': '$$',
                        id: '$id',
                      },
                      requestAdaptor: (rest) => {
                        let { ...other } = rest.data;
                        let datas = {
                          ...other,
                        };
                        if (Array.isArray(datas.filePath)) {
                          datas.filePath =
                            datas?.filePath
                              ?.map((item) => item.value)
                              ?.join(',') || '';
                        }
                        return {
                          ...rest,
                          data: datas,
                        };
                      },
                    },
                  },
                ],
                onSubmit: 'reload',
                //编辑表单1
                body: [
                  {
                    type: 'form',
                    columnCount: 2,
                    rules: [],
                    body: [
                      {
                        type: 'select',
                        name: 'enterpriseId',
                        label: '企业名称',
                        labelClassName: 'text-muted',
                        placeholder: '请选择企业',
                        labelField: 'name',
                        valueField: 'id',
                        disabledOn: 'true',
                        searchable: true,
                        required: true,
                        source: {
                          method: 'get',
                          url: `enterprise/listAllEnterprise`,
                        },
                      },
                      {
                        type: 'input-text',
                        name: 'leaseBillName',
                        label: '账单',
                        disabledOn: 'true',
                      },
                      {
                        type: 'input-text',
                        name: 'leaseBillId',
                        label: '账单',
                        disabledOn: 'true',
                        hidden: true,
                      },
                      {
                        type: 'input-text',
                        name: 'uniCode',
                        label: '企业信用代码',
                        visibleOn: '${enterpriseId}',
                        disabledOn: 'true',
                        required: true,
                        source: {
                          method: 'get',
                          url: 'enterprise/listAllEnterprise?enterpriseId=${enterpriseId}',
                          adaptor: function (payload, response, api, context) {
                            let data = {};
                            payload.map((item, index) => {
                              if (index == 0) {
                                data = {
                                  label: 'uniCode',
                                  value: item.uniCode,
                                };
                              }
                              return item;
                            });
                            return {
                              data,
                              msg: '请求成功',
                              status: 0,
                            };
                          },
                        },
                      },
                      {
                        type: 'input-text',
                        name: 'houseAreas',
                        label: '租赁面积',
                        valueField: 'houseAreas',
                        required: true,
                      },
                      {
                        type: 'input-date-range',
                        name: 'leaseStartDate',
                        extraName: 'leaseEndDate',
                        label: '租房期限',
                        valueFormat: 'YYYY-MM-DD HH:mm:ss',
                      },
                      {
                        type: 'input-number',
                        name: 'monthProperty',
                        precision: 2,
                        step: 0.01,
                        max: 99999999.99,
                        label: '物业费(元/平*月)',
                        required: true,
                      },
                      {
                        type: 'select',
                        name: 'payType',
                        label: '结算方式',
                        required: true,
                        options: [
                          {
                            label: '月付',
                            value: '1',
                          },
                          {
                            label: '季付',
                            value: '2',
                          },
                          {
                            label: '半年付',
                            value: '5',
                          },
                          {
                            label: '年付',
                            value: '3',
                          },
                          {
                            label: '一次性付清',
                            value: '4',
                          },
                        ],
                      },
                      {
                        type: 'input-date-range',
                        name: 'payStartDate',
                        extraName: 'payEndDate',
                        label: '收缴情况',
                        onDisable: 'true',
                        valueFormat: 'YYYY-MM-DD HH:mm:ss',
                      },
                      {
                        type: 'input-date',
                        name: 'alreadyPayDate',
                        label: '已缴至日期',
                        visibleOn: '${alreadyPayDate}',
                        required: false,
                        disabledOn: 'true',
                        valueFormat: 'YYYY-MM-DD',
                      },
                      {
                        type: 'input-date',
                        name: 'payDate',
                        required: true,
                        label: '企业收缴日期',
                        valueFormat: 'YYYY-MM-DD HH:mm:ss',
                        // valueFormat: 'x',
                      },
                      {
                        type: 'input-text',
                        name: 'billAmount',
                        label: '本次账单总金额（元）',
                        disabledOn: 'true',
                      },
                      {
                        type: 'input-number',
                        name: 'payProperty',
                        precision: 2,
                        step: 0.01,
                        min: 0,
                        max: '${billAmount || 99999999.99}',
                        label: '本次收缴金额（元）',
                        required: true,
                      },
                      {
                        type: 'input-file',
                        name: 'filePath',
                        label: '收缴凭证',
                        //downloadUrl: false,
                        useChunk: false,
                        maxSize: 20990000,
                        accept: '.jpg,.png,.gif,.bmp,.jepg,.webp',
                        multiple: true,
                        maxLength: 5,
                        receiver: {
                          method: 'POST',
                          url: `upload`,
                        },
                      },
                    ],
                  },
                ],
              },
            },
            //删除1
            {
              type: 'button',
              actionType: 'ajax',
              label: '删除',
              level: 'danger',
              visibleOn: '${isEditable}',
              confirmText:
                '点击删除，经审批人审批后该企业物业费记录进行删除，审批进程可在查看-审批记录中进行查看。是否确认删除？',
              api: {
                method: 'get',
                url: 'lease/property/removeById?id=${id}',
              },
            },
          ],
        },
      ],
      // 查询条件_
      alwaysShowPagination: true,
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      filter: {
        title: '',
        submitText: '搜索',
        controls: [
          {
            type: 'text',
            name: 'enterpriseName',
            size: 'md',
            label: '企业名称',
            placeholder: '请输入企业名称',
          },
          {
            type: 'select',
            name: 'payType',
            size: 'md',
            label: '结算方式',
            placeholder: '请选择结算方式',
            value: '',
            options: [
              {
                label: '全部',
                value: '',
              },
              {
                label: '月付',
                value: '1',
              },
              {
                label: '季付',
                value: '2',
              },
              {
                label: '半年付',
                value: '5',
              },
              {
                label: '年付',
                value: '3',
              },
              {
                label: '一次性付清',
                value: '4',
              },
            ],
          },
          {
            type: 'reset',
            label: '重置',
          },
          {
            type: 'submit',
            label: '搜索',
            primary: true,
          },
        ],
      },
    },
  ],
};
// 园区月度汇总
const amisjson2 = {
  type: 'page',
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: '0px 0px 0px 0px transparent',
  },
  body: [
    // {
    //   type: 'tpl',
    //   inline: true,
    //   wrapperComponent: '',
    //   syncLocation: false,
    //   style: {
    //     boxShadow: '0px 0px 0px 0px transparent',
    //     fontWeight: '500',
    //     lineHeight: '1.7',
    //     fontSize: '20px',
    //   },
    // },
    {
      type: 'crud',
      syncLocation: false,
      name: 'tab-s',
      api: {
        method: 'post',
        url: `org/statistics/page`,
        data: {
          pageNum: '${page}',
          pageSize: '${perPage}',
          type: '0',
          // startTimeStamp: new Date(rest.data.startTimeStamp ).getTime(),
          //   endTimeStamp: new Date(rest.data.endTimeStamp ).getTime(),
          startTimeStamp: '${startTime}',
          endTimeStamp: '${endTime}',
        },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: 'table',
      // 在表格头部添加汇总统计
      headerToolbar: [
        // 'filter-toggler',
        'columns-toggler',
        {
          type: 'service',
          api: {
            method: 'post',
            url: `org/statistics/stat`,
            requestAdaptor: (rest) => {
              let datas = {
                ...rest.data,
                startTimeStamp: rest.data.startTimeStamp,
                endTimeStamp: rest.data.endTimeStamp,
              };
              return {
                ...rest,
                data: datas,
              };
            },
            adaptor: function (payload, response, api, context) {
              let data = {
                endDate:
                  response?.data?.endDate ||
                  dayjs(Number(api?.body?.endTimeStamp)).format('YYYY-MM-DD'),
                startDate:
                  response?.data?.startDate ||
                  dayjs(Number(api?.body?.startTimeStamp)).format('YYYY-MM-DD'),
                value: response?.data?.value || 0,
              };
              return {
                data,
                msg: '请求成功',
                status: 0,
              };
            },
            data: {
              type: '0',
              startTimeStamp: '${startTime}',
              endTimeStamp: '${endTime}',
            },
            trackExpression: '${startTime}-${endTime}', // 监听时间变化
          },
          body: [
            {
              type: 'tpl',
              tpl: '<div style="margin: 10px 0;"><div style="display: flex; align-items: center; justify-content: space-between;"><div style="font-size: 18px;">收缴时间：<span style="color: #3571FF;">${startDate}至${endDate}</span> ；</div><div style="font-size: 18px;">收缴总金额：<span style="color: #3571FF; font-weight: bold;">${value}</span> 元</div> </div></div>',
            },
          ],
        },
      ],
      columns: [
        {
          name: 'name',
          width: 500,
          label: '统计名称',
          type: 'text',
        },
        {
          name: 'time',
          label: '时间',
          width: 200,
          type: 'text',
        },
        {
          name: 'value',
          label: '收缴金额（元）',
          type: 'text',
        },
      ],
      alwaysShowPagination: true,
      autoFillHeight: {
        height: 450,
      },
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      filter: {
        title: '',
        submitText: '搜索',
        controls: [
          {
            type: 'input-date-range',
            name: 'startTime',
            extraName: 'endTime',
            size: 'md',
            label: '时间范围',
            placeholder: '请选择时间范围',
            valueFormat: 'x',
          },
          // {
          //   type: 'reset',
          //   label: '重置',
          // },
          {
            type: 'submit',
            label: '搜索',
            primary: true,
          },
          {
            type: 'button',
            label: '导出',
            onClick: function (e, filters) {
              exportStatisticsList({
                startTimeStamp: filters.data.startTime,
                endTimeStamp: filters.data.endTime,
                type: '0',
              }).then((res) => {
                let blob = new Blob([res], {
                  type: 'text/csv,charset=UTF-8',
                });
                let objectUrl = URL.createObjectURL(blob);
                const fileName =
                  window.document.title +
                  '所有企业物业费收缴总金额（月度）.xlsx';
                const downloadLink = document.createElement('a');
                downloadLink.href = objectUrl;
                downloadLink.download = fileName;
                downloadLink.click();
              });
            },
          },
        ],
      },
    },
  ],
};
// 企业收缴总额
const amisjson1 = {
  type: 'page',
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: '0px 0px 0px 0px transparent',
  },
  body: [
    {
      type: 'tpl',
      inline: true,
      wrapperComponent: '',
      syncLocation: false,
      style: {
        boxShadow: '0px 0px 0px 0px transparent',
        fontWeight: '500',
        lineHeight: '1.7',
        fontSize: '20px',
      },
    },
    {
      // 查3
      type: 'crud',
      syncLocation: false,
      name: 'tab-s',
      api: {
        method: 'post',
        url: `lease/property/enterprise/page`,
        data: {
          pageNum: '${page}',
          pageSize: '${perPage}',
          enterpriseName: '${enterpriseName}',
          status: '${status}',
          uniCode: '${uniCode}',
          startDate: '${startTime}',
          endDate: '${endTime}',
        },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: 'table',
      // 在表格头部添加汇总统计
      // headerToolbar: [
      //   // 'filter-toggler',
      //   'columns-toggler',
      //   {
      //     type: 'service',
      //     api: {
      //       method: 'post',
      //       url: `org/statistics/stat`,
      //       requestAdaptor: (rest) => {
      //         let datas = {
      //           ...rest.data,
      //           startTimeStamp: rest.data.startTimeStamp,
      //           endTimeStamp: rest.data.endTimeStamp,
      //         };
      //         return {
      //           ...rest,
      //           data: datas,
      //         };
      //       },
      //       adaptor: function (payload, response, api, context) {
      //         let data = {
      //           endDate:
      //             response?.data?.endDate ||
      //             dayjs(Number(api?.body?.endTimeStamp)).format('YYYY-MM-DD'),
      //           startDate:
      //             response?.data?.startDate ||
      //             dayjs(Number(api?.body?.startTimeStamp)).format('YYYY-MM-DD'),
      //           value: response?.data?.value || 0,
      //         };
      //         return {
      //           data,
      //           msg: '请求成功',
      //           status: 0,
      //         };
      //       },
      //       data: {
      //         type: '0',
      //         startTimeStamp: '${startTime}',
      //         endTimeStamp: '${endTime}',
      //       },
      //       trackExpression: '${startTime}-${endTime}', // 监听时间变化
      //     },
      //     body: [
      //       {
      //         type: 'tpl',
      //         tpl: '<div style="margin: 10px 0;"><div style="display: flex; align-items: center; justify-content: space-between;"><div style="font-size: 18px;">收缴时间：<span style="color: #3571FF;">${startDate}至${endDate}</span></div> </div></div>',
      //       },
      //     ],
      //   },
      // ],
      columns: [
        {
          type: 'operation',
          label: '企业名称',
          width: 200,
          buttons: [
            //查看1
            {
              name: 'name',
              label: '${name}',
              type: 'button',
              level: 'link',
               size: 'md',
              actionType: 'dialog',
              dialog: checkDetial,
            },
          ],
        },
        // {
        //   name: 'name',
        //   width: 500,
        //   label: '企业名称',
        //   type: 'text',
        // },
        {
          name: 'uniCode',
          label: '社会信用代码',
          width: 300,
          type: 'text',
        },
        // TODO：
        {
          name: "${status?[ '-', '正常' ,'欠费'][status]:'-'}",
          label: '账单状态',
          type: 'text',
        },
        {
          name: 'value',
          label: '收缴物业费总金额(元)',
          type: 'text',
        },

        {
          name: 'lastUpdate',
          label: '最近一次更新时间',
          width: 300,
          type: 'text',
        },
        {
          type: 'operation',
          label: '操作',
          fixed: 'right', // 固定在右侧
          width: 150,
          buttons: [
            {
              type: 'button',
              label: '查看账单',
              level: 'primary',
              actionType: 'dialog',
              dialog: checkDetial,
            },
          ],
        },
      ],
      // 查询条件_
      alwaysShowPagination: true,
      autoFillHeight: {
        height: 450,
      },
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      filter: {
        title: '',
        submitText: '搜索',
        controls: [
          {
            type: 'text',
            name: 'enterpriseName',
            size: 'md',
            label: '企业名称',
            placeholder: '请输入企业名称',
          },

          {
            type: 'select',
            name: 'status',
            label: '账单状态',
            size: 'md',
            placeholder: '请选择账单状态',
            options: [
              {
                label: '全部',
                value: '',
              },
              {
                label: '正常',
                value: '1',
              },
              {
                label: '欠费',
                value: '2',
              },
            ],
          },
          // {
          //   type: 'input-date-range',
          //   name: 'startTime',
          //   extraName: 'endTime',
          //   size: 'md',
          //   label: '企业收缴日期',
          //   placeholder: '请选择时间范围',
          //   valueFormat: 'x',
          // },
          {
            type: 'reset',
            label: '重置',
          },
          {
            type: 'submit',
            label: '搜索',
            primary: true,
          },
          {
            type: 'button',
            label: '导出',
            actionType: 'dialog',
            dialog: {
              title: '导出设置',
              size: 'default',
              actions: [
                {
                  type: 'button',
                  actionType: 'cancel',
                  label: '取消',
                  level: 'default',
                },
                {
                  type: 'button',
                  actionType: 'confirm',
                  label: '确认导出',
                  primary: true,
                  onAction: function (e, props, data) {
                    exportEnterpriseList({
                      enterpriseName: data?.enterpriseName || null,
                      payType: data?.status || null,
                      startDate: data?.startTime || null,
                      endDate: data?.endTime || null,
                      type: '0',
                    })
                      .then((res) => {
                        let blob = new Blob([res], {
                          type: 'text/csv,charset=UTF-8',
                        });
                        let objectUrl = URL.createObjectURL(blob);
                        const fileName = '企业物业费收缴总额数据.xlsx';
                        const downloadLink = document.createElement('a');
                        downloadLink.href = objectUrl;
                        downloadLink.download = fileName;
                        downloadLink.click();
                      })
                      .catch((error) => {
                        console.error('导出失败:', error);
                      });
                  },
                },
              ],
              body: [
                {
                  type: 'form',
                  size: 'md',
                  body: [
                    {
                      type: 'input-text',
                      // size: 'md',
                      name: 'enterpriseName',
                      label: '企业名称',
                      placeholder: '请输入企业名称',
                    },
                    {
                      type: 'select',
                      name: 'status',
                      // size: 'md',
                      label: '账单状态',
                      placeholder: '请选择账单状态',
                      options: [
                        {
                          label: '全部',
                          value: '',
                        },
                        {
                          label: '正常',
                          value: '1',
                        },
                        {
                          label: '欠费',
                          value: '2',
                        },
                      ],
                    },
                    {
                      type: 'input-date-range',
                      name: 'startTime',
                      extraName: 'endTime',
                      // size: 'md',
                      label: '企业收缴日期',
                      placeholder: '请选择时间范围',
                      valueFormat: 'x',
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
  ],
};
onMounted(() => {
  let tabs = {
    type: 'tabs',
    unmountOnExit: true,
    // "tabsMode": "radio",
    tabs: [
      {
        title: '物业费管理',
        body: amisjson3,
      },
      {
        title: '园区月度汇总',
        body: amisjson2,
        className: 'message2',
      },
      {
        title: '企业收缴汇总',
        body: amisjson1,
      },
    ],
    id: 'u:238ba61a4079',
    className: 'tabsClass',
    linksClassName: 'tabsTitle',
  };
  amisjson.value = tabs;
});
</script>

<template>
  <div v-if="!!amisjson" class="zhengce publicTableStyle">
    <AmisComponent :amisjson="amisjson" />
    <ApprovalRecord
      v-if="dialogVisible"
      :dialogVisible="dialogVisible"
      @canleDig="canleDig"
      :data="ApprovalRecordList"
    ></ApprovalRecord>
  </div>
</template>
<style scoped lang="scss">
.tab {
  padding-top: 20px;
  padding-left: 10px;
}

.on {
  background-color: #169bd5;
  color: #fff;
}
</style>
