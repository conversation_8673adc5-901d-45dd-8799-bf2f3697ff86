<script  setup>
// import HelloWorld from './components/HelloWorld.vue'
// import TheWelcome from './components/TheWelcome.vue'
import AmisComponent from "@/components/amis/index.vue";
import { perFix, baseEvn, token } from "@/utils/utils";
import { downloadAll } from "@/api/updata";
import store from "@/store";
import { transformData } from "@/utils/formData";
import { ref, onMounted } from "vue";
let pitchId = ref(1);
const tabList = ref([
  {
    name: "楼栋列表",
    id: 1,
  },
  {
    name: "房源列表",
    id: 2,
  },
]);

function changeId(id) {
  if (pitchId.value != id) {
    //store.commit('user/tokenChange','')
    pitchId.value = id;
  }
}
// 楼栋列表
const amisjson3 = {
  type: "page",
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: "0px 0px 0px 0px transparent",
  },
  body: [
    {
      type: "tpl",
      inline: true,
      wrapperComponent: "",
      syncLocation: false,
      id: "u:3345e187f2df",
      style: {
        boxShadow: "0px 0px 0px 0px transparent",
        fontWeight: "500",
        lineHeight: "1.7",
        fontSize: "20px",
      },
    },
    {
      type: "crud",
      syncLocation: false,
      name: "tab-s",
      api: {
        method: "post",
        url: `building/getListPage`,
        data: {
          businessCode: "building",
          conditions: [
            {
              key: "name",
              compareType: "like",
              value: "${name}",
            },
          ],
          pageNum: "${page}",
          pageSize: "${perPage}",
        },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: "table",
      columns: [
        {
          name: "name",
          label: "楼栋名称",
          type: "text",
        },
        {
          name: "code",
          label: "楼栋编号",
          type: "text",
        },
        {
          name: "floor",
          label: "层数",
          type: "text",
        },
        {
          name: "total_area",
          label: "建筑总面积(㎡)",
          type: "text",
        },
        {
          type: "tpl",
          label: "楼栋种类",
          tpl: "<span class='${type==\"办公楼\" ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200\" : (type==\"生产楼\" ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200\" : \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 border border-purple-200\")}'>${type}</span>",
        },
        {
          type: "operation",
          label: "操作",
          fixed: "right", // 固定在右侧
          width: 200,
          buttons: [
            {
              type: "button",
              actionType: "ajax",
              label: "删除",
              level: "danger",
              confirmTitle: "提示",
              confirmText: "删除楼栋会删除该楼栋所有房源，确认进行删除吗？",
              api: {
                method: "get",
                url: `building/customDelete`,
                data: {
                  id: "${id}",
                },
              },
              messages: {
                success: "删除成功！",
              },
            },
          ],
        },
      ],
      alwaysShowPagination: true,
      autoFillHeight: false,
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      filter: {
        title: "",
        submitText: "搜索",
        controls: [
          {
            type: "text",
            name: "name",
            size: "md",
            label: "楼栋名称",
            placeholder: "请输入楼栋名称",
          },
          {
            type: "reset",
            label: "重置",
          },
          {
            type: "submit",
            label: "搜索",
            primary: true,
          },
          {
            type: "button",
            size: "md",
            label: "新增楼栋",
            actionType: "dialog",
            dialog: {
              title: "新增楼栋",
              data: {
                everyFloorCount: 1,
              },
              actions: [
                {
                  type: "button",
                  size: "md",
                  level: "default",
                  actionType: "cancel",
                  label: "取消",
                  primary: true,
                },
                {
                  label: "确认",
                  actionType: "confirm",
                  primary: true,
                  reload: "tab-s",
                  close: true,
                  type: "button",
                  size: "md",
                  api: {
                    method: "post",
                    url: `building/customAdd`,
                    data: {
                      name: "${name}",
                      floor: "${floor}",
                      type: "${type}",
                      everyFloorCount: "${everyFloorCount}",
                      avgArea: "${avgArea}",
                    },
                    messages: {
                      success: "新增楼栋成功！",
                    },
                  },
                },
              ],
              //表格
              body: [
                {
                  type: "form",
                  rules: {},
                  labelWidth: 100,
                  body: [
                    {
                      type: "input-text",
                      name: "name",
                      required: true,
                      label: "楼栋名称",
                      showCounter: true,
                      maxLength: 15,
                      placeholder: "请输入楼栋名称",
                    },
                    {
                      type: "input-number",
                      name: "floor",
                      required: true,
                      label: "层数",
                      max: 30,
                      min: 0,
                    },
                    {
                      type: "input-number",
                      name: "everyFloorCount",
                      required: true,
                      label: "每层房源数",
                      max: 20,
                      min: 1,
                    },
                    {
                      type: "input-number",
                      name: "avgArea",
                      required: true,
                      label: "房源平均面积",
                      min: 0,
                      precision: 2,
                    },
                    {
                      type: "select",
                      name: "type",
                      label: "楼栋种类",
                      required: true,
                      options: [
                        { label: "办公楼", value: "办公楼" },
                        { label: "生产楼", value: "生产楼" },
                        { label: "研发楼", value: "研发楼" },
                      ],
                    },
                  ],
                },
              ],
            },
          },
        ],
        onSubmit: "reload",
        actions: [],
      },
    },
  ],
};
// 房源列表
const amisjson2 = {
  type: "page",
  asideResizor: false,
  syncLocation: false,
  style: {
    boxShadow: "0px 0px 0px 0px transparent",
  },
  body: [
    {
      type: "tpl",
      inline: true,
      wrapperComponent: "",
      syncLocation: false,
      id: "u:3345e187f2df",
      style: {
        boxShadow: "0px 0px 0px 0px transparent",
        fontWeight: "500",
        lineHeight: "1.7",
        fontSize: "20px",
      },
    },
    {
      type: "crud",
      syncLocation: false,
      name: "tab-s",
      api: {
        method: "post",

        url: `/houses/customPageList`,
        requestAdaptor: (rest) => {
          // console.log(rest, 'rest');
          let { gmtCreate, ...other } = rest.data;
          let houseIds = rest.data.houseIds;
          houseIds = houseIds?.split(",");
          if (houseIds[0] == "") {
            houseIds = [];
          }
          let datas = {
            ...other,
            houseIds,
          };
          return {
            ...rest,
            data: datas,
          };
        },
        data: {
          /*   "businessCode": "houses",
            "conditions": [{
              "key": "name",
              "compareType": "like",
              "value": "${name}"
            }, {
              "key": "house_status",
              "compareType": "eq",
              "value": "${house_status}"
            }, {
              "key": "status",
              "compareType": "eq",
              "value": "${status}"
            }
            ], */
          rentStatus: "${house_status}",
          name: "${name}",
          houseIds: "${houseIds}",
          pageNum: "${page}",
          pageSize: "${perPage}",
        },
      },
      perPageAvailable: [10, 20],
      perPage: 10,
      filterTogglable: true,
      columnsTogglable: false,
      mode: "table",
      //列表
      columns: [
        {
          name: "name",
          label: "房源名称",
          type: "text",
        },
        {
          name: "buildingName",
          label: "所属楼栋",
          type: "text",
        },
        {
          name: "floors",
          label: "所属楼层",
          type: "text",
        },
        {
          name: "desc",
          label: "描述",
          type: "text",
        },
        /* {
          "name": "release_time",
          "label": "发布时间",
          "type": "date",
          "valueFormat": "x"
        }, */
        {
          name: "rent",
          label: "租金(元/㎡*月)",
          type: "text",
        },
        {
          name: "areas",
          label: "面积m2",
          type: "text",
        },
        /*  {
           "name": "rentable_date",
           "label": "可租日期",
           "type": "date",
           "valueFormat": "x"
         }, */
        {
          type: "tpl",
          label: "出租状态",
          tpl: "<span class='${status==0 ? \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200\" : \"inline-block px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200\"}'>${status==0 ? '未租' : '已租'}</span>",
        },
        {
          name: "floorHeight",
          label: "层高(m)",
          type: "text",
        },
        {
          name: "bearing",
          label: "承重(KN/㎡)",
          type: "text",
        } /* ,
        {
          "name": "${is_recommend==0 ? '不推荐' : '推荐'}",
          "label": "是否重点推荐",
          "type": "text"
        },
        {
          "name": "${status==0 ? '可租' : '不可租'}",
          "label": "是否可租",
          "type": "text"
        }, */,
        /*   {
            "name": "${status==0 ? '草稿' : '已发布'}",
            "label": "状态",
            "type": "text"
          }, */
        {
          type: "operation",
          label: "操作",
          fixed: "right", // 固定在右侧
          width: 200,
          buttons: [
            {
              type: "button",
              label: "查看",
              level: "primary",
              actionType: "dialog",
              dialog: {
                size: "lg",
                title: "房源详情",
                actions: [
                  {
                    type: "button",
                    size: "md",
                    actionType: "confirm",
                    label: "关闭",
                    primary: true,
                  },
                ],
                body: [
                  {
                    type: "form",
                    columnCount: 2,
                   labelWidth: 120,
                    body: [
                      {
                        type: "select",
                        name: "buildingId",
                        label: "所属楼栋",
                        required: true,
                        disabledOn: "true",
                        inline: true,
                        source: {
                          url: `building/getBuilding`,
                          adaptor: function (payload, response, api, context) {
                            let list = payload.map((item) => {
                              return {
                                value: item.buildingId,
                                label: item.buildingName,
                              };
                            });
                            return {
                              data: list,
                              msg: "请求成功",
                              status: 0,
                            };
                          },
                        },
                        onEvent: {
                          change: {
                            actions: [
                              {
                                actionType: "setValue",
                                componentName: "floors",
                                args: {
                                  value: "",
                                },
                              },
                            ],
                          },
                        },
                      },
                      {
                        type: "select",
                        name: "floors",
                        label: "所属楼层",
                        placeholder: "请选择楼层",
                        disabledOn: "true",
                        required: true,
                        initFetchOn: "data.buildingId",
                        source: {
                          url: `building/getFloors?buildingId=$buildingId`,
                          adaptor: function (payload, response, api, context) {
                            let list = payload.map((item) => {
                              return {
                                value: item,
                                label: "F" + item,
                              };
                            });
                            return {
                              data: list,
                              msg: "请求成功",
                              status: 0,
                            };
                          }, 
                        },
                      },
                      {
                        type: "input-text",
                        name: "name",
                        label: "房源名称",
                        required: true,
                        disabledOn: "true",
                        showCounter: true,
                        maxLength: 20,
                        perFix: "F",
                        placeholder: "请输入房源名称",
                      },
                      {
                        type: "input-text",
                        name: "rent",
                        disabledOn: "true",
                        label: "房源租金(元/㎡*月)",
                        placeholder: "请输入房源租金",
                      },
                      {
                        type: "input-number",
                        name: "areas",
                        disabledOn: "true",
                        label: "房源面积(㎡)",
                        min: 0,
                        precision: 2,
                      },
                      /* {
                        "type": "input-date",
                        "name": "rentable_date",
                        "label": "可租日期",
                        "valueFormat": "x"
                      }, */
                      {
                        type: "input-number",
                        name: "minLease",
                        disabledOn: "true",
                        label: "起租年限",
                        min: 0,
                      },
                      {
                        type: "input-number",
                        name: "waterFee",
                        label: "水费单价(元/t)",
                        disabledOn: "true",
                        precision: 2,
                      },
                      {
                        type: "input-number",
                        name: "electricFee",
                        disabledOn: "true",
                        label: "电费单价(元/kw/h)",
                        precision: 2,
                      },
                      {
                        type: "input-number",
                        name: "ownershipPeriod",
                        disabledOn: "true",
                        label: "产权年限",
                      },
                      {
                        type: "input-date",
                        name: "constructDate",
                        disabledOn: "true",
                        label: "建筑日期",
                        valueFormat: "x",
                      },
                      {
                        type: "input-date",
                        name: "rentableDate",
                        disabledOn: "true",
                        label: "可租日期",
                        valueFormat: "x",
                      },
                      {
                        type: "input-text",
                        name: "enterpriseType",
                        label: "意向招租企业类型",
                        disabledOn: "true",
                        showCounter: true,
                        maxLength: 30,
                      },
                      {
                        type: "input-number",
                        disabledOn: "true",
                        name: "floorHeight",
                        label: "层高(m)",
                        precision: 2,
                      },
                      {
                        type: "input-number",
                        name: "bearing",
                        label: "承重(KN/㎡)",
                        disabledOn: "true",
                        precision: 2,
                      },
                      /*                     {
                        "name": "specialType",
                        "disabledOn": "true",
                        "type": "radios",
                        "label": "是否为特殊房源",
                        "options": [
                          {
                            "label": "否",
                            "value": "0"
                          },
                          {
                            "label": "创新空间",
                            "value": "1"
                          },
                          {
                            "label": "孵化器",
                            "value": "2"
                          }
                        ]
                      }, */
                      {
                        name: "desc",
                        type: "textarea",
                        label: "房源描述",
                        disabledOn: "true",
                        showCounter: true,
                        maxLength: 80,
                      },
                      {
                        type: "input-image",
                        disabledOn: "true",
                        name: "originImage",
                        label: "上传图片",
                        multiple: true,
                        useChunk: false,
                        // "accept": ".jpg,.png,.PNG,.JPEG,",
                        maxSize: 10495000,
                        maxLength: 9,
                        receiver: {
                          method: "post",
                          url: `upload`,
                        },
                      },
                      {
                        name: "isRecommend",
                        type: "radios",
                        disabledOn: "true",
                        label: "是否重点推荐",
                        options: [
                          {
                            label: "否",
                            value: false,
                          },
                          {
                            label: "是",
                            value: true,
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            },
            {
              type: "button",
              size: "md",
              label: "编辑",
                    level: 'primary',
              actionType: "dialog",
              //"visibleOn": "status == '0'",
              dialog: {
                size: "lg",
                title: "编辑房源",
                actions: [
                  {
                    type: "button",
                    size: "md",
                    actionType: "cancel",
                    label: "取消",
                    level: "default",
                    primary: true,
                  },
                  {
                    label: "确认",
                    actionType: "confirm",
                    primary: true,
                    //"reload": "tree-updata",
                    reload: "tab-s,tab-s.fil.houseIds",
                    close: true,
                    type: "button",
                    size: "md",
                    api: {
                      method: "post",
                      url: `houses/customUpdate`,

                      requestAdaptor: (rest) => {
                        // console.log(rest.data, " rest.data");
                        if (
                          rest.data?.constructDate &&
                          rest.data?.constructDate.includes("-")
                        ) {
                          let dss = Date.parse(rest.data.constructDate);
                          rest.data.constructDate = dss;
                        }
                        if (
                          rest.data?.rentableDate &&
                          rest.data?.rentableDate.includes("-")
                        ) {
                          let dss = Date.parse(rest.data.rentableDate);
                          rest.data.rentableDate = dss;
                        }
                        let fileArray = rest.data.originImage?.split(",");
                        let olbfileArray = rest.data.images?.split(",");
                        let file = fileArray?.map((e) => {
                          if (e.slice(0, 5) === "https") {
                            let del = e;
                            olbfileArray.map((e1) => {
                              let list = e1.split("/");
                              let name = list[list.length - 1].split("-")[0];
                              const result = e.includes(name);
                              if (result) {
                                del = e1;
                              }
                            });
                            return del;
                          } else {
                            return e;
                          }
                        });
                        file = file?.toString();
                        rest.data.images = file;
                        return {
                          ...rest,
                        };
                      },
                      data: {
                        "&": "$$",
                        images: "$images",
                        id: "$id",
                      },
                      messages: {
                        success: "编辑房源成功！",
                      },
                    },
                  },
                ],
                body: [
                  {
                    type: "form",
                    columnCount: 2,
                  labelWidth: 120,
                    body: [
                      {
                        type: "select",
                        name: "buildingId",
                        label: "所属楼栋",
                        required: true,
                        disabledOn: "true",
                        inline: true,
                        source: {
                          url: `building/getBuilding`,
                          adaptor: function (payload, response, api, context) {
                            let list = payload.map((item) => {
                              return {
                                value: item.buildingId,
                                label: item.buildingName,
                              };
                            });
                            return {
                              data: list,
                              msg: "请求成功",
                              status: 0,
                            };
                          },
                        },
                        onEvent: {
                          change: {
                            actions: [
                              {
                                actionType: "setValue",
                                componentName: "floors",
                                args: {
                                  value: "",
                                },
                              },
                            ],
                          },
                        },
                      },
                      {
                        type: "select",
                        name: "floors",
                        label: "所属楼层",
                        placeholder: "请选择楼层",
                        disabledOn: "true",
                        required: true,
                        initFetchOn: "data.buildingId",
                        source: {
                          url: `building/getFloors?buildingId=$buildingId`,
                          adaptor: function (payload, response, api, context) {
                            let list = payload.map((item) => {
                              return {
                                value: item,
                                label: "F" + item,
                              };
                            });
                            return {
                              data: list,
                              msg: "请求成功",
                              status: 0,
                            };
                          },
                        },
                      },
                      {
                        type: "input-text",
                        name: "name",
                        label: "房源名称",
                        required: true,
                        showCounter: true,
                        maxLength: 20,
                        placeholder: "请输入房源名称",
                      },
                      {
                        type: "input-text",
                        name: "rent",
                        label: "房源租金(元/㎡*月)",
                        placeholder: "请输入房源租金",
                      },
                      {
                        type: "input-number",
                        name: "areas",
                        label: "房源面积(㎡)",
                        min: 0,
                        precision: 2,
                      },
                      /* {
                        "type": "input-date",
                        "name": "rentable_date",
                        "label": "可租日期",
                        "valueFormat": "x"
                      }, */
                      {
                        type: "input-number",
                        name: "minLease",
                        label: "起租年限",
                        min: 0,
                      },
                      {
                        type: "input-number",
                        name: "waterFee",
                        label: "水费单价(元/t)",
                        precision: 2,
                      },
                      {
                        type: "input-number",
                        name: "electricFee",
                        label: "电费单价(元/kw/h)",
                        precision: 2,
                      },
                      {
                        type: "input-number",
                        name: "ownershipPeriod",
                        label: "产权年限",
                      },
                      {
                        type: "input-date",
                        name: "constructDate",
                        label: "建筑日期",
                        valueFormat: "x",
                      },
                      {
                        type: "input-date",
                        name: "rentableDate",
                        label: "可租日期",
                        valueFormat: "x",
                      },
                      {
                        type: "input-text",
                        name: "enterpriseType",
                        label: "意向招租企业类型",
                        showCounter: true,
                        maxLength: 30,
                      },
                      {
                        type: "input-number",
                        name: "floorHeight",
                        label: "层高(m)",
                        precision: 2,
                      },
                      {
                        type: "input-number",
                        name: "bearing",
                        label: "承重(KN/㎡)",
                        precision: 2,
                      },
                      /*                       {
                        "name": "specialType",
                        "type": "radios",
                        "label": "是否为特殊房源",
                        "options": [
                          {
                            "label": "否",
                            "value": "0"
                          },
                          {
                            "label": "创新空间",
                            "value": "1"
                          },
                          {
                            "label": "孵化器",
                            "value": "2"
                          }
                        ]
                      }, */
                      {
                        name: "desc",
                        type: "textarea",
                        label: "房源描述",
                        showCounter: true,
                        maxLength: 80,
                      },
                      {
                        type: "input-image",
                        name: "originImage",
                        label: "上传图片",
                        multiple: true,
                        useChunk: false,
                        // "accept": ".jpg,.png,.PNG,.JPEG,",
                        maxSize: 10495000,
                        maxLength: 9,
                        receiver: {
                          method: "post",
                          url: `upload`,
                        },
                      },
                      {
                        name: "isRecommend",
                        type: "radios",
                        label: "是否重点推荐",
                        options: [
                          {
                            label: "否",
                            value: false,
                          },
                          {
                            label: "是",
                            value: true,
                          },
                        ],
                      },
                    ],
                  },
                ],
              },
            },
            {
              type: "button",
              size: "md",
              actionType: "ajax",
              label: "删除",
              level: "danger",
              confirmTitle: "提示",
              confirmText: "确认删除该房源吗？",
              api: {
                method: "get",
                url: `houses/customDelete`,
                data: {
                  id: "${id}",
                },
              },
              messages: {
                success: "删除成功！",
              },
            },
            /*  {
               "type": "button",
"size": "md",
               "actionType": "ajax",
               "label": "发布",
               "visibleOn": "status === '0'",
               "confirmText": "${house_status == '0' ? '该房源已出租，是否确认发布？' : '是否确认发布？'}",
               "api": {
                 "method": "post",
                 "url": `/billund/formSubmit`,
                 "data": {
                   "businessCode": "houses",
                   "action": "update",
                   "element":{
                       "status":"${status==1 ? '0' : '1'}"
                   },
                   "conditions": [
                     {
                       "key": 'id',
                       "value": "${id}"
                     }
                   ]
                 }
               },
             },
             {
               "type": "button",
"size": "md",
               "actionType": "ajax",
               "label": "下架",
               "visibleOn": "status == '1'",
               "confirmText": "是否确认下架",
               "api": {
                 "method": "post",
                 "url": `/billund/formSubmit`,
                 "data": {
                   "businessCode": "houses",
                   "action": "update",
                   "element":{
                       "status":"${status==1 ? '0' : '1'}"
                   },
                   "conditions": [
                     {
                       "key": 'id',
                       "value": "${id}"
                     },
                   ]
                 }
               },
             }, */
          ],
        },
      ],
      alwaysShowPagination: true,
      autoFillHeight: false,
      hideQuickSaveBtn: true,
      keepItemSelectionOnPageChange: false,
      filter: {
        title: "",
        name: "fil",
        submitText: "搜索",
        controls: [
          {
            type: "text",
            name: "name",
            size: "md",
            label: "房源名称",
            placeholder: "请输入房源名称",
          },
          {
            type: "select",
            name: "house_status",
            label: "出租状态",
            size: "md",
            placeholder: "请选择出租状态",
            value: "",
            options: [
              { label: "全部", value: "" },
              { label: "未租", value: "0" },
              { label: "已租", value: "1" },
            ],
          },
          {
            // "type": "nested-select",
            type: "nested-select",
            //"onlyLeaf": true,
            searchable: true, //可搜索
            multiple: true, //是否多选
            name: "houseIds",
            //"withChildren": true,//设置 true时，选中父节点时，值里面将包含子节点的值，否则只会保留父节点的值。
            onlyChildren: true, //多选时，选中父节点时，是否只将其子节点加入到值中。
            size: "md",
            label: "所属楼栋楼层",
            maxTagCount: 2, //	标签的最大展示数量，超出数量后以收纳浮层的方式展示，仅在多选模式开启后生效
            // "onlyChildren": true,
            source: {
              method: "get",
              url: `houses/getBuildingTree`,
              adaptor: function (payload, response, api, context) {
                let list = payload.map((item) => {
                  return {
                    value: item.buildingId,
                    label: item.buildingName,
                    children: transformData(item.houseList),
                  };
                });
                return {
                  data: list,
                  msg: "请求成功",
                  status: 0,
                };
              },
            },
          },
          {
            type: "reset",
            label: "重置",
          },
          {
            type: "submit",
            label: "搜索",
            primary: true,
          },
          {
            type: "button",
            size: "md",
            label: "一键导出",
            //"actionType": "download",
            onClick: function () {
              downloadAll().then((res) => {
                // console.log(res, "res");
                let blob = new Blob([res], {
                  type: "text/csv,charset=UTF-8",
                });
                let objectUrl = URL.createObjectURL(blob);
                const fileName = "房源数据导出.xlsx";
                const downloadLink = document.createElement("a");
                downloadLink.href = objectUrl;
                downloadLink.download = fileName;
                downloadLink.click();
              });
            },
            /* "api": {
              "url":`houses/downloadAll`,
              "method":"get",
               "headers":{
                "Sec-Fetch-Mode":'cors'
               }
            } */
            /* "api": {
              "method": "get",
                "url": `houses/downloadAll`,
                "headers": {
                  "token": `${token()}`,
                  "Access-Control-Expose-Headers":" Content-Disposition"
                },
            }, */
          },
          {
            type: "button",
            size: "md",
            label: "添加房源", 
            actionType: "dialog",
            //"visibleOn": "status == '0'",
            dialog: {
              size: "lg",
              title: "添加房源",
            //      "editorSetting": {
            //   "displayName": "我的设置"
            // },
            // "themeCss": {
            //   "dialogTitleClassName": {
            //     "font": {
            //       "fontSize": "var(--fonts-size-5)",
            //       "fontWeight": "var(--fonts-weight-4)"
            //     }
            //   }
            // },
              actions: [
                {
                  type: "button",
                  size: "md",
                  level: "default",
                  actionType: "cancel",
                  label: "取消",
                  level: "default",
                  primary: true,
                },
                {
                  label: "确认",
                  actionType: "confirm",
                  primary: true,
                  reload: "tab-s,tab-s.fil.houseIds",
                  close: true,
                  type: "button",
                  size: "md",
                  api: {
                    method: "post",
                    url: `houses/customAdd`,
                    data: {
                      "&": "$$",
                    },
                    messages: {
                      success: "添加房源成功！",
                    },
                  },
                },
              ],
              body: [
                {
                  type: "form",
                  columnCount: 2,
                  labelWidth: 120,
                  data: {
                    name: null,
                    rent: "5~25", //房源租金
                    minLease: 1, //起租年限
                    waterFee: 3.16, //水费
                    electricFee: 0.96, //电费
                    constructDate: 946656000000, //建筑日期
                    floorHeight: 3, //层高
                    //specialType: 0,//是否为特殊房源
                    rentableDate: Date.now(), //可租日期
                    ownershipPeriod: 40, //产权年限
                    bearing: 50, //承重
                    desc: "该房源具备优越的位置、齐全的设施、优美的环境、完善的服务、实惠的价格、灵活的租赁方式、方便的停车条件以及齐全的配套设施。",
                    isRecommend: false, //是否重点推荐
                  },
                  body: [
                    {
                      type: "select",
                      name: "buildingId",
                      label: "所属楼栋",
                      size: "lg",
                      required: true,
                      inline: true,
                      source: {
                        url: `building/getBuilding`,
                        adaptor: function (payload, response, api, context) {
                          let list = payload.map((item) => {
                            return {
                              value: item.buildingId,
                              label: item.buildingName,
                            };
                          });
                          return {
                            data: list,
                            msg: "请求成功",
                            status: 0,
                          };
                        },
                      },
                      onEvent: {
                        change: {
                          actions: [
                            {
                              actionType: "setValue",
                              componentName: "floors",
                              args: {
                                value: "",
                              },
                            },
                          ],
                        },
                      },
                    },
                    {
                      type: "select",
                      name: "floors",
                       size: "lg",
                      label: "所属楼层",
                      placeholder: "请选择楼层",
                      required: true,
                      initFetchOn: "data.buildingId",
                      source: {
                        url: `building/getFloors?buildingId=$buildingId`,
                        adaptor: function (payload, response, api, context) {
                          let list = payload.map((item) => {
                            return {
                              value: item,
                              label: "F" + item,
                            };
                          });
                          return {
                            data: list,
                            msg: "请求成功",
                            status: 0,
                          };
                        },
                      },
                    },
                    {
                      type: "input-text",
                      name: "name",
                      label: "房源名称",
                       size: "lg",
                      //"required": true,
                      showCounter: true,
                      maxLength: 20,
                      placeholder: "请输入房源名称",
                    },
                    {
                      type: "input-text",
                      name: "rent",
                       size: "lg",
                      label: "房源租金(元/㎡*月)",
                      placeholder: "请输入房源租金",
                    },
                    {
                      type: "input-number",
                      name: "areas",
                       size: "lg",
                      label: "房源面积(㎡)",
                      min: 0,
                      precision: 2,
                    },
                    /* {
                      "type": "input-date",
                      "name": "rentable_date",
                      "label": "可租日期",
                      "valueFormat": "x"
                    }, */
                    {
                      type: "input-number",
                      name: "minLease",
                       size: "lg",
                      label: "起租年限",
                      min: 0,
                    },
                    {
                      type: "input-number",
                      name: "waterFee",
                       size: "lg",
                      label: "水费单价(元/t)",
                      precision: 2,
                    },
                    {
                      type: "input-number",
                      name: "electricFee",
                       size: "lg",
                      label: "电费单价(元/kw/h)",
                      precision: 2,
                    },
                    {
                      type: "input-number",
                      name: "ownershipPeriod",
                       size: "lg",
                      label: "产权年限",
                    },
                    {
                      type: "input-date",
                      name: "constructDate",
                       size: "lg",
                      label: "建筑日期",
                      valueFormat: "x",
                    },
                    {
                      type: "input-date",
                      name: "rentableDate",
                       size: "lg",
                      label: "可租日期",
                      valueFormat: "x",
                    },
                    {
                      type: "input-text",
                      name: "enterpriseType",
                       size: "lg",
                      label: "意向招租企业类型",
                      showCounter: true,
                      maxLength: 30,
                    },
                    {
                      type: "input-number",
                      name: "floorHeight",
                       size: "lg",
                      label: "层高(m)",
                      precision: 2,
                    },
                    {
                      type: "input-number",
                      name: "bearing",
                       size: "lg",
                      label: "承重(KN/㎡)",
                      precision: 2,
                    },
                    /*                     {
                      "name": "specialType",
                      "type": "radios",
                      "label": "是否为特殊房源",
                      "options": [
                        {
                          "label": "否",
                          "value": "0"
                        },
                        {
                          "label": "创新空间",
                          "value": "1"
                        },
                        {
                          "label": "孵化器",
                          "value": "2"
                        }
                      ]
                    }, */
                    {
                      name: "desc",
                      type: "textarea",
                      label: "房源描述",
                       size: "lg",
                      showCounter: true,
                      maxLength: 80,
                    },
                    {
                      type: "input-image",
                      name: "images",
                       size: "lg",
                      label: "上传图片",
                      multiple: true,
                      useChunk: false,
                      // "accept": ".jpg,.png,.PNG,.JPEG,",
                      maxSize: 10495000,
                      maxLength: 9,
                      receiver: {
                        method: "post",
                        url: `upload`,
                      },
                    },
                    {
                      name: "isRecommend",
                      type: "radios",
                      label: "是否重点推荐",
                       size: "lg",
                      options: [
                        {
                          label: "否",
                          value: false,
                        },
                        {
                          label: "是",
                          value: true,
                        },
                      ],
                    },
                  ],
                },
              ],
            },
          },

          /*  {
             "type": "select",
             "name": "status",
             "label": "状态",
             "placeholder": "请选择状态",
             "value": "",
             "options": [
               { "label": "全部", "value": "" },
               { "label": "草稿", "value": "0" },
               { "label": "已发布", "value": "1" },
             ]
           } */
        ],
        onSubmit: "reload",
        actions: [],
      },
    },
  ],
};
let amisjson = ref(null);
onMounted(() => {
  let tabs = {
    type: "tabs",
    // "tabsMode": "radio",
    tabs: [
      {
        title: "楼栋列表",
        body: amisjson3,
      },
      {
        title: "房源列表",
        body: amisjson2,
      },
    ],
    className: "tabsClass",
    linksClassName: "tabsTitle",
  };
  // let data = publicConfig({ columns, api, filter });
  amisjson.value = tabs;
});
</script>

<template>
  <!--   <div class="tab">
    <el-button size="default" :class="pitchId == item.id ? 'on' : ''" @click="changeId(item.id)"
      v-for="(item, index) in  tabList" :key="index">{{ item.name }}</el-button>
  </div>

  <div v-if="pitchId == 1">
    <AmisComponent :amisjson="amisjson" />
  </div>
  <div v-else>
    <AmisComponent :amisjson="amisjson2" />
  </div> -->
  <div v-if="!!amisjson" class="zhengce publicTableStyle">
    <AmisComponent :amisjson="amisjson" />
  </div>
</template>
<style scoped lang="scss">
.tab {
  padding-top: 20px;
  padding-left: 10px;
}

.on {
  background-color: #169bd5;
  color: #fff;
}
</style>

