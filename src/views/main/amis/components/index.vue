<template>
	<div id="box"></div>
</template>

<script lang="ts" setup>
import { onMounted,defineProps } from "vue";
// console.log("ces ");
const props = defineProps({
	amisjson: {
		type: Object,
		required: true
	}
})


onMounted(() => {
	// @ts-ignore
	var amis = amisRequire('amis/embed');
	// console.log(amis);
// 	let amisScoped = amis.embed(
//     // amis.embed()有4个参数
//     // 1、容器id
//     // 2、schema：即页面配置，此例子对应的是amisJSON
//     // 3、props：初始值
//     // 4、可选的外部控制函数，其中包含了 theme：主题；当然还有其他的可选项
//     '#root', 
//     amisJSON,
//     {
//         // 这里是初始 props
//     },
//     {
//         theme:'antd'
//     }
// );
	let amisScoped = amis.embed('#box', props.amisjson,{},{
        theme:'antd'
    });
	// console.log(amisScoped.getComponentByName('page1.form1').getValues())
})

</script>