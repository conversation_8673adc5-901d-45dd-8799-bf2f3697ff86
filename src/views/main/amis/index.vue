<script setup>
// import HelloWorld from './components/HelloWorld.vue'
// import TheWelcome from './components/TheWelcome.vue'
import AmisComponent from "./components/index.vue";

const amisjson = {
  type: "page",
  id: "u:c1462d501073",
  asideResizor: false,
  style: {
    boxShadow: " 0px 0px 0px 0px transparent",
  },
  body: [
    {
      type: "tpl",
      tpl: "数据监控日志\n",
      inline: true,
      wrapperComponent: "",
      id: "u:3345e187f2df",
      style: {
        boxShadow: " 0px 0px 0px 0px transparent",
        fontWeight: "500",
        lineHeight: "1.7",
        fontSize: "20px",
      },
    },
    {
      type: "divider",
      id: "u:37cdf1cb7b99",
      lineStyle: "solid",
    },
    {
      type: "crud",
      syncLocation: false,
      api: {
        method: "get",
        url: "https://home.airuicloud.com/dpar/industryReport/pageByAdmin",
      },
      bulkActions: [],
      itemActions: [],
      id: "u:a6fd8925276f",
      perPageAvailable: [10],
      messages: {},
      perPage: 10,
      footerToolbar: [
        {
          type: "switch-per-page",
          align: "right",
          tpl: "内容",
          wrapperComponent: "",
          id: "u:7e8c42e22098",
        },
        {
          type: "pagination",
          tpl: "内容",
          wrapperComponent: "",
          id: "u:1567e06485ac",
          align: "right",
        },
        {
          type: "statistics",
          tpl: "内容",
          wrapperComponent: "",
          id: "u:cb1f575af89b",
          align: "right",
        },
      ],
      filterTogglable: true,
      columnsTogglable: false,
      headerToolbar: [
        {
          type: "filter-toggler",
          align: "left",
        },
        {
          type: "bulk-actions",
        },
        {
          type: "reload",
          align: "right",
          tooltip: "",
          level: "primary",
        },
        {
          type: "export-excel",
          tpl: "内容",
          wrapperComponent: "",
          id: "u:9b82ffae91ed",
          align: "left",
          filename: "自定义文件名${test}",
          api: "/amis/api/mock2/sample", //可配置
        },
      ],
      mode: "table",
      columns: [
        {
          name: "id",
          label: "序号",
          type: "text",
          id: "u:29736fae1568",
        },
        {
          name: "engine",
          label: "数据库",
          type: "text",
          id: "u:591f67a7e97c",
        },
        {
          type: "text",
          label: "数据表",
          name: "list",
          id: "u:e3e5b9c950cd",
        },
        {
          type: "text",
          label: "接口名称",
          name: "interface",
          id: "u:64dbdd425fb5",
        },
        {
          type: "text",
          label: "操作类型",
          name: "tpye",
          id: "u:6cc8a89515ac",
        },
        {
          type: "text",
          label: "账号",
          name: "account",
          id: "u:1cccf848b12f",
        },
        {
          type: "text",
          label: "IP地址",
          name: "ip",
          id: "u:d8304d3c149b",
        },
        {
          type: "text",
          label: "日志记录时间",
          name: "time",
          id: "u:f366a4e2937c",
        },
      ],
      alwaysShowPagination: true,
      autoFillHeight: false,
      hideQuickSaveBtn: true,
      filter: {
        title: "",
        body: [
          {
            type: "input-text",
            name: "key",
            id: "u:e67ab410a797",
            addOn: {
              label: "搜索",
              type: "submit",
              id: "u:8f14fdfe3bdd",
            },
            placeholder: "请输入关键字查询",
            size: "lg",
            mode: "inline",
          },
          {
            type: "button",
            size: "md",
            label: "新增",
            level: "primary",
            actionType: "dialog",
            className: "m-b-sm",
            dialog: {
              title: "新增表单",
              body: {
                type: "form",
                api: "post:/amis/api/mock2/sample",
                body: [
                  {
                    type: "input-text",
                    name: "engine",
                    label: "Engine",
                  },
                  {
                    type: "input-text",
                    name: "browser",
                    label: "Browser",
                  },
                ],
              },
            },
            id: "u:811867e89454",
          },
        ],
        id: "u:0f08dfb4b074",
        submitText: "",
        submitOnChange: true,
        mode: "inline",
        wrapWithPanel: false,
      },
      keepItemSelectionOnPageChange: false,
    },
  ],
};
</script>

<template>
  <div>
    <AmisComponent :amisjson="amisjson" />
  </div>
</template>

