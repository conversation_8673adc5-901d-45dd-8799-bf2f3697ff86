<template>
         <!-- <el-tabs   v-model="activeName" class="demo-tabs" size="default" @tab-click="handleClick">
    <el-tab-pane label="我的租约" name="first">
    </el-tab-pane>
    <el-tab-pane label="我的账单" name="second">
    </el-tab-pane>
  </el-tabs> -->
  <div class="topHeader">
    <el-radio-group size="default" v-model="activeName"  >
    <el-radio-button label="first">我的租约</el-radio-button>
    <el-radio-button label="second">我的账单</el-radio-button>
    
  </el-radio-group>
  </div>

  <div v-if="activeName==='first'">
    <Lease/>
  </div>
  <div v-if="activeName==='second'">
    <Check/>
  </div>
</template>
<script setup>
import{ref}from 'vue'
 import Lease from './lease/index.vue';
 import Check from './check/index.vue';
 const activeName=ref('first')
</script>
<style  lang='scss'>
.topHeader{
  width: 100%;
  display: flex;
  justify-content: flex-end;
}
.publicTableStyle.myCheck,.publicTableStyleNoFilter.myCheck{
  .antd-Page-content,.tableAutoStyle{
    // padding: 0 !important;
    background: #fafcff !important;

  }
  .antd-Table-contentWrap,.antd-Table.antd-Crud-body.antd-Table--autoFillHeight{
    padding: 0 !important;
  }
  .antd-Panel.antd-Table-searchableForm.antd-Panel--form{
border: 0px;
background:white;
margin-bottom: 20px;
box-shadow: 0 0 0 ;
}
}
</style>
