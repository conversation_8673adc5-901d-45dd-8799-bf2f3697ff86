<template>
  <div v-if="!!amisjson" class="publicTableStyle myCheck">
    <AmisComponent :amisjson="amisjson" />
  </div>
</template>
<script setup>
import { onBeforeMount, onMounted, ref } from 'vue';
import AmisComponent from '@/components/amis/index.vue';
import { perFix, publicConfig, baseEvn, token } from '@/utils/utils';

import { useStore } from 'vuex';
import { useRoute, useRouter } from 'vue-router';
const route = useRoute();
const router = useRouter();

const passForm = (state) => {
  return {
    title: state === '1' ? '我要续约' : '我要退租',
    actions: [
      {
        type: 'button',
        size: 'md',
        actionType: 'cancel',
        level: 'default',
        label: '取消',
        level: 'default',
        primary: true,
      },
      {
        label: '确认',
        actionType: 'confirm',
        primary: true,
        reload: 'tab-s',
        close: true,
        type: 'button',
        size: 'md',
        api: {
          method: 'post',
          url: `policy/declaration/follow`,
          headers: {
            token: `${token()}`,
          },
          messages: {
            success: '操作成功！',
          },
          data: {
            id: '${id|default:undefined}',
            dealStatus: state,
            remark: '${remarkNew|default:undefined}',
            uploadFileList: '${uploadFileList|default:undefined}',
          },
          requestAdaptor: (rest) => {
            let { uploadFileList, ...other } = rest.data;
            let fileArray = uploadFileList.split(',');
            let file = fileArray?.map((e) => {
              let list = e.split('/');
              let name = list[list.length - 1].split('-')[1];
              let realName = name.substring(0, name.lastIndexOf('.'));
              return {
                name: realName,
                path: e,
              };
            });
            let datas = {
              ...other,
              uploadFileList: file,
            };
            return {
              ...rest,
              data: datas,
              // pageNum: page,
              // pageSize: perPage
            };
          },
        },
      },
    ],

    body: [
      {
        type: 'form',
        columnCount: 1,
        rules: {},

        body: [
          // {
          //   "label": "回复",
          //   "type": "textarea",
          //   "name": "remarkNew",
          //   "id": "u:ea2bfdba9bea",
          //   "required": true
          // },
          {
            type: 'tpl',
            tpl: '确定向园区管理方提出诉求吗？',
            inline: true,
            wrapperComponent: '',
            id: 'u:f95176ac247b',
          },
        ],
      },
    ],
  };
};
let formData = {
  type: 'page',
  title: '详情',
  body: [
    {
      type: 'form',
      title: '',
      submitText: '',
      initApi: {
        url: `bill/myPageList`,
        headers: {
          token: `${token()}`,
        },
        method: 'get',
        data: {
          id: '${id|default:undefined}',
        },
        responseData: {
          '&': '$$',
        },
      },
      body: [
        {
          type: 'card',
          header: {
            title: '',
            //  "${enterprise_name}<span style='color:red;font-size:12px'>${gmtCreate}</span>",
          },
          titleClassName: 'headerTitle',
          data: '${first}',
          body: [
            {
              name: 'policyTitle',
              label: '申报政策',
              type: 'static',
              labelClassName: 'leftTitle',
            },
            // policyTitle
            //                       gmtCreate
            {
              name: 'gmtCreate',
              label: '申报时间',
              type: 'date',
              // "src": "${img}",
              disabledOn: 'true',
              labelClassName: 'leftTitle',
            },
            {
              name: 'enterpriseName',
              label: '企业名称',
              type: 'static',
              // "src": "${img}",
              disabledOn: 'true',
              labelClassName: 'leftTitle',
            },
            {
              name: 'submitMobile',
              label: '联系方式',
              type: 'static',
              labelClassName: 'leftTitle',
            },

            // {
            //   "type": "tpl",
            //   "tpl": 'wwww',
            //   // "<a>sdjfnksjdksd</a>",
            // },
            {
              name: 'remark',
              label: '申报描述',
              type: 'static',
              labelClassName: 'leftTitle',
            },
            {
              type: 'list',
              label: '附件',
              source: '$relationFiles',
              listItem: {
                body: [
                  {
                    type: 'tpl',
                    tpl: "<a href='${relationFiles[index].path}'>${relationFiles[index].name}</a>",
                  },
                ],
              },
            },

            // {
            //   "name": "relationFiles",
            //   "label": "文件",
            //   "type": "static",
            //   // "src": "${img}",
            //   // "disabledOn": "true",
            //   // "labelClassName": "leftTitle",
            //   // documentLink:true,
            //   // downloadUrl:"relationFiles[0].path",
            // },
          ],
          mode: 'horizontal',
          id: 'qqq',
        },
        {
          type: 'card',
          disabledOn: "${dealStatus !== '0'}",
          header: {
            title:
              "${enterprise_name1}<span style='color:red;font-size:12px'>${gmtCreate1}</span>",
          },
          titleClassName: 'headerTitle',
          body: [
            {
              name: 'dealStatus',
              name: "${dealStatus==='1'?'通过':'退回'}",

              label: '状态',
              type: 'static',
              labelClassName: 'leftTitle',
            },
            {
              name: 'record.remark',
              label: '原因',
              type: 'static',
              labelClassName: 'leftTitle',
            },
            // {
            //   "name": "record.uploadFileList",
            //   "label": "文件",
            //   "type": "input-file",
            //   // "src": "${img}",
            //   "disabledOn": "true",
            //   "labelClassName": "leftTitle"
            // },
            {
              label: '附件',
              type: 'list',
              source: '$record.uploadFileList',
              listItem: {
                body: [
                  {
                    type: 'tpl',
                    tpl: "<a href='${record.uploadFileList[index].path}'>${record.uploadFileList[index].name}</a>",
                  },
                ],
              },
            },
          ],

          id: 'u:15008a1115bf',
        },
      ],
    },
  ],
  actions: [],
};
const columns = [
  {
    name: 'enterpriseName',
    label: '企业名称',
    type: 'text',
    // "searchable": true
  },
  {
    name: 'type',
    // "${dealStatus == 0 ? '已缴纳' :(dealStatus==1? '拖欠':'待缴纳')}",
    name: "${['','租金','物业费','电费','水费',''][type]}",
    label: '账单类型',
    type: 'text',
    searchable: {
      type: 'select',
      name: 'type',
      label: '账单类型',
      placeholder: '选择账单类型',
      options: [
        {
          label: '租金',
          value: '1',
        },
        {
          label: '物业费',
          value: '2',
        },
        {
          label: '电费',
          value: '3',
        },
        {
          label: '水费',
          value: '4',
        },
      ],
    },
  },
  {
    name: 'billAmount',
    label: '账单金额',
    type: 'text',
  },
  {
    name: 'billDate',
    label: '账单日期',
    type: 'date',
    valueFormat: 'x',
    searchable: {
      type: 'input-date-range',
      name: 'startTimeStamp',
      extraName: 'endTimeStamp',
      label: '账单日期',
      placeholder: '输入账单日期',
    },
  },

  {
    name: 'status',
    name: "${status == 1 ? '已缴纳' :(status==2? '拖欠':'待缴纳')}",
    label: '缴费状态',
    type: 'text',
    searchable: {
      type: 'select',
      name: 'status',
      label: '状态',
      placeholder: '选择缴费状态',
      options: [
        {
          label: '已缴纳',
          value: '1',
        },
        {
          label: '拖欠',
          value: '2',
        },
        {
          label: '待缴纳',
          value: '0',
        },
      ],
    },
  },
  {
    name: 'paymentDate',
    label: '缴费日期',
    type: 'date',
    valueFormat: 'x',
  },
  {
    name: 'paymentAmount',
    label: '缴费金额',
    type: 'text',
  },
];
const api = {
  method: 'post',
  url: `bill/myPageList`,
  headers: {
    token: `${token()}`,
  },
  data: {
    endTimeStamp: '${endTimeStamp|default:undefined}',
    startTimeStamp: '${startTimeStamp|default:undefined}',
    status: '${status|default:undefined}',
    type: '${type|default:undefined}',
    pageNum: '${page|default:1}',
    pageSize: '${perPage|default:10}',
  },
  requestAdaptor: (rest) => {
    let { endTimeStamp, startTimeStamp, ...other } = rest.data;
    let datas = {
      ...other,
      startTimeStamp: startTimeStamp ? `${startTimeStamp * 1000}` : undefined,
      endTimeStamp: endTimeStamp ? `${endTimeStamp * 1000}` : undefined,
    };
    return {
      ...rest,
      data: datas,
    };
  },
  responseData: {
    '&': '$$',
    items: 'records',
  },
};
let amisjson = ref(null);
onMounted(() => {
  let data = publicConfig({ columns, api, id: '11111' });
  amisjson.value = data;
});
</script>
<style>
.amis-scope .headerTitle {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  text-align: right;
}

.amis-scope .leftTitle {
  line-height: 33px;
  width: 100px;
}
</style>
