<template>
  <div>
    <!-- 政策申报 -->
    <div v-if="!!amisjson" class="publicTableStyleNoFilter myCheck">
      <AmisComponent :amisjson="amisjson" />
    </div>
  </div>

  <el-dialog
    v-model="dialogVisible"
    :title="currentData?.enterpriseName"
    custom-class=""
    top="50px"
    :before-close="handleClose"
  >
    <div class="leaseBody">
      <LeaseStatement origin="onlineService" :id="currentData.id" />
    </div>
  </el-dialog>
</template>
  <script setup>
import LeaseStatement from "@/views/main/lease/com/addlease.vue";
import { onBeforeMount, onMounted, ref } from "vue";
import AmisComponent from "@/components/amis/index.vue";
import { perFix, publicConfig, baseEvn, token } from "@/utils/utils";

import { useStore } from "vuex";
import { useRoute, useRouter } from "vue-router";
const route = useRoute();
const router = useRouter();
let dialogVisible = ref();
let currentData = ref();
const passForm = (state) => {
  return {
    title: state === "1" ? "我要续约" : "我要退租",
    actions: [
      {
        type: "button",
        size: "md",
        actionType: "cancel",
        label: "取消",
        level: "default",
        primary: true,
      },
      {
        label: "确认",
        actionType: "confirm",
        primary: true,
        reload: "tab-s",
        close: true,
        type: "button",
        size: "md",
        api: {
          method: "post",
          url: `pendingLease/save`,
          data: {
            leaseId: "${id|default:undefined}",
            operateType: state,
          },
          messages: {
            success: "提交成功，耐心等待园区方联系",
            // "failed": "提交成功，耐心等待园区方联系"
          },
        },
      },
    ],

    body: [
      {
        type: "form",
        columnCount: 1,
        rules: {},
        body: [
          {
            type: "tpl",
            tpl: "确定向园区管理方提出诉求吗？",
            inline: true,
            wrapperComponent: "",
            id: "u:f95176ac247b",
          },
        ],
      },
    ],
  };
};

const columns = [
  {
    name: "enterpriseName",
    label: "企业名称",
    type: "text",
    // "searchable": true
  },
  {
    name: "contractNumber",
    label: "合同编号",
    type: "text",
  },
  {
    name: "signDate",
    label: "签约日期",
    type: "date",
    valueFormat: "x",
  },
  {
    name: "moveInDate",
    label: "起租日期",
    type: "date",
    valueFormat: "x",
  },
  {
    name: "moveInAddress",
    label: "入驻房源",
    type: "text",
    // "searchable": true
  },
  {
    name: "lengthStay",
    label: "租赁时长（年）",
    type: "text",
    // "searchable": true
  },
  {
    name: "endDate",
    label: "结束日期",
    type: "date",
    valueFormat: "x",
  },
  {
    name: "status",
    name: "${status == 0 ? '待生效' :(status==1? '生效中':'已失效')}",
    label: "租约状态",
    type: "text",
  },

  {
    type: "operation",
    label: "操作",
    fixed: "right", // 固定在右侧
    width: 200,
    buttons: [
      {
        // "icon": "fa fa-eye",
        label: "预览",
        type: "button", 
        onClick: function (ctx, e) {
          dialogVisible.value = true;
          currentData.value = e.data;
        },
      },

      {
        // "disabledOn": "this.dealStatus === '1'||this.dealStatus === '2'",
        label: "我要续约",
        type: "button",
 level: 'primary', 
        actionType: "dialog",
        dialog: passForm("1"),
      },
      {
        // "disabledOn": "this.dealStatus === '1'||this.dealStatus === '2'",
        label: "我要退租",
        className: "text-danger",
        // "type": "button",
        size: "md",
        actionType: "dialog",
        dialog: passForm("2"),
      },
    ],
  },
];
const api = {
  method: "post",
  url: `lease/myPage`,
  data: {
    pageNum: "${page|default:1}",
    pageSize: "${perPage|default:10}",
  },
  responseData: {
    "&": "$$",
    // "items": "data",
  },
};
let amisjson = ref(null);
onMounted(() => {
  let data = publicConfig({ columns, api });
  amisjson.value = data;
});
</script>
  <style >
.amis-scope .headerTitle {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 10px;
  text-align: right;
}

.amis-scope .leftTitle {
  line-height: 33px;
  width: 100px;
}
.leaseBody {
  height: 50vh;
  overflow: scroll;
}
</style>