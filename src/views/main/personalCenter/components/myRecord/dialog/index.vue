
<template>
    <div v-if="amisjson" class="dialog">
      <AmisComponent :amisjson="amisjson" />
    </div>
  </template>
  <script setup>
  import { ref, onMounted, defineProps, watch } from "vue";
  import AmisComponent from "@/components/amis/index.vue";
  import { perFix, baseEvn, token } from "@/utils/utils";
  import { dialogs } from "./dialog";
  
  const props = defineProps(['currentAction']);
  // 企业列表
  const amisjson = ref();
  watch((() => props.currentAction),((newVal) => {
      amisjson.value = dialogs(props.currentAction)
  }), { deep: true })
  onMounted(() => {
    amisjson.value = dialogs(props.currentAction)
  })
  </script>
  <style lang="scss">
  .formMain {
    .antd-Panel {
      border: 0;
      box-shadow: 0 0 0 0;
    }
    .toolTip {
      display: block;
      width: 100%;
      height: 40px;
      text-align: center;
    }
  }
  .dialog.submit {
    margin-left: 40% !important;
  }
  .detailPage{
    .antd-Panel{
      border: 0;
      box-shadow: 0 0 0;
    }
  }
  .detailTwo .antd-Card {
      border: 0;
    }
   .detailThree{
    .antd-Card {
      border: 1px solid var(--Card-borderColor);
    }
   }
 .el-dialog__title{
  font-weight: 500;
 }
  </style>
  