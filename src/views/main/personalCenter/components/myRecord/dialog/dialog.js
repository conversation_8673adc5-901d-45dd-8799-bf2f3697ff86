import { perFix, publicConfig, baseEvn, token } from "@/utils/utils";
import dayjs from "dayjs";

const getRealName = (fileArrays) => {
  let fileArray = fileArrays.split(",");
  let file = fileArray?.map((e) => {
    let list = e.split("/");
    let name = list[list.length - 1].split("-")[1];
    let realName = name.substring(0, name.lastIndexOf("."));
    return {
      name: realName,
      path: e,
    };
  });
  return file;
};
let first = {
  type: "card",
  header: {
    title:
      "${enterprise_name}<span style='color:red;font-size:12px'>${gmtCreate}</span>",
  },
  titleClassName: "headerTitle",
  data: "${first}",
  body: [
    {
      name: "demands_desc",
      label: "诉求描述",
      type: "static",
      labelClassName: "leftTitle",
    },
    {
      type: "list",
      label: "附件",
      source: "$demands_file",
      listItem: {
        body: [
          {
            type: "tpl",
            tpl: "<a href='${demands_file[index].fileUrl}'>${demands_file[index].fileName}</a>",
          },
        ],
      },
    },
  ],
  mode: "horizontal",
  id: "qqq",
};
let second = {
  // disabledOn:true,
  type: "card",
  header: {
    title:
      "${enterprise_name1}<span style='color:red;font-size:12px'>${gmtCreate1}</span>",
  },
  titleClassName: "headerTitle",
  body: [
    {
      name: "demands_desc1",
      label: "回复",
      type: "static",
      labelClassName: "leftTitle",
    },
    {
      name: "demands_image1",
      label: "图片",
      type: "input-image",
      // "src": "${img}",
      disabledOn: "true",
      labelClassName: "leftTitle",
    },
    {
      type: "list",
      label: "附件",
      source: "$demands_file1",
      listItem: {
        body: [
          {
            type: "tpl",
            tpl: "<a href='${demands_file1[index].fileUrl}'>${demands_file1[index].fileName}</a>",
          },
        ],
      },
    },
  ],

  id: "u:15008a1115bf",
};
const getSecond = (elements) => {
  let data = elements.length > 1 ? [first, second] : [first];
  return data;
};
// ["政策申报", "物业报修", "经营诉求", "经营数据上报"]
// ['policy','propertyRepair','businessAppeal','business']
export const dialogs = (data) => {
  let doms = {
    // // 经营诉求
    businessAppeal: {
      type: "page",
      title: "",
      className:'detailPage',
      body: [
        {
          type: "form",
          title: "",
          submitText: "",
          className:'detailTwo',
          "initApi": {
            "url": `billund/getList`,
            "method": "post",
            data: {
              businessCode: "business_demands_record",
              conditions: [
                {
                  compareType: "eq",
                  key: "business_demands_id",
                  value: data.originId,
                },
                {
                  compareType: "orderBy",
                  key: "gmt_create",
                  isDesc: "false",
                },
              ],
            },
            "responseData": {
              "&": "$$",
              items: "elements",
              enterprise_name: "${elements[0].principal_name}",
              gmtCreate: "${elements[0].gmt_create}",
              demands_desc: "${elements[0].demands_desc}",
              demands_img: "${elements[0].demands_img}",
              demands_file: "${elements[0].demands_file}",
              enterprise_name1: "${elements[1].principal_name}",
              gmtCreate1: "${elements[1].gmt_create}",
              demands_desc1: "${elements[1].demands_desc}",
              demands_image1: "${elements[1].demands_img}",
              demands_file1: "${elements[1].demands_file}",
            },
            adaptor: function (payload, response, api, context) {
              let datas = payload.elements.map((item) => {
                let { gmt_create, gmt_modify,demands_img, ...others } = item;
                return {
                  ...others,
                  gmt_create: dayjs(Number(gmt_create)).format("YYYY-MM-DD"),
                  gmt_modify: dayjs(Number(gmt_modify)).format("YYYY-MM-DD"),
                  demands_img:demands_img?.map(e=>e.fileUrl)||''
                };
              });
              let payloads = payload;
              payloads.elements = datas;
              return {
                ...payloads,
              };
            },
          },
        
          body: [
            {
              type: "card",
              className:'detailThree',
              // header: {
              //   title:
              //     "${principal_name}<span style='color:red;font-size:12px'>${time1}</span>",
              // },
              titleClassName: "headerTitle",
              data: "${first}",
              body: [first, second],

              mode: "horizontal",
              id: "qqq",
            },
          ],
          // "${dom}",
        },
      ],
      actions: [],
    },
    // 政策申报
    policy: {
      type: "page",
      title: "",
      className:'detailPage',
      body: [
        {
          type: "form",
          title: "",
          submitText: "",
        
          initApi: {
            url: `policy/declaration/detail`,
            headers: {
              token: `${token()}`,
            },
            method: "get",
            data: {
              id: data.originId,
            },
            responseData: {
              "&": "$$",
            },
          },
        
          body: [
            {
              type: "card",
              header: {
                title: "",
                //  "${enterprise_name}<span style='color:red;font-size:12px'>${gmtCreate}</span>",
              },
              titleClassName: "headerTitle",
              data: "${first}",
              body: [
                {
                  name: "policyTitle",
                  label: "申报政策",
                  type: "static",
                  labelClassName: "leftTitle",
                },
                // policyTitle
                //                       gmtCreate
                {
                  name: "gmtCreate",
                  label: "申报时间",
                  type: "date",
                  // "src": "${img}",
                  disabledOn: "true",
                  labelClassName: "leftTitle",
                },
                {
                  name: "enterpriseName",
                  label: "企业名称",
                  type: "static",
                  // "src": "${img}",
                  disabledOn: "true",
                  labelClassName: "leftTitle",
                },
                {
                  name: "submitMobile",
                  label: "联系方式",
                  type: "static",
                  labelClassName: "leftTitle",
                },
                {
                  name: "remark",
                  label: "申报描述",
                  type: "static",
                  labelClassName: "leftTitle",
                },
                {
                  type: "list",
                  label: "附件",
                  source: "$relationFiles",
                  listItem: {
                    body: [
                      {
                        type: "tpl",
                        tpl: "<a href='${relationFiles[index].path}'>${relationFiles[index].name}</a>",
                      },
                    ],
                  },
                },
              ],
              mode: "horizontal",
              id: "qqq",
            },
            {
              type: "card",
              disabledOn: "${dealStatus !== '0'}",
              header: {
                title:
                  "${enterprise_name1}<span style='color:red;font-size:12px'>${gmtCreate1}</span>",
              },
              titleClassName: "headerTitle",
              body: [
                {
                  name: "dealStatus",
                  name: "${dealStatus==='0'? '申报中': (dealStatus==='1' ?  '已通过' : '已退回') }",
                  label: "状态",
                  type: "static",
                  labelClassName: "leftTitle",
                },
                {
                  name: "record.remark",
                  label: "原因",
                  type: "static",
                  labelClassName: "leftTitle",
                },
                // {
                //   "name": "record.uploadFileList",
                //   "label": "文件",
                //   "type": "input-file",
                //   // "src": "${img}",
                //   "disabledOn": "true",
                //   "labelClassName": "leftTitle"
                // },
                {
                  label: "附件",
                  type: "list",
                  source: "$record.uploadFileList",
                  listItem: {
                    body: [
                      {
                        type: "tpl",
                        tpl: "<a href='${record.uploadFileList[index].path}'>${record.uploadFileList[index].name}</a>",
                      },
                    ],
                  },
                },
              ],

              id: "u:15008a1115bf",
            },
           
          ],
        },
      ],
      actions: [],
    },

    // 经营数据上报
    business: {
      type: "page",
      className: "formMain",

      initApi: {
        url: `enterprise/business/getOne`,
        headers: {
          token: `${token()}`,
        },
        method: "post",
        data: {
          businessCode: "enterprise_business",
          conditions: [
            {
              key: "id",
              value: data.originId,
            },
          ],
        },
        responseData: {
          "&": "$$",
        },
      },
      body: [
        {
          id: "u:775fee587691",
          type: "form",
          title: "",
          mode: "horizontal",
          dsType: "api",
          feat: "Insert",
          actions: [],
          body: [
            {
              type: "grid",
              columns: [
                {
                  body: [
                    {
                      type: "input-text",
                      label: "企业名称",
                      name: "enterprise_name",
                      id: "u:4fbe06f9ba6e",
                      required: true,
                      disabledOn: "true",
                    },
                    {
                      type: "input-text",
                      label: "统一社会信用代码",
                      name: "uni_code",
                      id: "u:2e21df42228e",
                      required: true,
                      disabledOn: "true",
                    },
                    {
                      type: "select",
                      name: "fillingRange",
                      label: "经营周期",
                      required: true,
                      disabledOn: "true",
                      labelClassName: "text-muted",
                      inline: true,
                      source: {
                        url: `enterprise/business/getFillingRanges?type=1`,
                        headers: {
                          token: `${token()}`,
                        },
                      },
                    },
                    {
                      type: "input-number",
                      label: "产值(万元)",
                      placeholder: "输入企业产值",
                      name: "output_value",
                      id: "u:90235be82f16",
                      required: true,
                      disabledOn: "true",
                    },
                    {
                      type: "input-number",
                      label: "税收(万元)",
                      placeholder: "输入企业税收",
                      name: "tax",
                      id: "u:68c278f0728c",
                      required: true,
                      disabledOn: "true",
                    },
                    {
                      type: "input-number",
                      label: "净利润 (万元)",
                      name: "profit",
                      id: "u:6657aa7e303e",
                     // required: true,
                      disabledOn: "true",
                    },
                  ],
                  id: "u:0ae4306f18d4",
                },
                {
                  body: [
                    {
                      type: "radios",
                      label: "是否高新",
                      name: "is_high_tech",
                      options: [
                        {
                          label: "是",
                          value: "1",
                        },
                        {
                          label: "否",
                          value: "0",
                        },
                      ],
                      id: "u:6129f795e34b",
                      value: "",
                      // "required": true,
                      disabledOn: "true",
                    },
                    {
                      type: "input-number",
                      label: "在职人数",
                      name: "employee",
                      placeholder: "输入企业本季度在职人数",
                      id: "u:7f78eb4b60d2",
                      // "required": true,
                      disabledOn: "true",
                    },
                    {
                      type: "input-text",
                      label: "人才数量",
                      placeholder: "输入企业本季度在职人数",
                      name: "talents_num",
                      id: "u:d1c1b7042444",
                      // "required": true,
                      disabledOn: "true",
                    },
                    {
                      type: "input-text",
                      label: "研发投入",
                      placeholder: "输入研发投入金额",
                      name: "rd_investment",
                      id: "u:9f8542a0741c",
                      //required: true,
                      disabledOn: "true",
                    },
                    {
                      type: "input-text",
                      label: "主营业务",
                      placeholder: "请输入企业主营业务",
                      name: "main_business",
                      id: "u:0da6a952d062",
                      // "required": true,
                      disabledOn: "true",
                    },
                    {
                      type: "input-text",
                      label: "未来发展方向",
                      placeholder: "未来发展方向",
                      name: "main_development",
                      id: "u:bf310d72ef9a",
                      disabledOn: "true",
                      // "required": true,
                    },
                  ],
                  id: "u:b476231f301e",
                },
              ],
              className:'gridClass',
              id: "u:75ca2cc6031a",
            },
            {
              type: "textarea",
              label: "职称情况",
              name: "pro_title_status",
              id: "u:fef68dd4f4e0",
              minRows: 3,
              maxRows: 20,

              maxLength: 200,
              placeholder: "请输入职称情况(中高级及以上),最多输入200字",
              // "required": true,
              disabledOn: "true",
            },
            {
              type: "textarea",
              label: "技术成果和专利情况",
              placeholder: "输入技术成果或专利情况,最多输入200字",
              name: "patents_achieve",
              id: "u:4583b95d042e",
              minRows: 3,
              disabledOn: "true",
              maxRows: 20,
              maxLength: 200,
            },
            {
              type: "input-file",
              name: "attachment",
              label: "附件",
              id: "u:d41a4dff65af",
              disabledOn: "true",
              useChunk:false,
              downloadUrl:false,
              //  maxLength:1,
              // "receiver": {
              //   "method": "POST",
              //   "url": `upload`,
              //   "headers": {
              //     "token": `${token()}`
              //   },
              // },
            },
          ],
          // api: {
          //   url: `enterprise/business/saveMyBusiness`,
          //   method: "post",
          //   headers: {
          //     token: `${token()}`,
          //   },
          // },

          resetAfterSubmit: true,
        },
      ],
      title: "",
      id: "u:c070dd0c3b4e",
    },
  };
  return doms[data.key];
};
