<template>
  <!-- my 收藏 -->
  <!-- <el-button type="primary">全部已读</el-button> -->
  <div v-if="!showHomePage">
    <div class="search">
      <div>办事类型：</div>
      <el-select
        v-model="select"
        placeholder="请输入"
        size="default"
        style="width: 250px"
        @change="changeSelect"
      >
        <el-option
          v-for="item in options"
          :label="item.label"
          :value="item.value"
          :key="item.value"
        />
      </el-select>
    </div>
  </div>
  <div class="search noData" v-if="listData && listData?.length === 0">
    <NoData />
  </div>
  <div v-else>
    <div v-if="showHomePage">
      <div class="recordContent">
        <div
          class="recordList"
          v-for="item in listData"
          :key="item"
          @click="handleOpen(item)"
        >
          <div class="tips">
            {{
              item.type === "3"
                ? ""
                : `${state[item.type]?.status[item.status] || ""}`
            }}
          </div>
          <div class="showText">
            <span class="policy" :class="'point' + item.type">
              {{ ["物业报修", "经营诉求", "经营数据上报"][item.type] }}</span
            >
            <span class="value"> {{ item.title }}</span>
          </div>
          <div class="showText">
            <span class="timeLabel">提交时间：{{ item.submitTime }}</span>
            <!-- <span class="labelText">这是政策名称></span> -->
          </div>
        </div>
        <!-- <div class="nodata" style="margin-top: 40%">
            <div class="noimg"></div>
            暂无数据
          </div> -->
      </div>
    </div>
    <div v-else>
      <div class="list">
        <div class="listItem" v-for="item in listData" :key="item">
          <div class="right">
            <div class="title">
              <span class="point" :class="'point' + item.type">
                {{ ["物业报修", "经营诉求", "经营数据上报"][item.type] }}
              </span>
              <span> {{ item.title }}</span>
            </div>
            <div class="time">提交时间：{{ item.submitTime }}</div>
          </div>
          <div class="left">
            <div class="des">
              {{
                item.type === "3"
                  ? ""
                  : ` 处理结果: ${state[item.type]?.status[item.status] || "-"}`
              }}
            </div>
          </div>

          <div class="button" @click="handleOpen(item)">
            查看详情>
            <!-- <el-button type="primary" size='default'></el-button> -->
          </div>
        </div>
      </div>
      <div class="pageNation">
        <el-pagination
          v-model:current-page="pageNum"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 40, 50]"
          layout="total,  prev, pager, next,sizes, jumper"
          :total="totalNum"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </div>
  </div>

  <!--  :top="(currentAction?.key !=='leaseStatement'&&currentAction?.key !== 'businessDataReporting')?'15vh':'50px'"
 -->
  <el-dialog
    v-model="dialogVisible"
    :title="currentAction?.title"
    custom-class=""
    top="50px"
    :before-close="handleClose"
    width="70%"
  >
    <div v-if="currentAction.type === '1'">
      <PropertyRepair origin="propertyRepair" :id="currentAction.originId" />
    </div>
    <div v-else>
      <OnlineDialog :currentAction="currentAction" />
    </div>
  </el-dialog>
</template>
<script setup>
import { onMounted, watch, ref, defineProps } from "vue";
import { getMyRecord } from "@/api/personalCenter";
import NoData from "@/components/noData/index.vue";
import OnlineDialog from "./dialog/index.vue";

import PropertyRepair from "@/views/main/propertyRepair/com/details.vue";
const currentAction = ref({
  title: "物业报修",
  id: 1,
  key: "propertyRepair",
});
const dialogVisible = ref(false);
const props = defineProps(["showHomePage"]);
onMounted(() => {
  getData(null);
});
let select = ref(null);
// ["政策申报", "物业报修", "经营诉求", "经营数据上报"]
let state = [
  {
    status: {
      0: "申报中",
      1: "已通过",
      2: "已退回",
    },
  },
  {
    status: {
      0: "待分派",
      1: "待维修",
      2: "已完成",
    },
  },
  {
    status: {
      0: "未回复",
      1: "已回复",
    },
  },
];
const options = [
  {
    label: "全部",
    value: null,
  },
  {
    label: "物业报修",
    value: 1,
  },
  {
    label: "经营诉求",
    value: 2,
  },
  {
    label: "经营数据上报",
    value: 3,
  },
];

let listData = ref([]);
let totalNum = ref();
let pageSize = ref(props.showHomePage ? 6 : 10);
let pageNum = ref(1);

const getData = (type) => {
  let userState = localStorage.getItem("userState");
  if (userState === "admin") {
    return;
  }
  getMyRecord({
    type: type,
    pageSize: pageSize.value,
    pageNum: pageNum.value,
  }).then((res) => {
    listData.value = res.data.records;
    totalNum.value = Number(res.data.total);
  });
};
const handleSizeChange = (val) => {
  pageSize.value = val;
  getData(select.value);
};
const handleCurrentChange = (val) => {
  pageNum.value = val;
  getData(select.value);
};
const changeSelect = (type) => {
  getData(type);
};

const handleOpen = (item) => {
  dialogVisible.value = true;
  // amis,0,amis,0
  // ["政策申报", "物业报修", "经营诉求", "经营数据上报"]
  ["policy", "propertyRepair", "businessAppeal", "business"];

  currentAction.value = {
    ...item,
    key: ["policy", "propertyRepair", "businessAppeal", "business"][item.type],
  };
};
</script>

<style scoped lang="scss">
.list {
  // border-bottom: 1px solid #dcdfe6;
  .listItem {
    width: 100%;
    margin-bottom: 15px;
    align-items: center;
    display: flex;
    justify-content: space-between;

    padding: 15px;
    background: #f7f9fd;
    border-radius: 4px 4px 4px 4px;
    opacity: 1;
    .right {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      align-content: center;
      width: 700px;
      flex-wrap: wrap;
      padding-right: 10px;

      .title {
        width: 100%;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        display: flex;
        align-items: center;
        margin-bottom: 5px;
      }
      .point {
        padding: 0 5px;
        min-width: 67px;
        border-radius: 2px 2px 2px 2px;
        display: block;

        // margin-top: 4px;
        margin-right: 5px;
      }

      .des {
        width: 100%;
        font-size: 18px;
        color: #747474;
      }
      .time {
        width: 100%;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
    .left {
      width: calc(100% - 700px);
      min-width: 200px;
      // height: 90px;
      // background: red;
      margin-right: 20px;
    }
    .button {
      width: 130px;

      font-size: 12px;
      // font-weight: 400;
      color: #437bff;
    }
  }
}
.noData {
  justify-content: center;
}
.search {
  width: 100%;
  height: 88%;

  display: flex;
  flex-wrap: nowrap;
  margin-bottom: 15px;
  align-items: center;
  div {
    &:first-child {
      margin-right: 15px;
      font-size: 14px;
    }
  }
}
.recordContent {
  padding: 16px;
}

.recordList {
  position: relative;
  width: 100%;
  // height: 75px;
  background: #f7f9fd;
  border-radius: 4px 4px 4px 4px;
  padding: 13px;
  color: black;
  display: flex;
  align-content: space-around;
  flex-wrap: wrap;
  margin-bottom: 15px;
  .showText {
    width: 87%;
  }
  .tips {
    right: 0;
    top: 0;
    position: absolute;
    // float: right;
    padding: 0px 12px;
    // width: 50px;
    background: #437bff;
    border-radius: 0px 4px 0px 4px;
    color: white;
    text-align: center;
  }
  .value {
    color: rgba(0, 0, 0, 0.65);
  }
  .timeLabel {
    margin-top: 8px;
    display: inherit;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
  }

  .policy {
    margin-right: 10px;
    padding: 1px 6px;
    //                 width: 56px;
    // height: 20px;
    // background: #E4F2FF;
    border-radius: 2px 2px 2px 2px;
    //                 width: 48px;
    // height: 26px;
    // color: #1483EB;
  }
}
.point1 {
  background: rgba(251, 244, 226, 1);
  color: #f68a25;
}
.point2 {
  background: rgba(223, 243, 235, 1);
  color: rgba(19, 188, 56, 1);
}
.point0,
.point3 {
  background: rgba(228, 242, 255, 1);
  color: rgba(58, 161, 255, 1);
}
</style>
