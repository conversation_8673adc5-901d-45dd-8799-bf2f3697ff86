<template>
    <div v-if="!showHomePage">
      <div class="search">
        <div>类型</div>
        <el-select
          v-model="select"
          placeholder="请选择"
          size="default"
          style="width: 250px"
          @change="changeSelect"
        >
          <el-option
            v-for="item in options"
            :label="item.label"
            :value="item.value"
            :key="item.value"
          />
        </el-select>
      </div>
      </div>
  <div v-if="listData?.length===0">
    <NoData />
  </div>
  <div v-else>
    <div v-if="!showHomePage">
      <!-- my 收藏 -->
      <!-- <el-button type="primary">全部已读</el-button> -->
      <div class="list">
        <div class="listItem" v-for="item in listData" :key="item" @click="showDetail(item)">
          <div class="left">
            <div class="title">
              <span>{{ item?.title }}</span>
              <!-- type -->
              <!--  -->
              <span class="point point2"  v-if="item.type==='2'">政策 </span>  
              <!-- <span class="point" :class="'point' + item.type">{{
                ["", "公告", "政策", "党建要闻", "党政法规", "活动"][item.type]
              }}</span> -->
            </div>
          </div>
          <div class="right">
            <div class="time"> 
              {{ item?.publishTime&&dayjs(Number(item?.publishTime)).format('YYYY/MM/DD')||'' }}
     
            </div>
          </div>
        </div>
      </div>
      <div class="pageNation">
        <el-pagination
          v-model:current-page="pageNum"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 40, 50]"
          layout="total,  prev, pager, next,sizes, jumper"
          :total="totalNum"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
        />
      </div>
    </div>
    <div v-else>
      <div class="content message"   >
        <div class="text" v-for="item in listData" :key="item" @click="showDetail(item)">
          <div class="textTitle" >
            <span> {{ item.title }}</span>
          <span class="point point2"   v-if="item.type==='2'">政策 </span>  
          </div>
         
          <span class="time">
            {{ item?.publishTime&&dayjs(Number(item?.publishTime)).format('YYYY/MM/DD')||'' }}</span
          >
        </div>
      </div>
      
        <!-- <div class="search noData" v-else>
    <NoData />
  </div> -->
      
    </div>
  </div>
  <el-dialog custom-class="screenTable1 padding0Dialog" v-model="innerVisible" width="30%" append-to-body>

  <!-- <el-dialog custom-class="screenTable padding0Dialog"   :title="headerTitle" v-model="innerVisible" width="30%" append-to-body> -->
  <!-- <el-dialog 

  v-model="innerVisible" width="30%" append-to-body> -->
        <DialogDetail state='personalCenter' :innerVisible="innerVisible" @changeInner="changeInner" :detailData="detailData" :title="headerTitle" />
    </el-dialog>
</template>
<script setup>
import { Star } from "@element-plus/icons";
import { onMounted, watch, ref } from "vue";
import { getMyCollect } from "@/api/personalCenter";
import NoData from "@/components/noData/index.vue";
import dayjs from 'dayjs'
import DialogDetail from '@/components/screenTable/dialog/detail.vue'
const props = defineProps(["showHomePage"]);
onMounted(() => {
  getData(null);
});
let innerVisible=ref(false)
let headerTitle=ref('')
const detailData = ref()
let select = ref(null);
const options = [
  {
    label: "全部",
    value: null,
  },
  {
    label: "公告",
    value: 1,
  },
  {
    label: "政策",
    value: 2,
  },
  {
    label: "党建要闻",
    value: 3,
  },
  {
    label: "党政法规",
    value: 4,
  },
  {
    label: "活动",
    value: 5,
  },
];
let listData = ref(null);
let totalNum = ref();
let pageSize = ref(props.showHomePage ? 5 : 10);
let pageNum = ref(1);
const changeInner=()=>{

}
const showDetail=(data)=>{
  innerVisible.value=true
  detailData.value=data
  headerTitle.value=data.title
}
const getData = (type) => {
  getMyCollect({
    type: type&&[type],
    pageSize: pageSize.value,
    pageNum: pageNum.value,
  }).then((res) => {
    listData.value = res.data.records;
    totalNum.value = Number(res.data.totalNum);
  });
};
const handleSizeChange = (val) => {
  pageSize.value = val;
  getData(select.value);
};
const handleCurrentChange = (val) => {
  pageNum.value = val;
  getData(select.value);
};
const changeSelect = (type) => {
  getData(type);
};
</script>

<style scoped lang="scss">
.list {
  // border-bottom: 1px solid #dcdfe6;
  .listItem {
    height: 50px;
    background: #f7f9fd;
    border-radius: 4px 4px 4px 4px;
    width: 100%;
    margin-bottom: 15px;
    padding: 0 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .right {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      align-content: center;
      width: 100px;
      flex-wrap: wrap;

      .time {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
      }
    }
    .left {
      .title {
        font-size: 14px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.65);
        width: 100%;
        display: flex;
        align-items: center;
        cursor: pointer;
      }

      .des {
        font-size: 18px;
        color: #747474;
      }
      width: calc(100% - 120px);

      align-items: center;
      // background: red;
      margin-right: 20px;
    }
  }
}
.search {
  width: 100%;
  display: flex;
  flex-wrap: nowrap;
  margin-bottom: 15px;
  align-items: center;
  div {
    &:first-child {
      margin-right: 15px;
      font-size: 14px;
    }
  }
}
.message {
  display: flex;
  flex-wrap: wrap;
  padding: 0px 16px;
  .text {
    display: flex;
    justify-content: space-between;
    width: 100%;
.textTitle{
  margin-bottom: 5px;
  display: flex;

span{
  &:first-child{
    cursor: pointer;
    display: block;
  overflow: hidden; //超出的文本隐藏
        text-overflow: ellipsis; //溢出用省略号显示
        white-space: nowrap; //溢出不换行
        max-width: calc(100% - 100px);
}
  
}
  width:  calc(100% - 100px)
  // display: flex;

}
    .time {
      text-align: right;
        font-size: 12px;
        color: rgba(0,0,0,0.45);
        display: block;
        width: 100px;
    }
  }
}
.point {
        padding: 0px 5px;
        border-radius: 2px 2px 2px 2px;

        display: block;

        font-size: 12px;
        margin-left: 10px;
      }
      .point1 {
        color: rgba(19, 188, 56, 1);
        border: 1px solid rgba(19, 188, 56, 1);
      }
      .point2 {
        border: 1px solid rgba(246, 138, 37, 0.6);
        color: #f68a25;
      }
      .point3 {
        border: 1px solid rgba(246, 138, 37, 0.6);
        color: #f68a25;
      }
      .point4 {
        border: 1px solid rgba(19, 188, 56, 1);
        color: rgba(19, 188, 56, 1);
      }
      .point5 {
        border: 1px solid rgba(58, 161, 255, 1);
        color: rgba(58, 161, 255, 1);
      }
</style>

<style lang="scss">
      .screenTable1 {
    // border: 1px solid rgba(64, 129, 203, 0.7);
    width: 1100px;
    height: 700px;
    // background-repeat: no-repeat;
    // background-position: center;
    // background-size: 101% 101%;
    border-radius: 5px;
  background :white;

    .el-dialog__headerbtn {
        // right: 0px;
        // padding: 05px;
        right: -7px;
    top: -1px;
    padding: 0;
    }
}
.padding0Dialog{
    .el-dialog__body {
        padding: 0 30px !important;
  }
}
.noData{
  justify-content: center;
}
</style>
 