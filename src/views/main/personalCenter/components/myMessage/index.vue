<template>
    <!-- my message -->
  <div v-if="(listData?.length===0)">
    <NoData/>
  </div> 
  <div v-else>
    <div v-if="!showHomePage">
    <!-- <div class="topButton">
        <el-button type="" size="default" @click="setMyPageRead">全部已读</el-button>
    </div> -->
   
<div class="list">
    <div class="listItem" v-for="item in listData" :key="item">
        <div class="title">
       <span> {{item.title}}</span>

       <span class="point" v-if="item.isRead==='0'"></span>
    </div>
    <div class="des">
        {{item.content}}
    </div>
    <div class="time">
      {{item?.msgTime}}
        <!-- {{ item?.msgTime&&dayjs(Number(item?.msgTime)).format('YYYY/MM/DD')||'' }} -->
    </div>
    </div>
</div>
<div class="pageNation">
      <el-pagination
        v-model:current-page="pageNum"
        v-model:page-size="pageSize"
        :page-sizes="[10, 20, 30, 40, 50]"
        layout="total,  prev, pager, next,sizes, jumper"
        :total="totalNum"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        background
      />
    </div>
    </div>
    <div v-else>
        <div class="content message">
              <div  v-for="item in listData" :key="item">
                <span
                  > {{item.content}}</span
                >
                <span>  
                  {{item?.msgTime}}
                  <!-- {{ item?.msgTime&&dayjs(Number(item?.msgTime)).format('YYYY/MM/DD')||'' }} -->
                </span>
              </div>
             
            </div>
    </div>
  </div>
 
</template>
 
<script setup>
import { onMounted, watch, ref,defineProps } from "vue";
import { getMyMessage ,getMessageOnline,setMyPage} from "@/api/personalCenter";
import NoData from "@/components/noData/index.vue";
import dayjs from 'dayjs'
import { ElMessage } from 'element-plus'

const props=defineProps(['showHomePage'])
let listData = ref();
let totalNum = ref();
let pageSize = ref(props.showHomePage?5:10);
let pageNum = ref(1);
onMounted(() => {
  getData(null);
});

const getData = (type) => {
  if(props.showHomePage){
    getMessageOnline().then((res) => {
    listData.value = res.data;
    // totalNum.value = Number(res.data.pageTotal);
  });
  }else{
    getMyMessage({
    // type: type,
    pageSize: pageSize.value,
    pageNum: pageNum.value,
  }).then((res) => {
    listData.value = res.data.records;
    totalNum.value = Number(res.data.total);
  });
  }
  
 
};
const handleSizeChange = (val) => {
  pageSize.value = val;
  getData();
};
const handleCurrentChange = (val) => {
  pageNum.value = val;
  getData();
};
 const setMyPageRead=()=>{
    setMyPage().then((res)=>{
        if(res.status==='0'){
            ElMessage({
        type: 'success',
        message:  res.msg
      })
      getData()
        }
       
    })
 }
</script>


<style scoped lang='scss'>
.list{
  .listItem{
    margin-bottom: 4px;
    width: 100%;
    border-bottom: 1px solid #dcdfe6; 
   .title{
    padding-top: 15px;
font-size: 16px;
color: rgba(0,0,0,0.85);
display: flex;
    align-items: center;
    padding-bottom: 11px;
    font-weight: 600;
   
   } 
.point{
    display: block;
    width: 6px;
    height: 6px;
    background: red;
    border-radius: 4px;
    margin-left: 5px;
    margin-top: -13px;
}
   .des{
    font-size: 14px;

    color: rgba(0,0,0,0.65);
   }
   .time{
    font-size: 12px;
   
color: rgba(0,0,0,0.45);
padding-bottom: 15px;
   }
  }  
}

.topButton{
    width: 100%;
display: flex;
justify-content: end;
// button{

// }
// flex-wrap: flex-end;
}
.message {
    display: flex;
    flex-wrap: wrap;
    padding: 0px 16px;
    div {
        display: flex;
        justify-content: space-between;
        width: 100%;
margin-bottom: 5px;
        span {
            &:first-child {
                overflow: hidden; //超出的文本隐藏
                text-overflow: ellipsis; //溢出用省略号显示
                white-space: nowrap; //溢出不换行
                width: calc(100% - 130px);
            }

            &:last-child {
              text-align: right;
              font-size: 12px;
              color: rgba(0,0,0,0.45);
                display: block;
                width: 120px;
            }
        }
    }
}
</style>