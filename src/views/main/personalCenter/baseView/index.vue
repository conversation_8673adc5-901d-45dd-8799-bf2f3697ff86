<template>
  <div class="personalTabs">
    <el-tabs
      v-model="activeName"
      class="demo-tabs"
      size="default"
      @tab-click="handleClick"
    >
      <!--              
                <el-tab-pane  v-if="unread>0" :label="renderTabTitle()" name="first">
                
                </el-tab-pane>
      -->

      <el-tab-pane name="first">
        <template #label>
          <span class="message">
            我的消息
            <span class="unread" v-if="unread > 0">{{ unread }}</span></span
          >
        </template>
      </el-tab-pane>

      <!-- 我的消息 -->

      <el-tab-pane label="我的收藏" name="second">
        <!-- 我的收藏 -->
      </el-tab-pane>
      <el-tab-pane label="租约账单" name="third">
        <!-- 租约账单 -->
      </el-tab-pane>
      <el-tab-pane label="办事记录" name="fourth">
        <!-- 办事记录 -->
      </el-tab-pane>
    </el-tabs>
  </div>
  <div class="realContent">
    <div v-if="activeName === 'first'">
      <MyMessage />
    </div>
    <div v-if="activeName === 'second'">
      <MyCollect />
    </div>
    <div v-if="activeName === 'third'">
      <MyCheck />
    </div>
    <div v-if="activeName === 'fourth'">
      <MyRecord />
    </div>
  </div>
</template>
<script setup >
import { onMounted, onBeforeUnmount, ref, computed, onBeforeMount } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useStore } from "vuex";
import MyMessage from "../components/myMessage/index.vue";
import MyCollect from "../components/myCollect/index.vue";
import MyCheck from "../components/myCheck/index.vue";
import MyRecord from "../components/myRecord/index.vue";

const store = useStore();
const router = useRouter();
const route = useRoute();
// import type { TabsPaneContext } from 'element-plus'
import { countUnRead } from "@/api/personalCenter";

const activeName = ref("first");
const unread = ref();
onBeforeMount(() => {
  getRead();
});
onMounted(() => {
  activeName.value = route.query.nameState;
});
const getRead = () => {
  if (localStorage.getItem("userState") === "user") {
    countUnRead().then((res) => {
      unread.value = res.data.unread;
    });
  }
};
const handleClick = (tab, event) => {
  //   console.log(tab.alue, event)
};
const renderTabTitle = (item) => {
  // 自定义标题渲染方法
  //   return (
  //     <span>
  //         我的消息<span>
  //        {unread.value}
  //     </span>
  //     </span>
  //   );
};
</script>
<style lang='scss'>
.personalTabs {
  .el-tabs__content {
    display: none;
    //   padding: 32px;
    //   color: #6b778c;
    //   font-size: 32px;
    //   font-weight: 600;
  }
  .el-tabs__nav-scroll {
    padding: 5px 40px 0px;
    .el-tabs__item {
      // font-size: 16px;
      // background: RED;;
      // height: 50px !important;
      // line-height: 50px;
    }
  }
}
</style>
<style scoped>
.personalTabs {
  /* margin-top: 50px; */
}
.realContent {
  padding: 0 20px 20px 20px;
}
.message {
  position: relative;
}
.unread {
  height: 17px;
  padding: 0px 5px;
  line-height: 16px;
  text-align: center;
  font-weight: 400;
  font-size: 12px;
  color: white;
  border-radius: 12px;
  background: red;
  margin-top: -6px;
  transform: scale(0.8);
  position: absolute;
}
</style>