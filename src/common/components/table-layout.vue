<!--列表-->
<template>
  <!-- <div @click="getHeight">aaa</div> -->
  <div class="ms-doc">
    <!--  查询条件 -->
    <div
      class="vue_search ms-title"
      :style="{ height: isShowParams ? 'auto' : '0px' }"
      @click="$emit('query')"
    >
      <slot name="elForm" />
    </div>
    <slot name="selected" />
    <div class="ms-table">
      <slot name="selTable" />
    </div>
  </div>
</template>

<script>
/* import Breadcrumb from '@/components/Breadcrumb'
import TablePagination from './table-pagination' */
/**
 * @property pageNo 当前tab索引，默认为 1
 * @property pageSize 当前tab名称数组
 * @property totalCount 是否显示查询右边向下箭头，默认 true 显示
 */
export default {
  name: 'TableLayout',
  components: {
    /*     TablePagination,
    Breadcrumb */
  },
  props: {
    pageNo: {
      type: Number,
      default: 0,
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    totalCount: {
      type: Number,
      default: 0,
    },

    selTabIndex: {
      type: Number,
      default: 1,
    },
    tabNameList: {
      type: Array,
      default: () => [],
    },
    //是否显示查询向下箭头
    showArrow: {
      type: Boolean,
      default: true,
    },
    //页面帮助显示
    isShowPageHelp: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isShowParams: true,
      border: {},
    };
  },
  computed: {},
  mounted() {},
  methods: {
    seltabway(index) {
      this.$emit('seltabway', index);
    },
    getHeight() {
      this.$emit('showParamStu');
      localStorage.setItem(
        'selectHeight',
        document.getElementById('app').clientHeight -
          this.$refs.msSelect.offsetHeight -
          600
      );
    },
    // 分页大小
    handleSizeChange(val) {
      this.$emit('size-change', val);
    },
    // 分页行数
    handleCurrentChange(val) {
      this.$emit('current-change', val);
    },
    //页面帮助说明事件
    onClickHelp(index) {
      this.$emit('onPageHelpClick', index);
    },
  },
};
</script>
<style lang="scss" scoped>
#app1 .vue_search {
  border-radius: 10px 10px 10px 10px !important;
  margin-bottom: 15px !important;
  padding-top: 0px !important;
  margin-top: 0 !important ;
  margin-left: 20px;
  margin-right: 20px;
  background: #fff;
    box-shadow: 0px 4px 12px 0px #eef1f8;
  .el-form-item {
    margin-bottom: 16px !important;
  }
}
.vue_search .demo-form-inline {
  margin: 0px !important;
  padding: 16px 0px 0px 0px !important;
  overflow: hidden;
}
.newStyle {
  background: #ffffff;
  box-shadow: 0px 4px 12px 0px #eef1f8;
  border-radius: 10px 10px 10px 10px;
}
.ms-table {
  background: #fff;
  padding: 20px 30px;
  margin: 0 20px 20px 20px;
  box-shadow: 4px 4px 10px 0px #eef1f8;
  border-radius: 10px;
  //   padding: 16px 32px;
  // //   width: 1198px;
  // // height: 748px;
  // background: #FFFFFF;
  // box-shadow: 0px 4px 12px 0px #EEF1F8;
  // border-radius: 10px 10px 10px 10px;
}
</style>
<style lang="scss">
#app1 {
  .el-table .el-table__body-wrapper.is-scrolling-none {
    min-height: 350px !important;
    .el-table__empty-block {
      min-height: 350px !important;
    }
  }
}
.el-table::before {
  height: 0 !important;
}
#app1 .el-table thead th {
  background: linear-gradient(180deg, #fafbff 0%, #f0f4ff 100%) !important;
}

.el-dialog,
.el-message-box__wrapper,
.el-message-box__wrapper .el-message-box {
  border-radius: 10px !important;
}
.el-message-box__header .el-message-box__title {
  padding: 10px !important;
}
.el-message-box__content {
  padding: 10px 25px !important;
}
.el-message-box__btns {
  margin-bottom: 10px !important;
}
</style>
