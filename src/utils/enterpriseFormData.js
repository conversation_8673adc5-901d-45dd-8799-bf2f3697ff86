export const enterpriseData = (state) => {

  const baseInfo = {
    name: '企业基本信息',
    data: [
      {
        type: 'input-text',
        name: 'name',
        label: '企业名称',
        size: 'lg',
        required: true,
      },
      {
        type: 'input-text',
        name: 'uni_code',
        label: '企业信用代码',
        size: 'lg',
        required: true,
        validations: 'matchRegexp:/^[A-Za-z0-9]{18}$/',
        validationErrors: {
          matchRegexp: '企业信用代码必须是18位的字母和数字组成',
        },
      },
      {
        type: 'select',
        name: 'rent_status',
        size: 'lg',
        label: '入驻状态',
        required: true,
        value: 'true',
        options: [
          { label: '入驻中', value: 'true' },
          { label: '未入驻', value: 'false' },
        ],
      },
      {
        type: 'input-date',
        name: 'establish_date',
        size: 'lg',
        label: '注册成立时间',
        valueFormat: 'x',
        required: true,
      },
      {
        type: 'input-text',
        name: 'enterprise_level',
        size: 'lg',
        label: '企业层级',
        placeholder:  state === 'preview' ? '无' : '请输入是否高新、专精特新等',
        required: true,
      },
      {
        type: 'select',
        name: 'enterprise_type',
        label: '企业类型',
        size: 'lg',
        required: true,
        value: '1',
        options: [
          { label: '独资', value: '1' },
          { label: '合资', value: '2' },
          { label: '民营', value: '3' },
          { label: '国有', value: '4' },
          { label: '股份制', value: '5' },
          { label: '其他', value: '0' },
        ],
      },
      {
        type: 'select',
        name: 'industry_type',
        size: 'lg',
        label: '行业分类',
        required: true,
        value: '1',
        options: [
          { label: '车联网', value: '1' },
          { label: '低空经济', value: '2' },
          { label: '数字基建', value: '3' },
          { label: '智能制造', value: '4' },
          { label: '其他', value: '0' },
        ],
      },
      {
        name: 'is_innovative',
        size: 'lg',
        type: 'radios',
        label: '是否为规上',
        required: true,
        options: [
          {
            label: '否',
            value: 0,
          },
          {
            label: '是',
            value: 1,
          },
        ],
        value: 0,
      },
      {
        name: 'is_high_tech',
        size: 'lg',
        type: 'radios',
        label: '是否为高新',
        required: true,
        options: [
          {
            label: '否',
            value: 0,
          },
          {
            label: '是',
            value: 1,
          },
        ],
        value: 0,
      },
     
      {
        type: 'input-text',
        name: 'contacts',
        label: '企业联系人',
        size: 'lg',
        required: true,
      },
      {
        type: 'input-text',
        name: 'contacts_way',
        size: 'lg',
        label: '企业联系方式',
        required: true,
      },
      {
        type: 'input-text',
        name: 'legal_person',
        label: '企业法人代表',
        required: true,
        size: 'lg',
      },
      {
        type: 'input-text',
        name: 'legal_person_contacts',
        label: '法人联系方式',
        size: 'lg',
      },
      {
        type: 'input-date',
        name: 'settle_date',
        required: true,
        label: '入驻日期',
        size: 'lg',
        valueFormat: 'x',
      },
      {
        name: 'is_mount',
        type: 'radios',
        label: '是否挂载企业',
        required: true,
        size: 'lg',
        value: 0,
        options: [
          {
            label: '否',
            value: 0,
          },
          {
            label: '是',
            value: 1,
          },
        ],
      },
      {
        type: 'select',
        size: 'lg',
        name: 'special_type',
        label: '园区载体配套',
        required: true,
        value: 0,
        options: [
          { label: '餐饮服务', value: '1' },
          { label: '产教融合基地', value: '2' },
          { label: '检验检测服务', value: '3' },
          { label: '其他', value: '0' },
        ],
      },
      // {
      //   type: 'page',
      //   size: 'lg',
      //   body: '',
      //   className: 'page-b-md height0 m-l font-bold',
      // },
       {
        type: 'input-number',
        name: 'registered_capital',
        min: 0,
        size: 'lg',
        label: '注册资产(万元)',
        required: true,
        precision: 3,
      },
      {
        type: 'input-number',
        name: 'fixed_assets',
        label: '投资总额/固定资产（万元）',
        size: 'lg',
        min: 0,
        required: true,
        precision: 3,
      },
      {
        name: 'business_scope',
        type: 'textarea',
        label: '行业领域(经营范围)',
        showCounter: true,
        maxLength: 500,
        className: 'textarea-custemor',
        placeholder: state === 'preview' ? '无' : '请输入行业领域(经营范围),不超500字',
      },
      // {
      //   type: 'page',
      //   size: 'lg',
      //   body: '',
      //   className: 'page-b-md  height0 m-l font-bold',
      // },
      {
        name: 'desc',
        type: 'textarea',
        label: '企业简介',
        showCounter: true,
        maxLength: 500,
        className: 'textarea-custemor',
        placeholder: state === 'preview' ? '无' : '请输入企业简介,不超过500字',
      },
    ],
  };
  const baseInfoCard = getForm(baseInfo,state);
  const personInfo = {
    name: '企业人员信息',
    data: [
      // {
      //   type: 'page',
      //   size: 'lg',
      //   body: 'height0',
      //   className: 'page-b-md m-l font-bold',
      // },
      {
        type: 'input-number',
        name: 'employee',
        size: 'lg',
        label: '企业人员总数',
        min: 0,
        required: true,
      },
      {
        type: 'input-number',
        name: 'rd_personnel',
        label: '研发人员总数',
        min: 0,
        size: 'lg',
        required: true,
      },
    ],
  };
  const personInfoCard = getForm(personInfo,state);

  const registerInfo = {
    name: '户籍比例',
    data: [
      {
        type: 'input-number',
        name: 'hometown_local',
        min: 0,
        size: 'lg',
        label: '本地户籍比例(%)',
      },
      {
        type: 'input-number',
        name: 'hometown_other',
        min: 0,
        size: 'lg',
        label: '外地户籍比例(%)',
      },
    ],
  };
  const registerInfoCard = getForm(registerInfo,state);

  const skillInfo = {
    name: '职称/技能情况',
    data: [
      {
        type: 'input-number',
        name: 'title_large',
        min: 0,
        size: 'lg',
        label: '高级职称数量（人）',
        required: true,
      },
      {
        type: 'input-number',
        name: 'title_middle',
        min: 0,
        size: 'lg',
        label: '中级职称数量（人）',
        required: true,
      },
    ],
  };
  const skillInfoCard = getForm(skillInfo,state);

  const educationInfo = {
    name: '员工学历情况',
    data: [
      {
        type: 'input-number',
        size: 'lg',
        min: 0,
        name: 'education_dr',
        label: '博士研究生（人）',
        required: true,
      },
      {
        type: 'input-number',
        name: 'education_master',
        label: '硕士研究生（人）',
        min: 0,
        size: 'lg',
        required: true,
      },
      {
        type: 'input-number',
        name: 'education_bachelor',
        label: '大学本科（人）',
        min: 0,
        size: 'lg',
        required: true,
      },
      {
        type: 'input-number',
        name: 'education_vocational',
        size: 'lg',
        label: '专科、高职及以下（人）',
        min: 0,
        required: true,
      },
    ],
  };
  const educationInfoCard = getForm(educationInfo,state);

  const ageInfo = {
    name: '年龄情况',
    data: [
      {
        type: 'input-number',
        name: 'age_0_35',
        label: '35岁及以下人员（人）',
        size: 'lg',
        min: 0,
        required: true,
      },
      {
        type: 'input-number',
        name: 'age_35_45',
        label: '35-45岁人员（人）',
        size: 'lg',
        min: 0,
        required: true,
      },
      {
        type: 'input-number',
        name: 'age_45_55',
        label: '45-55岁人员（人）',
        size: 'lg',
        min: 0,
        required: true,
      },
      {
        type: 'input-number',
        name: 'age_55_100',
        size: 'lg',
        min: 0,
        label: '55岁以上人员（人）',
        required: true,
      },
      {
        name: 'employee_education',
        type: 'textarea',
        className: 'textarea-custemor',
        required: true,
        label: "省、市、区级人才评定情况(如入选'紫琅英才引进计划'等')",
        showCounter: true,
        maxLength: 500,
        placeholder: state === 'preview' ? '无' : '请输入内容,不超500字',
      },
    ],
  };
  const ageInfoCard = getForm(ageInfo,state);

  const manageInfo = {
    name: '企业经营信息',
    data: [
      {
        type: 'input-number',
        name: 'last_year_output',
        size: 'lg',
        label: '上年度总产值（万元）',
        min: 0,
        required: true,
        precision: 3,
      },
      {
        type: 'input-number',
        name: 'this_year_output',
        label: '本年度预计总产值（万元）',
        min: 0,
        required: true,
        size: 'lg',
        precision: 3,
      },
      {
        type: 'input-number',
        name: 'last_year_input',
        label: '上年度主营业务收入（万元）',
        size: 'lg',
        min: 0,
        required: true,
        precision: 3,
      },
      {
        type: 'input-number',
        name: 'this_year_input',
        min: 0,
        size: 'lg',
        label: '本年度预计主营业务收入（万元）',
        required: true,
        precision: 3,
      },
      {
        type: 'input-number',
        name: 'last_year_tax',
        size: 'lg',
        min: 0,
        label: '上年度税收（万元）',
        required: true,
        precision: 3,
      },
      {
        type: 'input-number',
        min: 0,
        name: 'this_year_tax',
        label: '本年度预计税收（万元）',
        required: true,
        size: 'lg',
        precision: 3,
      },
      {
        type: 'input-number',
        name: 'high_tech_service_input',
        label: '高新技术产品服务收入（万元）',
        min: 0,
        size: 'lg',
        required: true,
        precision: 3,
      },
      {
        type: 'input-number',
        name: 'rd_investment',
        label: '上年度研发投入（万元）',
        min: 0,
        size: 'lg',
        required: true,
        precision: 3,
      },
      {
        type: 'input-text',
        name: 'rd_institutions',
        size: 'lg',
        label: '企业研发机构设立情况',
      },
      {
        type: 'input-text',
        name: 'rd_direction',
        size: 'lg',
        label: '企业研发方向',
      },
      // {
      //   type: 'page',
      //   size: 'lg',
      //   body: '',
      //   className: 'page-b-md height0 m-l font-bold',
      // },
      {
        name: 'product_advantage',
        type: 'textarea',
        className: 'textarea-custemor',
        label: '企业产品优势',
        showCounter: true,
        maxLength: 500,
        placeholder: state === 'preview' ? '无' : '请输入企业产品优势,不超500字',
      },
      // {
      //   type: 'page',
      //   size: 'lg',
      //   body: '',
      //   className: 'page-b-md height0 m-l font-bold',
      // },
      {
        name: 'IUR_cooperation',
        type: 'textarea',
        className: 'textarea-custemor',
        label: '企业产学研合作情况(是否相关合作需求)',
        showCounter: true,
        maxLength: 500,
        placeholder: state === 'preview' ? '无' : '请输入企业产学研合作情况,不超500字',
      },
      // {
      //   type: 'page',
      //   size: 'lg',
      //   body: '',
      //   className: 'page-b-md height0 m-l font-bold',
      // },
      {
        name: 'scientific_transformation',
        type: 'textarea',
        className: 'textarea-custemor',
        label: '科技成果转化项目情况',
        showCounter: true,
        maxLength: 500,
        placeholder: state === 'preview' ? '无' : '请输入科技成果转化项目情况,不超500字',
      },
      // {
      //   type: 'page',
      //   size: 'lg',
      //   body: '',
      //   className: 'page-b-md height0 m-l font-bold',
      // },
      {
        name: 'market_layout',
        type: 'textarea',
        className: 'textarea-custemor',
        label: '企业市场布局区域',
        showCounter: true,
        maxLength: 500,
        placeholder: state === 'preview' ? '无' : '请输入资质荣誉,不超500字',
      },
      // {
      //   type: 'page',
      //   size: 'lg',
      //   body: '',
      //   className: 'page-b-md height0 m-l font-bold',
      // },
      {
        name: 'develop_direction',
        type: 'textarea',
        label: '企业未来发展方向',
        className: 'textarea-custemor',
        showCounter: true,
        maxLength: 500,
        placeholder: state === 'preview' ? '无' : '请输入企业未来发展方向,不超500字',
      },

      // {
      //   type: 'page',
      //   size: 'lg',
      //   body: '',
      //   className: 'page-b-md height0 m-l font-bold',
      // },
      {
        name: 'achievement_patents',
        type: 'textarea',
        className: 'textarea-custemor',
        label: '主要技术成果或专利情况(发明专利/实用新型/外观专利)',
        showCounter: true,
        required: true,
        maxLength: 500,
        placeholder: state === 'preview' ? '无' : '请输入主要技术成果或专利情况,不超500字',
      },
      // {
      //   type: 'page',
      //   size: 'lg',
      //   body: '',
      //   className: 'page-b-md height0 m-l font-bold',
      // },
      {
        name: 'honors',
        type: 'textarea',
        className: 'textarea-custemor',
        label: '资质荣誉',
        showCounter: true,
        maxLength: 500,
        placeholder: state === 'preview' ? '无' : '请输入主要技术成果或专利情况,不超500字',
      },
      // {
      //   type: 'page',
      //   size: 'lg',
      //   body: '',
      //   className: 'page-b-md height0 m-l font-bold',
      // },
    ],
  };
  const manageInfoCard = getForm(manageInfo,state);

  return [
    baseInfoCard,
    personInfoCard,
    registerInfoCard,
    skillInfoCard,
    educationInfoCard,
    ageInfoCard,
    manageInfoCard,
  ];
};

const getForm = (side,state) => {
  return {
    type: 'flex',
    className: `p-1  p-border ${state}`,
    items: [
      {
        type: 'container',
        body: [
          {
            type: 'flex',
            className: 'p-1 form-title',
            items: [
              {
                type: 'container',
                body: [
                  {
                    type: 'tpl',
                    tpl: side.name,
                    inline: true,
                    wrapperComponent: '',
                    id: 'u:182e0e2bc97e',
                    className: 'title',
                  },
                ],
                size: 'xs',
                style: {
                  position: 'static',
                  display: 'block',
                  flex: '1 1 auto',
                  flexGrow: 1,
                  flexBasis: 'auto',
                },
                wrapperBody: false,
                isFixedHeight: false,
                isFixedWidth: false,
                id: 'u:446774e379ab',
              },
            ],
            style: {
              position: 'relative',
            },
            id: 'u:8a5c34da5573',
          },

          {
            type: 'flex',
            className: 'p-1',
            items: [
              {
                type: 'container',
                body: {
                  type: 'flex',
                  direction: 'row',
                  className: 'formCards',
                  wrap: true,
                  items: side.data,
                  //   .map((item, index) => ({
                  //   type: 'container',
                  //   size: 'xs',
                  //   style: {
                  //     width: '50%',
                  //     paddingRight: index % 2 === 0 ? '10px' : '0px',
                  //     paddingLeft: index % 2 === 1 ? '10px' : '0px',
                  //     marginBottom: '10px'
                  //   },
                  //   body: [item]
                  // }))
                },
                // size: 'xs',
                wrapperBody: false,
                isFixedHeight: false,
                isFixedWidth: false,
                id: 'u:446774e379ab',
              },
            ],
            style: {
              position: 'relative',
            },
            id: 'u:8a5c34da5573',
          },
        ],
        size: 'xs',
        style: {
          position: 'static',
          display: 'block',
          flex: '1 1 auto',
          flexGrow: 1,
          flexBasis: 'auto',
        },
        wrapperBody: false,
        isFixedHeight: false,
        isFixedWidth: false,
        id: 'u:446774e379ab',
      },
    ],
  };
};

export const transformObject = (obj) => {
  if (obj?.hasOwnProperty('other') && typeof obj.other === 'string') {
    try {
      const parsedData = JSON.parse(obj.other);
      Object.assign(obj, parsedData);
    } catch (e) {
      console.error('JSON 解析失败:', e);
    }
    delete obj.other;
  }
  return obj;
};

export const propertyFormData = [
  {
    type: 'input-text',
    name: 'enterpriseName',
    label: '企业名称',
    disabledOn: 'true',
  },
  {
    type: 'input-text',
    name: 'uniCode',
    label: '企业信用代码',
    disabledOn: 'true',
  },
  {
    name: 'leaseBillName',
    label: '账单',
    type: 'input-text',
    disabledOn: 'true',
  },
  {
    type: 'input-text',
    name: 'houseAreas',
    label: '租赁面积',
    disabledOn: 'true',
  },
  {
    type: 'input-text',
    name: 'leaseDate',
    label: '租房期限',
    disabledOn: 'true',
  },
  {
    type: 'input-text',
    name: 'monthProperty',
    label: '物业费(元/平*月)',
    disabledOn: 'true',
  },
  {
    type: 'select',
    name: 'payType',
    label: '结算方式',
    multiple: true,
    disabledOn: 'true',
    options: [
      {
        label: '月付',
        value: '1',
      },
      {
        label: '季付',
        value: '2',
      },
      {
        label: '半年付',
        value: '5',
      },
      {
        label: '年付',
        value: '3',
      },
      {
        label: '一次性付清',
        value: '4',
      },
    ],
  },
  {
    type: 'input-text',
    name: 'payDateName',
    label: '收缴情况',
    disabledOn: 'true',
  },
  {
    type: 'input-date',
    name: 'alreadyPayDate',
    label: '已缴至日期',
    visibleOn: '${alreadyPayDate}',
    valueFormat: 'YYYY-MM-DD',
    disabledOn: 'true',
  },
  {
    type: 'input-text',
    name: 'payProperty',
    label: '本次收缴金额（元）',
    disabledOn: 'true',
  },
  {
    type: 'input-date',
    name: 'payDate',
    required: true,
    disabledOn: 'true',
    label: '企业收缴日期',
    valueFormat: 'YYYY-MM-DD',
  },
  {
    name: 'files',
    type: 'static-images',
    enlargeAble: true,
    thumbMode: 'contain',
    label: '收缴凭证',
    disabledOn: 'true',
    source: '${files}',
    delimiter: ',', // 指定分隔符，如果图片URL是以逗号分隔的字符串
    showBadge: true, // 显示序号
    listClassName: 'flex-wrap', // 让图片列表可以换行显示
    imageClassName: 'm-r-xs m-b-xs', // 设置每张图片的间距
  },
];

export const rentFormData = [
  {
    type: 'input-text',
    name: 'enterpriseName',
    label: '企业名称',
    disabledOn: 'true',
  },
  {
    type: 'input-text',
    name: 'uniCode',
    label: '企业信用代码',
    disabledOn: 'true',
  },
  {
    name: 'leaseBillName',
    label: '账单',
    type: 'input-text',
    disabledOn: 'true',
  },
  {
    type: 'input-text',
    name: 'houseAreas',
    label: '租赁面积',
    disabledOn: 'true',
  },
  {
    type: 'input-text',
    name: 'leaseDate',
    label: '租房期限',
    disabledOn: 'true',
  },
  {
    type: 'input-text',
    name: 'monthRent',
    label: '租金（元/平*月）',
    disabledOn: 'true',
  },
  {
    type: 'select',
    name: 'payType',
    label: '结算方式',
    multiple: true,
    disabledOn: 'true',
    options: [
      {
        label: '月付',
        value: '1',
      },
      {
        label: '季付',
        value: '2',
      },
      {
        label: '半年付',
        value: '5',
      },
      {
        label: '年付',
        value: '3',
      },
      {
        label: '一次性付清',
        value: '4',
      },
    ],
  },
  {
    type: 'input-text',
    name: 'pay',
    label: '收缴情况',
    disabledOn: 'true',
  },
  {
    type: 'input-date',
    name: 'alreadyPayDate',
    label: '已缴至日期',
    visibleOn: '${alreadyPayDate}',
    disabledOn: 'true',
    valueFormat: 'YYYY-MM-DD',
  },
  {
    type: 'input-text',
    name: 'payRent',
    label: '本次收缴金额（元）',
    disabledOn: 'true',
  },
  {
    type: 'input-date',
    name: 'payDate',
    required: true,
    label: '企业收缴日期',
    valueFormat: 'YYYY-MM-DD',
    disabledOn: 'true',
  },
  {
    name: 'files',
    type: 'static-images',
    label: '收缴凭证',
    enlargeAble: 'true',
    disabledOn: 'true',
    source: '${files}',
  },
];
