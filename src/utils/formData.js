export const formatDate = function (format, date = new Date()) {
    var o = {
        "M+": date.getMonth() + 1, //月份
        "d+": date.getDate(), //日
        "H+": date.getHours(), //小时
        "m+": date.getMinutes(), //分
        "s+": date.getSeconds(), //秒
        "q+": Math.floor((date.getMonth() + 3) / 3), //季度
        "S": date.getMilliseconds() //毫秒
    };
    if (/(y+)/.test(format)) format = format.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o) {
        if (new RegExp("(" + k + ")").test(format)) {
        format = format.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        }
    }
    return format;
}
export const transformData =  function (data) {
      let result = {};
      data.forEach(item => {
        if (result[item.floors]) {
          result[item.floors].push(item);
        } else {
          result[item.floors] = [item];
        }
      })
      let keyValuePairs = Object.entries(result).map(([key, value]) => ({
        key: key,
        value: value
      }));
      let ultimately = keyValuePairs.map(it=>{
        return {
            label: "F" + it.key,
            children:it.value.map(i=>{
             return{
                value: i.houseId,
                label: i.houseName,
             }
            })
        }
      })
      return ultimately
  }