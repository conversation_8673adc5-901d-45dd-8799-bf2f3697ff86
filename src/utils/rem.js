import dayjs from "dayjs";

export function getRem(num) {
const baseSize = 16;

  // const scale = document.documentElement.clientWidth / 1920;
    // 当前页面宽度相对于 750 宽的缩放比例，可根据自己需要修改。
    const scale = document.documentElement.clientWidth / 1920;
    var autoWidth = Math.round(baseSize * Math.min(scale, 2));
    // 设置页面最小字体
    if (autoWidth < 10) {
      autoWidth = 10;
    }

    // 
  // return   num/16 + "rem";

  return 0.0625 * num + "rem";
}
export function getVh(num) {
  let height = document.documentElement.clientHeight;
  return (num / height) * 100 + "vh";
}

export function getYear(num) {
  let currentYear = dayjs().year();
  let yearArray = [];
  for (let i = 0; i < num; i++) {
    yearArray.push(currentYear - i);
  }

  return yearArray
}
