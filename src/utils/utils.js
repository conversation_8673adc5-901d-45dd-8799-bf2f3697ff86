
import { useStore } from 'vuex'
export const baseEvn = import.meta.env.VITE_BASE_URL
export const skipURL = import.meta.env.VITE_SKPI_URL
export const perFix = "/api";
export const perFix2 = "/mock";
import store from '@/store'
const TokenKey = 'Admin-Token-jh'
/**
 * 根据名字获取cookie值
 * @param name
 */
const getCookie = (name) => {
  const cookieArr = document.cookie.split('; ');
  for (let i = 0, length = cookieArr.length; i < length; i++) {
    const kv = cookieArr[i].split('=');
    if (kv[0] === name) {
      return kv[1];
    }
  }
  return '';
}
export const token = () => {
  return store?.state.user.token || JSON.parse(localStorage.getItem('JH_TOKEN') || '{}')?.value || sessionStorage.getItem('JH_TOKEN') || undefined
}

export const publicConfig = (config) => {
  let { title, columns, api, filter, id } = config;
  const amisjson = {
    type: "page",
    asideResizor: false,
    syncLocation: false,
    body: [
      {
        type: "tpl",
        inline: true,
        wrapperComponent: "",
        syncLocation: false,
        id: "u:3345e187f2df",
      },
      {
        type: "crud",
        syncLocation: false,
        name: "tab-s",
        api: api,
        perPageAvailable: [10, 20],
        perPage: 10,
        filterTogglable: true,
        columnsTogglable: false,
        mode: "table",
        columns: columns,
        alwaysShowPagination: true,
        autoFillHeight:
        {
          height: 450
        },
        affixHeader: true,
        hideQuickSaveBtn: true,
        autoGenerateFilter: {
          columnsNum: 4,
          // "showBtnToolbar": false//是否显示设置查询字段
        },
        keepItemSelectionOnPageChange: false,
        headerToolbar: title ? [
          {
            type: "tpl",
            tpl: title,
            wrapperComponent: "",
            id: "u:9fd2be92a7fd",
            align: "left",
          },
        ] : null,
        filter,
        footerToolbar: [
         {
            type: "statistics",
            tpl: "内容",
            wrapperComponent: "",
            id: "u:cb1f575af89b",
            align: "right",
          },
          // {
          //   type: "switch-per-page",
          //   align: "right",
          //   tpl: "内容",
          //   wrapperComponent: "",
          //   id: "u:7e8c42e22098",
          // },
            
          {
            type: "pagination",
            tpl: "内容",
            wrapperComponent: "",
            id: "u:1567e06485ac",
            align: "right",
          },
        
          // {
          //   type: "tpl",
          //   tpl: "内容",
          //   wrapperComponent: "",
          //   id: "u:2d5b1edd47b2",
          // },
        ],
        className: 'tableAutoStyle'
      },
    ],
    "id": "uuid"
  };
  return amisjson;
};


export const getOrg = () => {
  return JSON.parse(localStorage.getItem('userInfo') || "{}")?.orgCode
}

