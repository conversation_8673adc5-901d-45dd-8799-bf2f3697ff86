 
const manageInfo = [
  {
    type: 'input-number',
    name: 'last_year_output',
    size: 'lg',
    label: '上年度总产值（万元）',
    min: 0,
    required: true,
    precision: 3,
  },
  {
    type: 'input-number',
    name: 'this_year_output',
    label: '本年度预计总产值（万元）',
    min: 0,
    required: true,
    size: 'lg',
    precision: 3,
  },
  {
    type: 'input-number',
    name: 'last_year_input',
    label: '上年度主营业务收入（万元）',
    size: 'lg',
    min: 0,
    required: true,
    precision: 3,
  },
  {
    type: 'input-number',
    name: 'this_year_input',
    min: 0,
    size: 'lg',
    label: '本年度预计主营业务收入（万元）',
    required: true,
    precision: 3,
  },
  {
    type: 'input-number',
    name: 'last_year_tax',
    size: 'lg',
    min: 0,
    label: '上年度税收（万元）',
    required: true,
    precision: 3,
  },
  {
    type: 'input-number',
    min: 0,
    name: 'this_year_tax',
    label: '本年度预计税收（万元）',
    required: true,
    size: 'lg',
    precision: 3,
  },
  {
    type: 'input-number',
    name: 'high_tech_service_input',
    label: '高新技术产品服务收入（万元）',
    min: 0,
    size: 'lg',
    required: true,
    precision: 3,
  },
  {
    type: 'input-number',
    name: 'rd_investment',
    label: '上年度研发投入（万元）',
    min: 0,
    size: 'lg',
    required: true,
    precision: 3,
  },
  {
    type: 'input-text',
    name: 'rd_institutions',
    size: 'lg',
    label: '企业研发机构设立情况',
  },
  {
    type: 'input-text',
    name: 'rd_direction',
    size: 'lg',
    label: '企业研发方向',
  },
  {
    type: 'page',
    size: 'md',
    body: '',
    className: 'page-b-md height0 m-l font-bold',
  },
  {
    name: 'product_advantage',
    type: 'textarea',

    label: '企业产品优势',
    showCounter: true,
    maxLength: 500,
    placeholder: '请输入企业产品优势,不超500字',
  },
  {
    type: 'page',
    size: 'md',
    body: '',
    className: 'page-b-md height0 m-l font-bold',
  },
  {
    name: 'IUR_cooperation',

    type: 'textarea',
    label: '企业产学研合作情况(是否相关合作需求)',
    showCounter: true,
    maxLength: 500,
    placeholder: '请输入企业产学研合作情况,不超500字',
  },
  {
    type: 'page',
    size: 'md',
    body: '',
    className: 'page-b-md height0 m-l font-bold',
  },
  {
    name: 'scientific_transformation',
    type: 'textarea',

    label: '科技成果转化项目情况',
    showCounter: true,
    maxLength: 500,
    placeholder: '请输入科技成果转化项目情况,不超500字',
  },
  {
    type: 'page',
    size: 'md',
    body: '',
    className: 'page-b-md height0 m-l font-bold',
  },
  {
    name: 'market_layout',
    type: 'textarea',
    label: '企业市场布局区域',
    showCounter: true,
    maxLength: 500,
    placeholder: '请输入资质荣誉,不超500字',
  },
  {
    type: 'page',
    size: 'md',
    body: '',
    className: 'page-b-md height0 m-l font-bold',
  },
  {
    name: 'develop_direction',
    type: 'textarea',
    label: '企业未来发展方向',
    showCounter: true,
    maxLength: 500,
    placeholder: '请输入企业未来发展方向,不超500字',
  },

  {
    type: 'page',
    size: 'md',
    body: '',
    className: 'page-b-md height0 m-l font-bold',
  },
  {
    name: 'achievement_patents',
    type: 'textarea',

    label: '主要技术成果或专利情况(发明专利/实用新型/外观专利)',
    showCounter: true,
    required: true,
    maxLength: 500,
    placeholder: '请输入主要技术成果或专利情况,不超500字',
  },
  {
    type: 'page',
    size: 'md',
    body: '',
    className: 'page-b-md height0 m-l font-bold',
  },
  {
    name: 'honors',
    type: 'textarea',
    label: '资质荣誉',
    showCounter: true,
    maxLength: 500,
    placeholder: '请输入主要技术成果或专利情况,不超500字',
  },
  {
    type: 'page',
    size: 'md',
    body: '',
    className: 'page-b-md height0 m-l font-bold',
  }
]; 
  

