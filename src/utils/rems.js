// import router from '@/router'

/***1、创建rem.js文件
 **很多人写这种小工具文件会习惯性的加上闭包，这个其实是没有必要的。ES6中每个文件都是单独的一个模块。
 **/

/**一般：
 *移动端设计图为 750px，basesize=32,scale = document.documentElement.clientWidth / 750;
 *pc端设计图为 1920px，basesize=16,scale = document.documentElement.clientWidth / 1920;
 **/
// 基准大小

const baseSize = 16;

// 设置 rem 函数
export function setRem(router) {
  // 当前页面宽度相对于 750 宽的缩放比例，可根据自己需要修改。
  const scale = document.documentElement.clientWidth / 1920;
  var autoWidth = Math.round(baseSize * Math.min(scale, 2));
  // 设置页面最小字体
  if (router.path === '/home') {
    if (autoWidth < 12) {
      autoWidth = 12;
    }
  } else {
    if (autoWidth <= 14) {
      autoWidth = 14;
    }
  }
  // console.log('setRem**');
  // 设置页面根节点字体大小
  // document.documentElement.style.fontSize ='16px'
  document.documentElement.style.fontSize =
    (router.path === '/dashboard' || router.path.startsWith('/home') || router.path === '/onLineService' || router.path === '/onLineSer') ? `${autoWidth}px` : '16px';
}

export function setRootFontSize() {
  // console.log('setRootFontSize');

  const baseSize = 16; // 基准字体大小
  const baseWidth = 1920; // 设计稿基准宽度
  const scale = document.documentElement.clientWidth / baseWidth;
  const fontSize = baseSize * scale;

  // 设置最小和最大字体大小以防止过度缩放
  document.documentElement.style.fontSize = Math.max(12, Math.min(fontSize, 24)) + 'px';
}
