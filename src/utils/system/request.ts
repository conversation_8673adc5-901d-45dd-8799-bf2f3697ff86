import axios , { AxiosError, AxiosRequestConfig, AxiosResponse, AxiosInstance } from 'axios'
import store from '@/store'
// import { ElMessage } from 'element-plus'
import { useRoute, useRouter, RouteLocationMatched } from "vue-router";
import router from '@/router'
import message from '@/utils/optimizePop.js' //引入
import {  token,getOrg } from '@/utils/utils';

const baseURL: any = import.meta.env.VITE_BASE_URL
// const router = useRouter();

// console.log(useRouter(),'baseURL')
const service: AxiosInstance = axios.create({
  baseURL, 
  timeout: 60000
})

// 请求前的统一处理
service.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    config.headers['appId'] = 15;    
    if(config?.url.includes('/ai')){
      config.baseURL = import.meta.env.VITE_BASE_URL.replace('/jh', '');
    }
    if(config?.url.includes('sso/admin')){
      config.baseURL = import.meta.env.VITE_BASE_URL.replace('/jh', '');
    }
    if (config.baseURL === '/sso'){
      config.baseURL = import.meta.env.VITE_SKPI_URL + 'sso';
    }
    config.timeout=
    (config.url==="uploadAndReturnOssUrl"|| config.url==="upload"||config.url==='uploadByPart')? 9000000:config.timeout
    if ((config.params && config.params.token)) {
      config.headers['token'] = config.params.token
      delete config.params.token
    }
    if ((config.data && config.data.token)) {
      config.headers['token'] = config.data.token
      delete config.data.token
    }
    // JWT鉴权处理
    if(token()){
      config.headers['token'] =token()
      config.headers['OrgCode'] = getOrg()
    }    
    return config
  },
  (error: AxiosError) => {
    // console.log(error) // for debug
    return Promise.reject(error)
  }
)

service.interceptors.response.use(
  (response: AxiosResponse) => {
    const res = response.data
    if (res.status === '0'||res.code==='SUCCESS') {
      return {
        ...res,
      data:!res.data?res.status==='0':res.data
      }
    } else if ( res.size){
      return res
    }  else {
      showError(res)
      return Promise.reject({...res,
        data: 
        !res.data?res.status==='0':res.data
      })
    }
  },
  (error: AxiosError)=> {
    // console.log('error',error) // for debug
    const badMessage: any = error.message || error
    const code = parseInt(error.toString().replace('Error: Request failed with status code ', ''))
    showError({ code, message: badMessage })
    return Promise.reject( error)
  }
)

// 错误处理
function showError(error: any) {
  // token过期，清除本地数据，并跳转至登录页
  // console.log('error.code',error.code)
  if(error.code === 401||error.code === 403) {
      message({
      message:   '未登录或已超时',
      type: 'error',
      duration: 3 * 1000
    })
    // var redirectUrl = window.location.href.split("#")?.[1]
    // let redirectName=   JSON.parse(localStorage.getItem('userInfo')||"{}")?.username
    // localStorage.setItem('redirectName',redirectName)
    // error.code== "401"&& localStorage.setItem('redirect',`/${redirectUrl}`)
    store.dispatch('user/loginOut')
    router.push('/login')
   }
  else {
    if(
      error.message ==='timeout of 10000ms exceeded'
    ){
 
      message({
        message:'网络连接超时，请稍后再试。',
        type: 'error',
        duration: 3 * 1000
      })
    } else if(error.message==='Network Error'){
      message({
        message:'网络连接失败，请稍后再试。',
        type: 'error',
        duration: 3 * 1000
      })
    }
    
    else{ message({
      // error.msg || error.message || 
      message: error.msg || error.message || '服务异常',
      type: 'error',
      duration: 3 * 1000
    })}
   
  }
  
}

export default service