/*
 * @Author: luoxi
 * @Date: 2022-01-25 09:51:12
 * @LastEditors: luoxi
 * @LastEditTime: 2022-01-25 12:25:51
 * @FilePath: \vue-admin-box\src\router\index.ts
 * @Description:
 */
/**
 * @description 所有人可使用的参数配置列表
 * @params hideMenu: 是否隐藏当前路由结点不在导航中展示
 * @params alwayShow: 只有一个子路由时是否总是展示菜单，默认false
 */
import { reactive } from "vue";
import { useStore } from "vuex";
import { getAuthRoutes } from "@/router/permission";
import { createRouter, createWebHashHistory } from "vue-router";
import store from "@/store";
import i18n from "@/locale";
import NProgress from "@/utils/system/nprogress";
import { token } from "@/utils/utils";
import { changeTitle } from "@/utils/system/title";
import { setRem } from "@/utils/rems.js";
import message from '@/utils/optimizePop.js' //引入
// import Dashboard from "./modules/dashboard";

NProgress.configure({ showSpinner: false });

// 引入不需要权限的modules
import OnlineService from './modules/onlineService'
import System from "./modules/system";

// import FrontRoutes from "./permission/front";
import { el } from "element-plus/es/locale";

/**
 * @name 初始化必须要的路由
 * @description 使用reactive属性使得modules可以在路由菜单里面实时响应，搞定菜单回显的问题
 * @detail 针对modules的任何修改，均会同步至菜单级别，记住，是针对变量名为：moduels的修改
 **/
let modules = reactive([
  // ...OnlineService,
  // ...FrontRoutes(),
  ...System,
]);
// const { t } = i18n.global;

const router = createRouter({
  history: createWebHashHistory(),
  routes: modules,
});
let isLogin = false;
let isRoutesLoaded = false;
// 未授权时可访问的白名单
const whiteList = ['/login', '/retrievePassword' ,"/agreement"]

// 路由跳转前的监听操作
router.beforeEach((to, _from, next) => {

  if (to.path === '/login' || to.name === 'login') {
    isRoutesLoaded = false;
    // 清理动态路由
    resetDynamicRoutes(router)
    next();
    return
  }
  NProgress.start();
  if (token()) {
    if (to.path !== '/home' && to.path !== '/dashboard' && to.path !== '/dashboard/home'){
      store.commit("app/contentFullScreenChange", false);
    }
    if (!isRoutesLoaded) {
      modules.splice(0); // 清空数组
      // console.log(...System, '权限路由1****');
      modules.push(...System); // 重新添加初始模块
      // console.log(modules, '权限路由2****');
      getAuthRoutes().then((res) => {
        if (res&&res.length){
          modules.splice(0, 0, ...res);
          // console.log( modules.splice(0, 0, ...res), '权限路由21****');
          isRoutesLoaded = true;
          // debugger
          next({ ...to, replace: true });
        }else{
          message({
            message: '该账号无使用权限，请联系管理员',
            type: 'error',
            duration: 3 * 1000
          })
          next({ path: '/login', replace: true });
        }
      }).catch((error) => {
         console.error('加载权限路由失败', error);
         next('/login');
      });
    }else{
      to.meta.title ? changeTitle(to.meta.title) : ""; // 动态title
      next();
    }
  } else {
    if (whiteList.includes(to.path)) {
      to.meta.title ? changeTitle(to.meta.title) : ""; // 动态title
      next();
    } else {
      // 全部重定向到登录页
      next("/login");
    }
  }
  setRem(to);
});

// 路由跳转后的监听操作
router.afterEach((to, _from) => {
  if (to.matched.length === 0) {
    console.warn('未匹配到任何路由:', to.path);
    NProgress.done();
    return;
  }
  const arr = modules;
  if (to.path === '/' && (arr && arr.length)) {
    if (arr[0].path !== '/') {
      // 获取第一个元素并找到最深层的第一个children节点的第一个路由路径
      let firstRoute = arr[0];
      let deepestRoute = firstRoute;
      let fullPath = firstRoute.path;

      // 递归查找最深层的第一个children节点
      const findDeepestFirstChild = function (route) {
        if (route.children && route.children.length > 0) {
          // 优先查找第一个未隐藏的菜单项
          const visibleChild = route.children.find(child => !child.meta || child.meta.hidden !== true);
          if (visibleChild) {
            deepestRoute = visibleChild;
            fullPath = fullPath + '/' + visibleChild.path;
            findDeepestFirstChild(visibleChild);
          }
        }
      };

      findDeepestFirstChild(firstRoute);
      router.push(fullPath || '/home');
      return
    }
  }
  const keepAliveComponentsName =
    store.getters["keepAlive/keepAliveComponentsName"] || [];
  const name = to.matched[to.matched.length - 1].components?.default?.name;
  if (
    to.meta &&
    to.meta.cache &&
    name &&
    !keepAliveComponentsName.includes(name)
  ) {
    store.commit("keepAlive/addKeepAliveComponentsName", name);
  }
  NProgress.done();
});

// 新增清除动态路由方法
export const resetDynamicRoutes = (router) => {
  // 清除所有动态路由
  router.getRoutes().forEach(route => {
    if (route.meta.isDynamic) {
      router.removeRoute(route.name)
    }
  })
  // 清除404路由
  // router.removeRoute('404')
}


export { modules };

export default router;
