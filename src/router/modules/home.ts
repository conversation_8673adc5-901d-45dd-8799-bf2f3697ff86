import type { Route } from '../index.type'
import Layout from '@/layout/index.vue'
import { createNameComponent } from '../createNode'
const OnlineService: Route[] = [
  {
    path: '/homepage',
    component:createNameComponent(() => import( "@/layout/index.vue")),
    meta: { title: '园区概况', icon: '', },
    hideMenu: false,
    children: [
      {
        path: '/homepage',
        component: createNameComponent(() => import('@/views/main/homepage/index.vue')),
        meta: { title: '园区概况', icon: '', hideClose: true }
      }
    ]
  }
]

export default OnlineService