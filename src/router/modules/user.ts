import type { Route } from "../index.type";
import Layout from "@/layout/index.vue";
import { createNameComponent } from "../createNode";

//企业端 
const route: Route[] = [
      {
      path: "/accountCenter",
      name: "accountCenter",
      component:createNameComponent(() => import( "@/layout/index.vue")),
      redirect: "/accountCenter",
      meta: { title: "个人中心", icon: "User" },
      children: [
        {
          path: "/accountCenter",
          name: "accountC",
          component: createNameComponent(
            () => import("@/views/main/accountCenter/index.vue")
          ),
          meta: { title: "个人中心", icon: "", hideClose: true, hideTabs: true },
        },
      
      ]}

];

export default route;
