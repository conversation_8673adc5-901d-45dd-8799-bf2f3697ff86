import type { Route } from "../index.type";
import Layout from "@/layout/index.vue";
import { createNameComponent } from "../createNode";

const route: Route[] = [
      {
      path: "/systemSetups",
      name: "systemSetups",
      component:createNameComponent(() => import( "@/layout/index.vue")),
      meta: { title: "个人中心", icon: "User" },
      children: [
        {
          path: "accountInformation",
          name: "systemSetupsAccountInformation",
          component: createNameComponent(
            () => import("@/views/main/system-manage/components/accountInformation.vue")
          ),
          meta: { title: "账号信息", icon: "", hideClose: true, hideTabs: true },
        },
        {
          path: "PasswordSetting",
          name: "systemSetupsPasswordSetting",
          component: createNameComponent(
            () => import("@/views/main/system-manage/components/PasswordSetting.vue")
          ),
          meta: { title: "密码设置", icon: "", hideClose: true, hideTabs: true },
        },
      
      ]}

];

export default route;
