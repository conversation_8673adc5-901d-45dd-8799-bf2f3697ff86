import type { Route } from '../index.type'
import Layout from '@/layout/index.vue'
import { createNameComponent } from '../createNode'
const route: Route[] = [
  {
    path: '/dashboard',
    name: 'DashboardHome',
    component:createNameComponent(() => import( "@/layout/index.vue")),
    redirect: '/home',
    meta: { title: '驾驶舱-园区智服', icon: 'Monitor'  },
    hideMenu: false,
    children: [
      {
        path: '/home',
        name: 'Dashboard',
        component: createNameComponent(() => import('@/views/main/dashboard/index.vue')),
        meta: { title: '驾驶舱', icon: '', hideClose: true, hideTabs: true }
      }
    ]
  }
]

export default route