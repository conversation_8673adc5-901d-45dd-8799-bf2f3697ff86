import type { Route } from '../index.type'
import Layout from '@/layout/index.vue'
import { createNameComponent } from '../createNode'
const OnlineService: Route[] = [
  {
    path: '/onlineService',
    name: '/onlineService',
    component:createNameComponent(() => import( "@/layout/index.vue")),
    meta: { title: '在线办事', icon: 'House' },
    hideMenu: false,
    children: [
      {
        path: '',
        name: 'onlineService',
        component: createNameComponent(() => import('@/views/main/onlineService/index.vue')),
        meta: { title: '在线办事', icon: 'House', hideClose: true }
      }
    ]
  }
]

export default OnlineService