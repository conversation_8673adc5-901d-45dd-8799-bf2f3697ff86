import type { Route } from '../index.type'
import Layout from '@/layout/index.vue'
import { createNameComponent } from '../createNode'
const route: Route[] = [
  {
    path: '/',
    hideMenu: true,
    name: '/',
    meta: { title: '首页', icon: '' },
  },
  {
    path: '/system',
    component:createNameComponent(() => import( "@/layout/index.vue")),
    redirect: '/404',
    hideMenu: true,
    meta: { title: '404' },
    children: [
      {
        path: '/404',
        component: createNameComponent(() => import('@/views/system/404.vue')),
        meta: { title: '404', hideTabs: true }
      },
      {
        path: '/401',
        component: createNameComponent(() => import('@/views/system/401.vue')),
        meta: { title: '401', hideTabs: true }
      },
      {
        path: '/redirect/:path(.*)',
        component: createNameComponent(() => import('@/views/system/redirect.vue')),
        meta: { title: '刷新', hideTabs: true }
      }, {
        path: '/agreement',
        component: createNameComponent(() => import('@/views/main/agreement/index.vue')),
        meta: { title: '协议' , hideTabs: true }
      },{
        path: '/preview',
        component: createNameComponent(() => import('@/views/main/preview/index.vue')),
        meta: { title: '预览' , hideTabs: true }
        
      }
    ]
  },
  {
    path: '/login',
    component: createNameComponent(() => import('@/views/system/login/index.vue')),
    hideMenu: true,
    meta: { title: '登录', hideTabs: true }
  },
  /* 找回密码 */
  {
    path: '/retrievePassword',
    component: () => import('@/views/system/login/retrievePassword.vue'),
    hideMenu: true,
    meta: { title: '忘记密码', hideTabs: true }
  }, 
  {
    path: "/personalCenter",
    name: "personalCenter",
    component:createNameComponent(() => import( "@/layout/index.vue")),
    redirect: "/personalCenter",
    meta: { title: "个人中心",  hideTabs: true  },
    hideMenu: true,
    children: [
      {
        path: "/personalCenter",
        name: "personalC",
        component: createNameComponent(
          () => import("@/views/main/personalCenter/index.vue")
        ),
        meta: { title: "个人中心",   hideTabs: true },
      },
    
    ]},
]

export default route