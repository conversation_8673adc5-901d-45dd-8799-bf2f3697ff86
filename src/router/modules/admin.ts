import type { Route } from '../index.type';
import Layout from '@/layout/index.vue';
import { createNameComponent } from '../createNode';
const route: Route[] = [
  {
    path: '/workbench',
    component: createNameComponent(() => import('@/layout/index.vue')),
    name: 'workbench',
    meta: { title: '工作台', icon: 'OfficeBuilding' },
    children: [
      {
        path: 'messageReminding',
        name: 'workbenchMessageReminding',
        component: createNameComponent(
          () => import('@/views/main/messageReminding/index.vue')
        ),
        meta: { title: '消息提醒', icon: '', hideClose: true },
      },
      {
        path: 'building',
        name: 'workbenchBuilding',
        component: createNameComponent(
          () => import('@/views/main/house/index.vue')
        ),
        meta: { title: '房源管理', icon: '', hideClose: true },
      },
      {
        path: 'lease',
        name: 'workbenchLease',
        redirect: '/workbench/lease/property',
        meta: { title: '租约管理', icon: '', hideClose: true },
        children: [
          {
            path: 'property',
            name: 'workbenchLeaseProperty',
            component: createNameComponent(
              () => import('@/views/main/lease/property.vue')
            ),
            meta: { title: '物业费租约管理', icon: '', hideClose: true },
          },
          {
            path: 'rent',
            name: 'workbenchLeaseRent',
            component: createNameComponent(
              () => import('@/views/main/lease/rent.vue')
            ),
            meta: { title: '租金租约管理', icon: '', hideClose: true },
          },
          {
            path: 'addlease',
            name: 'workbenchLeaseAddlease',
            component: createNameComponent(
              () => import('@/views/main/lease/com/addlease.vue')
            ),
            meta: { title: '租约管理', icon: '', hideClose: true },
            hideMenu: true,
          },
          {
            path: 'commission',
            name: 'workbenchLeaseCommission',
            component: createNameComponent(
              () => import('@/views/main/lease/commission.vue')
            ),
            meta: { title: '待办租约', icon: '', hideClose: true },
          },
        ],
      },
      {
        path: 'energy',
        name: 'workbenchEnergy',
        component: createNameComponent(
          () => import('@/views/main/energy/index.vue')
        ),
        meta: { title: '能耗监测', icon: '', hideClose: true },
      },
      {
        path: 'industryChannel',
        name: 'workbenchIndustryChannel',
        component: createNameComponent(
          () => import('@/views/main/industryChannel/index.vue')
        ),
        meta: { title: '产业渠道', icon: '', hideClose: true },
      },
      {
        path: 'approval',
        name: 'workbenchApproval',
        redirect: '/workbench/approval/enterprise',
        meta: { title: '审批管理', icon: '', hideClose: true },
        children: [
          {
            path: 'enterprise',
            name: 'workbenchApprovalEnterprise',
            component: createNameComponent(
              () => import('@/views/main/approval/enterprise/index.vue')
            ),
            meta: { title: '企业审批', icon: '', hideClose: true },
          },
          {
            path: 'rent',
            name: 'workbenchApprovalRent',
            component: createNameComponent(
              () => import('@/views/main/approval/rent/index.vue')
            ),
            meta: { title: '租金审批', icon: '', hideClose: true },
          },
          {
            path: 'property',
            name: 'workbenchApprovalProperty',
            component: createNameComponent(
              () => import('@/views/main/approval/property/index.vue')
            ),
            meta: { title: '物业审批', icon: '', hideClose: true },
          },
        ],
      },
      {
        path: 'administration',
        name: 'workbenchAdministration',
        redirect: '/workbench/administration/newlyIncreased',
        meta: { title: '企业管理', icon: '', hideClose: true },
        children: [
          {
            path: 'newlyIncreased',
            name: 'workbenchAdministrationNewlyIncreased',
            component: createNameComponent(
              () => import('@/views/main/addEnterprise/index.vue')
            ),
            meta: { title: '企业新增', icon: '', hideClose: true },
          },
          {
            path: 'management',
            name: 'workbenchAdministrationManagement',
            component: createNameComponent(
              () => import('@/views/main/administration/index.vue')
            ),
            meta: { title: '企业管理', icon: '', hideClose: true },
          },
        ],
      },
      {
        path: 'propertyManagement',
        name: 'workbenchPropertyManagement',
        redirect: '/workbench/propertyManagement/addRent',
        meta: { title: '账单管理', icon: '', hideClose: true },
        children: [
          {
            path: 'addRent',
            name: 'workbenchPropertyManagementAddRent',
            component: createNameComponent(
              () => import('@/views/main/propertyManagement/addRent.vue')
            ),
            meta: { title: '租金新增', icon: '', hideClose: true },
          },
          {
            path: 'rentManagement',
            name: 'workbenchPropertyManagementRentManagement',
            component: createNameComponent(
              () => import('@/views/main/propertyManagement/rentManagement.vue')
            ),
            meta: { title: '租金管理', icon: '', hideClose: true },
          },
          {
            path: 'addPropertyFee',
            name: 'workbenchPropertyManagementAddPropertyFee',
            component: createNameComponent(
              () => import('@/views/main/propertyManagement/addPropertyFee.vue')
            ),
            meta: { title: '物业费新增', icon: '', hideClose: true },
          },
          {
            path: 'propertyFee',
            name: 'workbenchPropertyManagementPropertyFee',
            component: createNameComponent(
              () => import('@/views/main/propertyManagement/propertyFee.vue')
            ),
            meta: { title: '物业费管理', icon: '', hideClose: true },
          },
        ],
      },
      // {
      //   path: "propertyRepair",
      //   name: "workbenchPropertyRepair",
      //   component: createNameComponent(
      //     () => import("@/views/main/propertyRepair/index.vue")
      //   ),
      //   meta: { title: "物业报修", icon: "", hideClose: true },
      // },
      // {
      //     path: "propertyRepair/detail",
      //     name: "workbenchPropertyRepairDetail",
      //     component: createNameComponent(
      //       () => import("@/views/main/propertyRepair/com/details.vue")
      //     ),
      //     meta: { title: "物业报修", icon: "", hideClose: true },
      //     hideMenu: true,
      // },
      // {
      //   path: "business",
      //   name: "workbenchBusiness",
      //   component: createNameComponent(
      //     () => import("@/views/main/business/index.vue")
      //   ),
      //   meta: { title: "经营诉求", icon: "", hideClose: true },
      // },
      // {
      //   path: "business/detail",
      //   name: "workbenchBusinessDetail",
      //   component: createNameComponent(
      //     () => import("@/views/main/business/detail/index.vue")
      //   ),
      //   meta: { title: "amis", icon: "", hideClose: true },
      //   hideMenu: true,
      // },
      {
        path: 'cooperative',
        name: 'workbenchCooperative',
        component: createNameComponent(
          () => import('@/views/main/cooperative/index.vue')
        ),
        meta: { title: '合作咨询', icon: '', hideClose: true },
      },
      {
        path: 'cooperative/follow',
        name: 'workbenchCooperativeFollow',
        component: createNameComponent(
          () => import('@/views/main/cooperative/com/index.vue')
        ),
        meta: { title: '合作咨询', icon: '', hideClose: true },
        hideMenu: true,
      },
      {
        path: 'investment/foll',
        name: 'workbenchInvestmentFoll',
        component: createNameComponent(
          () => import('@/views/main/investment/com/del.vue')
        ),
        hideMenu: true,
        meta: { title: '跟进记录', icon: '', hideClose: true },
      },
      {
        path: 'investment',
        name: 'workbenchInvestment',
        component: createNameComponent(
          () => import('@/views/main/investment/index.vue')
        ),
        hideMenu: false,
        meta: { title: '招商管理', icon: '', hideClose: true },
      },
    ],
  },
  {
    path: '/backstage',
    name: 'backstage',
    component: createNameComponent(() => import('@/layout/index.vue')),
    meta: { title: '管理后台', icon: 'Refrigerator' },
    children: [
      {
        path: 'release',
        name: 'backstageRelease',
        component: createNameComponent(
          () => import('@/views/main/release/index.vue')
        ),
        meta: { title: '信息发布', icon: '', hideClose: true },
      },
      {
        path: 'release/message',
        name: 'backstageReleaseMessage',
        component: createNameComponent(
          () => import('@/views/main/release/com/add.vue')
        ),
        hideMenu: true,
        meta: { title: '信息发布', icon: '', hideClose: true },
      },
      {
        path: 'message',
        name: 'backstageMessage',
        component: createNameComponent(
          () => import('@/views/main/message/index.vue')
        ),
        meta: { title: '消息通知', icon: '', hideClose: true },
      },
      {
        path: 'accountCheck',
        name: 'backstageAccountCheck',
        component: createNameComponent(
          () => import('@/views/main/system/accountCheck/index.vue')
        ),
        meta: { title: '账号审核', icon: '', hideClose: true },
      },
    ],
  },
  {
    path: '/systemSetup',
    name: 'systemSetup',
    component: createNameComponent(() => import('@/layout/index.vue')),
    meta: { title: '系统设置', icon: 'Setting' },
    children: [
      {
        path: 'userManagement',
        name: 'systemSetupUserManagement',
        component: createNameComponent(
          () => import('@/views/main/system-manage/components/department.vue')
        ),
        meta: { title: '统一用户管理', icon: '', hideClose: true },
      },
      {
        path: 'roleManagement',
        name: 'systemSetupRoleManagement',
        component: createNameComponent(
          () => import('@/views/main/system-manage/roleManagement/index.vue')
        ),
        meta: { title: '角色管理', icon: '', hideClose: true },
      },
    ],
  },
];

export default route;
