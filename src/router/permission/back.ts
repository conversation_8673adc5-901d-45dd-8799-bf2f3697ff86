/** 动态路由实现基础组件 */
/** 引入全局Layout组件 */
import Layout from '@/layout/index.vue'
/** 引入多级菜单控制器组件 */
import MenuBox from '@/components/menu/index.vue'
/** 引入带有系统自定义name的组件，方便keep-alive实现 */
import { createNameComponent } from '../createNode'
/** 引入所有的配置清单 */
import backConfig from './backConfig'
import { getMenuApi } from '@/api/user'
import _ from 'lodash'
import { el } from 'element-plus/es/locale'

/** 获取后台模板配置清单 */
const getMenu = async () => {
  const result = await getMenuApi()
  let name=['yqjsc','gzt','glht','grzx'];
  let showTree:any=[]
  name.map((item)=>{
    let tree= _.find(result.result,(e)=>{return e.aclCode===item})
    if(item==='yqjsc'&&tree){
     let {aclDTOList,...props}=tree
     showTree.push({
 ...props
     })
    }else if(tree&&item!=='yqjsc'){
     showTree.push(tree)
    }
 })
  const backRoutes = getComponents(showTree)
  return backRoutes
}

/** 循环取出component */
const getComponents = (data: any[], level = 1) => {
 
  // console.log('showTree',level,data)
  const newData: any[] = data.map((item) => {
  //   // 1目录 2菜单 3接口
    if (item.aclDTOList) {
 let newChild=_.filter(item.aclDTOList,(e)=>e.aclType===1)
      if (level == 1) {
        if(item.aclType===1){
          return {
            ...backConfig[item.aclCode],//TODO
    //         ...item,//换成后编辑的
            // component:createNameComponent(() => import( "@/layout/index.vue")),
            children:newChild.length>0? getComponents(item.aclDTOList, level + 1):null
          }
        }
 
      } else {
        if(item.aclType===1){
        return {
          ...backConfig[item.aclCode],//TODO
  //         ...item,//换成后编辑的
          // component: MenuBox,
          children: newChild.length>0? getComponents(item.aclDTOList, level + 1):null
        }}
      }
    } else {
      const component = backConfig[item.aclCode]
      return {
         ...component,
      }
    }
  })
  // console.log(newData)
 return newData
}

export default getMenu