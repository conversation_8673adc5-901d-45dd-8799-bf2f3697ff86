/**
 * 前端路由管理
 **/

/** 路由类型 */
import type { Route } from '../index.type'
import _ from 'lodash'
/** 引入需要权限的Modules */
// import Dashboard from '../modules/dashboard'
// import Pages from '../modules/pages'
import OnlineService from '../modules/onlineService'
import Admin from '../modules/admin' //园区端 
import Dashboard from "../modules/dashboard";
// import Menu from '../modules/menu' 
// import Component from '../modules/component'
// import Directive from '../modules/directive'
// import SystemManage from '../modules/systemManage'
// import Chart from '../modules/chart'
// import home from '../modules/home'//园区端主页
// import Print from '../modules/print'
// import Community from '../modules/community'
// import Tab from '../modules/tab'
import User from '../modules/user'//企业端 
import User0 from '../modules/user0'

import {orgApi, getMenuTree} from '@/api/user'

import { createNameComponent } from "../createNode";

/** 登录后根据请求结果动态加入的路由 */
const FrontRoutes = async (): Promise<RouteConfig[]> => {
  try {
    const org = await orgApi();
    const mine = org.result?.selected;
    
    if (!mine || !Array.isArray(mine.basicRoles) || mine.basicRoles.length === 0) {
      return [];
    }
    const role = mine.basicRoles[0];
    if (role?.roleName?.includes("园区端")) {
      const localeArr = [...OnlineService, ...Admin, ...Dashboard, ...User0];
      const resUser = await getMenuTree();
      if (resUser.code === 'SUCCESS') {
        let newArray = findCommonNodes(resUser?.result || [], localeArr);
        return newArray;
      }else{
        return localeArr;
      }
    }else{
       return [...OnlineService,...User];
    }
    return [];
  } catch (error) {
    console.error("获取组织信息失败", error);
    return [];
  }
};

function findCommonNodes(tree1, tree2) {
  // 用于存放所有节点的v值
  const values = new Set();
  
  // 深度优先搜索，将所有节点的v值添加到集合中
  function dfsAdd(node) {
      values.add(node.resourceCode);
      if (node.childList) {
          node.childList.forEach(dfsAdd);
      }
  }
  
  // 遍历第一个树，收集所有的v值
  tree1.forEach(dfsAdd);
  // console.log(values,'values*8888');

  // 递归处理节点
  function dfs(node) {
    // 如果当前节点在权限列表中，则保留该节点及其完整的子树结构
    if (values.has(node.name)) {
      // 递归处理子节点，保留完整结构
      if (node.children) {
        const processedChildren = node.children
          .map(dfs)
          .filter(Boolean);
        
        return {
          ...node,
          children: processedChildren.length > 0 ? processedChildren : undefined
        };
      }
      return node;
    }
    if (node.hideMenu) {
       return node;
    }
    // 如果当前节点不在权限列表中，检查子节点
    if (node.children) {
      const processedChildren = node.children
        .map(dfs)
        .filter(Boolean);
      
      // 检查处理后的子节点是否全部为隐藏菜单
      const allHidden = processedChildren.length > 0 && 
                        processedChildren.every(child => child.hideMenu);
      
      // 如果子节点全部是隐藏菜单，则不保留当前节点及其子节点
      if (allHidden) {
        return null;
      }
      
      // 如果子节点中有可见菜单，则保留当前节点并只显示可见子节点
      if (processedChildren.length > 0) {
        const visibleChildren = processedChildren.filter(child => !child.hideMenu);
        return {
          ...node,
          children: visibleChildren.length > 0 ? visibleChildren : undefined
        };
      }
    }
    
    // 如果节点既不在权限列表中，也没有需要保留的子节点，则不保留
    return null;
  }

  // 3. 遍历 tree2 的每个根节点
  const result = [];
  for (const node of tree2) {
    const filteredNode = dfs(node);
    if (filteredNode) {
      result.push(filteredNode);
    }
  }
  return result;
}
export default FrontRoutes

 