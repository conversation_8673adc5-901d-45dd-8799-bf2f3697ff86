/*
 * @Date: 2023-03-10 19:34:30
 * @Description: 
 */
import { createNameComponent } from '../createNode';
/**  */
 
  

 
/** 页面 */
const pages = {
  // 园区驾驶舱
  yqjsc : {
    "path": "/",
    "redirect": "/dashboard",
    "meta": {
      "title": '园区驾驶舱'
    },
    router:'/dashboard',
    component: createNameComponent(() => import('@/views/main/dashboard/index.vue')),
  },
  gzt:{
    "path": "/",
    "redirect": "/dashboard",
    "meta": {
      "title": '工作台'
    },
    router:'/dashboard',
    component: createNameComponent(() => import('@/views/main/dashboard/index.vue')),
  },
  glht:{
    "path": "/",
    "redirect": "/dashboard",
    "meta": {
      "title": '管理后台'
    },
    router:'/dashboard',
    component: createNameComponent(() => import('@/views/main/dashboard/index.vue')),
  },
/*********** **工作台 ********/
  fygl:{
   // 房源管理
   component:createNameComponent( () => import("@/views/main/house/index.vue")),
   "path": "/workbench/building",
   "redirect": "/workbench/building",
   "meta": {
     "title": '房源管理'
   },
   router:"/workbench/building",
  },
    // 租约管理
    zygl:{
      component:createNameComponent(
      () => import("@/views/main/lease/index.vue")
    ),
    "path": "/workbench/lease",
    "redirect": "/workbench/lease",
    "meta": {
      "title": '租约管理'
    },
    router:"/workbench/lease",
  },
  zdgl:{
    component:createNameComponent(
      () => import("@/views/main/check/index.vue")
    ),
    "path": "/workbench/check",
    "redirect": "/workbench/check",
    "meta": {
      "title": '账单管理'
    },
    router:"/workbench/check",
  },
   
  nhjc:{
    component:createNameComponent(
      () => import("@/views/main/energy/index.vue")
    ),
    "path": "/workbench/energy",
    "redirect": "/workbench/energy",
    "meta": {
      "title": '能耗监测'
    },
    router:"/workbench/energy",
  },
  
  qygl:{
    component:createNameComponent(
      () => import("@/views/main/administration/index.vue")
    ),
    "path": "/workbench/administration",
    "redirect": "/workbench/administration",
    "meta": {
      "title": '能耗监测'
    },
    router:"/workbench/administration",
  },
  wywx:{
    component:createNameComponent(
      () => import("@/views/main/propertyRepair/index.vue")
    ),
    "path": "/workbench/propertyRepair",
    "redirect": "/workbench/propertyRepair",
    "meta": {
      "title": '物业报修'
    },
    router:"/workbench/propertyRepair",
  },
  jysq:{
    component:createNameComponent(
      () => import("@/views/main/business/index.vue")
    ),
    "path": "/workbench/business",
    "redirect": "/workbench/business",
    "meta": {
      "title": '物业报修'
    },
    router:"/workbench/business",
  },
  
  hzzx:{
    component:createNameComponent(
      () => import("@/views/main/cooperative/index.vue")
    ),
    "path": "/workbench/cooperative",
    "redirect": "/workbench/cooperative",
    "meta": {
      "title": '合作咨询'
    },
    router:"/workbench/cooperative",
  },
  
  zsgl:{
    component:createNameComponent(
      () => import("@/views/main/investment/index.vue")
    ),
    "path": "/workbench/investment",
    "redirect": "/workbench/investment",
    "meta": {
      "title": '物业报修'
    },
    router:"/workbench/investment",
  },
  xxfb:{
    component:createNameComponent(
      () => import("@/views/main/business/index.vue")
    ),
    "path": "/backstage/release",
    "redirect": "/backstage/release",
    "meta": {
      "title": '信息发布'
    },
    router:"/backstage/release",
  },
  
  xxtz:{
    component:createNameComponent(
      () => import("@/views/main/business/index.vue")
    ),
    "path": "/backstage/message",
    "redirect": "/backstage/message",
    "meta": {
      "title": '消息通知'
    },
    router:"/backstage/message",
  },
  //***系统设置 */
  xtsz:{
    component:createNameComponent(
      () => import("@/views/main/business/index.vue")
    ),
    "path": "/backstage/message",
    "redirect": "/backstage/message",
    "meta": {
      "title": '系统设置'
    },
    router:"/backstage/message",
  },
  // qxgl
  jsgl:{
    component:createNameComponent(
      () => import("@/views/main/business/index.vue")
    ),
    "path": "/backstage/system/role",
    "redirect": "/backstage/system/role",
    "meta": {
      "title": '角色管理'
    },
    router:"/backstage/system/role",
  },
  zhgl:{
    component:createNameComponent(
      () => import("@/views/main/business/index.vue")
    ),
    "path": "/backstage/system/account",
    "redirect": "/backstage/system/account",
    "meta": {
      "title": '账号管理'
    },
    router:"/backstage/system/account",
  },
 

  zhsh:{
    component:createNameComponent(
      () => import("@/views/main/business/index.vue")
    ),
    "path": "/backstage/system/accountCheck",
    "redirect": "/backstage/system/accountCheck",
    "meta": {
      "title": '账号审核'
    },
    router:"/backstage/system/accountCheck",
  },
  个人中心:{
    component:createNameComponent(
      () => import("@/views/main/business/index.vue")
    ),
    "path": "/accountCenter",
    "redirect": "/accountCenter",
    "meta": {
      "title": '个人中心'
    },
    router:"/accountCenter",
  },


  



}
 
 
/** 导出所有路由，供后端配置使用 */
const allRoutes = {
 
  // document,//文档
  // component,//组件
  ...pages,//页面
  // menu,//多级嵌套菜单
  // directive,//自定义指令
  // echarts,//echarts图表
  // systemManage,//系统管理
  // print,
  // community,
  // tab,
}

export default allRoutes