/*
 * @Date: 2022-07-24 21:43:47
 * @Description: 
 */
/** 引入类型 */
import type { RouteRecordRaw } from 'vue-router'

/** 引入路由相关的资源 */
import router, { modules } from './index'
/** 引入vuex实例 */
// import store from '@/store'
/** 引入后端路由控制器 */
// import { isBackMenu } from '@/config'

/** 引入纯前端路由 */
import FrontRoutes from './permission/front'
/** 引入后端路由 */
// import getMenu from './permission/back'
import {  token } from '@/utils/utils';

/** 
 * @name 动态路由的权限新增，供登录后调用
 * @other 如果需要进行后端接口控制菜单的话，请在此拿到后端的菜单树与asyncRoutes对比，生成一个新的值
 */
async function addRoutes(): Promise<RouteRecordRaw[]> {
  try {
    const add = await FrontRoutes();
    
    // 对每个路由的children进行排序：hideMenu为false的在前面，为true的在后面
    add.map((addItem: any) => {
      if (addItem.children && addItem.children.length > 0) {
        addItem.children.sort((a: any, b: any) => {
          const aHideMenu = a?.hideMenu === true;
          const bHideMenu = b?.hideMenu === true;
          
          // hideMenu为false的排在前面
          if (!aHideMenu && bHideMenu) return -1;
          if (aHideMenu && !bHideMenu) return 1;
          return 0;
        });
      }
      // console.log('addItem', addItem);
      return addItem;
    });
    
    // 排序完成后执行router.addRoute()
    add.forEach((route: any) => {
      router.addRoute({
        ...route,
        meta: { ...route.meta, isDynamic: true } // 标记动态路由
      })
    });
    
    // console.log('add', add);  
    return Promise.resolve(add);

  } catch (error) {
    // console.error('动态路由添加失败:', error);
    return Promise.resolve([]);
  }
}
/**
 * @des 登录了之后会执行这个方法，实现动态路由的功能
 */

export async function getAuthRoutes(): Promise<RouteRecordRaw[]> {
  if (!token()) {
    return []
  }
  try {
    const add = await addRoutes()
    return add
  } catch (error) {
    // console.error('Failed to add routes:', error)
    return []
  }
}