@import './transition.scss';
// @import '@/theme/index.scss';

@font-face {
  font-family: "YouSheBiaoTiHei";
  src: url('@/assets/text/YouSheBiaoTiHei-2.ttf');
}
::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 0px;
  /* 对应横向滚动条的宽度 */
  height: 5px;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
 background-color: #3241563c;
 border-radius: 32px;
}
.antd-Html{
  word-wrap: break-word;
}
.layout-container {
  // background-color: var(--system-container-main-background);
  width: calc(100% - 30px);
  height: calc(100% - 30px);
  // margin: 15px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  &-form {
    display: flex;
    justify-content: space-between;
    padding: 15px 15px 0;

    &-handle {
      display: flex;
      justify-content: flex-start;

      .export-excel-btn {
        margin-left: 15px;
      }
    }

    &-search {
      display: flex;
      justify-content: flex-end;

      .search-btn {
        margin-left: 15px;
      }
    }

    .el-form-item {
      margin-bottom: 0;
    }
  }

  &-table {
    flex: 1;
    height: 100%;
    padding: 15px;
    overflow: auto;
  }
}

.flex-box {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 15px;
  box-sizing: border-box;
}

.flex {
  display: flex;
}

.center {
  justify-content: center;
  align-items: center;
  text-align: center;
}

a {
  text-decoration: none;
}

/** element-plus **/
.el-icon {
  text-align: center;
}

/** 用于提示信息 **/
.my-tip {
  background-color: #f1f1f1;
  padding: 5px 10px;
  text-align: left;
  border-radius: 4px;
}

.system-scrollbar {
  &::-webkit-scrollbar {
    display: none;
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: rgba(144, 147, 153, 0.3);
  }

  &:hover {
    &::-webkit-scrollbar {
      display: block;
    }

    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: rgba(144, 147, 153, 0.3);

      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }
  }
}

.publicTableStyle {
  .antd-Drawer-content.in {
    .antd-NumberControl {
      width: 100px;

      .numberLey {
        width: 30px;
        margin: -4px 0 0 8px;
      }
    }

    .antd-Card-body {
      padding: 0 5px;
    }

    .antd-Card-fieldValue {
      .antd-Card.antd-Card--link {
        border: 0;
      }
    }
  }

  .amis-scope .antd-Tabs--line>.antd-Tabs-linksContainer-wrapper::before {
    border-bottom: 0px
  }

  .antd-Table.antd-Crud-body {
    background: #fff;
    padding: 20px 30px;
    margin:0 20px 20px 20px;
    box-shadow: 4px 4px 10px 0px #eef1f8;
     border-radius: 10px;
  }
  .amis-scope .antd-Panel-btnToolbar{
    display: flex;
    justify-content: flex-end;
  }
  .amis-scope .antd-Crud-filter {
    margin:0 20px 20px 20px;
    padding: 15px 30px 0 30px;
    border-radius: 10px;
    box-shadow: 4px 4px 10px 0px #eef1f8;
    

  }

  .antd-Crud.tableAutoStyle,
  .antd-Tabs-content,
  .antd-Page-content {
    background: #F0F2F5 !important;
  }

  .amis-scope {
    .antd-Page-content {
      padding: 20px !important;
    }

    .antd-Page-body,
    .antd-Tabs-pane {
      padding: 0px;
    }
  }

  .amis-scope .antd-Panel--default {
    border: 0px;
  }

  .antd-TplField {
    // font-size: 16px;
    color: #000000;

  }
.antd-Tabs-linksContainer-wrapper{
background: #fafcff !important;

}
  .tabsClass {
    .tabsTitle {
      padding: 15px 50px 0;
background: #fafcff !important;
      color: rgba(0, 0, 0, 0.85);
      margin-bottom: 16px;

      li {
        a {
          font-size: 16px !important;
        }
      }
    }

  }

}
.publicTableStyleNoFilter {
 
  .antd-Panel.antd-Table-searchableForm.antd-Panel--form,  .antd-Table-contentWrap,.antd-Table-toolbar.antd-Table-footToolbar{
    border: 0px;
    margin: 0 20px;
    box-shadow: 0px 4px 12px 0px #eef1f8;
    border-radius: 10px;
    .antd-Panel-body{
      padding: 20px 16px;
    }
  }
  .amis-scope .antd-Page-body {
    padding:0px;
}
.antd-Crud.tableAutoStyle,
.antd-Tabs-content,
.antd-Page-content {
  // padding: 20px;
  background: #fafcff !important;
}
.antd-Panel.antd-Table-searchableForm.antd-Panel--form{
border: 0px;
background:white;
margin-bottom: 20px;
}
.antd-Table-contentWrap,.antd-Table-toolbar.antd-Table-footToolbar{
  padding:  30px;
  background-color: white;
}
}

.antd-Toast-wrap.antd-Toast-wrap--topCenter{
  .antd-Toast.antd-Toast--error {
 display: none;
  }
 

}

.antd-Button--primary{
  background:#437BFF !important;
}
thead,thead > tr > th {
  font-size: 15PX  !important;
}
tbody,.antd-Form-label{
font-size: 14PX !important;
}
  #app1{
 .el-card.is-always-shadow{
background: #FFFFFF;
border-radius: 10px 10px 10px 10px;
  box-shadow: 0px 4px 12px 0px #EEF1F8 !important;
  border: 0px !important;

  .el-card__body{
    padding: 20px 16px;
  }
 }  }

 .antd-Table{
  .antd-Table-toolbar.antd-Table-footToolbar{
    .antd-Crud-toolbar{
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
 }
.antd-Panel-body{


 .antd-Form-label{
  margin-right: 12px !important;
 }
 }
 .amis-scope .antd-Form-item--inline{
  margin-bottom: 16px !important;
  .antd-Form-control--sizeMd{
width: 220px !important;
  }
 }