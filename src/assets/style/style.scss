@-webkit-keyframes marching-ants {
  0% {
    background-position:
      0 0,
      0 100%,
      0 0,
      100% 0;
  }
  to {
    background-position:
      20px 0,
      -20px 100%,
      0 -20px,
      100% 20px;
  }
}
@keyframes marching-ants {
  0% {
    background-position:
      0 0,
      0 100%,
      0 0,
      100% 0;
  }
  to {
    background-position:
      20px 0,
      -20px 100%,
      0 -20px,
      100% 20px;
  }
}
:root {
  --rc-drag-handle-size: 12px;
  --rc-drag-handle-mobile-size: 24px;
  --rc-drag-handle-bg-colour: rgba(0, 0, 0, 0.2);
  --rc-drag-bar-size: 6px;
  --rc-border-color: rgba(255, 255, 255, 0.7);
  --rc-focus-color: #0088ff;
}
.wujie_iframe{


.ReactCrop {
  position: relative;
  display: inline-block;
  cursor: crosshair;
  max-width: 100%;
}
.ReactCrop *,
.ReactCrop *:before,
.ReactCrop *:after {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
.ReactCrop--disabled,
.ReactCrop--locked {
  cursor: inherit;
}
.ReactCrop__child-wrapper {
  overflow: hidden;
  max-height: inherit;
}
.ReactCrop__child-wrapper > img,
.ReactCrop__child-wrapper > video {
  display: block;
  max-width: 100%;
  max-height: inherit;
}
.ReactCrop:not(.ReactCrop--disabled) .ReactCrop__child-wrapper > img,
.ReactCrop:not(.ReactCrop--disabled) .ReactCrop__child-wrapper > video {
  -ms-touch-action: none;
  touch-action: none;
}
.ReactCrop:not(.ReactCrop--disabled) .ReactCrop__crop-selection {
  -ms-touch-action: none;
  touch-action: none;
}
.ReactCrop__crop-mask {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  pointer-events: none;
}
.ReactCrop__crop-selection {
  position: absolute;
  top: 0;
  left: 0;
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  cursor: move;
}
.ReactCrop--disabled .ReactCrop__crop-selection {
  cursor: inherit;
}
.ReactCrop--circular-crop .ReactCrop__crop-selection {
  border-radius: 50%;
}
.ReactCrop--circular-crop .ReactCrop__crop-selection:after {
  pointer-events: none;
  content: "";
  position: absolute;
  top: -1px;
  right: -1px;
  bottom: -1px;
  left: -1px;
  border: 1px solid var(--rc-border-color);
  opacity: 0.3;
}
.ReactCrop--no-animate .ReactCrop__crop-selection {
  outline: 1px dashed white;
}
.ReactCrop__crop-selection:not(.ReactCrop--no-animate .ReactCrop__crop-selection) {
  -webkit-animation: marching-ants 1s;
  animation: marching-ants 1s;
  background-image:
    -webkit-gradient(linear, left top, right top, color-stop(50%, #fff), color-stop(50%, #444)),
    -webkit-gradient(linear, left top, right top, color-stop(50%, #fff), color-stop(50%, #444)),
    -webkit-gradient(linear, left top, left bottom, color-stop(50%, #fff), color-stop(50%, #444)),
    -webkit-gradient(linear, left top, left bottom, color-stop(50%, #fff), color-stop(50%, #444));
  background-image: linear-gradient(to right, #fff 50%, #444 50%), linear-gradient(to right, #fff 50%, #444 50%),
    linear-gradient(to bottom, #fff 50%, #444 50%), linear-gradient(to bottom, #fff 50%, #444 50%);
  background-size:
    10px 1px,
    10px 1px,
    1px 10px,
    1px 10px;
  background-position:
    0 0,
    0 100%,
    0 0,
    100% 0;
  background-repeat: repeat-x, repeat-x, repeat-y, repeat-y;
  color: #fff;
  -webkit-animation-play-state: running;
  animation-play-state: running;
  -webkit-animation-timing-function: linear;
  animation-timing-function: linear;
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}
.ReactCrop__crop-selection:focus {
  outline: 2px solid var(--rc-focus-color);
  outline-offset: -1px;
}
.ReactCrop--invisible-crop .ReactCrop__crop-mask,
.ReactCrop--invisible-crop .ReactCrop__crop-selection {
  display: none;
}
.ReactCrop__rule-of-thirds-vt:before,
.ReactCrop__rule-of-thirds-vt:after,
.ReactCrop__rule-of-thirds-hz:before,
.ReactCrop__rule-of-thirds-hz:after {
  content: "";
  display: block;
  position: absolute;
  background-color: #fff6;
}
.ReactCrop__rule-of-thirds-vt:before,
.ReactCrop__rule-of-thirds-vt:after {
  width: 1px;
  height: 100%;
}
.ReactCrop__rule-of-thirds-vt:before {
  left: 33.3333333333%;
}
.ReactCrop__rule-of-thirds-vt:after {
  left: 66.6666666667%;
}
.ReactCrop__rule-of-thirds-hz:before,
.ReactCrop__rule-of-thirds-hz:after {
  width: 100%;
  height: 1px;
}
.ReactCrop__rule-of-thirds-hz:before {
  top: 33.3333333333%;
}
.ReactCrop__rule-of-thirds-hz:after {
  top: 66.6666666667%;
}
.ReactCrop__drag-handle {
  position: absolute;
  width: var(--rc-drag-handle-size);
  height: var(--rc-drag-handle-size);
  background-color: var(--rc-drag-handle-bg-colour);
  border: 1px solid var(--rc-border-color);
}
.ReactCrop__drag-handle:focus {
  background: var(--rc-focus-color);
}
.ReactCrop .ord-nw {
  top: 0;
  left: 0;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  cursor: nw-resize;
}
.ReactCrop .ord-n {
  top: 0;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  cursor: n-resize;
}
.ReactCrop .ord-ne {
  top: 0;
  right: 0;
  -webkit-transform: translate(50%, -50%);
  transform: translate(50%, -50%);
  cursor: ne-resize;
}
.ReactCrop .ord-e {
  top: 50%;
  right: 0;
  -webkit-transform: translate(50%, -50%);
  transform: translate(50%, -50%);
  cursor: e-resize;
}
.ReactCrop .ord-se {
  bottom: 0;
  right: 0;
  -webkit-transform: translate(50%, 50%);
  transform: translate(50%, 50%);
  cursor: se-resize;
}
.ReactCrop .ord-s {
  bottom: 0;
  left: 50%;
  -webkit-transform: translate(-50%, 50%);
  transform: translate(-50%, 50%);
  cursor: s-resize;
}
.ReactCrop .ord-sw {
  bottom: 0;
  left: 0;
  -webkit-transform: translate(-50%, 50%);
  transform: translate(-50%, 50%);
  cursor: sw-resize;
}
.ReactCrop .ord-w {
  top: 50%;
  left: 0;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  cursor: w-resize;
}
.ReactCrop__disabled .ReactCrop__drag-handle {
  cursor: inherit;
}
.ReactCrop__drag-bar {
  position: absolute;
}
.ReactCrop__drag-bar.ord-n {
  top: 0;
  left: 0;
  width: 100%;
  height: var(--rc-drag-bar-size);
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
}
.ReactCrop__drag-bar.ord-e {
  right: 0;
  top: 0;
  width: var(--rc-drag-bar-size);
  height: 100%;
  -webkit-transform: translate(50%);
  transform: translate(50%);
}
.ReactCrop__drag-bar.ord-s {
  bottom: 0;
  left: 0;
  width: 100%;
  height: var(--rc-drag-bar-size);
  -webkit-transform: translateY(50%);
  transform: translateY(50%);
}
.ReactCrop__drag-bar.ord-w {
  top: 0;
  left: 0;
  width: var(--rc-drag-bar-size);
  height: 100%;
  -webkit-transform: translate(-50%);
  transform: translate(-50%);
}
.ReactCrop--new-crop .ReactCrop__drag-bar,
.ReactCrop--new-crop .ReactCrop__drag-handle,
.ReactCrop--fixed-aspect .ReactCrop__drag-bar,
.ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-n,
.ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-e,
.ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-s,
.ReactCrop--fixed-aspect .ReactCrop__drag-handle.ord-w {
  display: none;
}
@media (pointer: coarse) {
  .ReactCrop .ord-n,
  .ReactCrop .ord-e,
  .ReactCrop .ord-s,
  .ReactCrop .ord-w {
    display: none;
  }
  .ReactCrop__drag-handle {
    width: var(--rc-drag-handle-mobile-size);
    height: var(--rc-drag-handle-mobile-size);
  }
}
._wrap_5y04w_1 {
  display: flex;
  height: 100%;
  max-width: 100%;
  overflow: hidden;
  line-height: 0;
  flex-direction: column;
  border: 1px dashed hsl(var(--richtext-border)) !important;
  border-radius: 6px;
}
._wrap_5y04w_1 ._handlerWrap_5y04w_11 {
  display: flex;
  padding: 10px;
}
._wrap_5y04w_1 ._innerWrap_5y04w_15 {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: var(--richtext-border-radius);
  flex: 1;
}
._wrap_5y04w_1 ._emptyWrap_5y04w_23 {
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
}
._wrap_5y04w_1 iframe {
  width: 100%;
  height: 100%;
  border: 0;
  border: none !important;
}
._wrap_15k3c_1 {
  position: relative;
  max-width: 100%;
  overflow: visible;
  line-height: 0;
}
._wrap_15k3c_1 ._renderWrap_15k3c_7 {
  border: 1px dashed hsl(var(--richtext-border)) !important;
  border-radius: 6px;
}
._wrap_15k3c_1 ._renderWrap_15k3c_7:after {
  background-color: transparent !important;
}
._wrap_15k3c_1 ._title_15k3c_14 {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 2;
}
._wrap_15k3c_1 ._title_15k3c_14 ._icon_15k3c_20 {
  display: flex;
  width: 18px;
  height: 18px;
  color: #fff;
  background-color: #f80;
  border-radius: 2px;
  justify-content: center;
  align-items: center;
}
._wrap_15k3c_1 ._handlerWrap_15k3c_30 {
  position: absolute;
  right: 10px;
  bottom: 10px;
  z-index: 2;
  padding: 2px 4px;
  border: 1px solid hsl(var(--richtext-border));
  border-radius: 6px;
}
:root {
  --richtext-border-spacing-x: 0;
  --richtext-border-spacing-y: 0;
  --richtext-translate-x: 0;
  --richtext-translate-y: 0;
  --richtext-rotate: 0;
  --richtext-skew-x: 0;
  --richtext-skew-y: 0;
  --richtext-scale-x: 1;
  --richtext-scale-y: 1;
  --richtext-pan-x: ;
  --richtext-pan-y: ;
  --richtext-pinch-zoom: ;
  --richtext-scroll-snap-strictness: proximity;
  --richtext-gradient-from-position: ;
  --richtext-gradient-via-position: ;
  --richtext-gradient-to-position: ;
  --richtext-ordinal: ;
  --richtext-slashed-zero: ;
  --richtext-numeric-figure: ;
  --richtext-numeric-spacing: ;
  --richtext-numeric-fraction: ;
  --richtext-ring-inset: ;
  --richtext-ring-offset-width: 0px;
  --richtext-ring-offset-color: #fff;
  --richtext-ring-color: rgb(59 130 246 / 0.5);
  --richtext-ring-offset-shadow: 0 0 #0000;
  --richtext-ring-shadow: 0 0 #0000;
  --richtext-shadow: 0 0 #0000;
  --richtext-shadow-colored: 0 0 #0000;
  --richtext-blur: ;
  --richtext-brightness: ;
  --richtext-contrast: ;
  --richtext-grayscale: ;
  --richtext-hue-rotate: ;
  --richtext-invert: ;
  --richtext-saturate: ;
  --richtext-sepia: ;
  --richtext-drop-shadow: ;
  --richtext-backdrop-blur: ;
  --richtext-backdrop-brightness: ;
  --richtext-backdrop-contrast: ;
  --richtext-backdrop-grayscale: ;
  --richtext-backdrop-hue-rotate: ;
  --richtext-backdrop-invert: ;
  --richtext-backdrop-opacity: ;
  --richtext-backdrop-saturate: ;
  --richtext-backdrop-sepia: ;
  --richtext-contain-size: ;
  --richtext-contain-layout: ;
  --richtext-contain-paint: ;
  --richtext-contain-style: ;
}
::backdrop {
  --richtext-border-spacing-x: 0;
  --richtext-border-spacing-y: 0;
  --richtext-translate-x: 0;
  --richtext-translate-y: 0;
  --richtext-rotate: 0;
  --richtext-skew-x: 0;
  --richtext-skew-y: 0;
  --richtext-scale-x: 1;
  --richtext-scale-y: 1;
  --richtext-pan-x: ;
  --richtext-pan-y: ;
  --richtext-pinch-zoom: ;
  --richtext-scroll-snap-strictness: proximity;
  --richtext-gradient-from-position: ;
  --richtext-gradient-via-position: ;
  --richtext-gradient-to-position: ;
  --richtext-ordinal: ;
  --richtext-slashed-zero: ;
  --richtext-numeric-figure: ;
  --richtext-numeric-spacing: ;
  --richtext-numeric-fraction: ;
  --richtext-ring-inset: ;
  --richtext-ring-offset-width: 0px;
  --richtext-ring-offset-color: #fff;
  --richtext-ring-color: rgb(59 130 246 / 0.5);
  --richtext-ring-offset-shadow: 0 0 #0000;
  --richtext-ring-shadow: 0 0 #0000;
  --richtext-shadow: 0 0 #0000;
  --richtext-shadow-colored: 0 0 #0000;
  --richtext-blur: ;
  --richtext-brightness: ;
  --richtext-contrast: ;
  --richtext-grayscale: ;
  --richtext-hue-rotate: ;
  --richtext-invert: ;
  --richtext-saturate: ;
  --richtext-sepia: ;
  --richtext-drop-shadow: ;
  --richtext-backdrop-blur: ;
  --richtext-backdrop-brightness: ;
  --richtext-backdrop-contrast: ;
  --richtext-backdrop-grayscale: ;
  --richtext-backdrop-hue-rotate: ;
  --richtext-backdrop-invert: ;
  --richtext-backdrop-opacity: ;
  --richtext-backdrop-saturate: ;
  --richtext-backdrop-sepia: ;
  --richtext-contain-size: ;
  --richtext-contain-layout: ;
  --richtext-contain-paint: ;
  --richtext-contain-style: ;
}
:root {
  --richtext-background: 0 0% 100%;
  --richtext-foreground: 240 10% 3.9%;
  --richtext-muted: 240 4.8% 95.9%;
  --richtext-muted-foreground: 240 3.8% 46.1%;
  --richtext-popover: 0 0% 100%;
  --richtext-popover-foreground: 240 10% 3.9%;
  --richtext-card: 0 0% 100%;
  --richtext-card-foreground: 240 10% 3.9%;
  --richtext-border: 240 5.9% 90%;
  --richtext-input: 240 5.9% 90%;
  --richtext-primary: 240 5.9% 10%;
  --richtext-primary-foreground: 0 0% 98%;
  --richtext-secondary: 240 4.8% 95.9%;
  --richtext-secondary-foreground: 240 5.9% 10%;
  --richtext-accent: 0 0% 88.24%;
  --richtext-accent-foreground: 240 5.9% 10%;
  --richtext-destructive: 0 84.2% 60.2%;
  --richtext-destructive-foreground: 0 0% 98%;
  --richtext-ring: 240 10% 3.9%;
  --richtext-radius: 0.5rem;
  --red: #ff5c33;
}
.dark {
  --richtext-background: 240 10% 3.9%;
  --richtext-foreground: 0 0% 98%;
  --richtext-muted: 240 3.7% 15.9%;
  --richtext-muted-foreground: 240 5% 64.9%;
  --richtext-popover: 240 10% 3.9%;
  --richtext-popover-foreground: 0 0% 98%;
  --richtext-card: 240 10% 3.9%;
  --richtext-card-foreground: 0 0% 98%;
  --richtext-border: 240 3.7% 15.9%;
  --richtext-input: 240 3.7% 15.9%;
  --richtext-primary: 0 0% 98%;
  --richtext-primary-foreground: 240 5.9% 10%;
  --richtext-secondary: 240 3.7% 15.9%;
  --richtext-secondary-foreground: 0 0% 98%;
  --richtext-accent: 240 3.7% 15.9%;
  --richtext-accent-foreground: 0 0% 98%;
  --richtext-destructive: 0 62.8% 30.6%;
  --richtext-destructive-foreground: 0 0% 98%;
  --richtext-ring: 240 4.9% 83.9%;
  --red: #ff5c33;
}
.richtext-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.richtext-pointer-events-none {
  pointer-events: none;
}
.richtext-pointer-events-auto {
  pointer-events: auto;
}
.richtext-fixed {
  position: fixed;
}
.richtext-absolute {
  position: absolute;
}
.richtext-relative {
  position: relative;
}
.richtext-inset-0 {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
.richtext-inset-y-0 {
  top: 0;
  bottom: 0;
}
.richtext-left-0 {
  left: 0;
}
.richtext-left-2 {
  left: 0.5rem;
}
.richtext-left-\[1px\] {
  left: 1px;
}
.richtext-left-\[50\%\] {
  left: 50%;
}
.richtext-right-2 {
  right: 0.5rem;
}
.richtext-right-4 {
  right: 1rem;
}
.richtext-start-0 {
  inset-inline-start: 0px;
}
.richtext-top-0 {
  top: 0;
}
.richtext-top-2 {
  top: 0.5rem;
}
.richtext-top-4 {
  top: 1rem;
}
.richtext-top-\[-1px\] {
  top: -1px;
}
.richtext-top-\[50\%\] {
  top: 50%;
}
.richtext-z-0 {
  z-index: 0;
}
.richtext-z-50 {
  z-index: 50;
}
.richtext-z-\[100\] {
  z-index: 100;
}
.richtext-z-\[1\] {
  z-index: 1;
}
.richtext-z-\[99999\] {
  z-index: 99999;
}
.richtext-col-\[1\/-1\] {
  grid-column: 1/-1;
}
.\!richtext-mx-1 {
  margin-left: 0.25rem !important;
  margin-right: 0.25rem !important;
}
.\!richtext-mx-2 {
  margin-left: 0.5rem !important;
  margin-right: 0.5rem !important;
}
.\!richtext-mx-\[10px\] {
  margin-left: 10px !important;
  margin-right: 10px !important;
}
.\!richtext-my-2 {
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
}
.richtext--mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}
.richtext-mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.richtext-mx-\[auto\] {
  margin-left: auto;
  margin-right: auto;
}
.richtext-my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.richtext-my-\[10px\] {
  margin-top: 10px;
  margin-bottom: 10px;
}
.richtext-my-\[12px\] {
  margin-top: 12px;
  margin-bottom: 12px;
}
.richtext-my-\[5px\] {
  margin-top: 5px;
  margin-bottom: 5px;
}
.\!richtext-mr-1 {
  margin-right: 0.25rem !important;
}
.richtext-mb-8 {
  margin-bottom: 2rem;
}
.richtext-mb-\[10px\] {
  margin-bottom: 10px;
}
.richtext-mb-\[16px\] {
  margin-bottom: 16px;
}
.richtext-mb-\[5px\] {
  margin-bottom: 5px;
}
.richtext-mb-\[6px\] {
  margin-bottom: 6px;
}
.richtext-mb-\[8px\] {
  margin-bottom: 8px;
}
.richtext-ml-1 {
  margin-left: 0.25rem;
}
.richtext-ml-\[4px\] {
  margin-left: 4px;
}
.richtext-ml-auto {
  margin-left: auto;
}
.richtext-mt-1 {
  margin-top: 0.25rem;
}
.richtext-mt-2 {
  margin-top: 0.5rem;
}
.richtext-mt-\[8px\] {
  margin-top: 8px;
}
.richtext-box-border {
  box-sizing: border-box;
}
.richtext-block {
  display: block;
}
.richtext-inline-block {
  display: inline-block;
}
.\!richtext-flex {
  display: flex !important;
}
.richtext-flex {
  display: flex;
}
.richtext-inline-flex {
  display: inline-flex;
}
.richtext-grid {
  display: grid;
}
.richtext-size-5 {
  width: 1.25rem;
  height: 1.25rem;
}
.\!richtext-h-3 {
  height: 0.75rem !important;
}
.\!richtext-h-4 {
  height: 1rem !important;
}
.\!richtext-h-\[16px\] {
  height: 16px !important;
}
.\!richtext-h-\[32px\] {
  height: 32px !important;
}
.\!richtext-h-auto {
  height: auto !important;
}
.richtext-h-10 {
  height: 2.5rem;
}
.richtext-h-11 {
  height: 2.75rem;
}
.richtext-h-12 {
  height: 3rem;
}
.richtext-h-2 {
  height: 0.5rem;
}
.richtext-h-3 {
  height: 0.75rem;
}
.richtext-h-3\.5 {
  height: 0.875rem;
}
.richtext-h-4 {
  height: 1rem;
}
.richtext-h-5 {
  height: 1.25rem;
}
.richtext-h-6 {
  height: 1.5rem;
}
.richtext-h-7 {
  height: 1.75rem;
}
.richtext-h-8 {
  height: 2rem;
}
.richtext-h-9 {
  height: 2.25rem;
}
.richtext-h-\[18px\] {
  height: 18px;
}
.richtext-h-\[1em\] {
  height: 1em;
}
.richtext-h-\[1px\] {
  height: 1px;
}
.richtext-h-\[26px\] {
  height: 26px;
}
.richtext-h-\[28px\] {
  height: 28px;
}
.richtext-h-\[32px\] {
  height: 32px;
}
.richtext-h-\[var\(--radix-select-trigger-height\)\] {
  height: var(--radix-select-trigger-height);
}
.richtext-h-auto {
  height: auto;
}
.richtext-h-full {
  height: 100%;
}
.richtext-h-px {
  height: 1px;
}
.\!richtext-max-h-\[180px\] {
  max-height: 180px !important;
}
.richtext-max-h-60 {
  max-height: 15rem;
}
.richtext-max-h-96 {
  max-height: 24rem;
}
.richtext-max-h-\[280px\] {
  max-height: 280px;
}
.richtext-max-h-\[320px\] {
  max-height: 320px;
}
.richtext-max-h-\[min\(80vh\,24rem\)\] {
  max-height: min(80vh, 24rem);
}
.richtext-max-h-full {
  max-height: 100%;
}
.richtext-max-h-screen {
  max-height: 100vh;
}
.richtext-min-h-\[80px\] {
  min-height: 80px;
}
.\!richtext-w-12 {
  width: 3rem !important;
}
.\!richtext-w-3 {
  width: 0.75rem !important;
}
.\!richtext-w-4 {
  width: 1rem !important;
}
.richtext-w-10 {
  width: 2.5rem;
}
.richtext-w-11 {
  width: 2.75rem;
}
.richtext-w-2 {
  width: 0.5rem;
}
.richtext-w-3 {
  width: 0.75rem;
}
.richtext-w-3\.5 {
  width: 0.875rem;
}
.richtext-w-32 {
  width: 8rem;
}
.richtext-w-4 {
  width: 1rem;
}
.richtext-w-48 {
  width: 12rem;
}
.richtext-w-5 {
  width: 1.25rem;
}
.richtext-w-6 {
  width: 1.5rem;
}
.richtext-w-7 {
  width: 1.75rem;
}
.richtext-w-72 {
  width: 18rem;
}
.richtext-w-80 {
  width: 20rem;
}
.richtext-w-\[18px\] {
  width: 18px;
}
.richtext-w-\[1em\] {
  width: 1em;
}
.richtext-w-\[1px\] {
  width: 1px;
}
.richtext-w-\[200px\] {
  width: 200px;
}
.richtext-w-\[28px\] {
  width: 28px;
}
.richtext-w-\[32px\] {
  width: 32px;
}
.richtext-w-\[60px\] {
  width: 60px;
}
.richtext-w-auto {
  width: auto;
}
.richtext-w-fit {
  width: fit-content;
}
.richtext-w-full {
  width: 100%;
}
.richtext-min-w-24 {
  min-width: 6rem;
}
.richtext-min-w-32 {
  min-width: 8rem;
}
.richtext-min-w-4 {
  min-width: 1rem;
}
.richtext-min-w-48 {
  min-width: 12rem;
}
.richtext-min-w-\[8rem\] {
  min-width: 8rem;
}
.richtext-min-w-\[var\(--radix-select-trigger-width\)\] {
  min-width: var(--radix-select-trigger-width);
}
.\!richtext-max-w-\[1300px\] {
  max-width: 1300px !important;
}
.richtext-max-w-24 {
  max-width: 6rem;
}
.richtext-max-w-\[286px\] {
  max-width: 286px;
}
.richtext-max-w-\[600px\] {
  max-width: 600px;
}
.richtext-max-w-lg {
  max-width: 32rem;
}
.richtext-max-w-sm {
  max-width: 24rem;
}
.richtext-flex-1 {
  flex: 1 1 0%;
}
.richtext-flex-\[0_0_auto\] {
  flex: 0 0 auto;
}
.richtext-flex-shrink-0 {
  flex-shrink: 0;
}
.\!richtext-shrink-0 {
  flex-shrink: 0 !important;
}
.richtext-shrink-0 {
  flex-shrink: 0;
}
.richtext-flex-grow {
  flex-grow: 1;
}
.richtext-translate-x-\[-50\%\] {
  --richtext-translate-x: -50%;
  transform: translate(var(--richtext-translate-x), var(--richtext-translate-y)) rotate(var(--richtext-rotate))
    skew(var(--richtext-skew-x)) skewY(var(--richtext-skew-y)) scaleX(var(--richtext-scale-x))
    scaleY(var(--richtext-scale-y));
}
.richtext-translate-y-\[-50\%\] {
  --richtext-translate-y: -50%;
  transform: translate(var(--richtext-translate-x), var(--richtext-translate-y)) rotate(var(--richtext-rotate))
    skew(var(--richtext-skew-x)) skewY(var(--richtext-skew-y)) scaleX(var(--richtext-scale-x))
    scaleY(var(--richtext-scale-y));
}
.richtext-cursor-default {
  cursor: default;
}
.richtext-cursor-grab {
  cursor: grab;
}
.richtext-cursor-pointer {
  cursor: pointer;
}
.richtext-select-none {
  -webkit-user-select: none;
  user-select: none;
}
.richtext-grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.richtext-grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.richtext-grid-cols-8 {
  grid-template-columns: repeat(8, minmax(0, 1fr));
}
.richtext-flex-row {
  flex-direction: row;
}
.richtext-flex-col {
  flex-direction: column;
}
.richtext-flex-col-reverse {
  flex-direction: column-reverse;
}
.richtext-flex-wrap {
  flex-wrap: wrap;
}
.richtext-flex-nowrap {
  flex-wrap: nowrap;
}
.\!richtext-items-center {
  align-items: center !important;
}
.richtext-items-center {
  align-items: center;
}
.richtext-justify-start {
  justify-content: flex-start;
}
.richtext-justify-end {
  justify-content: flex-end;
}
.\!richtext-justify-center {
  justify-content: center !important;
}
.richtext-justify-center {
  justify-content: center;
}
.richtext-justify-between {
  justify-content: space-between;
}
.richtext-gap-0\.5 {
  gap: 0.125rem;
}
.richtext-gap-1 {
  gap: 0.25rem;
}
.richtext-gap-1\.5 {
  gap: 0.375rem;
}
.richtext-gap-2 {
  gap: 0.5rem;
}
.richtext-gap-3 {
  gap: 0.75rem;
}
.richtext-gap-4 {
  gap: 1rem;
}
.richtext-gap-\[10px\] {
  gap: 10px;
}
.richtext-gap-\[4px\] {
  gap: 4px;
}
.richtext-gap-\[6px\] {
  gap: 6px;
}
.richtext-gap-x-1 {
  column-gap: 0.25rem;
}
.richtext-gap-y-1 {
  row-gap: 0.25rem;
}
.richtext-space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --richtext-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--richtext-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--richtext-space-x-reverse)));
}
.richtext-space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --richtext-space-x-reverse: 0;
  margin-right: calc(1rem * var(--richtext-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--richtext-space-x-reverse)));
}
.richtext-space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --richtext-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--richtext-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--richtext-space-y-reverse));
}
.richtext-self-end {
  align-self: flex-end;
}
.richtext-overflow-auto {
  overflow: auto;
}
.richtext-overflow-hidden {
  overflow: hidden;
}
.richtext-overflow-y-auto {
  overflow-y: auto;
}
.richtext-overflow-x-hidden {
  overflow-x: hidden;
}
.\!richtext-overflow-y-scroll {
  overflow-y: scroll !important;
}
.richtext-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.richtext-whitespace-nowrap {
  white-space: nowrap;
}
.richtext-break-all {
  word-break: break-all;
}
.\!richtext-rounded-sm {
  border-radius: calc(var(--richtext-radius) - 4px) !important;
}
.richtext-rounded-\[0\.5rem\] {
  border-radius: 0.5rem;
}
.richtext-rounded-\[10px\] {
  border-radius: 10px;
}
.richtext-rounded-\[12px\] {
  border-radius: 12px;
}
.richtext-rounded-\[2px\] {
  border-radius: 2px;
}
.richtext-rounded-\[6px\] {
  border-radius: 6px;
}
.richtext-rounded-full {
  border-radius: 9999px;
}
.richtext-rounded-lg {
  border-radius: var(--richtext-radius);
}
.richtext-rounded-md {
  border-radius: calc(var(--richtext-radius) - 2px);
}
.richtext-rounded-sm {
  border-radius: calc(var(--richtext-radius) - 4px);
}
.\!richtext-border,
.\!richtext-border-\[1px\] {
  border-width: 1px !important;
}
.richtext-border {
  border-width: 1px;
}
.richtext-border-2 {
  border-width: 2px;
}
.richtext-border-\[1px\] {
  border-width: 1px;
}
.\!richtext-border-b {
  border-bottom-width: 1px !important;
}
.richtext-border-t {
  border-top-width: 1px;
}
.richtext-border-solid {
  border-style: solid;
}
.\!richtext-border-border {
  border-color: hsl(var(--richtext-border)) !important;
}
.\!richtext-border-neutral-200 {
  --richtext-border-opacity: 1 !important;
  border-color: rgb(229 229 229 / var(--richtext-border-opacity, 1)) !important;
}
.\!richtext-border-primary {
  border-color: hsl(var(--richtext-primary)) !important;
}
.richtext-border-\[\#ccc\] {
  --richtext-border-opacity: 1;
  border-color: rgb(204 204 204 / var(--richtext-border-opacity, 1));
}
.richtext-border-border {
  border-color: hsl(var(--richtext-border));
}
.richtext-border-destructive {
  border-color: hsl(var(--richtext-destructive));
}
.richtext-border-input {
  border-color: hsl(var(--richtext-input));
}
.richtext-border-neutral-200 {
  --richtext-border-opacity: 1;
  border-color: rgb(229 229 229 / var(--richtext-border-opacity, 1));
}
.richtext-border-transparent {
  border-color: transparent;
}
.\!richtext-bg-foreground {
  background-color: hsl(var(--richtext-foreground)) !important;
}
.\!richtext-bg-primary {
  background-color: hsl(var(--richtext-primary)) !important;
}
.\!richtext-bg-transparent {
  background-color: transparent !important;
}
.\!richtext-bg-white {
  --richtext-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--richtext-bg-opacity, 1)) !important;
}
.richtext-bg-accent {
  background-color: hsl(var(--richtext-accent));
}
.richtext-bg-background {
  background-color: hsl(var(--richtext-background));
}
.richtext-bg-black\/80 {
  background-color: #000c;
}
.richtext-bg-border {
  background-color: hsl(var(--richtext-border));
}
.richtext-bg-destructive {
  background-color: hsl(var(--richtext-destructive));
}
.richtext-bg-muted {
  background-color: hsl(var(--richtext-muted));
}
.richtext-bg-popover {
  background-color: hsl(var(--richtext-popover));
}
.richtext-bg-secondary {
  background-color: hsl(var(--richtext-secondary));
}
.richtext-bg-transparent {
  background-color: transparent;
}
.richtext-bg-white {
  --richtext-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--richtext-bg-opacity, 1));
}
.richtext-bg-opacity-10 {
  --richtext-bg-opacity: 0.1;
}
.richtext-fill-current {
  fill: currentColor;
}
.\!richtext-p-0 {
  padding: 0 !important;
}
.\!richtext-p-2 {
  padding: 0.5rem !important;
}
.\!richtext-p-\[4px\] {
  padding: 4px !important;
}
.\!richtext-p-\[6px\] {
  padding: 6px !important;
}
.richtext-p-0 {
  padding: 0;
}
.richtext-p-0\.5 {
  padding: 0.125rem;
}
.richtext-p-1 {
  padding: 0.25rem;
}
.richtext-p-2 {
  padding: 0.5rem;
}
.richtext-p-3 {
  padding: 0.75rem;
}
.richtext-p-4 {
  padding: 1rem;
}
.richtext-p-6 {
  padding: 1.5rem;
}
.richtext-p-\[10px\] {
  padding: 10px;
}
.richtext-px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.richtext-px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.richtext-px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.richtext-px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.richtext-px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.richtext-px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.richtext-px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.richtext-px-\[5px\] {
  padding-left: 5px;
  padding-right: 5px;
}
.richtext-py-0 {
  padding-top: 0;
  padding-bottom: 0;
}
.richtext-py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.richtext-py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.richtext-py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.richtext-pl-10 {
  padding-left: 2.5rem;
}
.richtext-pl-4 {
  padding-left: 1rem;
}
.richtext-pl-8 {
  padding-left: 2rem;
}
.richtext-pr-2 {
  padding-right: 0.5rem;
}
.richtext-pr-8 {
  padding-right: 2rem;
}
.richtext-text-left {
  text-align: left;
}
.richtext-text-center {
  text-align: center;
}
.\!richtext-text-lg {
  font-size: 1.125rem !important;
  line-height: 1.75rem !important;
}
.richtext-text-\[0\.65rem\] {
  font-size: 0.65rem;
}
.richtext-text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.richtext-text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.richtext-text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}
.richtext-font-medium {
  font-weight: 500;
}
.richtext-font-normal {
  font-weight: 400;
}
.richtext-font-semibold {
  font-weight: 600;
}
.richtext-uppercase {
  text-transform: uppercase;
}
.richtext-leading-none {
  line-height: 1;
}
.richtext-tracking-tight {
  letter-spacing: -0.025em;
}
.richtext-tracking-wider {
  letter-spacing: 0.05em;
}
.richtext-tracking-widest {
  letter-spacing: 0.1em;
}
.\!richtext-text-black {
  --richtext-text-opacity: 1 !important;
  color: rgb(0 0 0 / var(--richtext-text-opacity, 1)) !important;
}
.\!richtext-text-current {
  color: currentColor !important;
}
.\!richtext-text-neutral-500 {
  --richtext-text-opacity: 1 !important;
  color: rgb(115 115 115 / var(--richtext-text-opacity, 1)) !important;
}
.\!richtext-text-neutral-800 {
  --richtext-text-opacity: 1 !important;
  color: rgb(38 38 38 / var(--richtext-text-opacity, 1)) !important;
}
.\!richtext-text-primary-foreground {
  color: hsl(var(--richtext-primary-foreground)) !important;
}
.richtext-text-destructive-foreground {
  color: hsl(var(--richtext-destructive-foreground));
}
.richtext-text-foreground {
  color: hsl(var(--richtext-foreground));
}
.richtext-text-foreground\/50 {
  color: hsl(var(--richtext-foreground) / 0.5);
}
.richtext-text-gray-500 {
  --richtext-text-opacity: 1;
  color: rgb(107 114 128 / var(--richtext-text-opacity, 1));
}
.richtext-text-gray-800 {
  --richtext-text-opacity: 1;
  color: rgb(31 41 55 / var(--richtext-text-opacity, 1));
}
.richtext-text-muted-foreground {
  color: hsl(var(--richtext-muted-foreground));
}
.richtext-text-neutral-600 {
  --richtext-text-opacity: 1;
  color: rgb(82 82 82 / var(--richtext-text-opacity, 1));
}
.richtext-text-popover-foreground {
  color: hsl(var(--richtext-popover-foreground));
}
.richtext-text-primary {
  color: hsl(var(--richtext-primary));
}
.richtext-text-red-500 {
  --richtext-text-opacity: 1;
  color: rgb(239 68 68 / var(--richtext-text-opacity, 1));
}
.richtext-text-secondary-foreground {
  color: hsl(var(--richtext-secondary-foreground));
}
.richtext-text-zinc-500 {
  --richtext-text-opacity: 1;
  color: rgb(113 113 122 / var(--richtext-text-opacity, 1));
}
.richtext-text-zinc-600 {
  --richtext-text-opacity: 1;
  color: rgb(82 82 91 / var(--richtext-text-opacity, 1));
}
.richtext-underline {
  text-decoration-line: underline;
}
.richtext-underline-offset-4 {
  text-underline-offset: 4px;
}
.richtext-opacity-0 {
  opacity: 0;
}
.richtext-opacity-50 {
  opacity: 0.5;
}
.richtext-opacity-60 {
  opacity: 0.6;
}
.richtext-opacity-70 {
  opacity: 0.7;
}
.richtext-opacity-90 {
  opacity: 0.9;
}
.richtext-shadow {
  --richtext-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --richtext-shadow-colored: 0 1px 3px 0 var(--richtext-shadow-color), 0 1px 2px -1px var(--richtext-shadow-color);
  box-shadow: var(--richtext-ring-offset-shadow, 0 0 #0000), var(--richtext-ring-shadow, 0 0 #0000),
    var(--richtext-shadow);
}
.richtext-shadow-lg {
  --richtext-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --richtext-shadow-colored: 0 10px 15px -3px var(--richtext-shadow-color), 0 4px 6px -4px var(--richtext-shadow-color);
  box-shadow: var(--richtext-ring-offset-shadow, 0 0 #0000), var(--richtext-ring-shadow, 0 0 #0000),
    var(--richtext-shadow);
}
.richtext-shadow-md {
  --richtext-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --richtext-shadow-colored: 0 4px 6px -1px var(--richtext-shadow-color), 0 2px 4px -2px var(--richtext-shadow-color);
  box-shadow: var(--richtext-ring-offset-shadow, 0 0 #0000), var(--richtext-ring-shadow, 0 0 #0000),
    var(--richtext-shadow);
}
.richtext-shadow-sm {
  --richtext-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --richtext-shadow-colored: 0 1px 2px 0 var(--richtext-shadow-color);
  box-shadow: var(--richtext-ring-offset-shadow, 0 0 #0000), var(--richtext-ring-shadow, 0 0 #0000),
    var(--richtext-shadow);
}
.richtext-outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.richtext-outline {
  outline-style: solid;
}
.richtext-outline-1 {
  outline-width: 1px;
}
.richtext-ring-0 {
  --richtext-ring-offset-shadow: var(--richtext-ring-inset) 0 0 0 var(--richtext-ring-offset-width)
    var(--richtext-ring-offset-color);
  --richtext-ring-shadow: var(--richtext-ring-inset) 0 0 0 calc(0px + var(--richtext-ring-offset-width))
    var(--richtext-ring-color);
  box-shadow: var(--richtext-ring-offset-shadow), var(--richtext-ring-shadow), var(--richtext-shadow, 0 0 #0000);
}
.\!richtext-ring-offset-background {
  --richtext-ring-offset-color: hsl(var(--richtext-background)) !important;
}
.richtext-ring-offset-background {
  --richtext-ring-offset-color: hsl(var(--richtext-background));
}
.richtext-transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}
.richtext-transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}
.richtext-transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}
.richtext-transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}
.richtext-duration-200 {
  transition-duration: 0.2s;
}
.richtext-ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
@keyframes enter {
  0% {
    opacity: var(--richtext-enter-opacity, 1);
    transform: translate3d(var(--richtext-enter-translate-x, 0), var(--richtext-enter-translate-y, 0), 0)
      scale3d(var(--richtext-enter-scale, 1), var(--richtext-enter-scale, 1), var(--richtext-enter-scale, 1))
      rotate(var(--richtext-enter-rotate, 0));
  }
}
@keyframes exit {
  to {
    opacity: var(--richtext-exit-opacity, 1);
    transform: translate3d(var(--richtext-exit-translate-x, 0), var(--richtext-exit-translate-y, 0), 0)
      scale3d(var(--richtext-exit-scale, 1), var(--richtext-exit-scale, 1), var(--richtext-exit-scale, 1))
      rotate(var(--richtext-exit-rotate, 0));
  }
}
.richtext-animate-in {
  animation-name: enter;
  animation-duration: 0.15s;
  --richtext-enter-opacity: initial;
  --richtext-enter-scale: initial;
  --richtext-enter-rotate: initial;
  --richtext-enter-translate-x: initial;
  --richtext-enter-translate-y: initial;
}
.richtext-fade-in-0 {
  --richtext-enter-opacity: 0;
}
.richtext-zoom-in-95 {
  --richtext-enter-scale: 0.95;
}
.richtext-duration-200 {
  animation-duration: 0.2s;
}
.richtext-ease-in-out {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.\[transition-property\:top\,_left\] {
  transition-property: top, left;
}
html body[data-scroll-locked] {
  --removed-body-scroll-bar-size: 0 !important;
  position: initial !important;
}
.character-count--warning {
  color: var(--red) !important;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror {
  z-index: 0;
  padding: 4rem 2rem 4rem 5rem;
  caret-color: #000;
  outline-width: 0px;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror:is([class~="dark"] *) {
  caret-color: #fff;
}
@media (min-width: 1024px) {
  .reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror {
  min-height: 180px;
  padding: 32px 80px;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .img-placeholder {
  width: fit-content;
  position: relative;
  background-color: #ffffff4d;
  --richtext-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--richtext-backdrop-blur) var(--richtext-backdrop-brightness)
    var(--richtext-backdrop-contrast) var(--richtext-backdrop-grayscale) var(--richtext-backdrop-hue-rotate)
    var(--richtext-backdrop-invert) var(--richtext-backdrop-opacity) var(--richtext-backdrop-saturate)
    var(--richtext-backdrop-sepia);
  backdrop-filter: var(--richtext-backdrop-blur) var(--richtext-backdrop-brightness) var(--richtext-backdrop-contrast)
    var(--richtext-backdrop-grayscale) var(--richtext-backdrop-hue-rotate) var(--richtext-backdrop-invert)
    var(--richtext-backdrop-opacity) var(--richtext-backdrop-saturate) var(--richtext-backdrop-sepia);
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .img-placeholder:before {
  content: "";
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 24px;
  height: 24px;
  margin: -12px 0 0 -12px;
  display: inline-block;
}
@keyframes richtext-spin {
  to {
    transform: rotate(360deg);
  }
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .img-placeholder:before {
  animation: richtext-spin 1s linear infinite;
  border-radius: 9999px;
  border-width: 3px;
  border-color: currentColor;
  border-top-color: transparent;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .tableWrapper {
  margin: 1em 0;
  overflow-x: auto;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .tableWrapper table {
  overflow: hidden;
  display: table;
  width: 100%;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor
  .ProseMirror.ProseMirror.ProseMirror
  .tableWrapper
  table
  .column-resize-handle {
  pointer-events: none;
  position: absolute;
  bottom: -2px;
  right: -2px;
  top: 0;
  z-index: 10;
  width: 0.25rem;
  background-color: hsl(var(--richtext-primary));
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .tableWrapper table .resize-cursor {
  cursor: "col-resize";
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .tableWrapper table .selectedCell {
  border-style: double;
  border-color: #0003;
  background-color: #0000000d;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor
  .ProseMirror.ProseMirror.ProseMirror
  .tableWrapper
  table
  .selectedCell:is([class~="dark"] *) {
  border-color: #fff3;
  background-color: #ffffff1a;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror [data-type="horizontalRule"] {
  cursor: pointer;
  transition-property: all;
  transition-duration: 0.1s;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  animation-duration: 0.1s;
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.reactjs-tiptap-editor.reactjs-tiptap-editor
  .ProseMirror.ProseMirror.ProseMirror
  [data-type="horizontalRule"].ProseMirror-selectednode {
  background-color: #0000000d;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor
  .ProseMirror.ProseMirror.ProseMirror
  [data-type="horizontalRule"].ProseMirror-selectednode:is([class~="dark"] *) {
  background-color: #ffffff1a;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor
  .ProseMirror.ProseMirror.ProseMirror
  [data-type="horizontalRule"].ProseMirror-selectednode
  hr {
  border-top-color: #0000004d;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor
  .ProseMirror.ProseMirror.ProseMirror
  [data-type="horizontalRule"].ProseMirror-selectednode
  hr:is([class~="dark"] *) {
  border-top-color: #ffffff4d;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor
  .ProseMirror.ProseMirror.ProseMirror
  [data-type="horizontalRule"]:hover:not(
    .reactjs-tiptap-editor.reactjs-tiptap-editor
      .ProseMirror.ProseMirror.ProseMirror
      [data-type="horizontalRule"].ProseMirror-selectednode
  ) {
  background-color: #0000000d;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor
  .ProseMirror.ProseMirror.ProseMirror
  [data-type="horizontalRule"]:hover:not(
    .reactjs-tiptap-editor.reactjs-tiptap-editor
      .ProseMirror.ProseMirror.ProseMirror
      [data-type="horizontalRule"].ProseMirror-selectednode
  ):is([class~="dark"] *) {
  background-color: #ffffff1a;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor
  .ProseMirror.ProseMirror.ProseMirror
  :not(.dragging)
  .ProseMirror-selectednode:not(.image-view) {
  background-color: #0000001a;
  --richtext-shadow: 0 0 #0000;
  --richtext-shadow-colored: 0 0 #0000;
  box-shadow: var(--richtext-ring-offset-shadow, 0 0 #0000), var(--richtext-ring-shadow, 0 0 #0000),
    var(--richtext-shadow);
  outline: 2px solid transparent;
  outline-offset: 2px;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor
  .ProseMirror.ProseMirror.ProseMirror
  :not(.dragging)
  .ProseMirror-selectednode:not(.image-view):is([class~="dark"] *) {
  background-color: #fff3;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .selection:not(.image-view),
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror *:not(.image-view)::selection {
  background-color: #0000001a;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor
  .ProseMirror.ProseMirror.ProseMirror
  .selection:not(.image-view):is([class~="dark"] *),
.reactjs-tiptap-editor.reactjs-tiptap-editor
  .ProseMirror.ProseMirror.ProseMirror
  *:not(.image-view):is([class~="dark"] *)::selection {
  background-color: #fff3;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .is-empty:before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  width: 100%;
  height: 0;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .columns.is-empty:before {
  display: none !important;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .is-editor-empty:first-child:before {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  width: 100%;
  height: 0;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror p.is-editor-empty:first-child:before {
  float: left;
  height: 0;
  color: #adb5bd;
  pointer-events: none;
  width: 100%;
  content: attr(data-placeholder);
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .ProseMirror-gapcursor {
  position: relative;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  max-width: 42rem;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .ProseMirror-gapcursor:after {
  top: -1.5em;
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
  width: 100%;
  max-width: 42rem;
  border-top-color: #0006;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor
  .ProseMirror.ProseMirror.ProseMirror
  .ProseMirror-gapcursor:is([class~="dark"] *):after {
  border-top-color: #fff6;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .image-view {
  display: inline-block;
  float: none;
  max-width: 100%;
  line-height: 0;
  vertical-align: baseline;
  -webkit-user-select: none;
  user-select: none;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .image-view__body {
  position: relative;
  display: inline-block;
  max-width: 100%;
  clear: both;
  outline: transparent solid 2px;
  transition: all 0.2s ease-in;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .image-view__body__button-wrap {
  display: none;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 9px;
  right: 9px;
  background: #26262699;
  border-radius: 8px;
  z-index: 4;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .image-view__body:hover {
  outline-color: #ffc83d;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor
  .ProseMirror.ProseMirror.ProseMirror
  .image-view__body:hover
  .image-view__body__button-wrap {
  display: block;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .image-view__body--focused:hover,
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .image-view__body--resizing:hover {
  outline-color: hsl(var(--richtext-primary));
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .image-view__body__placeholder {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .image-view__body__image {
  margin: 0;
  cursor: pointer !important;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor
  .ProseMirror.ProseMirror.ProseMirror
  .image-view
  .image-view__body--focused {
  outline-color: hsl(var(--richtext-primary)) !important;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .image-view.focus img {
  outline-style: solid;
  outline-width: 2px;
  outline-color: hsl(var(--richtext-primary));
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .image-view img {
  display: inline;
  vertical-align: baseline;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .image-resizer {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  border-width: 1px !important;
  border-color: hsl(var(--richtext-border)) !important;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .image-resizer__handler {
  position: absolute;
  z-index: 2;
  box-sizing: border-box;
  display: block;
  width: 12px;
  height: 12px;
  border: 1px solid #fff;
  border-radius: 2px;
  background-color: hsl(var(--richtext-primary));
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .image-resizer__handler--tl {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .image-resizer__handler--tr {
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .image-resizer__handler--bl {
  bottom: -6px;
  left: -6px;
  cursor: sw-resize;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror.ProseMirror.ProseMirror .image-resizer__handler--br {
  right: -6px;
  bottom: -6px;
  cursor: se-resize;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor.dense .ProseMirror {
  min-height: 32px;
  padding: 6px 12px;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor.dense p {
  padding: 0;
  line-height: 1.2rem;
}
.reactjs-tiptap-editor.reactjs-tiptap-editor .ProseMirror[contenteditable="true"].resize-cursor {
  cursor: col-resize;
}
.slash-command-active.slash-command-active {
  background-color: hsl(var(--richtext-accent)) !important;
}
[data-dui-1-3-5~="dui-tooltip-wrapper"] {
  display: inline-block;
  position: relative;
}
.heading-0 {
  font-weight: 700;
  font-size: 16px;
}
.heading-1 {
  font-weight: 700;
  font-size: 28px;
  line-height: 1.6;
}
.heading-2 {
  font-weight: 700;
  font-size: 24px;
  line-height: 1.6;
}
.heading-3 {
  font-size: 20px;
  font-weight: 700;
  line-height: 1.6;
}
.heading-4 {
  font-size: 16px;
  font-weight: 700;
  line-height: 1.6;
}
.heading-5,
.heading-6 {
  font-size: 14px;
  font-weight: 700;
  line-height: 1.6;
}
.tableCellActive {
  border-radius: 2px !important;
  transition: all 0.1s ease-in-out;
}
.tableCellActive > div {
  border: unset !important;
}
.node-tableOfContents.focus {
  border-width: 1px;
  border-radius: 6px;
}
div[data-twitter] > div {
  margin: 10px auto;
}
.react-renderer.node-twitter.focus {
  border-width: 1px;
  border-radius: 6px;
}
.ProseMirror p {
  margin-bottom: 0.375rem;
  line-height: 1.625;
}
.ProseMirror p:first-child {
  margin-top: 0;
}
.ProseMirror p:last-child {
  margin-bottom: 0;
}
.ProseMirror > p {
  margin-bottom: 0.375rem;
}
.ProseMirror > p:first-child {
  margin-top: 0;
}
.ProseMirror > p:last-child {
  margin-bottom: 0;
}
.ProseMirror h1 {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.ProseMirror h2 {
  font-size: 1.5rem;
  line-height: 2rem;
}
.ProseMirror h3 {
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.ProseMirror h4 {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.ProseMirror h5 {
  font-size: 1rem;
  line-height: 1.5rem;
}
.ProseMirror h6 {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  font-weight: 700;
}
.ProseMirror h1:first-child,
.ProseMirror h2:first-child,
.ProseMirror h3:first-child,
.ProseMirror h4:first-child,
.ProseMirror h5:first-child,
.ProseMirror h6:first-child {
  margin-top: 0;
}
.ProseMirror h1:last-child,
.ProseMirror h2:last-child,
.ProseMirror h3:last-child,
.ProseMirror h4:last-child,
.ProseMirror h5:last-child,
.ProseMirror h6:last-child {
  margin-bottom: 0;
}
.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3 {
  margin-top: 3rem;
}
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  margin-top: 2rem;
}
.ProseMirror a.link {
  font-weight: 800;
  --richtext-text-opacity: 1;
  color: rgb(59 130 246 / var(--richtext-text-opacity, 1));
}
.ProseMirror a.link:is([class~="dark"] *) {
  --richtext-text-opacity: 1;
  color: rgb(96 165 250 / var(--richtext-text-opacity, 1));
}
.ProseMirror mark {
  border-radius: calc(var(--richtext-radius) - 4px);
  --richtext-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--richtext-bg-opacity, 1));
  -webkit-box-decoration-break: clone;
  box-decoration-break: clone;
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0;
  padding-right: 0;
  color: inherit;
}
.ProseMirror mark:is([class~="dark"] *) {
  --richtext-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--richtext-bg-opacity, 1));
}
.ProseMirror img {
  height: auto;
  max-width: 100%;
}
.ProseMirror *[data-indent="1"] {
  text-indent: 2em !important;
}
.ProseMirror *[data-indent="2"] {
  text-indent: 4em !important;
}
.ProseMirror *[data-indent="3"] {
  text-indent: 6em !important;
}
.ProseMirror *[data-indent="4"] {
  text-indent: 8em !important;
}
.ProseMirror *[data-indent="5"] {
  text-indent: 10em !important;
}
.ProseMirror *[data-indent="6"] {
  text-indent: 12em !important;
}
.ProseMirror *[data-indent="7"] {
  text-indent: 14em !important;
}
.ProseMirror iframe {
  border-width: 1px !important;
  margin-top: 0.5rem;
  height: 400px;
  width: 100%;
  border-radius: calc(var(--richtext-radius) - 4px);
  border-color: hsl(var(--richtext-border));
}
.ProseMirror [data-type="horizontalRule"] {
  margin-top: 2rem;
  margin-bottom: 2rem;
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.ProseMirror [data-type="horizontalRule"] hr {
  border-width: 0px;
  border-top-width: 1px;
  border-color: #0003;
  background-color: #000c;
}
.ProseMirror [data-type="horizontalRule"] hr:is([class~="dark"] *) {
  border-color: #fff3;
  background-color: #fffc;
}
.ProseMirror .blockquote {
  border-radius: var(--richtext-radius);
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left-width: 4px;
  --richtext-border-opacity: 1;
  border-left-color: rgb(64 64 64 / var(--richtext-border-opacity, 1));
  --richtext-bg-opacity: 0.8;
  padding: 0.5rem 1rem;
  --richtext-text-opacity: 1;
  color: rgb(0 0 0 / var(--richtext-text-opacity, 1));
}
.ProseMirror .blockquote:is([class~="dark"] *) {
  --richtext-border-opacity: 1;
  border-left-color: rgb(212 212 212 / var(--richtext-border-opacity, 1));
  --richtext-text-opacity: 1;
  color: rgb(255 255 255 / var(--richtext-text-opacity, 1));
}
.ProseMirror code {
  border-radius: calc(var(--richtext-radius) - 4px);
  --richtext-bg-opacity: 1;
  background-color: rgb(23 23 23 / var(--richtext-bg-opacity, 1));
  font-family:
    ui-monospace,
    SFMono-Regular,
    Menlo,
    Monaco,
    Consolas,
    Liberation Mono,
    Courier New,
    monospace;
  --richtext-text-opacity: 1;
  color: rgb(255 255 255 / var(--richtext-text-opacity, 1));
  caret-color: #fff;
  --richtext-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --richtext-shadow-colored: 0 10px 15px -3px var(--richtext-shadow-color), 0 4px 6px -4px var(--richtext-shadow-color);
  box-shadow: var(--richtext-ring-offset-shadow, 0 0 #0000), var(--richtext-ring-shadow, 0 0 #0000),
    var(--richtext-shadow);
}
.ProseMirror code::selection {
  background-color: #ffffff4d;
}
.ProseMirror pre {
  margin-top: 3rem;
  margin-bottom: 3rem;
  border-radius: 0.25rem;
  border-width: 1px !important;
  --richtext-border-opacity: 1;
  border-color: rgb(0 0 0 / var(--richtext-border-opacity, 1));
  --richtext-bg-opacity: 1;
  background-color: rgb(64 64 64 / var(--richtext-bg-opacity, 1));
  padding: 1rem;
  --richtext-text-opacity: 1;
  color: rgb(255 255 255 / var(--richtext-text-opacity, 1));
  caret-color: #fff;
}
.ProseMirror pre:is([class~="dark"] *) {
  --richtext-border-opacity: 1;
  border-color: rgb(38 38 38 / var(--richtext-border-opacity, 1));
  --richtext-bg-opacity: 1;
  background-color: rgb(23 23 23 / var(--richtext-bg-opacity, 1));
}
.ProseMirror pre *::selection {
  background-color: #fff3;
}
.ProseMirror pre code {
  background-color: inherit;
  padding: 0;
  color: inherit;
  --richtext-shadow: 0 0 #0000;
  --richtext-shadow-colored: 0 0 #0000;
  box-shadow: var(--richtext-ring-offset-shadow, 0 0 #0000), var(--richtext-ring-shadow, 0 0 #0000),
    var(--richtext-shadow);
}
.ProseMirror ol {
  list-style-type: decimal;
}
.ProseMirror ul {
  list-style-type: disc;
}
.ProseMirror ul,
.ProseMirror ol {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
  padding-top: 0;
  padding-bottom: 0;
  padding-left: 2rem;
  padding-right: 2rem;
}
.ProseMirror ul:first-child,
.ProseMirror ol:first-child {
  margin-top: 0;
}
.ProseMirror ul:last-child,
.ProseMirror ol:last-child {
  margin-bottom: 0;
}
.ProseMirror ul ul,
.ProseMirror ul ol,
.ProseMirror ul li,
.ProseMirror ol ul,
.ProseMirror ol ol,
.ProseMirror ol li {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.ProseMirror ul p,
.ProseMirror ol p {
  margin-top: 0;
  margin-bottom: 0.25rem;
}
.ProseMirror > ul,
.ProseMirror > ol {
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.ProseMirror > ul:first-child,
.ProseMirror > ol:first-child {
  margin-top: 0;
}
.ProseMirror > ul:last-child,
.ProseMirror > ol:last-child {
  margin-bottom: 0;
}
.ProseMirror ul[data-type="taskList"] {
  list-style-type: none;
  padding: 0;
}
.ProseMirror ul[data-type="taskList"] p {
  margin: 0;
}
.ProseMirror ul[data-type="taskList"] li {
  display: flex;
}
.ProseMirror ul[data-type="taskList"] li > label {
  margin-right: 0.5rem;
  display: inline;
  flex: 1 1 auto;
  flex-shrink: 0;
  flex-grow: 0;
  -webkit-user-select: none;
  user-select: none;
}
.ProseMirror ul[data-type="taskList"] li > div {
  flex: 1 1 auto;
}
.ProseMirror ul[data-type="taskList"] li[data-checked="true"] {
  text-decoration-line: line-through;
}
.ProseMirror table {
  border: 1px solid;
  box-sizing: border-box;
  display: block;
  table-layout: fixed;
  border-collapse: collapse;
  overflow-x: auto;
  overflow-y: hidden;
  border-color: #0000001a;
}
.ProseMirror table:is([class~="dark"] *) {
  border-color: #fff3;
}
.ProseMirror table td,
.ProseMirror table th {
  border: 1px solid;
  border-width: 1px !important;
  position: relative;
  box-sizing: border-box;
  min-width: 1em;
  border-color: #0000001a;
  padding: 0.5rem;
  text-align: left;
  vertical-align: top;
}
.ProseMirror table td:is([class~="dark"] *),
.ProseMirror table th:is([class~="dark"] *) {
  border-color: #fff3;
}
.ProseMirror table td:first-of-type:not(a),
.ProseMirror table th:first-of-type:not(a) {
  margin-top: 0;
}
.ProseMirror table td p,
.ProseMirror table th p {
  margin: 0;
}
.ProseMirror table td p + p,
.ProseMirror table th p + p {
  margin-top: 0.75rem;
}
.ProseMirror table th {
  text-align: left;
  font-weight: 700;
}
.ProseMirror .search-result {
  background: #c4eed0;
}
.ProseMirror .search-result-current {
  background: #6cd58b;
}
.columns {
  display: flex;
  width: 100%;
  gap: 8px;
  margin-top: 0.75em;
}
.columns .column {
  min-width: 0;
  padding: 12px;
  border-width: 1px;
  border-style: solid;
  border-color: hsl(var(--richtext-border));
  border-radius: 2px;
  flex: 1 1 0%;
  box-sizing: border-box;
}
.columns .column p:first-of-type {
  margin-top: 0;
}
.mention {
  padding: 2px 6px;
  color: #fff;
  background-color: #666e76;
  border-radius: 6px;
}
.file\:richtext-border-0::file-selector-button {
  border-width: 0px;
}
.file\:richtext-bg-transparent::file-selector-button {
  background-color: transparent;
}
.file\:richtext-text-sm::file-selector-button {
  font-size: 0.875rem;
  line-height: 1.25rem;
}
.file\:richtext-font-medium::file-selector-button {
  font-weight: 500;
}
.placeholder\:richtext-text-muted-foreground::placeholder {
  color: hsl(var(--richtext-muted-foreground));
}
.first\:richtext-mt-0\.5:first-child {
  margin-top: 0.125rem;
}
.last\:richtext-pb-2:last-child {
  padding-bottom: 0.5rem;
}
.hover\:richtext-cursor-pointer:hover {
  cursor: pointer;
}
.hover\:richtext-border-border:hover {
  border-color: hsl(var(--richtext-border));
}
.hover\:\!richtext-bg-accent:hover {
  background-color: hsl(var(--richtext-accent)) !important;
}
.hover\:\!richtext-bg-primary\/90:hover {
  background-color: hsl(var(--richtext-primary) / 0.9) !important;
}
.hover\:richtext-bg-accent:hover {
  background-color: hsl(var(--richtext-accent));
}
.hover\:richtext-bg-destructive\/90:hover {
  background-color: hsl(var(--richtext-destructive) / 0.9);
}
.hover\:richtext-bg-muted:hover {
  background-color: hsl(var(--richtext-muted));
}
.hover\:richtext-bg-red-400:hover {
  --richtext-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--richtext-bg-opacity, 1));
}
.hover\:richtext-bg-secondary:hover {
  background-color: hsl(var(--richtext-secondary));
}
.hover\:richtext-bg-secondary\/80:hover {
  background-color: hsl(var(--richtext-secondary) / 0.8);
}
.hover\:richtext-bg-opacity-20:hover {
  --richtext-bg-opacity: 0.2;
}
.hover\:richtext-text-accent-foreground:hover {
  color: hsl(var(--richtext-accent-foreground));
}
.hover\:richtext-text-foreground:hover {
  color: hsl(var(--richtext-foreground));
}
.hover\:richtext-text-muted-foreground:hover {
  color: hsl(var(--richtext-muted-foreground));
}
.hover\:richtext-underline:hover {
  text-decoration-line: underline;
}
.hover\:richtext-opacity-100:hover {
  opacity: 1;
}
.hover\:richtext-shadow-sm:hover {
  --richtext-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --richtext-shadow-colored: 0 1px 2px 0 var(--richtext-shadow-color);
  box-shadow: var(--richtext-ring-offset-shadow, 0 0 #0000), var(--richtext-ring-shadow, 0 0 #0000),
    var(--richtext-shadow);
}
.focus\:richtext-bg-accent:focus {
  background-color: hsl(var(--richtext-accent));
}
.focus\:richtext-bg-red-400:focus {
  --richtext-bg-opacity: 1;
  background-color: rgb(248 113 113 / var(--richtext-bg-opacity, 1));
}
.focus\:richtext-bg-opacity-30:focus {
  --richtext-bg-opacity: 0.3;
}
.focus\:richtext-text-accent-foreground:focus {
  color: hsl(var(--richtext-accent-foreground));
}
.focus\:richtext-text-red-500:focus {
  --richtext-text-opacity: 1;
  color: rgb(239 68 68 / var(--richtext-text-opacity, 1));
}
.focus\:richtext-opacity-100:focus {
  opacity: 1;
}
.focus\:richtext-outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:richtext-ring-2:focus {
  --richtext-ring-offset-shadow: var(--richtext-ring-inset) 0 0 0 var(--richtext-ring-offset-width)
    var(--richtext-ring-offset-color);
  --richtext-ring-shadow: var(--richtext-ring-inset) 0 0 0 calc(2px + var(--richtext-ring-offset-width))
    var(--richtext-ring-color);
  box-shadow: var(--richtext-ring-offset-shadow), var(--richtext-ring-shadow), var(--richtext-shadow, 0 0 #0000);
}
.focus\:richtext-ring-ring:focus {
  --richtext-ring-color: hsl(var(--richtext-ring));
}
.focus\:richtext-ring-offset-2:focus {
  --richtext-ring-offset-width: 2px;
}
.focus-visible\:\!richtext-outline-none:focus-visible {
  outline: 2px solid transparent !important;
  outline-offset: 2px !important;
}
.focus-visible\:richtext-outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus-visible\:\!richtext-ring-2:focus-visible {
  --richtext-ring-offset-shadow: var(--richtext-ring-inset) 0 0 0 var(--richtext-ring-offset-width)
    var(--richtext-ring-offset-color) !important;
  --richtext-ring-shadow: var(--richtext-ring-inset) 0 0 0 calc(2px + var(--richtext-ring-offset-width))
    var(--richtext-ring-color) !important;
  box-shadow: var(--richtext-ring-offset-shadow), var(--richtext-ring-shadow), var(--richtext-shadow, 0 0 #0000) !important;
}
.focus-visible\:richtext-ring-2:focus-visible {
  --richtext-ring-offset-shadow: var(--richtext-ring-inset) 0 0 0 var(--richtext-ring-offset-width)
    var(--richtext-ring-offset-color);
  --richtext-ring-shadow: var(--richtext-ring-inset) 0 0 0 calc(2px + var(--richtext-ring-offset-width))
    var(--richtext-ring-color);
  box-shadow: var(--richtext-ring-offset-shadow), var(--richtext-ring-shadow), var(--richtext-shadow, 0 0 #0000);
}
.focus-visible\:\!richtext-ring-ring:focus-visible {
  --richtext-ring-color: hsl(var(--richtext-ring)) !important;
}
.focus-visible\:richtext-ring-ring:focus-visible {
  --richtext-ring-color: hsl(var(--richtext-ring));
}
.focus-visible\:\!richtext-ring-offset-2:focus-visible {
  --richtext-ring-offset-width: 2px !important;
}
.focus-visible\:richtext-ring-offset-2:focus-visible {
  --richtext-ring-offset-width: 2px;
}
.focus-visible\:richtext-ring-offset-background:focus-visible {
  --richtext-ring-offset-color: hsl(var(--richtext-background));
}
.disabled\:richtext-pointer-events-none:disabled {
  pointer-events: none;
}
.disabled\:\!richtext-cursor-not-allowed:disabled {
  cursor: not-allowed !important;
}
.disabled\:richtext-cursor-not-allowed:disabled {
  cursor: not-allowed;
}
.disabled\:\!richtext-opacity-50:disabled {
  opacity: 0.5 !important;
}
.disabled\:richtext-opacity-50:disabled {
  opacity: 0.5;
}
.richtext-group:hover .group-hover\:richtext-opacity-100 {
  opacity: 1;
}
.richtext-group.destructive .group-\[\.destructive\]\:richtext-border-muted\/40 {
  border-color: hsl(var(--richtext-muted) / 0.4);
}
.richtext-group.destructive .group-\[\.destructive\]\:richtext-text-red-300 {
  --richtext-text-opacity: 1;
  color: rgb(252 165 165 / var(--richtext-text-opacity, 1));
}
.richtext-group.destructive .group-\[\.destructive\]\:hover\:richtext-border-destructive\/30:hover {
  border-color: hsl(var(--richtext-destructive) / 0.3);
}
.richtext-group.destructive .group-\[\.destructive\]\:hover\:richtext-bg-destructive:hover {
  background-color: hsl(var(--richtext-destructive));
}
.richtext-group.destructive .group-\[\.destructive\]\:hover\:richtext-text-destructive-foreground:hover {
  color: hsl(var(--richtext-destructive-foreground));
}
.richtext-group.destructive .group-\[\.destructive\]\:hover\:richtext-text-red-50:hover {
  --richtext-text-opacity: 1;
  color: rgb(254 242 242 / var(--richtext-text-opacity, 1));
}
.richtext-group.destructive .group-\[\.destructive\]\:focus\:richtext-ring-destructive:focus {
  --richtext-ring-color: hsl(var(--richtext-destructive));
}
.richtext-group.destructive .group-\[\.destructive\]\:focus\:richtext-ring-red-400:focus {
  --richtext-ring-opacity: 1;
  --richtext-ring-color: rgb(248 113 113 / var(--richtext-ring-opacity, 1));
}
.richtext-group.destructive .group-\[\.destructive\]\:focus\:richtext-ring-offset-red-600:focus {
  --richtext-ring-offset-color: #dc2626;
}
.richtext-peer:disabled ~ .peer-disabled\:richtext-cursor-not-allowed {
  cursor: not-allowed;
}
.richtext-peer:disabled ~ .peer-disabled\:richtext-opacity-70 {
  opacity: 0.7;
}
.data-\[disabled\]\:richtext-pointer-events-none[data-disabled] {
  pointer-events: none;
}
.data-\[side\=bottom\]\:richtext-translate-y-1[data-side="bottom"] {
  --richtext-translate-y: 0.25rem;
  transform: translate(var(--richtext-translate-x), var(--richtext-translate-y)) rotate(var(--richtext-rotate))
    skew(var(--richtext-skew-x)) skewY(var(--richtext-skew-y)) scaleX(var(--richtext-scale-x))
    scaleY(var(--richtext-scale-y));
}
.data-\[side\=left\]\:richtext--translate-x-1[data-side="left"] {
  --richtext-translate-x: -0.25rem;
  transform: translate(var(--richtext-translate-x), var(--richtext-translate-y)) rotate(var(--richtext-rotate))
    skew(var(--richtext-skew-x)) skewY(var(--richtext-skew-y)) scaleX(var(--richtext-scale-x))
    scaleY(var(--richtext-scale-y));
}
.data-\[side\=right\]\:richtext-translate-x-1[data-side="right"] {
  --richtext-translate-x: 0.25rem;
  transform: translate(var(--richtext-translate-x), var(--richtext-translate-y)) rotate(var(--richtext-rotate))
    skew(var(--richtext-skew-x)) skewY(var(--richtext-skew-y)) scaleX(var(--richtext-scale-x))
    scaleY(var(--richtext-scale-y));
}
.data-\[side\=top\]\:richtext--translate-y-1[data-side="top"] {
  --richtext-translate-y: -0.25rem;
  transform: translate(var(--richtext-translate-x), var(--richtext-translate-y)) rotate(var(--richtext-rotate))
    skew(var(--richtext-skew-x)) skewY(var(--richtext-skew-y)) scaleX(var(--richtext-scale-x))
    scaleY(var(--richtext-scale-y));
}
.data-\[state\=checked\]\:richtext-translate-x-5[data-state="checked"] {
  --richtext-translate-x: 1.25rem;
  transform: translate(var(--richtext-translate-x), var(--richtext-translate-y)) rotate(var(--richtext-rotate))
    skew(var(--richtext-skew-x)) skewY(var(--richtext-skew-y)) scaleX(var(--richtext-scale-x))
    scaleY(var(--richtext-scale-y));
}
.data-\[state\=unchecked\]\:richtext-translate-x-0[data-state="unchecked"],
.data-\[swipe\=cancel\]\:richtext-translate-x-0[data-swipe="cancel"] {
  --richtext-translate-x: 0px;
  transform: translate(var(--richtext-translate-x), var(--richtext-translate-y)) rotate(var(--richtext-rotate))
    skew(var(--richtext-skew-x)) skewY(var(--richtext-skew-y)) scaleX(var(--richtext-scale-x))
    scaleY(var(--richtext-scale-y));
}
.data-\[swipe\=end\]\:richtext-translate-x-\[var\(--radix-toast-swipe-end-x\)\][data-swipe="end"] {
  --richtext-translate-x: var(--radix-toast-swipe-end-x);
  transform: translate(var(--richtext-translate-x), var(--richtext-translate-y)) rotate(var(--richtext-rotate))
    skew(var(--richtext-skew-x)) skewY(var(--richtext-skew-y)) scaleX(var(--richtext-scale-x))
    scaleY(var(--richtext-scale-y));
}
.data-\[swipe\=move\]\:richtext-translate-x-\[var\(--radix-toast-swipe-move-x\)\][data-swipe="move"] {
  --richtext-translate-x: var(--radix-toast-swipe-move-x);
  transform: translate(var(--richtext-translate-x), var(--richtext-translate-y)) rotate(var(--richtext-rotate))
    skew(var(--richtext-skew-x)) skewY(var(--richtext-skew-y)) scaleX(var(--richtext-scale-x))
    scaleY(var(--richtext-scale-y));
}
.data-\[state\=active\]\:richtext-bg-background[data-state="active"] {
  background-color: hsl(var(--richtext-background));
}
.data-\[state\=checked\]\:\!richtext-bg-primary[data-state="checked"] {
  background-color: hsl(var(--richtext-primary)) !important;
}
.data-\[state\=checked\]\:richtext-bg-primary[data-state="checked"] {
  background-color: hsl(var(--richtext-primary));
}
.data-\[state\=on\]\:richtext-bg-accent[data-state="on"],
.data-\[state\=open\]\:richtext-bg-accent[data-state="open"] {
  background-color: hsl(var(--richtext-accent));
}
.data-\[state\=unchecked\]\:richtext-bg-input[data-state="unchecked"] {
  background-color: hsl(var(--richtext-input));
}
.data-\[state\=active\]\:richtext-text-foreground[data-state="active"] {
  color: hsl(var(--richtext-foreground));
}
.data-\[state\=checked\]\:\!richtext-text-primary-foreground[data-state="checked"] {
  color: hsl(var(--richtext-primary-foreground)) !important;
}
.data-\[state\=on\]\:richtext-text-accent-foreground[data-state="on"] {
  color: hsl(var(--richtext-accent-foreground));
}
.data-\[state\=open\]\:richtext-text-muted-foreground[data-state="open"] {
  color: hsl(var(--richtext-muted-foreground));
}
.data-\[disabled\]\:richtext-opacity-50[data-disabled] {
  opacity: 0.5;
}
.data-\[state\=active\]\:richtext-shadow-sm[data-state="active"] {
  --richtext-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --richtext-shadow-colored: 0 1px 2px 0 var(--richtext-shadow-color);
  box-shadow: var(--richtext-ring-offset-shadow, 0 0 #0000), var(--richtext-ring-shadow, 0 0 #0000),
    var(--richtext-shadow);
}
.data-\[swipe\=move\]\:richtext-transition-none[data-swipe="move"] {
  transition-property: none;
}
.data-\[state\=open\]\:richtext-animate-in[data-state="open"] {
  animation-name: enter;
  animation-duration: 0.15s;
  --richtext-enter-opacity: initial;
  --richtext-enter-scale: initial;
  --richtext-enter-rotate: initial;
  --richtext-enter-translate-x: initial;
  --richtext-enter-translate-y: initial;
}
.data-\[state\=closed\]\:richtext-animate-out[data-state="closed"],
.data-\[swipe\=end\]\:richtext-animate-out[data-swipe="end"] {
  animation-name: exit;
  animation-duration: 0.15s;
  --richtext-exit-opacity: initial;
  --richtext-exit-scale: initial;
  --richtext-exit-rotate: initial;
  --richtext-exit-translate-x: initial;
  --richtext-exit-translate-y: initial;
}
.data-\[state\=closed\]\:richtext-fade-out-0[data-state="closed"] {
  --richtext-exit-opacity: 0;
}
.data-\[state\=closed\]\:richtext-fade-out-80[data-state="closed"] {
  --richtext-exit-opacity: 0.8;
}
.data-\[state\=open\]\:richtext-fade-in-0[data-state="open"] {
  --richtext-enter-opacity: 0;
}
.data-\[state\=closed\]\:richtext-zoom-out-95[data-state="closed"] {
  --richtext-exit-scale: 0.95;
}
.data-\[state\=open\]\:richtext-zoom-in-95[data-state="open"] {
  --richtext-enter-scale: 0.95;
}
.data-\[side\=bottom\]\:richtext-slide-in-from-top-2[data-side="bottom"] {
  --richtext-enter-translate-y: -0.5rem;
}
.data-\[side\=left\]\:richtext-slide-in-from-right-2[data-side="left"] {
  --richtext-enter-translate-x: 0.5rem;
}
.data-\[side\=right\]\:richtext-slide-in-from-left-2[data-side="right"] {
  --richtext-enter-translate-x: -0.5rem;
}
.data-\[side\=top\]\:richtext-slide-in-from-bottom-2[data-side="top"] {
  --richtext-enter-translate-y: 0.5rem;
}
.data-\[state\=closed\]\:richtext-slide-out-to-left-1\/2[data-state="closed"] {
  --richtext-exit-translate-x: -50%;
}
.data-\[state\=closed\]\:richtext-slide-out-to-right-full[data-state="closed"] {
  --richtext-exit-translate-x: 100%;
}
.data-\[state\=closed\]\:richtext-slide-out-to-top-\[48\%\][data-state="closed"] {
  --richtext-exit-translate-y: -48%;
}
.data-\[state\=open\]\:richtext-slide-in-from-left-1\/2[data-state="open"] {
  --richtext-enter-translate-x: -50%;
}
.data-\[state\=open\]\:richtext-slide-in-from-top-\[48\%\][data-state="open"] {
  --richtext-enter-translate-y: -48%;
}
.data-\[state\=open\]\:richtext-slide-in-from-top-full[data-state="open"] {
  --richtext-enter-translate-y: -100%;
}
.dark\:\!richtext-border-neutral-800:is([class~="dark"] *) {
  --richtext-border-opacity: 1 !important;
  border-color: rgb(38 38 38 / var(--richtext-border-opacity, 1)) !important;
}
.dark\:richtext-border-neutral-800:is([class~="dark"] *) {
  --richtext-border-opacity: 1;
  border-color: rgb(38 38 38 / var(--richtext-border-opacity, 1));
}
.dark\:\!richtext-bg-black:is([class~="dark"] *) {
  --richtext-bg-opacity: 1 !important;
  background-color: rgb(0 0 0 / var(--richtext-bg-opacity, 1)) !important;
}
.dark\:richtext-bg-black:is([class~="dark"] *) {
  --richtext-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--richtext-bg-opacity, 1));
}
.dark\:\!richtext-text-neutral-200:is([class~="dark"] *) {
  --richtext-text-opacity: 1 !important;
  color: rgb(229 229 229 / var(--richtext-text-opacity, 1)) !important;
}
.dark\:richtext-text-gray-100:is([class~="dark"] *) {
  --richtext-text-opacity: 1;
  color: rgb(243 244 246 / var(--richtext-text-opacity, 1));
}
.dark\:richtext-text-neutral-200:is([class~="dark"] *) {
  --richtext-text-opacity: 1;
  color: rgb(229 229 229 / var(--richtext-text-opacity, 1));
}
.dark\:hover\:richtext-bg-opacity-20:hover:is([class~="dark"] *) {
  --richtext-bg-opacity: 0.2;
}
.dark\:hover\:richtext-text-red-500:hover:is([class~="dark"] *) {
  --richtext-text-opacity: 1;
  color: rgb(239 68 68 / var(--richtext-text-opacity, 1));
}
@media (min-width: 640px) {
  .sm\:richtext-bottom-0 {
    bottom: 0;
  }
  .sm\:richtext-right-0 {
    right: 0;
  }
  .sm\:richtext-top-auto {
    top: auto;
  }
  .sm\:richtext-flex-row {
    flex-direction: row;
  }
  .sm\:richtext-flex-col {
    flex-direction: column;
  }
  .sm\:richtext-justify-end {
    justify-content: flex-end;
  }
  .sm\:richtext-space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --richtext-space-x-reverse: 0;
    margin-right: calc(0.5rem * var(--richtext-space-x-reverse));
    margin-left: calc(0.5rem * calc(1 - var(--richtext-space-x-reverse)));
  }
  .sm\:richtext-rounded-lg {
    border-radius: var(--richtext-radius);
  }
  .sm\:richtext-text-left {
    text-align: left;
  }
  .data-\[state\=open\]\:sm\:richtext-slide-in-from-bottom-full[data-state="open"] {
    --richtext-enter-translate-y: 100%;
  }
}
@media (min-width: 768px) {
  .md\:richtext-max-w-\[420px\] {
    max-width: 420px;
  }
}
.\[\&\>span\]\:richtext-line-clamp-1 > span {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
._wrap_134f4_1 {
  position: relative;
}
._wrap_134f4_1 code {
  margin: 0 !important;
}
._wrap_134f4_1 pre {
  margin: 10px 0 !important;
}
._wrap_134f4_1._maxHeight_134f4_10 code {
  max-height: 370px;
}
._wrap_134f4_1:hover ._btnCopy_134f4_13,
._wrap_134f4_1:hover ._btnDelete_134f4_14 {
  opacity: 1;
}
._blockInfo_134f4_18 {
  display: flex;
  align-items: center;
  position: absolute;
  z-index: 2;
  top: 5px;
  right: 5px;
  gap: 8px;
}
._blockInfoEditable_134f4_28 {
  display: none;
}
._selectLang_134f4_32 {
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 4px;
  border-width: 0px !important;
}
._selectLang_134f4_32 > button {
  padding: 0 !important;
  height: initial !important;
  outline: none !important;
  border: none !important;
  outline-offset: 0px !important;
  color: #fff !important;
}
._selectLang_134f4_32 ._richtext-SelectContent_134f4_46 {
  max-height: 200px;
  overflow-y: auto;
}
._btnCopy_134f4_13 {
  color: #ebebebbf;
  cursor: pointer;
  transition: all 0.3s;
  opacity: 0;
}
._copied_134f4_58 {
  color: #7dcf02;
}
._btnDelete_134f4_14 {
  color: #ebebebbf;
  cursor: pointer;
  transition: all 0.3s;
  opacity: 0;
}
._btnDelete_134f4_14:hover {
  color: #ff4d4f;
}
._toc_aag8a_1 {
  width: max-content;
  max-width: 100%;
  border-radius: 0.5rem;
  opacity: 0.75;
}
._toc_aag8a_1._visible_aag8a_7 {
  padding: 0.75rem;
  margin: 0.75em 0;
}
._toc_aag8a_1 ._list_aag8a_11 {
  padding: 0;
  margin: 0 0 12px;
  list-style: none;
}
._toc_aag8a_1 ._item_aag8a_16 a:hover {
  opacity: 0.5;
}
._toc_aag8a_1 ._item--3_aag8a_19 {
  padding-left: 1rem;
}
._toc_aag8a_1 ._item--4_aag8a_22 {
  padding-left: 2rem;
}
._toc_aag8a_1 ._item--5_aag8a_25 {
  padding-left: 3rem;
}
._toc_aag8a_1 ._item--6_aag8a_28 {
  padding-left: 4rem;
}
._listUsers_en3pm_1 {
  width: 160px;
  max-height: 320px;
  overflow-x: hidden;
  overflow-y: auto;
  border-radius: 4px;
  box-shadow:
    #091e424f 0 0 1px,
    #091e4240 0 4px 8px -2px;
}
._itemUser_en3pm_10 {
  display: flex;
  width: 100%;
  padding: 12px 12px 11px;
  color: #091e42;
  text-decoration: none;
  cursor: pointer;
  background-color: #fff;
  border: 0;
  border-radius: 0;
  flex: 0 0 auto;
  align-items: center;
}
._itemUser_en3pm_10:hover {
  background-color: #f4f5f7;
}
._itemUser_en3pm_10 img {
  width: 1em;
  height: 1em;
}
._selectedUser_en3pm_31 {
  color: #0052cc;
  text-decoration: none;
  background-color: #deebff;
  fill: #deebff;
}
._attachment_1x1ms_1,
._wrap_1x1ms_2 {
  border-width: 1px !important;
  border-radius: 4px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 10px 0;
}
._attachment_1x1ms_1 .attachment__icon,
._wrap_1x1ms_2 .attachment__icon {
  width: 32px;
  text-align: center;
}
._attachment_1x1ms_1 .attachment__icon svg,
._wrap_1x1ms_2 .attachment__icon svg {
  width: 32px;
  display: inline-block;
}
}