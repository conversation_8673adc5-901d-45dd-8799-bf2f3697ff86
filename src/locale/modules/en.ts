import locale from 'element-plus/dist/locale/en.mjs'

const lang = {
  el: locale.el, // element-plus i18 setting
  message: {
    language: 'English',
    common: {
      search: 'search',
      searchTip: 'please input keyword',
      add: 'add',
      update: 'update',
      del: 'delete',
      delBat: 'delete choose',
      delTip: 'Are you sure delete the selection data ?',
      handle: 'handle',
      exportExcel:'export excel',
      exportExcelTip:'please input file name'
    },
    menu: {
      dashboard: {
        name: 'dashboard',
        index: 'index'
      },
      system: {
        name: 'system',
        redirect: 'redirect',
        '404': '404',
        '401': '401'
      },
      component: {
        name: 'component',
        button: 'button',
        wordEditor: 'wordEditor',
        mdEditor: 'mdEditor',
        codeEditor: 'codeEditor',
        jsonEditor: 'jsonEditor',
        dragPane: 'dragPane',
        map: 'map',
        cutPhoto: 'cutPhoto',
        rightMenu: 'rightMenu',
        exportExcel:'exportExcel'
      },
      page: {
        name: 'page',
        crudTable: 'crudTable',
        categoryTable: 'categoryTable',
        treeTable: 'treeTable',
        card: 'card',
        work: 'work',
        baidu: 'iframe case',
      },
      menu: {
        name: 'menu',
        menu_1: 'menu-1',
        menu_1_1: 'menu-1-1',
        menu_1_1_1: 'menu-1-1-1',
        menu_1_1_2: 'menu-1-1-2',
        menu_1_2: 'menu-1-2',
        menu_2: 'menu-2',
        menu_3: 'menu-3'
      },
      directive: {
        name: 'directive',
        dragable: 'v-dragable',
        copy: 'v-copy',
        waterMarker: 'v-waterMarker',
        longpress: 'v-longpress',
        debounce: 'v-debounce',
        scroll: 'v-infinite-scroll',
        clickOutside: 'v-click-outside',
      },
      echarts: {
        name: 'echarts',
        bar: 'bar',
        line: 'line chart',
        pie: 'pie chart',
        radar: 'radar chart',
        map: 'map',
      },
      systemManage: {
        name: 'systemManage',
        menu: 'menu',
        role: 'role',
        user: 'user'
      },
      print: {
        name: 'print',
        jsPrint: 'print in JS'
      },
      community: {
        name: 'community',
        qq: 'qqGroup',
        site: 'vue3 resource'
      },
      document: {
        name: 'document',
        intro: 'intro',
        function: 'function',
        menu: 'diy route menus',
        keepAlive: 'how to use keepAlive',
        crud: 'crud table and form',
        theme: 'how to diy your theme',
        systemfont: 'icon in project',
        api: 'api document'
      },
      tab: {
        name: 'tab',
      },
    },
    system: {
      title: 'backendsystem',
      subTitle: 'few lines to write beautiful admin',
      welcome: 'welcome login',
      login: 'login',
      userName: 'userName',
      password: 'password',
      contentScreen: 'content full screen',
      fullScreen: 'fullscreen',
      fullScreenBack: 'back fullscreen',
      github: 'visit github',
      changePassword: 'change password',
      loginOut: 'login out',
      user: 'admin',
      size: {
        default: 'default',
        large: 'large',
        small: 'small',
      },
      setting: {
        name: 'setting',
        style: {
          name: 'full style setting',
          default: 'default menu style',
          light: 'light menu style',
          chinese: 'chinese menu style',
          dark: 'dark menu style'
        },
        primaryColor: {
          name: 'primary color',
          blue: 'default blue',
          red: 'rose red',
          violet: 'grace violet',
          green: 'story green',
          cyan: 'cyan',
          black: 'geek black'
        },
        other: {
          name: 'other setting',
          showLogo: 'show logo',
          showBreadcrumb: 'show breadcrumb',
          keepOnlyOneMenu: 'keep only one menu open',
        }
      },
      tab: {
        reload: 'refresh',
        closeAll: 'close all tags',
        closeOther: 'close other tags',
        closeCurrent: 'close current tag'
      }
    },
  }
}

export default lang