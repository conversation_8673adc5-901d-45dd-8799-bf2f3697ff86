import locale from 'element-plus/dist/locale/ja.mjs'

const lang = {
  el: locale.el, // element-plus i18 setting
  message: {
    language: '日本語',
    common: {
      search: '検索',
      searchTip: 'キーワードを入力してください',
      add: '追加',
      update: '更新',
      del: '削除',
      delBat: '選択した項目を削除',
      delTip: '選択したデータを削除してもよろしいですか？',
      handle: '操作',
      exportExcel: 'Excelにエクスポート',
      exportExcelTip: 'ファイル名を入力してください'
    },
    menu: {
      dashboard: {
        name: 'ダッシュボード',
        index: 'インデックス'
      },
      system: {
        name: 'システム',
        redirect: 'リダイレクト',
        '404': '404エラー',
        '401': '401エラー'
      },
      component: {
        name: 'コンポーネント',
        button: 'ボタン',
        wordEditor: 'テキストエディター',
        mdEditor: 'Markdownエディター',
        codeEditor: 'コードエディター',
        jsonEditor: 'JSONエディター',
        dragPane: 'ドラッグペイン',
        map: '地図',
        cutPhoto: '写真の切り抜き',
        rightMenu: '右クリックメニュー',
        exportExcel: 'Excelにエクスポート'
      },
      page: {
        name: 'ページ',
        crudTable: 'CRUDテーブル',
        categoryTable: 'カテゴリーテーブル',
        treeTable: 'ツリーテーブル',
        card: 'カード',
        work: '作業',
        baidu: 'iframeケース',
      },
      menu: {
        name: 'メニュー',
        menu_1: 'メニュー-1',
        menu_1_1: 'メニュー-1-1',
        menu_1_1_1: 'メニュー-1-1-1',
        menu_1_1_2: 'メニュー-1-1-2',
        menu_1_2: 'メニュー-1-2',
        menu_2: 'メニュー-2',
        menu_3: 'メニュー-3'
      },
      directive: {
        name: 'ディレクティブ',
        dragable: 'v-dragable',
        copy: 'v-copy',
        waterMarker: 'v-waterMarker',
        longpress: 'v-longpress',
        debounce: 'v-debounce',
        scroll: 'v-infinite-scroll',
        clickOutside: 'v-click-outside',
      },
      echarts: {
        name: 'ECharts',
        bar: '棒グラフ',
        line: '折れ線グラフ',
        pie: 'パイチャート',
        radar: 'レーダーチャート',
        map: '地図',
      },
      systemManage: {
        name: 'システム管理',
        menu: 'メニュー',
        role: 'ロール',
        user: 'ユーザー'
      },
      print: {
        name: '印刷',
        jsPrint: 'JSで印刷'
      },
      community: {
        name: 'コミュニティ',
        qq: 'QQグループ',
        site: 'Vue3リソース'
      },
      document: {
        name: 'ドキュメント',
        intro: '導入',
        function: '機能',
        menu: '自分で作成したルートメニュー',
        keepAlive: 'keepAliveの使い方',
        crud: 'CRUDテーブルとフォーム',
        theme: 'テーマのカスタマイズ方法',
        systemfont: 'プロジェクト内のアイコン',
        api: 'APIドキュメント'
      },
      tab: {
        name: 'タブ',
      },
    },
    system: {
      title: 'バックエンドシステム',
      subTitle: '美しい管理画面を書くためのわずかな行数',
      welcome: 'ログインへようこそ',
      login: 'ログイン',
      userName: 'ユーザー名',
      password: 'パスワード',
      contentScreen: 'コンテンツ全画面表示',
      fullScreen: '全画面表示',
      fullScreenBack: '全画面表示を終了',
      github: 'GitHubを訪問',
      changePassword: 'パスワードの変更',
      loginOut: 'ログアウト',
      user: '管理者',
      size: {
        default: 'デフォルト',
        large: 'ラージ',
        small: 'スモール',
      },
      setting: {
        name: '設定',
        style: {
          name: 'フルスタイル設定',
          default: 'デフォルトメニュースタイル',
          light: 'ライトメニュースタイル',
          chinese: '中国風メニュースタイル',
          dark: 'ダークメニュースタイル'
        },
        primaryColor: {
          name: 'プライマリーカラー',
          blue: 'デフォルトの青',
          red: 'ローズレッド',
          violet: 'グレイスバイオレット',
          green: 'ストーリーグリーン',
          cyan: 'シアン',
          black: 'ギークブラック'
        },
        other: {
          name: 'その他の設定',
          showLogo: 'ロゴを表示',
          showBreadcrumb: 'パンくずリストを表示',
          keepOnlyOneMenu: 'メニューを1つだけ開いたままにする',
        }
      },
      tab: {
        reload: 'リロード',
        closeAll: 'すべてのタグを閉じる',
        closeOther: '他のタグを閉じる',
        closeCurrent: '現在のタグを閉じる'
      }
    },
  }
}

export default lang
