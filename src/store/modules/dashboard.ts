import { getVieData ,getEnterpriseInsight,getThirdLis} from '@/api/dashboard'
import { ActionContext } from 'vuex'

export interface dashboard {
  viewData: object
  secondViewData:object
  thirdViewData:object
}
const state = (): dashboard => ({
  viewData: {},  // 用户信息
  secondViewData:{},
  thirdViewData:{}
})

// getters
// const getters = {
//   token(state:dashboard) {
//     return state
//   }
// }

// mutations
const mutations = {
    changeViewData(state:any,info:object){
    state.viewData = info
  },
  changeSecondViewData(state:any,info:object){
    // console.log(state,info)
    state.secondViewData =info 
    // state
  },
  changeThirdViewData(state:any,info:object){
    // console.log(state,info)
    state.thirdViewData =info 
    // state
  }
//   infoChange(state: dashboard, info: object) {
//     state.info = info
//   }
}

// actions
const actions = {
  // login by login.vue
  getViewData({ commit, dispatch }: ActionContext<userState, userState>, params: any) {
    return new Promise((resolve, reject) => {
        getVieData(params)
        .then(res => {
            // console.log(res)
            // commit('changeViewData', res.data)
            // console.log(2222,res)
            resolve(res)
        }).catch(err => {
          reject(err)
        })
    })
  },
  getSecondViewData({ commit, dispatch }: ActionContext<userState, userState>, params: any) {
    return new Promise((resolve, reject) => {
      getEnterpriseInsight(params)
        .then(res => {
            // console.log(res)
            commit('changeSecondViewData', res.data)
            // console.log(2222,res)
            resolve(res)
        }).catch(err => {
          reject(err)
        })
    })
  },
  getThirdLisData({ commit, dispatch }: ActionContext<userState, userState>, params: any) {
    return new Promise((resolve, reject) => {
      getThirdLis(params)
        .then(res => {
            // console.log(res)
            commit('changeThirdViewData', res.data)
            // console.log(2222,res)
            resolve(res)
        }).catch(err => {
          reject(err)
        })
    })
  },
  
 
}

export default {
  namespaced: true,
  state,
  actions,
  // getters,
  mutations
}
