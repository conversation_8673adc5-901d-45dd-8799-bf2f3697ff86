import { login<PERSON>pi, getInfo<PERSON>pi, loginOut<PERSON>pi, sign<PERSON>pi,getueser,jurisdiction } from '@/api/user'
import { ActionContext } from 'vuex'
import { useRoute, useRouter, RouteLocationMatched } from "vue-router";
import message from '@/utils/optimizePop.js' //引入
import Cookies from "js-cookie";
const baseURL: any = import.meta.env.VITE_BASE_URL
// const router = useRouter();
import router from '@/router'
import { el } from 'element-plus/es/locale';
import remove from 'lodash/remove';
export interface userState {
  token: string,
  info: any,
  insight:Boolean,
  energy:Boolean,
  userState:String
}
const state = (): userState => ({
  token: '',
  //  'eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIxNTg2NjY2NjY2NiMjLSMjUTAxVEZBNTUjIzEyNy4wLjAuMSMjNWF5ZDM5dm9tNSIsImlhdCI6MTY5NTI4NzE1OCwiZXhwIjoxNjk1ODkxOTU4fQ.Rn4put3gTMl4anWZEuSI3iq8tE_t7uBsW8ra25jt92FW2Py_jBccPXlBFAjrtUDfJARaYF2xWz9cX1QS13iTww'
   // 登录token
  info: null,  // 用户信息
  insight:false,
  energy:false,
  userState:'',
})

// getters
const getters = {
  token(state: userState) {
    return state.token
  }
}

// mutations
const mutations = {
  tokenChange(state: userState, token: string) {
    state.token = token
  },
  infoChange(state: userState, info: object) {
    state.info = info
  },
  insightChange(state: userState, info: Boolean){
    state.insight = info
  },
  energyChange(state: userState, info: Boolean){
    state.energy = info
  },
  userStateChange(state: userState, info: String){
    state.userState = info
  }
}

// actions
const actions = {
  getjurisdiction({state, commit, dispatch }: ActionContext<userState, userState>,token:String) {
    return new Promise((resolve, reject) => {
      let url = baseURL.replace("/jh", "");
      let urls =url + 'sso/admin/roleResource/queryLoginUserResourceTree'
      let params ={
        routePreUrl:'/jh'
      }
      jurisdiction(urls,params,token)
        .then(res => {
          // console.log('res获取权限树',res);    
          commit('energyChange', false)
          commit('insightChange', false)
          res.result.forEach((it: any) => {
           if(it.resourceName==='园区驾驶仓'){
            if(it.childList){
              it.childList.forEach((item: any) =>{
                if(item.resourceName=='企业洞察'){
                  commit('insightChange', true)
                }
                if(item.resourceName=='能耗统计'){
                  commit('energyChange', true)
                }
              })
             }
           }
          });
          // console.log(state);
          
          if(state.userState=="zhengwu"){
            // console.log('政务端');
            
             location.reload()
          }
          resolve(res)
        }).catch(err => {
          // console.log('获取权限树失败',err);
          reject(err)
        })
    })
  },
  login2({ commit, dispatch }: ActionContext<userState, userState>,token:String) {
    commit('tokenChange', token)
    return new Promise((resolve, reject) => {
      getueser(token)
        .then(res => {
          // console.log('获取用户信息',res);
          
          //commit('tokenChange', res.result.token)
          commit('infoChange', res.result)
          // console.log('login',res)
          if(res.result.roleList.indexOf('园区端')>-1){
            localStorage.setItem('userState','admin')
            commit('userStateChange', 'admin')
            router.push('/')
            // console.log('园区端');
            
            if(res?.result.roleList.indexOf("招商经理")>-1||res.result.roleList.indexOf("招商人员")>-1){
              localStorage.setItem('roleOfInvestment','true')}
          }else  if(res.result.roleList.indexOf('政务端')>-1){
            localStorage.setItem('userState','zhengwu')
            commit('userStateChange', 'zhengwu')
              router.push('/dashboard?tab=3')
            //location.reload()
          }else if(res.result.roleList.indexOf('企业端')>-1){
            localStorage.setItem('userState','user')
            commit('userStateChange', 'user')
            router.push('/onLineService')
          }else{
            commit('userStateChange', '')
            localStorage.setItem('userState','')
          }
          // console.log(localStorage.getItem('userState'),'接口调用后');
          resolve(res.data)
          dispatch('getjurisdiction', { token})
        sessionStorage.setItem('firstLogin','true')
        //router.push('/dashboard?tab=3')
        //location.reload()
        sessionStorage.removeItem('loginOut')
        sessionStorage.removeItem('status')
        }).catch(err => {
          reject(err)
        })
    })
  },

  // login by login.vue
  login({ commit, dispatch }: ActionContext<userState, userState>, params: any) {
    return new Promise((resolve, reject) => {
      // console.log("loginApi****2");
      loginApi(params)
        .then(res => {
          commit('tokenChange', res.result.token)
          commit('infoChange', res.result)
          // console.log('login2**',res)
          if(res.result.roleList.indexOf('园区端')>-1){
            localStorage.setItem('userState','admin')
            if(res.result.roleList.indexOf("招商经理")>-1||res.result.roleList.indexOf("招商人员")>-1){
              localStorage.setItem('roleOfInvestment','true')}
          }else  if(res.result.roleList.indexOf('政务端')>-1){
            localStorage.setItem('userState','zhengwu')
          }else if(res.result.roleList.indexOf('企业端')>-1){
            localStorage.setItem('userState','user')
          }else{
            localStorage.setItem('userState','')
          }
        
          resolve(res.data)
        //   dispatch('getInfo', { token: res.result.token })
        //     .then(infoRes => {
        //       resolve(res.data.token)
        //     })
        sessionStorage.setItem('firstLogin','true')
        // router.push('/dashboard?tab=3')
        location.reload()
        sessionStorage.removeItem('loginOut')
        sessionStorage.removeItem('status')
        }).catch(err => {
          reject(err)
        })
    })
  },
  updataToken({ commit, dispatch }: ActionContext<userState, userState>, params: any) {
    commit('tokenChange', params)
  },
  signUp({ commit }: ActionContext<userState, userState>, params: any) {
    return new Promise((resolve, reject) => {
      signApi(params)
        .then(res => {
          resolve(res.data)
        })
    })
  },
  // get user info after user logined
  getInfo({ commit }: ActionContext<userState, userState>, params: any) {
    return new Promise((resolve, reject) => {
      let user=JSON.parse(localStorage.getItem('userInfo')||"{}")?.value
      // getInfoApi(params)
      //   .then(res => {
          commit('infoChange', user)
          resolve(user)
        // })
    })
  },
  router(){
    return useRouter();
  },
  loginOut({ commit }: ActionContext<userState, userState>) {
      loginOutApi()
        .then(res => {
          commit('tokenChange', '')
          commit('infoChange','')
          commit('energyChange', false)
          commit('insightChange', false)
          commit('userStateChange', '')
        })
        .catch(error => {
  
        })
        .finally(() => {
          Cookies.remove("userInfo");

          localStorage.removeItem("userInfo");
          localStorage.removeItem("JH_TOKEN");
          localStorage.removeItem('roleOfInvestment')
          localStorage.removeItem('user')
          localStorage.removeItem('tabs')
          localStorage.removeItem('vuex')
          localStorage.removeItem('userState')
          localStorage.clear()

          sessionStorage.removeItem("JH_TOKEN");
          sessionStorage.removeItem('firstLogin')
          sessionStorage.removeItem('vuex')
          sessionStorage.removeItem('userInfo')
          sessionStorage.setItem('loginOut', 'true')
          sessionStorage.clear()
          message({
              message: '退出登录成功',
              type: 'warning',
              duration: 3 * 1000
          });
        })
    }
}

export default {
  namespaced: true,
  state,
  actions,
  getters,
  mutations
}
