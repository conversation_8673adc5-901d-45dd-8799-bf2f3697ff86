<template>
  <el-config-provider :locale="locale" :size="size">
    <router-view></router-view>
  </el-config-provider>
</template>

<script lang="ts">
import { defineComponent, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useStore } from 'vuex';
export default defineComponent({
  name: 'App',
  setup() {
    const store = useStore();
    const i18n = useI18n();
    const size = computed(() => store.state.app.elementSize);
    const messages: any = i18n.messages.value;
    const locale = computed(() => {
      return {
        name: i18n.locale.value,
        el: messages[i18n.locale.value].el,
      };
    });
    onMounted(() => {});
    return {
      locale,
      size,
    };
  },
});
</script>

<style lang="scss">
#app1,
#app2 {
  font-family: sans-serif, Avenir, Helvetica, Arial;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* text-align: center; */
  color: #2c3e50;
  width: 100%;
  height: 100vh;
}

:focus-visible {
  outline: none;
}
.mineClass {
  display: none;
}
#wujie #app1 {
  .amis-scope .antd-ImageGallery {
    background-color: #000000 !important;
  }
  .antd-ImageGallery-close {
    left: 16px;
  }
  .el-main {
    // margin-top: 55px ;
    height: calc(100% - 65px);
    background: #fafcff !important;
    border-radius: 0 0 0 20px;

    // padding: 25px;

    .mainContent {
      // padding: 0 16px;
      // box-shadow: 0px 4px 12px 0px #eef1f8;
      // border-radius: 10px;
      // .el-main-box {
      //   border-radius: 10px;
      // }
    }
    .amis-scope .antd-Table-table > thead > tr {
      background: linear-gradient(180deg, #fafbff 0%, #f0f4ff 100%) !important;
    }
  }
  .publicTableStyle .antd-Crud.tableAutoStyle,
  .publicTableStyle .antd-Page-content {
    background: #fafcff !important;
  }
  .publicTableStyle .amis-scope .antd-Page-content {
    padding: 0px !important;
  }
  // .el-aside.show-side,.el-header,.el-aside.hide-aside{
  //   display: none !important;
  // }

  .navbar {
    display: none !important;
    box-shadow: 0 0 0 !important;
    background: transparent !important;
    div {
      display: none !important;
    }
  }
  .sidebar-container,
  .logo {
    display: none !important;
  }
  .main-container.hasTagsView {
    margin-left: 0 !important;
  }
  .el-overlay {
    background: transparent !important;
  }
  .antd-Modal-overlay.in,
  .antd-Drawer-overlay.in {
    opacity: 0 !important;
  }
  .antd-Drawer-content.in {
    box-shadow: -10px 0 12px -4px rgb(153 163 190 / 20%) !important;
  }
  .antd-Modal-content {
    box-shadow: 0px 0 16px 10px rgb(153 163 190 / 20%) !important;
  }
  .antd-Modal-header .antd-Modal-title,
  .antd-Drawer-title {
    font-size: 18px;
    color: #000000;
  }
  .antd-Drawer-footer {
    box-shadow: 0px -4px 10px 0px rgba(0, 0, 0, 0.1);
  }
  .el-input-number.is-center .el-input__inner {
    text-align: left;
  }
}
.emptyCustomize {
  .el-empty__description {
    font-size: 16px;
    font-weight: 600;
    color: #86909c;
    line-height: 24px;
    margin-top: 0;
  }
}
.enterpriseDetail {
  .antd-Drawer-body {
    &::before {
      height: 0 !important;
    }
  }
  .add.form-b-sm {
    .formCards {
      .antd-Form-control {
        width: 100% !important;
      }
    }
  }
  .form-b-sm {
    height: 100%;
    padding: 10px;

    .preview {
      .antd-TextareaControl-counter {
        display: none;
      }
    }
    .antd-Flex.p-1.p-border {
      margin-top: 16px;
      border: 1px solid #f7f7f7;
      width: 100%;
      padding: 0px;
      border-radius: 4px;
      .antd-Container {
        width: 100%;
        padding: 0px;
      }
      .form-title {
        background: #f7f7f7;
        padding: 6px 10px;
        font-weight: 600;
      }
      .formCards {
        width: 100%;
        flex-wrap: wrap;
        place-content: space-between !important;
        padding: 16px;
        .textarea-custemor.antd-Form-item {
          width: 100%;
        }
        .antd-Form-item {
          width: 50%;
          .antd-Form-label .antd-Form-star {
            right: -7px !important;
            font-size: 14px;
            top: 0;
          }
          .antd-TextareaControl-counter.is-empty {
            display: none;
          }
        }
        .antd-NumberControl {
          width: 100% !important;
        }
      }
    }
  }
  .height0 {
    height: 0;
  }
}
.el-splitter-panel{
box-shadow: -10px 0 0.75rem -4px rgba(153, 163, 190, 0.2) !important;
.el-drawer__header
{
  padding-bottom: 16px;
  margin-bottom: 16px;
  border-bottom: var(--Drawer-content-borderWidth) solid var(--Drawer-header-borderColor);
}
}
.demo-drawer__body {
    box-shadow: -4px 4px 10px 0px #eef1f8;
    .el-drawer__body{
  padding: 0px;
    }
  
 
}
.el-message-box__btns{
  .el-button{
   padding: 15px 18px;
  }
}
</style>
