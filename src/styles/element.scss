
/* theme color */

$--color-primary:#3370FF;
$--color-success: #13ce66;
$--color-warning: #ffba00;
$--color-danger: #ff4949;
// $--color-info: #1E1E1E;
/* 停车状态 颜色 */
$--color-gray:#999;

/*标题背景色*/
$--color-title-bg:#fbfbfb;

$--button-font-weight: 400;

// $--color-text-regular: #1f2d3d;

$--border-color-light: #dfe4ed;
$--border-color-lighter: #e6ebf5;

$--table-border: 1px solid #dfe6ec;

@forward 'element-plus/theme-chalk/src/common/var.scss' with (
  $colors: (
    'primary': (
      'base': #3370FF,
    ),
  )
);

.el-button--primary {
  background-color: $--color-primary;
};
:root {
  --el-color-primary: #3370FF;
};