// import { style } from './theme/index';
/*
 * @Date: 2022-05-22 20:44:25
 * @Description: 
 */
import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import { baidu } from './utils/system/statistics'
import 'element-plus/theme-chalk/display.css' // 引入基于断点的隐藏类
import 'element-plus/dist/index.css'
import 'normalize.css' // css初始化
import './assets/style/common.scss' // 公共css
import './theme/modules/chinese/index.scss'
import './styles/element.scss'
import App from './App.vue'
import store from './store'
import router from './router'
import { useRoute, useRouter } from "vue-router";
import { getAuthRoutes } from './router/permission'
import i18n from './locale'
import "amis/lib/themes/default.css";
// import "amis/lib/helper.css";
import "amis/sdk/iconfont.css";
import "amis/sdk/helper.css";
import "amis/sdk/antd.css";
import "amis/sdk/sdk.css";
import './assets/style/style.scss'
import "amis/sdk/sdk";
// import '@/utils/rems.js'
// import 'swiper/swiper.scss';
// import "tailwindcss/tailwind.css"
import {setRem} from '@/utils/rems.js'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'




if (import.meta.env.MODE !== 'development') { // 非开发环境调用百度统计
  baidu()
}
// 改变窗口大小时重新设置 rem
window.onresize = function () {
  let path={path:window.location.hash.slice(1,11)}
  setRem(path);
};
/** 权限路由处理主方法 */

// getAuthRoutes().then(() => {
  const app = createApp(App)
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
  }
  app.use(router)
app.use(ElementPlus )
  // store.state.app.elementSize
  app.use(store)
  app.use(i18n)
  app.mount("#app1");

window.addEventListener('message', function (e) {  // 监听 message 事件
  // console.log(e);
  localStorage.removeItem('JH_TOKEN')
  let hosts= window.location.hostname==='localhost'? 
  "https://localhost:3001": window.location.origin
  if (e.origin !== hosts) {  // 验证消息来源地址
      return;
  }
JSON.stringify(e.data?.mainUserInfo)&& localStorage.setItem('userInfo', JSON.stringify(e.data?.mainUserInfo))
if( e.data?.token&&e.data?.token!=='undefined') { localStorage.setItem('JH_TOKEN', e.data?.token)}
});



function setRootFontSize() {
  const baseSize = 16; // 基准字体大小
  const baseWidth = 1920; // 设计稿基准宽度
  const scale = document.documentElement.clientWidth / baseWidth;
  const fontSize = baseSize * scale;
  
  // 设置最小和最大字体大小以防止过度缩放
  document.documentElement.style.fontSize = Math.max(12, Math.min(fontSize, 24)) + 'px';
}

// 初始化
setRootFontSize();

// 监听窗口大小变化
window.addEventListener('resize', setRootFontSize);