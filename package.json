{"name": "vue-admin-box", "version": "1.1.0", "scripts": {"dev": "vite  --mode dev", "start": "vite ", "build:pro": "vite build --mode=pro", "build:dev": "vite build --mode=development", "build:test": "vite build --mode=test", "build": "vite build --mode=development", "build:stag": "vite build --mode=staging", "serve": "vite preview"}, "dependencies": {"@antv/ava": "^3.0.7", "@antv/g2plot": "^2.4.31", "@element-plus/icons": "^0.0.11", "@element-plus/icons-vue": "^2.3.1", "@kangc/v-md-editor": "^2.3.5", "@kjgl77/datav-vue3": "^1.6.1", "@vueuse/core": "^8.0.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "amis": "^3.6.4", "axios": "^0.26.1", "codemirror": "^5.65.2", "copy-to-clipboard": "^3.3.3", "cropperjs": "^1.5.12", "dayjs": "^1.11.9", "echarts": "^5.3.1", "element-plus": "^2.10.3", "jsencrypt": "^3.3.2", "lodash": "^4.17.21", "mockjs": "^1.1.0", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "postcss-pxtorem": "^6.0.0", "qs": "^6.11.2", "react": "^18.2.0", "react-dom": "^18.2.0", "sortablejs": "^1.14.0", "split": "^1.0.1", "splitpanes": "^3.1.1", "swiper": "^10.2.0", "throttle-debounce": "^3.0.1", "v-contextmenu": "^3.0.0", "vue": "^3.5.17", "vue-awesome-swiper": "^5.0.1", "vue-cropper": "^1.1.1", "vue-i18n": "9.1.10", "vue-router": "4", "vuex": "^4.0.2", "xlsx": "^0.18.3"}, "devDependencies": {"@types/node": "^17.0.21", "@types/qs": "^6.9.8", "@vitejs/plugin-vue": "^2.2.4", "@vue/compiler-sfc": "^3.2.31", "@webxrd/vite-plugin-svg": "^1.0.5", "eslint": "^8.11.0", "js-cookie": "^3.0.5", "sass": "^1.49.9", "typescript": "^4.6.2", "unplugin-auto-import": "^0.6.4", "unplugin-vue-components": "^0.18.0", "vite": "^2.8.6", "vite-plugin-mock": "2.9.6", "vue-tsc": "^0.32.1"}}